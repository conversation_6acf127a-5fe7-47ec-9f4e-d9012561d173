<template>
  <div class="scene-tool-container">
    <div class="compare-title" @click="closeCompare">
      <svg-icon icon-class="close" />
    </div>
    <div class="viewer3d-wrap">
      <div id="viewer-left" class="customContainer v-left" />
      <Resource class="resource-compare" :viewer3d="viewers[0]" :has-floor="false"
                :is-only-add-terrain-layer="true"
      />
    </div>
    <div class="viewer3d-wrap">
      <div id="viewer-right" class=" customContainer v-right" />
      <Resource class="resource-compare" :viewer3d="viewers[1]" :has-floor="false"
                :is-only-add-terrain-layer="true"
      />
    </div>
  </div>
</template>

<script setup name="ScreenCompare">
import Resource from "@/components/GISTools/3d/Resource/Resource"

import useMapViewStore from "@/store/modules/map/mapView.js"
import { destroyViewer, flyToPoint, setSceneEffect } from "@/utils/Cesium/CesiumTool.js"
import { addIserverLayer, addTdtImageLayer, addTDTTerrainLayer } from "@/utils/Cesium/CesiumLayer.js"

const { proxy } = getCurrentInstance()
const mapInfo = useMapViewStore().mapInfo

const isShowCompare = ref(true)
const viewerLeft = ref(undefined)
const viewerRight = ref(undefined)
const viewerIds = ref(["viewer-left","viewer-right"])
const viewers = ref([])


watch(viewers, (viewerArr, oldVal) => {
  if (viewerArr.length == 2){
    viewerArr.forEach(viewer3d => {
      // 设置图层显示
      const wmtsOption = {
        url: mapInfo.url,
        layerName: mapInfo.layerName
      }
      addIserverLayer(viewer3d,wmtsOption)
    })
  }
})


const closeCompare = ()=>{
  document.querySelector("#defaultViewer3d").style.display = "block"
  proxy.$emit("closeCompare")
  viewers.value.forEach(viewer3d => {
    destroyViewer(viewer3d)
  })
}

const initMap = ()=>{
  viewerIds.value.forEach(viewerId => {
    const viewer3d = new Cesium.Viewer(viewerId,{
      selectionIndicator: false,
      timeline: false,
      baseLayerPicker: false,
      shadows: true,
      infoBox: false,
      geocoder: false,
      skyBox: false, // 关闭天空盒会一同关闭太阳，场景会变暗
      navigation: false, // 初始化导航控件
      contextOptions: {
        contextType: 2 // Webgl2:2 ; WebGPU:3
      }
    })
    addTDTTerrainLayer(viewer3d)
    addTdtImageLayer(viewer3d)
    setSceneEffect(viewer3d)
    // 去除 SuperMap logo
    viewer3d._cesiumWidget._creditContainer.style.display = "none";
    flyToPoint(viewer3d)
    viewers.value.push(viewer3d)
  })
  setViewerState()
}

const setViewerState = ()=>{
  viewers.value.forEach(viewerObj => {
    const handler = new Cesium.ScreenSpaceEventHandler(viewerObj.scene.canvas)
    handler.setInputAction((movement) => {
      viewers.value.forEach(viewer => {
        if (viewerObj !== viewer) {
          const camera = viewerObj.camera
          viewer.camera.setView({
            destination: camera.position,
            orientation: {
              direction: camera.direction,
              up: camera.up,
              heading: camera.heading,
              pitch: camera.pitch,
              roll: camera.roll
            }
          })
        }
      })
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE)
    handler.setInputAction((movement) => {
      viewers.value.forEach(viewer => {
        if (viewerObj !== viewer) {
          const camera = viewerObj.camera
          viewer.camera.setView({
            destination: camera.position,
            orientation: {
              direction: camera.direction,
              up: camera.up,
              heading: camera.heading,
              pitch: camera.pitch,
              roll: camera.roll
            }
          })
        }
      })
    }, Cesium.ScreenSpaceEventType.WHEEL)
  })
}

onMounted(()=>{
  initMap()
})

</script>

<style scoped lang="scss">
.scene-tool-container{
  position: absolute;
  z-index: 999;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.customContainer {
  position: absolute;
  top: 0;
  left:0;
  width: 100%;
  height: 100%;
  background-color: #ffffff;
}
.v-left{
  width: 50%;
}
.v-right{
  left: 50%;
  width: 50%;
}
.compare-title{
  position: absolute;
  z-index: 999;
  top: 10px;
  left: 98%;
  transform: translateX(-50%);
  background-color: #0e0d0d;
  padding: 10px;
  color: #ffff;
  border-radius: 50%;
  font-size: 16px;
  &:hover{
    transition: all ease-in-out .25s;
    cursor: pointer;
    color: red;
    background-color: #fff;
  }
}
.viewer3d-wrap{
  width: 100vw;
  height: 100vh;
}
.resource-compare{
  position: absolute;
  margin-top: 20px;
  margin-left: 20px;
  overflow-y: auto;
  max-height: 66%;
  background: linear-gradient(to right, #03070bed, #153357d1, #03060c6b) !important;
}
.map-tree-warp{
  max-height: 100%!important;
}
.map-tree{
  overflow-y: auto;
  max-height: 66%;
}
</style>
