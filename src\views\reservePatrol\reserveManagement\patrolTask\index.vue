<template>
  <div class="main-content">
    <div v-if="!showDetail">
      <transition>
        <div
          v-show="showSearch"
          class="mb-[10px]"
        >
          <el-card shadow="hover">
            <el-form
              class="query-form"
              ref="queryFormRef"
              :model="queryParams" :inline="true"
            >
              <el-form-item
                label="项目名称"
                prop="name"
              >
                <el-input
                  v-model="queryParams.name"
                  placeholder="请输入项目名称"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item
                label="地块名称"
                prop="dkmc"
              >
                <el-input
                  v-model="queryParams.dkmc"
                  placeholder="请输入地块名称"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item
                label="任务状态"
                prop="rwzt"
              >
                <el-select
                  v-model="queryParams.rwzt"
                  clearable
                  style="width: 200px"
                >
                  <el-option
                    :key="1"
                    label="已巡查" :value="0"
                  />
                  <el-option
                    :key="2"
                    label="未巡查" :value="1"
                  />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  icon="Search" @click="handleQuery"
                >搜索
                </el-button>
                <el-button
                  icon="Refresh"
                  @click="resetQuery"
                >重置</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </div>
      </transition>
      <el-row
        :gutter="10"
        class="mb8 searchbar"
      >
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="Upload"
            @click="handleExport"
          >导出
          </el-button>
        </el-col>
        <right-toolbar
          v-model:showSearch="showSearch"
          @queryTable="getList"
        />
      </el-row>
      <el-card class="result-wrap">
        <el-table
          v-loading="loading"
          :data="patrolTaskList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            label="项目名称"
            align="center" prop="name"
          />
          <el-table-column
            label="临时用途"
            align="center" prop="temp.lslyyt"
          />
          <el-table-column
            label="地块名称"
            align="center" prop="land.dkmc"
          />
          <!--          <el-table-column-->
          <!--            label="地块编号"-->
          <!--            align="center" prop="land.dkbh"-->
          <!--          />-->
          <el-table-column
            label="地块面积(公顷)"
            align="center" prop="land.dkmj"
          />
          <!--          <el-table-column-->
          <!--            label="地块位置"-->
          <!--            align="center" prop="land.dz"-->
          <!--          />-->
          <el-table-column
            label="年度"
            align="center" prop="releaseTime"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.yqzxsj, "{y}") }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="巡查状态"
            align="center" prop="rwzt"
          >
            <template #default="scope">
              <el-tag
                v-if="scope.row.rwzt === '1'"
                type="success"
              >已巡查</el-tag>
              <el-tag
                v-else
                type="danger"
              >未巡查</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="180"
          >
            <template #default="scope">
              <el-button
                plain
                type="primary" size="small" @click="handleUpdate(scope.row)"
              >巡查</el-button>
              <el-button
                plain
                type="danger" size="small" @click="handleDelete(scope.row)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>
  </div>
</template>

<script setup name="ReservePatrolTask">
/**
 * CHN：储备管护【巡查任务】
 */
import {
  listPatrolTask,
  delPatrolTask,
  addPatrolTask,
  updatePatrolTask
} from "@/api/reserve/reserveManage.js";

import {
  getLandDetail,
  getTemporaryManage,
  listTemporaryManage
} from "@/api/patrol/tempMaintenance.js";

const { proxy } = getCurrentInstance();
import { useRouter } from "vue-router"
const router = useRouter()


const showDetail = ref(false);
const patrolTaskList = ref([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref();
const patrolTaskFormRef = ref();

const initFormData = {
  id: undefined,
  dkId: undefined,
  name: undefined,
  releaseTime: undefined,
  dkmj: undefined,
  dkbh: undefined,
  tbbh: undefined,
  dkzl: undefined,
  rwfbr: undefined,
  fbrId: undefined,
  rwjsr: undefined,
  jsrId: undefined,
  yqzxsj: undefined,
  sfyfb: false,
  rwzt: undefined,
  bz: undefined
};
const data = reactive({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    dkId: undefined,
    name: undefined,
    releaseTime: undefined,
    dkmj: undefined,
    dkbh: undefined,
    tbbh: undefined,
    dkzl: undefined,
    rwfbr: undefined,
    fbrId: undefined,
    rwjsr: undefined,
    jsrId: undefined,
    yqzxsj: undefined,
    sfyfb: undefined,
    rwzt: undefined,
    bz: undefined,
    params: {}
  },
  rules: {
    name: [{ required: true, message: "任务名称不能为空", trigger: "blur" }],
    releaseTime: [{ required: true, message: "任务发布时间不能为空", trigger: "blur" }],
    dkbh: [{ required: true, message: "地块编号不能为空", trigger: "blur" }],
    dkzl: [{ required: true, message: "地块坐落不能为空", trigger: "blur" }],
    yqzxsj: [{ required: true, message: "预期执行时间不能为空", trigger: "blur" }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询巡查任务管理列表 */
const getList = async () => {
  loading.value = true;
  const res = await listPatrolTask(queryParams.value);
  patrolTaskList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  showDetail.value = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  temp.value = {};
  patrolTaskFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};
//标题变量
const ghxcTitle = ref('新建管护巡查任务')

/** 多选框选中数据 */
const handleSelectionChange = (selection) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  ghxcTitle.value = '新建管护巡查任务'
  showDetail.value = true;
};

const temp = ref({});
/** 发布巡查任务 */
const handleUpdate = (row) => {
  router.push({ path: "/reserveProject/reserveLand/reservePatrolTaskDetail",query: { id: row.id } })
};

/** 提交按钮 */
const submitForm = () => {
  patrolTaskFormRef.value?.validate(async (valid) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updatePatrolTask(form.value).finally(
          () => (buttonLoading.value = false)
        );
      } else {
        await addPatrolTask(form.value).finally(
          () => (buttonLoading.value = false)
        );
      }
      proxy?.$modal.msgSuccess("操作成功");
      showDetail.value = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal
    .confirm('是否确认删除巡查任务管理编号为"' + _ids + '"的数据项？')
    .finally(() => (loading.value = false));
  await delPatrolTask(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    "patrol/patrolTask/export",
    {
      ...queryParams.value
    },
    `patrolTask_${new Date().getTime()}.xlsx`
  );
};

const dataChange = (e) => {
  if (e) {
    rules.value.rwjsr = [{ required: true, message: "预期执行时间不能为空", trigger: "blur" }];
  } else {
    rules.value.rwjsr = [];
  }
};

const querySearch = async (queryString, callback) => {
  const results = await createFilter(queryString)
  callback(results.rows);
}
const createFilter = async (queryString) => {
  return await listTemporaryManage({
    htbh: queryString,
    sfysh: false,
    pageNum: 1,
    pageSize: 10
  });
}

const handleSelect = async (row) => {
  const tempRes = await getTemporaryManage(row.id);
  temp.value = tempRes.data;
  form.value.ghId = tempRes.data.id;

  const landRes = await getLandDetail(tempRes.data.dkId);
  form.value.dkbh = landRes.data.dkbh;
  form.value.dkzl = landRes.data.dz;
  form.value.tbbh = landRes.data.tbbh;
  form.value.dkmj = landRes.data.dkmj;
  form.value.dkId = landRes.data.id;
}


onMounted(() => {
  getList();
});
</script>
<style lang="scss" scoped>
.main-content {
  padding: 10px;
}

.result-wrap {
  margin-top: 10px;
}

.content {
  border: 1px solid rgb(233, 233, 233);
  border-radius: 4px;
}

.add-content-task {
  padding: 0px 10px;
}

.add-header-title {
  padding: 10px;
  display: flex;
  justify-content: space-between;
  height: 50px;
  background-color: rgb(222, 239, 255);
  box-sizing: border-box;
  border-bottom: 1px solid rgb(233, 233, 233);
  font-weight: 700;
  font-size: 14px;
  line-height: 28px;
}

.add-title-return {
  display: flex;
  align-content: center;
  color: rgb(32, 119, 255);
  cursor: pointer;
  font-weight: normal;
}

.footer-button-task {
  display: flex;
  justify-content: center;
  margin: 20px -10px 0 -10px;
  border-top: 1px solid #e0e0e0;
  padding: 20px 0;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  button {
    height: 40px;
  }
}

.back {
  height: 18px;
  width: 18px;
  margin-top: 5px;
}

.backlist {
  padding-left: 6px;
  font-size: 14px;
}
.searchbar {
  margin-top: 10px;
}

.el-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.query-form{
  /**防止输入框出现清除按钮时输入框产生宽度变化**/
  :deep(.el-input--suffix) {
    // 固定宽度
    width: 200px !important;
  }
}
@media(max-width: 1000px){
  .query-form{
    :deep(.el-form-item) {
      margin-bottom: 18px !important;
    }
  }
}
@media(min-width: 1500px){
  .query-form{
    :deep(.el-form-item) {
      margin-bottom: 0 !important;
    }
  }
}
</style>
