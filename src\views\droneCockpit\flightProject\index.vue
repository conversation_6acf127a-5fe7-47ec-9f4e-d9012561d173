<template>
  <div class="container">
    <div v-if="!showDetail" class="project-list">
      <transition name="fade">
        <div v-show="showSearch" class="search-container">
          <el-card shadow="hover" class="search-card">
            <el-form ref="queryFormRef" :model="queryParams" :inline="true">
              <el-form-item label="项目名称" prop="name" class="serchtop">
                <el-input v-model="queryParams.name" placeholder="请输入项目名称" clearable @keyup.enter="handleQuery"/>
              </el-form-item>
              <el-form-item class="serchtop">
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button type="primary" icon="Search" @click="cs">cs</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </div>
      </transition>

      <el-card shadow="never" class="result-card">
        <el-table v-loading="loading" :data="reserveProjectList" @selection-change="handleSelectionChange" style="width: 100%" stripe>
          <el-table-column label="项目名称" align="center" prop="name" width="180" />
          <el-table-column label="项目创建时间" align="center" prop="createdAt" width="180"/>
          <el-table-column label="项目更新时间" align="center" prop="updatedAt" width="180" />
          <el-table-column label="经度" align="center" prop="latitude"  width="180">
            <template #default="scope">
                {{ StatusUtils.toFixed3(scope.row.projectWorkCenterPoint.latitude)}}
            </template>
          </el-table-column>
          <el-table-column label="纬度" align="center" prop="longitude" width="180" >
            <template #default="scope">
              {{ StatusUtils.toFixed3(scope.row.projectWorkCenterPoint.longitude)}}
            </template>
          </el-table-column>
          <el-table-column label="项目描述" align="center" prop="introduction" />
          <el-table-column label="操作" align="center" width="100" fixed="right">
            <template #default="scope">
              <el-button plain type="primary" size="small" @click="handleUpdate(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
            class="pagination-container"
        />
      </el-card>
    </div>

    <div v-else class="content">
      <div class="add-header-title">
        <div class="add-title">飞行项目详情</div>
        <div class="add-title-return" @click="cancel">
          <img src="@/assets/images/img-return.png" class="back"/>
          <div class="backlist">返回列表</div>
        </div>
      </div>

      <div class="detail-section">
        <div class="content-title-1">
            <img src="@/assets/images/left.png"/>
            <p>地块信息</p>
          </div>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="项目名称">{{ projectInfo.name }}</el-descriptions-item>
          <el-descriptions-item label="项目创建时间">{{ projectInfo.createdAt }}</el-descriptions-item>
          <el-descriptions-item label="经度">{{ StatusUtils.toFixed3(projectInfo.projectWorkCenterPoint.longitude) }}</el-descriptions-item>
          <el-descriptions-item label="项目更新时间">{{ projectInfo.updatedAt }}</el-descriptions-item>
          <el-descriptions-item label="纬度">{{ StatusUtils.toFixed3(projectInfo.projectWorkCenterPoint.latitude) }}</el-descriptions-item>
          <el-descriptions-item label="项目描述">{{ projectInfo.introduction}}</el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="detail-section">
        <div class="content-title-1">
            <img src="@/assets/images/left.png"/>
            <p>无人机设备</p>
          </div>
        <el-table :data="drones" empty-text="暂无无人机设备" style="width: 100%">
          <el-table-column prop="name" label="无人机设备型号" >
            <template #default="scope">
              {{ scope.row.drone.deviceModel.name }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="detail-section">
        <div class="content-title-1">
            <img src="@/assets/images/left.png"/>
            <p>航线信息</p>
          </div>
        <el-table :data="routes" empty-text="暂无航线信息" style="width: 100%">
          <el-table-column prop="name" label="航线名称" />
          <el-table-column prop="templateTypes" label="航线类型" />
        </el-table>
      </div>

      <div class="detail-section">
        <div class="content-title-1">
            <img src="@/assets/images/left.png"/>
            <p>飞行任务</p>
          </div>
        <el-table :data="tasks" empty-text="暂无飞行任务" style="width: 100%">
          <el-table-column prop="name" label="任务名称"/>
          <el-table-column prop="status" label="任务状态"/>
          <el-table-column prop="taskType" label="任务类型"/>
          <el-table-column prop="resumableStatus" label="上传状态"/>
          <el-table-column prop="mediaUploadStatus" label="文件状态"/>
          <el-table-column prop="beginAt" label="开始时间">
            <template #default="scope">
              {{ formatDate(scope.row.beginAt) }}
            </template>
          </el-table-column>
          <el-table-column prop="endAt" label="结束时间">
            <template #default="scope">
              {{ formatDate(scope.row.endAt) }}
            </template>
          </el-table-column>
          <el-table-column prop="totalWaypoints" label="航点总数"/>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup name="ReserveProject">
import { InfoFilled, Collection, MapLocation, Aim } from '@element-plus/icons-vue';
import {organizationProjectList} from "@/api/uav/flightHub2/organization/project.js";
import {projectByProjectId} from "@/api/uav/project/index.js";
import {formatDate} from "@/constants/flightUtils.js";
import {StatusUtils} from  "@/constants/flightTask.js"
import {identifyImage} from "@/api/identify/index.js";
const { proxy } = getCurrentInstance();
const showDetail = ref(false);
const reserveProjectList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const queryFormRef = ref();
const initFormData = {
  id: undefined,
  bz: undefined,
  rightBos: undefined,
  landBos: undefined,
};
const data = reactive({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    params: {},
  },
  rules: {
    id: [{ required: true, message: "$comment不能为空", trigger: "blur" }],
  },
});
const { queryParams, form, rules } = toRefs(data);
// 项目信息
const projectInfo = ref({});
// 无人机设备
const drones = ref([
  { name: "一号无人机" },
  { name: "二号无人机" },
  { name: "三号无人机" },
]);

// 航线信息
const routes = ref([
  { name: "航线名称1" },
  { name: "航线名称2" },
  { name: "航线名称3" },
]);

// 飞行任务
const tasks = ref([
  { name: "任务名称1" },
  { name: "任务名称2" },
  { name: "任务名称3" },
]);

// 查看无人机设备
const viewDrone = (drone) => {
  proxy.$modal.alert(`查看无人机设备: ${drone.name}`);
};

// 查看航线信息
const viewRoute = (route) => {
  proxy.$modal.alert(`查看航线信息: ${route.name}`);
};

// 查看飞行任务
const viewTask = (task) => {
  proxy.$modal.alert(`查看飞行任务: ${task.name}`);
};

/** 查询储备项目列表 */
const getList = async () => {
  loading.value = true;
  const res = await organizationProjectList(queryParams.value);
  reserveProjectList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  showDetail.value = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};
/** 搜索按钮操作 */
const cs = () => {
  identifyImage()
};
/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 修改按钮操作 */
const handleUpdate =async  (row) => {
  const res =await projectByProjectId(row.uuid)
  projectInfo.value = row
  drones.value = res.data.devices;
  tasks.value = res.data.tasks;
  routes.value = res.data.waylines;
  showDetail.value = true;
};

onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.container {
  padding: 20px;
  background-color: #f5f7fa;
  overflow-y: auto; /* 当内容超出容器高度时显示滚动条 */
  height: 85vh; /* 需要设置一个固定高度或最大高度 */
}
.content {
  border: 1px solid rgb(233, 233, 233);
  border-radius: 4px;
  background: #ffffff;
}
.search-container {
  margin-bottom: 20px;

  .search-card {
    border-radius: 8px;

    :deep(.el-card__body) {
      padding: 20px;
    }
  }
}

.result-card {
  border-radius: 8px;

  :deep(.el-card__body) {
    padding: 0;
  }
}

.pagination-container {
  padding: 16px;
  text-align: right;
}

.project-detail {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;

  .detail-title {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
    margin: 0;
  }

  .back-button {
    border-radius: 4px;
  }
}

.detail-section {
  padding: 15px;
  :deep(.el-card__body) {
    padding: 10px;
  }
  :deep(.el-table--default .cell) {
    padding: 0 12px;
    text-align: center;
}
  :deep(.el-card) {
    --el-card-border-color: none;
    --el-card-border-radius: 0;
    box-shadow: none !important;
    border: none !important;
  }
  :deep(.el-table) {
  margin-top: 1px;
}
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;

  .el-icon {
    margin-right: 8px;
    color: #409eff;
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// :deep(.el-descriptions) {
//   margin-top: 16px;
// }

:deep(.el-table) {
  margin-top: 16px;

  .el-button {
    padding: 5px 12px;
  }
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: #fafafa;
}

:deep(.el-table th.el-table__cell) {
  background-color: #f5f7fa;
}

.serchtop {
  margin-bottom: 0 !important;
}
.add-header-title {
  padding: 10px;
  display: flex;
  justify-content: space-between;
  height: 50px;
  font-weight: bold;
  background-color: rgb(222, 239, 255);
  box-sizing: border-box;
  border-bottom: 1px solid rgb(233, 233, 233);
  font-weight: 700;
  font-size: 16px;
  line-height: 28px;
}

.add-title-return {
  display: flex;
  align-content: center;
  color: rgb(32, 119, 255);
  cursor: pointer;
  font-weight: normal;
}

.back {
  height: 18px;
  width: 18px;
  margin-top: 5px;
}

.backlist {
  padding-left: 6px;
  font-size: 14px;
}
.content-title-1 {
  font-weight: bold;
  display: flex;
  align-items: center;

  p {
    margin-left: 8px;
  }
}
</style>

