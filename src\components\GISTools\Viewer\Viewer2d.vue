<template>
  <div
    :id="viewerId"
    class="viewer2d-container"
    ref="viewer2dRef"
    v-loading="isLoading"
    element-loading-text="正在加载"
  />
</template>

<script setup name="Viewer2d">
import eventBus from '@/utils/eventBus.js'
import useMapInitStore from "@/store/modules/map/mapInit.js"
import useMapViewStore from "@/store/modules/map/mapView.js"

defineProps({
  viewerId: {
    type: String,
    required: false,
    default: "defaultViewer2d"
  }
})

const viewer2dRef = ref(null)
const isLoading = ref(false)


// 监听是否显示地图遮罩层
eventBus.on('showMapLoading',value=>{
  isLoading.value = value
})

// const initMap = ()=>{
//   const useViewer2d = new UseViewer2d(map2dContainerRef.value,useMapInitStore(),useMapViewStore())
//   useViewer2d.createViewer2d()
// }

// 在子组件暴露属性给父组件
// defineExpose({
//   map2dContainerRef,
//   initMap
// })

const emits = defineEmits({
  onCreateViewer2d: (viewer2dId)=>{
    if(!viewer2dId){
      throw new Error("地图挂载元素或ID无效，请重新设置！")
    }
    return true
  }
})

onMounted(()=>{
  emits('onCreateViewer2d',viewer2dRef.value,useMapInitStore(),useMapViewStore())
})

</script>

<style scoped lang="scss">
.viewer2d-container {
  //position: absolute;
  //top: 0;
  //left:0;
  //right: 0;
  //bottom: 0;
  width: 100%;
  height: 100%;
  background-color: #ffffff;
}
:deep(.el-loading-mask){
  background-color:#031f3094!important;
}
</style>
