import request from "@/utils/request";

/**
 * 查询拆迁巡查任务管理列表
 * @param query
 * @returns {*}
 */

export const listRelPatrolTask = (query) => {
  return request({
    url: "/patrol/relPatrolTask/list",
    method: "get",
    params: query
  });
};

/**
 * 查询拆迁巡查任务管理详细
 * @param id
 */
export const getRelPatrolTask = (id) => {
  return request({
    url: "/patrol/relPatrolTask/" + id,
    method: "get"
  });
};

/**
 * 新增拆迁巡查任务管理
 * @param data
 */
export const addRelPatrolTask = (data) => {
  return request({
    url: "/patrol/relPatrolTask",
    method: "post",
    data: data
  });
};

/**
 * 修改拆迁巡查任务管理
 * @param data
 */
export const updateRelPatrolTask = (data) => {
  return request({
    url: "/patrol/relPatrolTask",
    method: "put",
    data: data
  });
};

/**
 * 删除拆迁巡查任务管理
 * @param id
 */
export const delRelPatrolTask = (id) => {
  return request({
    url: "/patrol/relPatrolTask/" + id,
    method: "delete"
  });
};

/**
 * 导出征拆巡查项目任务
 */
export const exportRelPatrolTask = () => {
  return request({
    url: "/patrol/relPatrolTask/export",
    method: "post",
    responseType: "blob"
  })
}

/**
 *  上传巡查照片数据
 */
export const exportPatrolImages = ()=>{
  return request({
    url: "/patrol/adduceImage/uploadImages",
    method: "post"
  })
}