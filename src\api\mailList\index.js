/**
 * 通讯录
 */

import request from '@/utils/request'

/** 
 * 查询通讯录部门列表
 * @param {string} deptId 部门id
  * @param {string} parentId 父部门id
  * @param {string} deptName 部门名称
  * @param {string} addressName 单位地址
  * @param {string} postalCode 邮政编号
  * @param {string} phone 联系电话
  * @param {string} email 单位电子邮箱
  * @param {string} fax 传真
  * @param {string} sortNum 排序
  * @param {string} createDept 创建部门
  * @param {string} createBy 创建者
  * @param {string} createTime 创建时间
  * @param {string} updateBy 更新者
  * @param {string} updateTime 更新时间
  * @returns
 */
export function getMailListDept(query) {
    return request({
      url: '/document/bookDept/list',
      method: 'get',
      params: query
    })
  }

/** 
 * 删除通讯录部门
 * @param {string} deptIds 主键串
  * @returns
 */
export function delUnitDept(deptIds) {
    return request.delete(`/document/bookDept/${deptIds}`);
  }

/** 
 * 新增通讯录部门
 * @param {object} params 通讯录部门业务对象 address_book_dept
 * @param {number} params.createDept 创建部门
 * @param {number} params.createBy 创建者
 * @param {object} params.createTime 创建时间
 * @param {number} params.updateBy 更新者
 * @param {object} params.updateTime 更新时间
 * @param {object} params.params 请求参数
 * @param {number} params.deptId 部门id
 * @param {number} params.parentId 父部门id
 * @param {string} params.deptName 部门名称
 * @param {string} params.addressName 单位地址
 * @param {string} params.postalCode 邮政编号
 * @param {string} params.phone 
 * @param {string} params.email 单位电子邮箱
 * @param {string} params.fax 传真
 * @param {number} params.sortNum 排序
 * @param {array} params.depts 对应单位的子部门
 * @returns
 */
export function addBookDept(data) {
    return request({
        url: '/document/bookDept',
        method: 'post',
        data: data
    })
}

/** 
 * 获取通讯录部门详细信息
 * @param {string} deptId 主键
  * @returns
 */
export function getDeptForId(deptId) {
    return request.get(`/document/bookDept/${deptId}`);
  }

/** 
 * 修改通讯录部门
 * @param {object} params 通讯录部门业务对象 address_book_dept
 * @param {number} params.createDept 创建部门
 * @param {number} params.createBy 创建者
 * @param {object} params.createTime 创建时间
 * @param {number} params.updateBy 更新者
 * @param {object} params.updateTime 更新时间
 * @param {object} params.params 请求参数
 * @param {number} params.deptId 部门id
 * @param {number} params.parentId 父部门id
 * @param {string} params.deptName 部门名称
 * @param {string} params.addressName 单位地址
 * @param {string} params.postalCode 邮政编号
 * @param {string} params.phone 
 * @param {string} params.email 单位电子邮箱
 * @param {string} params.fax 传真
 * @param {number} params.sortNum 排序
 * @param {array} params.depts 对应单位的子部门
 * @returns
 */
export function updataDept(data) {
    return request({
        url: '/document/bookDept',
        method: 'put',
        data: data
    })
  }

/** 
 * 新增通讯录用户
 * @param {object} params 通讯录用户业务对象 address_book_user
 * @param {number} params.createDept 创建部门
 * @param {number} params.createBy 创建者
 * @param {object} params.createTime 创建时间
 * @param {number} params.updateBy 更新者
 * @param {object} params.updateTime 更新时间
 * @param {object} params.params 请求参数
 * @param {number} params.userId 用户ID
 * @param {number} params.parentDeptId 查询单位ID
 * @param {number} params.deptId 部门ID
 * @param {string} params.deptName 部门名称
 * @param {string} params.deptNumber 办公室编号
 * @param {string} params.jobName 职务名称
 * @param {string} params.userName 用户姓名
 * @param {string} params.phonenumber 手机号码
 * @param {string} params.phone 办公室电话
 * @param {string} params.landline 座机
 * @param {string} params.email 用户邮箱
 * @param {string} params.sex 用户性别（0男 1女 2未知）
 * @param {string} params.remark 备注
 * @returns
 */
export function addUser(params) {
    return request.post(`/document/bookUser`, params);
  }

/** 
 * 查询通讯录用户列表
 * @param {string} userId 用户ID
  * @param {string} parentDeptId 查询单位ID
  * @param {string} deptId 部门ID
  * @param {string} deptName 部门名称
  * @param {string} deptNumber 办公室编号
  * @param {string} jobName 职务名称
  * @param {string} userName 用户姓名
  * @param {string} phonenumber 手机号码
  * @param {string} phone 办公室电话
  * @param {string} landline 座机
  * @param {string} email 用户邮箱
  * @param {string} sex 用户性别（0男 1女 2未知）
  * @param {string} remark 备注
  * @param {string} createDept 创建部门
  * @param {string} createBy 创建者
  * @param {string} createTime 创建时间
  * @param {string} updateBy 更新者
  * @param {string} updateTime 更新时间
  * @param {string} pageSize 分页大小
  * @param {string} pageNum 当前页数
  * @param {string} orderByColumn 排序列
  * @param {string} isAsc 排序的方向desc或者asc
  * @returns
 */
export function getBookUserList(query) {
    return request({
      url: '/document/bookUser/list',
      method: 'get',
      params: query
    })
  }

/** 
 * 获取通讯录用户详细信息
 * @param {string} userId 主键
  * @returns
 */
export function getUserForId(userId) {
  return request.get(`/document/bookUser/${userId}`);
}

/** 
 * 修改通讯录用户
 * @param {object} params 通讯录用户业务对象 address_book_user
 * @param {number} params.createDept 创建部门
 * @param {number} params.createBy 创建者
 * @param {object} params.createTime 创建时间
 * @param {number} params.updateBy 更新者
 * @param {object} params.updateTime 更新时间
 * @param {object} params.params 请求参数
 * @param {number} params.userId 用户ID
 * @param {number} params.deptId 部门ID
 * @param {string} params.deptName 部门名称
 * @param {string} params.deptNumber 办公室编号
 * @param {string} params.jobName 职务名称
 * @param {string} params.userName 用户姓名
 * @param {string} params.phonenumber 手机号码
 * @param {string} params.phone 办公室电话
 * @param {string} params.landline 座机
 * @param {string} params.email 用户邮箱
 * @param {string} params.sex 用户性别（0男 1女 2未知）
 * @param {number} params.sortNum 排序
 * @param {string} params.remark 备注
 * @returns
 */
export function updataUser(data) {
  return request({
      url: '/document/bookUser',
      method: 'put',
      data: data
  })
}

/** 
 * 删除通讯录用户
 * @param {string} userIds 主键串
  * @returns
 */
export function delUserInfo(userIds) {
  return request.delete(`/document/bookUser/${userIds}`);
}


// 统计分类待办
export function getGroupNotice(userId) {
  return request.get(`/document/notify/groupNoticeByUserId?userId=${userId}`);
}
