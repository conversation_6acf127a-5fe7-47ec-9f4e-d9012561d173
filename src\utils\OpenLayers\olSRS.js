/**
 * @description：ol坐标应用系统
 * @author: zyc
 * @time: 2022-09-01
 **/

import { register } from "ol/proj/proj4";
import proj4 from "proj4";
import Projection from "ol/proj/Projection";

const EPSG4326 = new Projection({
  code: "EPSG:4326",
  units: "degrees",
  extent: [], // 投影坐标范围
  worldExtent: [] // 世界经纬度范围
});

const getEPSG4490 = () => {
  const def = "+proj=longlat +ellps=GRS80 +no_defs";
  proj4.defs("EPSG:4490", def);
  register(proj4);
  return new Projection({
    code: "EPSG:4490",
    units: "degrees",
    extent: [73.62, 16.7, 134.77, 53.56], // 投影坐标范围
    worldExtent: [-180, -90, 180, 90] // 世界经纬度范围
  });
};

const getEPSG3857 = () => {
  const def =
    "+proj=merc +a=6378137 +b=6378137 +lat_ts=0.0 +lon_0=0.0 +x_0=0.0 +y_0=0 +k=1.0 +units=m +nadgrids=@null +wktext  +no_defs";
  proj4.defs("EPSG:3857", def);
  register(proj4);
  return new Projection({
    code: "EPSG:3857",
    units: "m",
    extent: [], // 投影坐标范围
    worldExtent: [] // 世界经纬度范围
  });
};
const getEPSG4522 = () => {
  const def =
    "+proj=tmerc +lat_0=0 +lon_0=102 +k=1 +x_0=34500000 +y_0=0 +ellps=GRS80 +units=m +no_defs +type=crs";
  proj4.defs("EPSG:4522", def);
  register(proj4);
  return new Projection({
    code: "EPSG:4522",
    units: "m",
    extent: [34344166.57, 2337470.19, 34655833.43, 4729373.22], // 投影坐标范围
    worldExtent: [100.5, 21.13, 103.5, 42.69] // 世界经纬度范围
  });
};
const EPSG3857 = getEPSG3857();
const EPSG4490 = getEPSG4490();
const EPSG4522 = getEPSG4522();
export default {
  4326: EPSG4326,
  4490: EPSG4490,
  3857: EPSG3857,
  4522: EPSG4522
};
