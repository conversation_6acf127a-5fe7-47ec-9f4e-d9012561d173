define(["./PrimitivePipeline-3db2c374","./createTaskProcessorWorker","./buildModuleUrl-9085faaa","./Cartographic-3309dd0d","./Check-7b2a090c","./when-b60132fc","./Math-119be1a3","./Rectangle-dee65d21","./FeatureDetection-806b12f0","./Cartesian4-3ca25aab","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Event-16a2dfbf","./ComponentDatatype-c140a87d","./GeometryAttribute-c65394ac","./Cartesian2-db21342c","./GeometryAttributes-252e9929","./GeometryPipeline-7a733318","./AttributeCompression-0a087f75","./EncodedCartesian3-f1396b05","./IndexDatatype-8a5eead4","./IntersectionTests-0d6905a3","./Plane-a3d8b3d2","./WebMercatorProjection-a4b885f9"],(function(e,t,a,i,r,n,o,c,b,d,s,m,u,P,p,f,l,C,y,G,v,k,h,A){"use strict";return t((function(t,a){var i=e.PrimitivePipeline.unpackCombineGeometryParameters(t),r=e.PrimitivePipeline.combineGeometry(i);return e.PrimitivePipeline.packCombineGeometryResults(r,a)}))}));
