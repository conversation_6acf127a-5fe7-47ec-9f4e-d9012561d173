<template>
  <div class="con-select">
    <p class="title">
      <a style="margin-left:29px;line-height:35px">分屏设置</a>
    </p>
    <div class="select-layer clearfix">
      <ul @click="splitScreen">
        <li class="split-type" data-split-number="two">二分屏</li>
        <li class="split-type" data-split-number="four">四分屏</li>
        <li class="split-type" data-split-number="six">六分屏</li>
        <li class="split-type" data-split-number="eight">八分屏</li>
      </ul>
    </div>
    <div v-show="splitLayers.length" class="split-container">
      <div
        v-for="(layer,index) in splitLayers"
        :ref="index"
        :key="index"
        class="split-map-container clearfix"
        :style="{width:width,height:height}"
      >
        <div :id="'split-map-'+index" class="map-container">
          <div class="layer-title" @mousemove="showMapTree(index)" @mouseleave="hideMapTree(index,$event)">
            <span>{{ layer.filterText || '请选择对比查看图层' }}</span>
          </div>
        </div>
        <div
          v-if="isShowTree && activeIndex===index"
          class="split-layer-info"
          data-split-layer="splitLayer"
          @mouseleave="hideLayerTree(index)"
        >
          <transition name="fade">
            <el-tree
              ref="tree"
              data-split-layer="splitLayer"
              :data-split-number="'split-map-'+index"
              :render-after-expand="false"
              class="map-tree"
              :data="layer.layers"
              node-key="id"
              :props="layerDefaultProps"
              :default-expanded-keys="idArr"
              :filter-node-method="filterNode"
              icon-class="none"
              element-loading-text="正在加载"
            >
              <template v-slot="{ node, data }">
                <span class="custom-tree-node" @click="treeSelect(node, data,$event)">
                  <template v-if="data.serviceValue==='catalog'">
                    <span class="layer-parent">
                      <svg-icon v-if="data.icon" :icon-class="data.icon" class-name="layer-parent-icon" />
                      <span>{{ node.label }}</span>
                      <span v-if="data.children.length" class="layer-count">{{ " (" + data.children.length + ")" }}</span>
                    </span>
                  </template>
                  <template v-else>
                    <span class="layer-children">
                      <span v-if="node.label.length>14">
                        <el-tooltip class="item" effect="dark" :content="node.label"
                                    placement="top"
                        >
                          <span :data-split-map="'split-map-'+index">{{ node.label.toString().substring(0, 14) }} ···</span>
                        </el-tooltip>
                      </span>
                      <span v-else :data-split-map="'split-map-'+index">{{ node.label }}</span>
                    </span>
                  </template>
                </span>
              </template>
            </el-tree>
          </transition>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="分屏对比">
import { getConfigKey } from '@/api/system/config'
import { getTreeDataByRole } from '@/api/gis/layerTree'
import { removeAllLayer } from '@/utils/OpenLayers/olTool'
import { addLayer } from "@/utils/OpenLayers/olLayer";
import { Map, View } from "ol/index";
import olSRS from "@/utils/OpenLayers/olSRS";
import useUserStore from "@/store/modules/user.js"
import useMapInitStore from "@/store/modules/map/mapInit.js"

const isExpand = ref(true)
const layerResources = ref([])
const splitLayers = ref([])
const layerDefaultProps = ref({
  disabled: (data, node) => {
    return !data?.children && !data.url
  }
})
const idArr = ref([])
const maps = ref([])
const serviceUrl = ref('')
const isShowTree = ref(false)
const activeIndex = ref('')
const filterText = ref('')


const user = computed(()=>useUserStore().user)
// 地图初始化
const mapInfo = computed(()=>useMapInitStore().mapInfo)


const width = computed(()=> {
  let width = '50%'
  const splitNumber = splitLayers.value.length
  if (splitNumber === 4){
    width = '50%'
  }
  return width
})

const height = computed(()=> {
  const splitNumber = splitLayers.value.length
  return (100 / (splitNumber / 2)) + '%'
})

const filterNode = (value, data)=> {
  if (!value) return true
  return data.name.indexOf(value) !== -1
}

// 请求叠加图层：获取空间分析服务下拉树列表
const getOverlapLayer = ()=> {
  const getSysID = new Promise((resolve, reject) => {
    getConfigKey('sys.id').then((res) => {
      resolve(res.data)
    })
  })
  const getMap2dID = new Promise((resolve, reject) => {
    getConfigKey('sys.map2d.id').then((res) => {
      resolve(res.data)
    })
  })
  Promise.all([getSysID, getMap2dID]).then((res) => {
    getTreeDataByRole({ userId: user.value.userId, systemId: res[0], mapId: res[1] }).then(res => {
      const mapResource = res.data[0]
      if (!mapResource.children.length){
        console.error("请检查是否赋予图层权限？")
        return
      }
      layerResources.value = mapResource.children
      // 默认展开前两个图层
      if (layerResources.value.length > 1) {
        idArr.value.push(layerResources.value[0].id)
        idArr.value.push(layerResources.value[1].id)
      }
    })
  })
}

const splitScreen = ($event)=>{
  splitLayers.value = []
  if (maps.value.length){
    maps.value.forEach(obj => {
      obj.map.setTarget(null)
    })
  }
  maps.value = []
  const splitType = $event.target.dataset["splitNumber"]
  let number = 0
  if (splitType === "two"){
    number = 2
  }
  if (splitType === "four"){
    number = 4
  }
  if (splitType === "six"){
    number = 6
  }
  if (splitType === "eight"){
    number = 8
  }
  if (splitType === "nine"){
    number = 9
  }
  for (let i = 0; i < number; i++){
    const splitObj = {
      "layers": layerResources.value,
      "filterText": ""
    }
    splitLayers.value.push(splitObj)
  }
  // 传递 splitLayers
  nextTick(() => {
    initMap(number)
  })
}

const initMap = (number)=> {
  serviceUrl.value = mapInfo.value.serviceType === "geoserver" ?
    mapInfo.value.url : import.meta.env.VITE_APP_ISERVER_URL + mapInfo.value.url
  const wmtsOptions = {
    url: serviceUrl.value,
    layerName: mapInfo.value.layerName.toUpperCase(),
    isBaseMap: true,
    layerId: '1'
  }
  for (let i = 0; i < number; i++){
    const map = new Map({
      target: 'split-map-' + i,
      view: new View({
        center: mapInfo.value.center,
        zoom: mapInfo.value.zoom,
        maxZoom: 18,
        minZoom: 7,
        projection: mapInfo.value.projection // 定义坐标系
      }),
      controls: []
    })
    addLayer(mapInfo.value.serviceType,wmtsOptions,map)
    const mapObj = {
      name: 'split-map-' + i,
      map: map
    }
    maps.value.push(mapObj)
  }
  mapState()
}
const mapState = ()=> {
  if (maps.value.length === 1) return
  maps.value.forEach(mapObj => {
    mapObj.map.on('pointerdrag', () => {
      maps.value.map(item => {
        if (mapObj !== item) {
          item.map.setView(mapObj.map.getView())
        }
      })
    })
    mapObj.map.on('rendercomplete', () => {
      maps.value.map(item => {
        if (mapObj !== item) {
          item.map.setView(mapObj.map.getView())
        }
      })
    })
  })
}

const showMapTree = (index)=>{
  isShowTree.value = true
  activeIndex.value = index
}

const hideMapTree = (index,$event)=>{
  const isSplitLayer = $event.toElement.dataset.splitLayer === "splitLayer"
  if (isSplitLayer){
    isShowTree.value = true
  } else {
    isShowTree.value = false
  }
  activeIndex.value = index
}
const hideLayerTree = (index)=>{
  isShowTree.value = false
  activeIndex.value = index
}

const treeSelect = (node, data,$event)=> {
  filterText.value = ''
  nextTick(() => {
    const selectMap = $event.target.dataset["splitMap"]
    if (data.serviceValue !== "catalog") {
      maps.value.forEach(mapObj => {
        if (mapObj.name === selectMap){
          removeAllLayer(mapObj.map)
          filterText.value = data.label
          splitLayers.value[activeIndex.value].filterText = data.label

          const wmtsOptions = {
            url: data.url,
            layerId: data.id,
            layerName: data.layerName,
            isBaseMap: false
          }
          // console.log("图层配置：",wmtsOptions);
          addLayer(data.serviceType,wmtsOptions,mapObj.map)
        }
      })
    }
  })
}

onBeforeMount(()=>{
  getOverlapLayer()
})

</script>

<style scoped lang="scss">
.con-select {
  .title {
    font-size: 14px;
    font-weight: bold;
    color: #fff;
    background: url(/src/assets/images/yy_19.png) no-repeat center center /
      cover;
  }

  .select-layer {
    margin: 10px;
    .split-type{
      float: left;
      padding: 10px 20px;
      background-color: #0167cc;
      text-align: center;
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      width: 70%;
      color: #fff;
      margin: 5px;
      border-radius: 2.5px;
      &:hover{
        cursor: pointer;
        filter: brightness(85%);
      }
    }
    .select-div {
      padding-left: 6px;
      border-top-left-radius: 5px;
      border-top-right-radius: 5px;
      background-color: #0167cce6;

      .select-header {
        line-height: 30px;
        padding-left: 6px;
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;
        background-color: #0167cce6;
        color: #fff;
      }
      span {
        margin-left: 5px;
        font-weight: bold;
      }
    }

    &:hover {
      cursor: pointer;
    }

    .layer {
      border: 1.5px solid #0167ccba;
      border-bottom-left-radius: 5px;
      border-bottom-right-radius: 5px;
      border-top: none;
      font-size: 14px;

      ul {
        padding: 5px 0;

        li {
          margin: 5px 0 5px -1px;
          padding: 5px 10px;

          &:hover {
            background: #0167cc;
          }
        }
      }

      .checkbox {
        width: 16px;
        height: 16px;
        vertical-align: bottom;
      }

      .layer-name {
        font-size: 14px;
        margin-left: 5px;
        color: #fff;
      }
    }

    .layer-fade-enter-active, .layer-fade-leave-active {
      transition: height ease-in .35s;
    }

    .layer-fade-enter, .layer-fade-leave-to {
      transition: height ease-out .35s;
      opacity: 0;
    }
  }
}

.split-container{
  position: absolute;
  top: 0;
  right: 0;
  left: 299px;
  height: 100%;
  width: 80vw;
  .split-map-container{
    position: relative;
    float: left;
    width: 100%;
    height: 100%;
    border: 1px solid #0167cce8;
    .map-container{
      width: 100%;
      height: 100%;
      background: #fff;
    }
    .split-layer-info{
      position: absolute;
      z-index: 669;
      top: 5%;
      left: 50%;
      right: 50%;
      bottom: 5%;
      width: 50%;
      transform: translateX(-50%);
      overflow-y: auto;
      background-color: #fff;
      padding-top: 30px;
    }
    .layer-title {
      position: absolute;
      top: 10px;
      height: 45px;
      z-index: 999;
      left: 50%;
      right: 50%;
      transform: translateX(-50%);
      width: 50%;
      span{
        display: inline-block;
        background: #0f7dff;
        width: 100%;
        line-height: 45px;
        text-align: center;
        color: #fff;
        font-size: 16px;
        border-radius: 5px;
      }
    }
    .map-tree{
      position: absolute;
      max-height: 35%;
      padding:30px 20px;
      width: 100%;
      .layer-parent {
        font-size: 16px;
        font-family: 'Arial Negreta', 'Arial Normal', 'Arial';
        .layer-parent-icon {
          height: 16px;
          width: 16px;
          color: #0167CC;
        }

        .layer-count {
          color: #ec0000;
        }
      }

      .layer-children {
        font-size: 14px;
      }
    }
  }
}
</style>
