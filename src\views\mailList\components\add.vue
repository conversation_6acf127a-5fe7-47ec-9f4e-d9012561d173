<template>
  <div class="contentBody customBox">
    <div>
      <el-button
        type="primary" size="mini" @click="submit"
        v-if="
          type === 'add' ||
            type === 'edit' ||
            type === 'addUser' ||
            type === 'editUser'
        "
      >保存</el-button>
      <el-button
        type="primary"
        size="small"
      >打印</el-button>
      <el-button
        type="primary"
        size="small"
      >发送</el-button>
      <el-button
        type="primary"
        size="small" @click="emitClose"
      >关闭</el-button>
    </div>
    <div
      class="card mt5"
      style="flex: 1"
    >
      <div
        class="addUnit"
        v-if="pageType === 'dept'"
      >
        <div class="flx-center mt50">
          <div style="width: 55%">
            <el-form
              :model="form"
              label-width="auto"
            >
              <table style="width: 100%">
                <tr>
                  <td colspan="2">
                    <el-form-item
                      label="单位名称"
                      prop="title"
                    >
                      <el-input
                        v-model="form.deptName"
                        placeholder="请输入单位名称" :disabled="disabledDept"
                      />
                      <div
                        class="mt5"
                        v-if="type === 'add' || type === 'edit'"
                      >
                        <el-button
                          type="primary"
                          size="mini" @click="addUnitDept"
                        >增加下属部门</el-button>
                        <div
                          style="display: flex"
                          class="mt5" v-for="(item, index) in form.depts" :key="item.deptName"
                        >
                          <div class="mr5">{{ index + 1 }}</div>
                          <el-input
                            v-model="item.deptName"
                            placeholder="请输入政策法规名称"
                          />
                          <el-button
                            type="danger"
                            size="mini" class="ml5" @click="delDept(item, index)"
                          >删除</el-button>
                        </div>
                      </div>
                    </el-form-item>
                  </td>
                </tr>
                <tr>
                  <td colspan="2">
                    <el-form-item
                      label="单位地址"
                      prop="title"
                    >
                      <el-input
                        v-model="form.addressName"
                        placeholder="请输入单位地址" :disabled="disabledDept"
                      />
                    </el-form-item>
                  </td>
                </tr>
                <tr>
                  <td>
                    <el-form-item label="邮政编号">
                      <el-input
                        v-model="form.postalCode"
                        placeholder="请输入邮政编号" :disabled="disabledDept"
                      />
                    </el-form-item>
                  </td>
                  <td>
                    <el-form-item label="单位电子邮箱">
                      <el-input
                        v-model="form.email"
                        placeholder="请输入单位电子邮箱" :disabled="disabledDept"
                      />
                    </el-form-item>
                  </td>
                </tr>
                <tr>
                  <td>
                    <el-form-item label="办公室电话">
                      <el-input
                        v-model="form.phone"
                        placeholder="请输入办公室电话" :disabled="disabledDept"
                      />
                    </el-form-item>
                  </td>
                  <td>
                    <el-form-item label="传真">
                      <el-input
                        v-model="form.fax"
                        placeholder="请输入传真" :disabled="disabledDept"
                      />
                    </el-form-item>
                  </td>
                </tr>
              </table>
            </el-form>
          </div>
        </div>
      </div>
      <div
        class="addUser"
        v-if="pageType === 'user'"
      >
        <div class="flx-center mt50">
          <div style="width: 55%">
            <div
              class="mb10"
              v-show="type === 'addUser' || type === 'editUser'"
            >
              当前编辑单位：{{ parentData.deptName }}
            </div>
            <el-form
              :model="formUser"
              label-width="auto"
            >
              <table style="width: 100%">
                <tr>
                  <td>
                    <el-form-item
                      label="部门"
                      prop="title"
                    >
                      <el-select
                        v-model="formUser.deptId" placeholder="请选择部门" style="width: 100%"
                        @change="handleSelect" :disabled="disabledDept"
                      >
                        <el-option
                          v-for="item in selectDeptOption" :key="item.deptId" :label="item.deptName"
                          :value="item.deptId"
                        />
                      </el-select>
                    </el-form-item>
                  </td>
                  <td>
                    <el-form-item
                      label="座机"
                      prop="title"
                    >
                      <el-input
                        v-model="formUser.landline"
                        placeholder="请输入座机" :disabled="disabledDept"
                      />
                    </el-form-item>
                  </td>
                </tr>
                <tr>
                  <td>
                    <el-form-item
                      label="姓名"
                      prop="title"
                    >
                      <el-input
                        v-model="formUser.userName"
                        placeholder="请输入姓名" :disabled="disabledDept"
                      />
                    </el-form-item>
                  </td>
                  <td>
                    <el-form-item
                      label="移动电话"
                      prop="title"
                    >
                      <el-input
                        v-model="formUser.phonenumber"
                        placeholder="请输入移动电话" :disabled="disabledDept"
                      />
                    </el-form-item>
                  </td>
                </tr>
                <tr>
                  <td>
                    <el-form-item label="职务">
                      <el-input
                        v-model="formUser.jobName"
                        placeholder="请输入职务" :disabled="disabledDept"
                      />
                    </el-form-item>
                  </td>
                  <td>
                    <el-form-item label="办公室编号">
                      <el-input
                        v-model="formUser.deptNumber"
                        placeholder="请输入办公室编号" :disabled="disabledDept"
                      />
                    </el-form-item>
                  </td>
                </tr>
                <tr>
                  <td>
                    <el-form-item label="备注">
                      <el-input
                        v-model="formUser.remark"
                        placeholder="请输入备注" :disabled="disabledDept"
                      />
                    </el-form-item>
                  </td>
                  <td>
                    <el-form-item label="显示排序">
                      <el-input
                        v-model="formUser.sortNum"
                        placeholder="请输入排序值" :disabled="disabledDept"
                      />
                    </el-form-item>
                  </td>
                </tr>
              </table>
            </el-form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref } from "vue";
const emit = defineEmits(["close"]); //delUnitDept
import {
  delUnitDept,
  addBookDept,
  getDeptForId,
  updataDept,
  addUser,
  getUserForId,
  updataUser
} from "@/api/mailList/index.js";
import { ElMessage } from "element-plus";

const props = defineProps({
  type: String,
  deptId: String,
  pageType: String,
  parentData: Object
});

const form = ref({
  parentId: 0,
  deptName: "",
  addressName: "",
  postalCode: "",
  phone: "",
  email: "",
  fax: "",
  depts: [
    {
      deptName: ""
    }
  ]
});

// 根据部门id获取单位详情
const getUnitDetail = () => {
  getDeptForId(props.deptId).then((res) => {
    (form.value.deptId = res.data.deptId),
    (form.value.parentId = res.data.parentId),
    (form.value.deptName = res.data.deptName),
    (form.value.addressName = res.data.addressName),
    (form.value.postalCode = res.data.postalCode),
    (form.value.phone = res.data.phone),
    (form.value.email = res.data.email),
    (form.value.fax = res.data.fax),
    (form.value.depts = res.data.children);
  });
};

// 增加下属部门
const addUnitDept = () => {
  form.value.depts.push({ deptName: "" });
};
//
const delDept = (item, index) => {
  if (item.deptId)
  {
    delUnitDept(item.deptId).then((res) => {
      console.log("res");
    });
  } else
  {
    form.value.depts.splice(index, 1);
  }
};

//user

const formUser = ref({
  deptId: "",
  deptName: "",
  deptNumber: "",
  jobName: "",
  userName: "",
  phonenumber: "",
  phone: "",
  landline: "",
  email: "",
  sex: "",
  remark: "",
  sortNum: ""
});

// 获取当前单位下的所有部门
const selectDeptOption = ref([]);
const currentDeptList = () => {
  getDeptForId(props.parentData.deptId).then((res) => {
    selectDeptOption.value = res.data.children;
  });
};
const handleSelect = (e) => {
  selectDeptOption.value.forEach((item) => {
    if (item.deptId === e)
    {
      formUser.value.deptName = item.deptName;
    }
  });
};

//getUserForId
const getUserDetail = () => {
  getUserForId(props.deptId).then((res) => {
    formUser.value.deptId = res.data.deptId;
    formUser.value.deptName = res.data.deptName;
    formUser.value.userId = res.data.userId;
    formUser.value.deptNumber = res.data.deptNumber;
    formUser.value.jobName = res.data.jobName;
    formUser.value.userName = res.data.userName;
    formUser.value.phonenumber = res.data.phonenumber;
    formUser.value.phone = res.data.phone;
    formUser.value.landline = res.data.landline;
    formUser.value.email = res.data.email;
    formUser.value.sortNum = res.data.sortNum;
    formUser.value.remark = res.data.remark;
  });
};

// submit
const submit = () => {
  if (props.type === "add")
  {
    addBookDept(form.value).then((res) => {
      if (res.code === 200)
      {
        ElMessage({
          message: "新增完成",
          type: "success"
        });
        emitClose();
      }
    });
  } else if (props.type === "edit")
  {
    updataDept(form.value).then((res) => {
      if (res.code === 200)
      {
        ElMessage({
          message: "编辑完成",
          type: "success"
        });
        emitClose();
      }
    });
  } else if (props.type === "addUser")
  {
    formUser.value.parentDeptId = props.parentData.deptId;
    console.log("form", formUser.value);
    addUser(formUser.value).then((res) => {
      if (res.code === 200)
      {
        ElMessage({
          message: "新增完成",
          type: "success"
        });
        emitClose();
      }
    });
  } else if (props.type === "editUser")
  {
    updataUser(formUser.value).then((res) => {
      if (res.code === 200)
      {
        ElMessage({
          message: "编辑完成",
          type: "success"
        });
        emitClose();
      }
    });
  }
};

//
const emitClose = () => {
  emit("close", false);
};

const disabledDept = ref(false);

onMounted(() => {
  console.log("parentData", props);

  if (props.pageType === "dept")
  {
    if ((props.type === "edit" || props.type === "view") && props.deptId)
    {
      console.log('查阅');

      getUnitDetail();
    }
    if (props.type === "view")
    {
      disabledDept.value = true;
    }
  } else
  {
    currentDeptList();
    if (
      (props.type === "editUser" || props.type === "viewUser") &&
      props.deptId
    )
    {
      getUserDetail();
    }
    if (props.type === "viewUser")
    {
      disabledDept.value = true;
    }
  }
});
</script>

<style scoped lang="scss">
.customBox {
  display: flex;
  flex-direction: column;
}
.card {
  background: #dce2f1;
}
</style>