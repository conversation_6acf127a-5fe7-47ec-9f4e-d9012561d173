<template>
  <div v-if="isShow" class="analyse-wrap">
    <!-- <Header :header-info="headerInfo" @closePanel="closePanel" /> -->
    <div class="tab-container">
      <div class="tool-bottom">
        <div class="tool-btn" @click="startAnalyse">
          <svg-icon icon-class="start_analyse" />
          <span>绘制分析范围</span>
        </div>
        <div class="tool-btn" @click="clearResult">
          <svg-icon icon-class="clear" />
          <span>清除</span>
        </div>
      </div>
    </div>

    <div v-if="isShowAction" ref="modelTool" class="model-action">
      <div class="action-title">
        <h5>属性编辑</h5>
      </div>
      <div class="action-panel">
        <div class="action-item clearfix">
          <h3 class="action-header">坡度区间</h3>
          <div>
            <label for="" class="action-label">最小坡度</label>
            <el-input
              v-model="minSlopeDegree"
              placeholder=""
              :disabled="true"
              size="small"
            />
            <el-slider
              v-model="minSlopeValue"
              class="action-slider"
              :min="0"
              :max="78"
              :step="1"
              @change="handleAction('minSlope')"
            />
          </div>
          <div>
            <label for="" class="action-label">最大坡度</label>
            <el-input
              v-model="maxSlopeDegree"
              placeholder="填入维度"
              size="small"
              :disabled="true"
            />
            <el-slider
              v-model="maxSlopeValue"
              class="action-slider"
              :min="0"
              :max="78"
              :step="1"
              @change="handleAction('maxSlope')"
            />
          </div>
        </div>
        <div class="action-item clearfix">
          <h3 class="action-header">颜色条带</h3>
          <label for="" class="action-label">选择条带</label>
          <div class="color-band clearfix">
            <button class="color-btn" />
            <el-select
              ref="colorSelect"
              v-model="selectedColor"
              placeholder="请选择色带"
              class="color-select"
              @change="selectChange"
            >
              <el-option
                v-for="item in colorOptions"
                :key="item.value"
                :label="item.value"
                :value="item.value"
                data-content="<span class='label band1'>&nbsp</span>"
              >
                <span class="label" :class="`band${item.value}`" />
              </el-option>
            </el-select>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="TerrainSlopeAnalysis" text="坡度分析">
import useMapViewStore from "@/store/modules/map/mapView.js"
import { createTooltip, setCursor, setLayerSelectStatus } from "@/utils/Cesium/CesiumTool.js"
import { ElMessage } from "element-plus"

const { proxy } = getCurrentInstance()

defineProps({
  isShow: {
    type: Boolean,
    default: true
  },
  headerInfo: {
    type: Object
  }
})
const analyseRegionMode = Cesium.HypsometricSettingEnum.AnalysisRegionMode.ARM_REGION
const minSlopeValue = ref(0)
const maxSlopeValue = ref(78)
const isShowAction = ref(false)
const slope = ref(undefined) // 坡度对象
const drawPolygonHandler = ref(undefined)
const selectedColor = ref(undefined)
const colorTable = ref(undefined)
const opacity = ref(0.5)
const toolTip = ref(undefined)
const colorOptions = ref([
  {
    value: 1,
    label: "",
    background: "linear-gradient(to left, #95e8f9, #002794)"
  },
  {
    value: 2,
    label: "",
    background: "linear-gradient(to left, #a2fbc2, #ff6767)"
  },
  {
    value: 3,
    label: "",
    background: "linear-gradient(to left, #e6c6ff, #9d00ff)"
  },
  {
    value: 4,
    label: "",
    background: "linear-gradient(to left, #0909d4,#00a1ff 20%, #14bb12 40%, #dde007 60%, #d20f0f)"
  },
  {
    value: 5,
    label: "",
    background: "linear-gradient(to left, #baffe5, #1ab99c)"
  }
])

const viewer3d = computed(()=>useMapViewStore().viewer3d)

const minSlopeDegree = computed(()=>{
  return minSlopeValue.value + '°'
})
const maxSlopeDegree = computed(()=>{
  return maxSlopeValue.value + '°'
})

const emits = defineEmits(['closePanel'])
const closePanel = ()=>{
  emits('closePanel')
}

/**
 * 开始分析
 */
const startAnalyse = ()=> {
  const scene = viewer3d.value.scene;
  setLayerSelectStatus(viewer3d.value, false)

  // 初始化提示信息
  toolTip.value = createTooltip(document.body)

  // 初始化坡度分析对象
  slope.value = new Cesium.SlopeSetting();
  // 展示面和箭头
  slope.value.DisplayMode = Cesium.SlopeSettingEnum.DisplayMode.FACE_AND_ARROW;

  slope.value.MinVisibleValue = minSlopeValue.value;
  slope.value.MaxVisibleValue = maxSlopeValue.value;

  // 创建颜色面板
  colorTable.value = new Cesium.ColorTable();
  colorTable.value.insert(80, new Cesium.Color(255 / 255, 0 / 255, 0 / 255));
  colorTable.value.insert(50, new Cesium.Color(221 / 255, 224 / 255, 7 / 255));
  colorTable.value.insert(30, new Cesium.Color(20 / 255, 187 / 255, 18 / 255));
  colorTable.value.insert(20, new Cesium.Color(0, 161 / 255, 1));
  colorTable.value.insert(0, new Cesium.Color(9 / 255, 9 / 255, 255 / 255));
  slope.value.ColorTable = colorTable.value;

  slope.value.Opacity = opacity.value;

  // 初始化绘制对象：绘制面，空间模式
  drawPolygonHandler.value = new Cesium.DrawHandler(viewer3d.value,Cesium.DrawMode.Polygon,Cesium.ClampMode.Space)
  drawPolygonHandler.value.activate()
  drawPolygonHandler.value.activeEvt.addEventListener(active => {
    // console.log("active:","绘制激活事件")
  })
  drawPolygonHandler.value.movingEvt.addEventListener(windowPosition => {
    setCursor(viewer3d.value,'crosshair')
    toolTip.value.showAt(windowPosition,"<p>左键开始绘制，右键结束绘制</p>")
  })
  drawPolygonHandler.value.drawEvt.addEventListener(result => {
    console.log(result);
    setCursor(viewer3d.value,'pointer')
    toolTip.value.setVisible(false)
    if (!result.positions.length) {
      drawPolygonHandler.value.clear();
      drawPolygonHandler.value.deactivate();
      ElMessage.warning("未获取到位置信息，请重新位置")
      return;
    }

    const polygonPositions = result.positions
    const positions = [];
    for (let i = 0, len = polygonPositions.length; i < len; i++) {
      const cartographic = Cesium.Cartographic.fromCartesian(polygonPositions[i]);
      const longitude = Cesium.Math.toDegrees(cartographic.longitude);
      const latitude = Cesium.Math.toDegrees(cartographic.latitude);
      const h = cartographic.height;
      if (positions.indexOf(longitude) == -1 && positions.indexOf(latitude) == -1) {
        positions.push(longitude);
        positions.push(latitude);
        positions.push(h);
      }
    }
    slope.value.CoverageArea = positions;
    scene.globe.SlopeSetting = {
      slopeSetting: slope.value,
      analysisMode: analyseRegionMode
    };
  })
}

/**
 * 颜色条带选择事件
 * @param $event：条带号
 */
const selectChange = ($event)=>{
  console.log("条带顺序：",$event);
  // 移除颜色表
  // colorTable.clear()
  colorTable.value.remove(0);
  colorTable.value.remove(20);
  colorTable.value.remove(30);
  colorTable.value.remove(50);
  colorTable.value.remove(80);
  const targetEle = document.querySelector(".color-btn");
  if (targetEle) {
    targetEle.remove();
  }
  // console.log("event:",$event)
  const selectEle = proxy.$refs.colorSelect.$el;
  const targetOpt = colorOptions.value.find((opt) => opt.value === +$event);
  // console.log(targetOpt);
  const buttonEle = document.createElement("button");
  buttonEle.className = "color-btn";
  buttonEle.style.background = targetOpt.background;
  selectEle.prepend(buttonEle);

  switch ($event) {
    case 1:
      colorTable.value.insert(0, new Cesium.Color(9 / 255, 9 / 255, 255 / 255));
      colorTable.value.insert(20, new Cesium.Color(0, 161 / 255, 1));
      colorTable.value.insert(30, new Cesium.Color(20 / 255, 187 / 255, 18 / 255));
      colorTable.value.insert(50, new Cesium.Color(221 / 255, 224 / 255, 7 / 255));
      colorTable.value.insert(80, new Cesium.Color(255 / 255, 0 / 255, 0 / 255));
      break;
    case 2:
      colorTable.value.insert(0, new Cesium.Color(162 / 255, 251 / 255, 194 / 255));
      colorTable.value.insert(20, new Cesium.Color(180 / 255, 200 / 255, 170 / 255));
      colorTable.value.insert(30, new Cesium.Color(200 / 255, 160 / 255, 130 / 255));
      colorTable.value.insert(50, new Cesium.Color(225 / 255, 130 / 255, 130 / 255));
      colorTable.value.insert(80, new Cesium.Color(1, 103 / 255, 103 / 255));
      break;
    case 3:
      colorTable.value.insert(0, new Cesium.Color(230 / 255, 198 / 255, 1));
      colorTable.value.insert(20, new Cesium.Color(210 / 255, 150 / 255, 1));
      colorTable.value.insert(30, new Cesium.Color(190 / 255, 100 / 255, 1));
      colorTable.value.insert(50, new Cesium.Color(165, 50 / 255, 1));
      colorTable.value.insert(80, new Cesium.Color(157 / 255, 0, 1));
      break;
    case 4:
      colorTable.value.insert(0, new Cesium.Color(0, 39 / 255, 148 / 255));
      colorTable.value.insert(20, new Cesium.Color(0, 39 / 255, 148 / 255));
      colorTable.value.insert(30, new Cesium.Color(70 / 255, 116 / 255, 200 / 255));
      colorTable.value.insert(50, new Cesium.Color(149 / 255, 232 / 255, 249 / 255));
      colorTable.value.insert(80, new Cesium.Color(149 / 255, 232 / 255, 249 / 255));
      break;
    case 5:
      colorTable.value.insert(0, new Cesium.Color(186 / 255, 1, 190 / 255));
      colorTable.value.insert(20, new Cesium.Color(186 / 255, 1, 180 / 255));
      colorTable.value.insert(30, new Cesium.Color(106 / 255, 255 / 255, 170 / 255));
      colorTable.value.insert(50, new Cesium.Color(26 / 255, 255 / 255, 160 / 255));
      colorTable.value.insert(80, new Cesium.Color(26 / 255, 255 / 255, 156 / 255));
      break;
    default:
      break;
  }
  slope.value.ColorTable = colorTable.value;
  viewer3d.value.scene.globe.SlopeSetting = {
    slopeSetting: slope.value,
    analysisMode: analyseRegionMode
  };
}


// 参数change事件
const handleAction = (type)=> {
  console.log(type)
  switch (type){
    case "minSlope":
      slope.value.MinVisibleValue = minSlopeValue.value;
      viewer3d.value.scene.globe.SlopeSetting = {
        slopeSetting: slope.value,
        analysisMode: analyseRegionMode
      };
      break
    case "maxSlope":
      slope.value.MaxVisibleValue = maxSlopeValue.value;
      viewer3d.value.scene.globe.SlopeSetting = {
        slopeSetting: slope.value,
        analysisMode: analyseRegionMode
      };
      break
  }
}

const clearResult = ()=> {
  viewer3d.value.entities.removeAll();
  isShowAction.value = false

  if (slope.value){
    slope.value.CoverageArea = [];
    viewer3d.value.scene.globe.SlopeSetting = {
      slopeSetting: slope.value,
      analysisMode: Cesium.HypsometricSettingEnum.AnalysisRegionMode.ARM_NONE
    };
    slope.value = null
  }
  if (drawPolygonHandler.value){
    drawPolygonHandler.value.polygon.show = false;
    drawPolygonHandler.value.polyline.show = false;
    drawPolygonHandler.value = null
  }
}
onBeforeUnmount(()=>{
  clearResult();
  setLayerSelectStatus(viewer3d.value,true)
})

</script>

<style scoped lang="scss">
.analyse-wrap {
  color: #fff;
  background-image: url("@/assets/images/map/tool.png");
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-size: cover;
  box-shadow: 0 0 8px 0 #057595;
}

.tool-tabs {
  display: flex;
  justify-items: center;
  justify-content: space-between;
  background-color: #4f7287;

  span {
    display: inline-block;
    width: 50%;
    padding: 10px 5px;
    text-align: center;
    color: #fff;

    &:hover {
      cursor: pointer;
      background-color: #0f7dff;
      filter: brightness(110%);
      color: #fff;
      transition: background-color 0.25s;
    }
  }
}

.tab-active {
  color: #fff;
  background: linear-gradient(to bottom right, #00baff, #0f7dff);
}

.tool-tab-content {
  padding-top: 20px;
}

:deep(.el-form-item__label) {
  width: 60px !important;
  color: #b5b5b5;
}

.tab-container {
  display: flex;
  padding: 10px;
  flex-direction: column;
  column-count: 2;
}

.height-value {
  margin: 10px;

  label {
    line-height: 40px;
  }
}

.input-value {
  float: right;
  width: 80%;
}

.tool-bottom {
  display: flex;
  justify-content: space-between;
  justify-items: center;
}

.tool-btn {
  margin-top: 20px;
  padding: 10px;
  width: 48%;
  background-color: #0f7dff;
  border-color: #fff;
  text-align: center;
  border-radius: 4px;
  color: #ffffff;

  &:hover {
    cursor: pointer;
    color: #fff;
    filter: brightness(110%) opacity(100%);
    transition: all 0.5s ease-in;
    background: linear-gradient(to bottom right, #00baff, #0f7dff);
  }

  svg {
    margin-right: 10px;
  }
}

.label {
  display: inline;
  padding: 0.01em 3.5em 0.01em 3.5em;
  font-size: 150%;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25em;
  line-height: 1.1;
}

.model-action {
  position: absolute;
  top: 50px;
  left: -270px;
  width: 80%;
  background-color: #001d3bdb;
  border-radius: 5px;
  z-index: 999;
}

.action-title {
  padding: 15px;
  background-image: url("@/assets/images/map/queryResultTitle2.png");
  background-repeat: no-repeat;
  font-size: 14px;
}

.action-panel {
  padding: 10px;
}

.action-item {
  line-height: 40px;
}

.action-slider {
  float: right;
  width: 45%;
  margin-right: 10px;
}

.action-move {
  margin: 5px 0;
}

.color-picker {
  float: right;
  margin-right: 44px;
}

.action-header {
  padding: 5px;
  margin: 10px 0;
  background-color: #113c56;
}

:deep(.el-select){
  width: 75%;
  float: right;
}

:deep(.el-input) {
  width: 22%;
  float: right;
}
:deep(.el-select){
  .el-input {
    width: 100%;
  }
}

.band1 {
  background: -webkit-linear-gradient(left, #95e8f9, #002794) !important;
}
.band2 {
  background: -webkit-linear-gradient(left, #a2fbc2, #ff6767) !important;
}
.band3 {
  background: -webkit-linear-gradient(left, #e6c6ff, #9d00ff) !important;
}
.band4 {
  background: -webkit-linear-gradient(
      left,
      #0909d4,
      #00a1ff 20%,
      #14bb12 40%,
      #dde007 60%,
      #d20f0f
  ) !important;
}
.band5 {
  background: -webkit-linear-gradient(left, #baffe5, #1ab99c) !important;
}
.band6 {
  background: -webkit-linear-gradient(
      left,
      #93f602,
      #2fac01,
      #74cb01,
      #d0ef01,
      #e1ce01,
      #e16a01,
      #f70701
  ) !important;
}

.color-set {
  margin: 10px;
  line-height: 40px;
}
.color-band {
  position: relative;
  float: right;
  width: 70%;
}
:deep(.color-select) {
  width: 100%;
}
:deep(.color-btn) {
  position: absolute;
  z-index: 99;
  top: 50%;
  left: 45%;
  transform: translate(-50%, -50%);
  height: 70%;
  width: 80%;
  border-radius: 2.5px;
  background: -webkit-linear-gradient(left, #95e8f9, #002794);
}
</style>
