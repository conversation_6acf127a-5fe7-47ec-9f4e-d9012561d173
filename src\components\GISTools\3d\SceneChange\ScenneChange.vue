<template>
  <div
    v-if="isShow"
    class="tool-wrap"
  >
    <!-- <Header :header-info="headerInfo" @closePanel="closePanel" /> -->
    <div class="scene-container">
      <div
        v-for="(opt, index) in sceneOptions"
        :key="index"
        class="scene-item"
        :class="toogleIndex === index ? 'is-active' : ''"
        @click="toogleScene(opt, index)"
      >
        <svg-icon :icon-class="opt.iconClass" />
        <span class="scene-name">{{ opt.name }}</span>
      </div>
    </div>
  </div>
</template>

<script setup name="SceneChange">
import useMapViewStore from "@/store/modules/map/mapView.js"
import { flyToPoint } from "@/utils/Cesium/CesiumTool.js"
defineProps({
  isShow: {
    type: Boolean,
    default: true
  },
  headerInfo: {
    type: Object,
    default: ()=>{}
  }
})

const toolTitle = ref("场景切换")
const toogleIndex = ref("")

const sceneOptions = ref([
  {
    name: "蓝天",
    iconClass: "blue-day",
    funcName: "openSceneOfBlueDay"
  },
  {
    name: "晴天",
    iconClass: "sun-day",
    funcName: "openSceneOfSunDay"
  },
  {
    name: "晚霞",
    iconClass: "sunset",
    funcName: "openSceneOfSunset"
  },
  {
    name: "夜晚",
    iconClass: "night-sky",
    funcName: "openSceneOfNight"
  }
])
const isBlueDay = ref(false) // 蓝天
const isSunDay = ref(false) // 晴天
const isSunset = ref(false) // 晚霞
const isNight = ref(false) // 夜晚
// 天空盒
const blueSkyBox = shallowRef(undefined)
const sunSkyBox = shallowRef(undefined)
const sunsetSkyBox = shallowRef(undefined)
const nightSkyBox = shallowRef(undefined)
const currentSkyBox = shallowRef(undefined)
const skyListener = shallowRef(undefined)
const defaultSkyBox = shallowRef(undefined)

const viewer3d = computed(()=>useMapViewStore().viewer3d)
const scene = viewer3d.value.scene;

watch(isBlueDay,()=>{
  isSunDay.value = false
  isSunset.value = false
  isNight.value = false
})
watch(isSunDay,()=>{
  isBlueDay.value = false
  isSunset.value = false
  isNight.value = false
})
watch(isSunset,()=>{
  isBlueDay.value = false
  isSunDay.value = false
  isNight.value = false
})
watch(isNight,()=>{
  isBlueDay.value = false
  isSunDay.value = false
  isSunset.value = false
})

const emits = defineEmits(["closePanel"])

/**
 * 头部事件
 */
const closePanel = ()=> {
  emits("closePanel");
}

/**
 * 初始化场景-天空盒子
 */
const initSkyBox = async (scene)=> {
  defaultSkyBox.value = scene.skyBox;
  // 创建蓝天天空盒
  blueSkyBox.value = new Cesium.SkyBox({
    sources: {
      // positiveX: bRight,
      // negativeX: bLeft,
      // positiveY: bFront,
      // negativeY: bBack,
      // positiveZ: bUp,
      // negativeZ: bDown

      positiveX: new URL("../../../../assets/images/skyBox2/lantian/Right.jpg",import.meta.url).href,
      negativeX: new URL("../../../../assets/images/skyBox2/lantian/Left.jpg",import.meta.url).href,
      positiveY: new URL("../../../../assets/images/skyBox2/lantian/Front.jpg",import.meta.url).href,
      negativeY: new URL("../../../../assets/images/skyBox2/lantian/Back.jpg",import.meta.url).href,
      positiveZ: new URL("../../../../assets/images/skyBox2/lantian/Up.jpg",import.meta.url).href,
      negativeZ: new URL("../../../../assets/images/skyBox2/lantian/Down.jpg",import.meta.url).href

      // 这个颜色蓝，但是比较模糊
      // positiveX: import("../../../../assets/images/skyBox/blueSkyBox/Right.jpg"),
      // negativeX: import("../../../../assets/images/skyBox/blueSkyBox/Left.jpg"),
      // positiveY: import("../../../../assets/images/skyBox/blueSkyBox/Front.jpg"),
      // negativeY: import("../../../../assets/images/skyBox/blueSkyBox/Back.jpg"),
      // positiveZ: import("../../../../assets/images/skyBox/blueSkyBox/Up.jpg"),
      // negativeZ: import("../../../../assets/images/skyBox/blueSkyBox/Down.jpg")

      // 这个分辨率高，但是影像有问题
      // positiveX: import("../../../../assets/images/skyBox/highBlueSkyBox/2/Right.jpg"),
      // negativeX: import("../../../../assets/images/skyBox/highBlueSkyBox/2/Left.jpg"),
      // positiveY: import("../../../../assets/images/skyBox/highBlueSkyBox/2/Front.jpg"),
      // negativeY: import("../../../../assets/images/skyBox/highBlueSkyBox/2/Back.jpg"),
      // positiveZ: import("../../../../assets/images/skyBox/highBlueSkyBox/2/Up.jpg"),
      // negativeZ: import("../../../../assets/images/skyBox/highBlueSkyBox/2/Down.jpg")
    }
  });
  // 创建晴天天空盒
  sunSkyBox.value = new Cesium.SkyBox({
    sources: {
      positiveX: new URL("../../../../assets/images/skyBox2/qingtian/Right.jpg",import.meta.url).href,
      negativeX: new URL("../../../../assets/images/skyBox2/qingtian/Left.jpg",import.meta.url).href,
      positiveY: new URL("../../../../assets/images/skyBox2/qingtian/Front.jpg",import.meta.url).href,
      negativeY: new URL("../../../../assets/images/skyBox2/qingtian/Back.jpg",import.meta.url).href,
      positiveZ: new URL("../../../../assets/images/skyBox2/qingtian/Up.jpg",import.meta.url).href,
      negativeZ: new URL("../../../../assets/images/skyBox2/qingtian/Down.jpg",import.meta.url).href
    }
  });
  // 创建晚霞天空盒
  sunsetSkyBox.value = new Cesium.SkyBox({
    sources: {
      positiveX: new URL("../../../../assets/images/skyBox2/wanxia/Right.png",import.meta.url).href,
      negativeX: new URL("../../../../assets/images/skyBox2/wanxia/Left.png",import.meta.url).href,
      positiveY: new URL("../../../../assets/images/skyBox2/wanxia/Front.png",import.meta.url).href,
      negativeY: new URL("../../../../assets/images/skyBox2/wanxia/Back.png",import.meta.url).href,
      positiveZ: new URL("../../../../assets/images/skyBox2/wanxia/Up.png",import.meta.url).href,
      negativeZ: new URL("../../../../assets/images/skyBox2/wanxia/Down.png",import.meta.url).href
    }
  });
  // 创建夜晚天空盒
  nightSkyBox.value = new Cesium.SkyBox({
    sources: {
      // positiveX: new URL("../../../../assets/images/skyBox2/yewan/Right.jpg",import.meta.url).href,
      // negativeX: new URL("../../../../assets/images/skyBox2/yewan/Left.jpg",import.meta.url).href,
      // positiveY: new URL("../../../../assets/images/skyBox2/yewan/Front.jpg",import.meta.url).href,
      // negativeY: new URL("../../../../assets/images/skyBox2/yewan/Back.jpg",import.meta.url).href,
      // positiveZ: new URL("../../../../assets/images/skyBox2/yewan/Up.jpg",import.meta.url).href,
      // negativeZ: new URL("../../../../assets/images/skyBox2/yewan/Down.jpg",import.meta.url).href

      // 自定义下载天空盒
      positiveX: new URL("../../../../assets/images/skyBox2/nightSkyBox/px.png",import.meta.url).href,
      negativeX: new URL("../../../../assets/images/skyBox2/nightSkyBox/nx.png",import.meta.url).href,
      positiveY: new URL("../../../../assets/images/skyBox2/nightSkyBox/ny.png",import.meta.url).href,
      negativeY: new URL("../../../../assets/images/skyBox2/nightSkyBox/py.png",import.meta.url).href,
      positiveZ: new URL("../../../../assets/images/skyBox2/nightSkyBox/pz.png",import.meta.url).href,
      negativeZ: new URL("../../../../assets/images/skyBox2/nightSkyBox/nz.png",import.meta.url).href
    }
  });
}
/**
 * 场景切换
 */
const toogleScene = (opt, index)=> {
  toogleIndex.value === index ?
    (toogleIndex.value = "") :
    (toogleIndex.value = index);
  excuteFunc(opt.funcName)()
}
/**
 * 返回回调函数
 */
const excuteFunc = (funcName)=>{
  const funcObj = {
    "openSceneOfBlueDay": openSceneOfBlueDay,
    "openSceneOfSunDay": openSceneOfSunDay,
    "openSceneOfSunset": openSceneOfSunset,
    "openSceneOfNight": openSceneOfNight
  }
  return funcObj[funcName]
}

/**
 * 隐藏天空盒
 */
const hideSkyBox = ()=>{
  blueSkyBox.value.show = false
  sunSkyBox.value.show = false
  sunsetSkyBox.value.show = false
  nightSkyBox.value.show = false
  if (skyListener.value){
    viewer3d.value.scene.postRender.removeEventListener(skyListener.value);
  }
}

/**
 * 场景-蓝天
 */
const openSceneOfBlueDay = ()=> {
  isBlueDay.value = !isBlueDay.value;
  hideSkyBox()
  blueSkyBox.value.show = true
  if (isBlueDay.value) {
    blueSkyBox.value.WSpeed = 1.25;
    currentSkyBox.value = blueSkyBox.value;
    // 问题出在这里
    scene.skyBox = blueSkyBox.value;
    handlerCamera("skyBox"); //切换视角
    gradualChange();
  } else {
    scene.skyBox = defaultSkyBox.value;
    scene.skyAtmosphere.show = true;
    flyToPoint(viewer3d.value); //切换视角
  }
}

/**
 * 场景-晴天
 */
const openSceneOfSunDay = ()=> {
  isSunDay.value = !isSunDay.value;
  hideSkyBox()
  sunSkyBox.value.show = true
  if (isSunDay.value) {
    sunSkyBox.value.WSpeed = 1.25;
    currentSkyBox.value = sunSkyBox.value;
    scene.skyBox = sunSkyBox.value;
    handlerCamera("skyBox"); //切换视角
    gradualChange();
  } else {
    scene.skyBox = defaultSkyBox.value;
    scene.skyAtmosphere.show = true;
    flyToPoint(viewer3d.value); //切换视角
  }
}

/**
 * 场景-晚霞
 */
const openSceneOfSunset = ()=> {
  isSunset.value = !isSunset.value;
  hideSkyBox()
  sunsetSkyBox.value.show = true
  if (isSunset.value) {
    sunsetSkyBox.value.WSpeed = 1.25;
    currentSkyBox.value = sunsetSkyBox.value;
    scene.skyBox = sunsetSkyBox.value;
    handlerCamera("skyBox"); //切换视角
    gradualChange();
  } else {
    scene.skyBox = defaultSkyBox.value;
    scene.skyAtmosphere.show = true;
    flyToPoint(viewer3d.value); //切换视角
  }
}

/**
 * 场景-夜晚
 */
const openSceneOfNight = ()=> {
  isNight.value = !isNight.value;
  hideSkyBox()
  nightSkyBox.value.show = true
  if (isNight.value) {
    scene.sun.show = false;
    // scene.lightSource.ambientLightColor = new SuperMap3D.Color(0, 0, 0, 1);
    // 设置夜晚环境光
    scene.lightSource.ambientLightColor = new Cesium.Color(0.15, 0.15, 0.15, 1);
    // 新增点光源-整个环境
    const options = {
      color: new Cesium.Color(1, 1, 1, 1),
      cutoffDistance: 1000,
      decay: 1,
      intensity: 1
    };
    const position = Cesium.Cartesian3.fromDegrees(102.840703,24.860141,1938.268156)
    const pointLight = new Cesium.PointLight(position, options);

    scene.addLightSource(pointLight);

    nightSkyBox.value.WSpeed = 1.25;
    currentSkyBox.value = nightSkyBox.value;
    scene.skyBox = nightSkyBox.value;
    handlerCamera("skyBox"); //切换视角
    gradualChange();
  } else {
    scene.skyBox = defaultSkyBox.value;
    scene.skyAtmosphere.show = true;
    flyToPoint(viewer3d.value); //切换视角
  }
}

/**
 * 创建场景渐变
 */
const gradualChange = ()=> {
  skyListener.value = () => {
    const cameraHeight = scene.camera.positionCartographic.height;
    const skyAtmosphereH1 = 22e4; // 大气开始渐变的最大高度
    const skyBoxH1 = 15e4; // 天空开始渐变的最大高度
    const skyBoxH2 = 12e4; // 天空开始渐变的最小高度
    const bufferHeight = 1e4;
    if (
      cameraHeight < skyAtmosphereH1 &&
      Cesium.defined(currentSkyBox.value)
    ) {
      let skyAtmosphereT = (cameraHeight - skyBoxH2) / (skyAtmosphereH1 - skyBoxH2);

      if (skyAtmosphereT > 1.0) {
        skyAtmosphereT = 1.0;
      } else if (skyAtmosphereT < 0.0) {
        skyAtmosphereT = 0.0;
      }
      let skyBoxT = (cameraHeight - skyBoxH2) / (skyBoxH1 - skyBoxH2);
      if (skyBoxT > 1.0) {
        skyBoxT = 1.0;
      } else if (skyBoxT < 0.0) {
        skyBoxT = 0.0;
      }
      currentSkyBox.value.alpha = 1.0 - skyBoxT;
      if (cameraHeight > skyBoxH2) {
        scene.skyAtmosphere.show = true;
        scene.skyAtmosphere.alpha = skyAtmosphereT;
        scene.skyBox = currentSkyBox.value;
      } else {
        scene.skyAtmosphere.show = false;
      }
    } else {
      scene.skyAtmosphere.alpha = 1.0;
      scene.skyBox = defaultSkyBox.value;
    }
    // 控制相机 速率
    if (scene.skyBox !== defaultSkyBox.value) {
      // zoomFactor 获取或设置相机缩放速度，默认值为5.0。
      if (
        cameraHeight > skyBoxH2 - 2 * bufferHeight &&
        cameraHeight < skyBoxH1 + 3 * bufferHeight
      ) {
        scene.screenSpaceCameraController.zoomFactor = 0.4;
      } else {
        scene.screenSpaceCameraController.zoomFactor = 5.0;
      }
    } else {
      scene.skyBox.alpha = 0;
      scene.skyAtmosphere.alpha = 0;
      scene.screenSpaceCameraController.zoomFactor = 5.0;
    }
  };
  scene.postRender.addEventListener(skyListener.value);
}

/**
 * 操作相机
 */
const handlerCamera = (sceneType)=> {
  if (sceneType == "cloud") {
    scene.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(
        102.801686,
        24.820287,
        12718852.74864528
      )
    });
  } else if (sceneType == "skyBox") {
    scene.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(
        102.848696,
        24.324227,
        15000
      ),
      orientation: {
        heading: 0,
        pitch: -0.04680090697592232,
        roll: 0
      }
    });
  }
}

/**
 * 恢复默认天空盒
 */
const resetSkyBox = ()=>{
  // 开启大气层
  scene.skyAtmosphere.show = true
  scene.skyAtmosphere.brightnessShift = 0.25; // 大气光的强度
  scene.skyAtmosphere.saturationShift = 0.25; // 大气光的饱和度

  blueSkyBox.value.show = false
  sunsetSkyBox.value.show = false
  sunSkyBox.value.show = false
  nightSkyBox.value.show = false
  scene.skyBox = defaultSkyBox.value

  if (skyListener.value){
    viewer3d.value.scene.postRender.removeEventListener(skyListener.value);
    skyListener.value = null
  }
}

onMounted(()=>{
  initSkyBox(viewer3d.value.scene);
})

onBeforeUnmount(()=>{
  resetSkyBox()
})
</script>

<style scoped lang="scss">
.tool-wrap {
  color: #fff;
  background-image: url("@/assets/images/map/tool.png");
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-size: cover;
  box-shadow: 0 0 8px 0 #057595;
}
.scene-container {
  display: flex;
  padding: 10px;
  flex-wrap: wrap;
  justify-content: space-around;
  .scene-item {
    cursor: pointer;
    .svg-icon {
      width: 1.25em;
      height: 1.25em;
    }
  }
  .scene-item.is-active {
    text-shadow: 0 0 3px #ffffff21, 0 0 2px #ffffff2e, 0 0 10px #ffffff21, 0 0 10px #409eff, 0 0 10px #409eff, 0 0 6px #00cffa, 0 0 10px #409eff, 0 0 5px #409eff;
  }
}
:deep(.el-button--primary) {
  margin: 10px;
}
.scene-name {
  margin-left: 5px;
}
</style>
