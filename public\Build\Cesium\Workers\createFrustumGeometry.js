define(["./when-b60132fc","./FrustumGeometry-d656d5b9","./buildModuleUrl-9085faaa","./Cartographic-3309dd0d","./Check-7b2a090c","./Math-119be1a3","./Rectangle-dee65d21","./FeatureDetection-806b12f0","./Cartesian4-3ca25aab","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Event-16a2dfbf","./ComponentDatatype-c140a87d","./GeometryAttribute-c65394ac","./Cartesian2-db21342c","./GeometryAttributes-252e9929","./Plane-a3d8b3d2","./VertexFormat-6446fca0"],(function(e,t,a,r,n,c,d,u,o,b,i,m,f,s,y,G,C,l){"use strict";return function(a,r){return e.defined(r)&&(a=t.FrustumGeometry.unpack(a,r)),t.FrustumGeometry.createGeometry(a)}}));
