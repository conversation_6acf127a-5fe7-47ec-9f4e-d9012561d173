<template>
  <div class="main-content">
    <transition>
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="发布年份" prop="fbnf">
              <el-input
                v-model="queryParams.fbnf"
                placeholder="请输入发布年份"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="发布月份" prop="fbyf">
              <el-input
                v-model="queryParams.fbyf"
                placeholder="请输入发布月份"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="企业名称" prop="qymc">
              <el-input
                v-model="queryParams.qymc"
                placeholder="请输入企业名称"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="子公司名称" prop="zgsmc">
              <el-input
                v-model="queryParams.zgsmc"
                placeholder="请输入子公司名称"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="综合评分" prop="zhpf">
              <el-input
                v-model="queryParams.zhpf"
                placeholder="请输入综合评分"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="备注" prop="bz">
              <el-input
                v-model="queryParams.bz"
                placeholder="请输入备注"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card class="result-wrap">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="Plus"
              @click="handleAdd"
              v-hasPermi="['patrol:topicCompany:add']"
              >新增</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              plain
              icon="Edit"
              :disabled="single"
              @click="handleUpdate()"
              v-hasPermi="['patrol:topicCompany:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="Delete"
              :disabled="multiple"
              @click="handleDelete()"
              v-hasPermi="['patrol:topicCompany:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="Download"
              @click="handleExport"
              v-hasPermi="['patrol:topicCompany:export']"
              >导出</el-button
            >
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>
      </template>

      <el-table
        v-loading="loading"
        :data="topicCompanyList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column label="发布年份" align="center" prop="fbnf" />
        <el-table-column label="发布月份" align="center" prop="fbyf" />
        <el-table-column label="企业名称" align="center" prop="qymc" />
        <el-table-column label="子公司名称" align="center" prop="zgsmc" />
        <el-table-column label="综合评分" align="center" prop="zhpf" />
        <el-table-column label="备注" align="center" prop="bz" />
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button
                link
                type="primary"
                icon="Edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['patrol:topicCompany:edit']"
              ></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button
                link
                type="primary"
                icon="Delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['patrol:topicCompany:remove']"
              ></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
    <!-- 添加或修改公司名录对话框 -->
    <el-dialog
      :title="dialog.title"
      v-model="dialog.visible"
      width="500px"
      append-to-body
    >
      <el-form
        ref="topicCompanyFormRef"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="发布年份" prop="fbnf">
          <el-input v-model="form.fbnf" placeholder="请输入发布年份" />
        </el-form-item>
        <el-form-item label="发布月份" prop="fbyf">
          <el-input v-model="form.fbyf" placeholder="请输入发布月份" />
        </el-form-item>
        <el-form-item label="企业名称" prop="qymc">
          <el-input v-model="form.qymc" placeholder="请输入企业名称" />
        </el-form-item>
        <el-form-item label="子公司名称" prop="zgsmc">
          <el-input v-model="form.zgsmc" placeholder="请输入子公司名称" />
        </el-form-item>
        <el-form-item label="综合评分" prop="zhpf">
          <el-input v-model="form.zhpf" placeholder="请输入综合评分" />
        </el-form-item>
        <el-form-item label="备注" prop="bz">
          <el-input v-model="form.bz" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="TopicCompany">
import {
  listTopicCompany,
  getTopicCompany,
  delTopicCompany,
  addTopicCompany,
  updateTopicCompany,
} from "@/api/company/company.js";

const { proxy } = getCurrentInstance();

const topicCompanyList = ref([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref();
const topicCompanyFormRef = ref();

const dialog = reactive({
  visible: false,
  title: "",
});

const initFormData = {
  id: undefined,
  fbnf: undefined,
  fbyf: undefined,
  qymc: undefined,
  zgsmc: undefined,
  zhpf: undefined,
  bz: undefined,
};
const data = reactive({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    fbnf: undefined,
    fbyf: undefined,
    qymc: undefined,
    zgsmc: undefined,
    zhpf: undefined,
    bz: undefined,
    params: {},
  },
  rules: {
    id: [{ required: true, message: "id不能为空", trigger: "blur" }],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公司名录列表 */
const getList = async () => {
  loading.value = true;
  const res = await listTopicCompany(queryParams.value);
  topicCompanyList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  topicCompanyFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加公司名录";
};

/** 修改按钮操作 */
const handleUpdate = async (row) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getTopicCompany(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改公司名录";
};

/** 提交按钮 */
const submitForm = () => {
  topicCompanyFormRef.value?.validate(async (valid) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateTopicCompany(form.value).finally(
          () => (buttonLoading.value = false),
        );
      } else {
        await addTopicCompany(form.value).finally(
          () => (buttonLoading.value = false),
        );
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal
    .confirm('是否确认删除公司名录编号为"' + _ids + '"的数据项？')
    .finally(() => (loading.value = false));
  await delTopicCompany(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    "patrol/topicCompany/export",
    {
      ...queryParams.value,
    },
    `topicCompany_${new Date().getTime()}.xlsx`,
  );
};

onMounted(() => {
  getList();
});
</script>
<style lang="scss" scoped>
.main-content {
  padding: 10px;
}
.result-wrap {
  margin-top: 10px;
}
</style>
