/**
 * @name: MeasureDistance-Cesium
 * @description: 量测距离工具类
 * @author: zyc
 * @time: 2024-04-29
 **/
import startImage from '@/assets/images/map/start.png'
import endImage from '@/assets/images/map/end.png'

import { setCursor } from '@/utils/Cesium/CesiumTool'
import CesiumMeasureBaseClass from "@/utils/Cesium/CesiumMeasureBaseClass";
export default class CesiumMeasureDistanceClass extends CesiumMeasureBaseClass{
  constructor(props) {
    super(props)

    // label为string类型，如果改为数字会报错
    this.distance = ""
    this.totalDistance = 0

    this.positions = [] // 存储坐标
    this.pointEntities = [] // 存储点
  }
  initEvent(){
    setCursor(this.viewer3d,'crosshair')
    // 注册测量事件
    this.handler = new Cesium.ScreenSpaceEventHandler(this.viewer3d.canvas)

    const polyline = {
      name: "绘制线段",
      show: true,
      polyline: {
        positions: new Cesium.CallbackProperty(() => {
          return this.positions;
        }, false),
        width: 2.5,
        show: true,
        material: Cesium.Color.BLANCHEDALMOND,
        clampToGround: this.clampToGround
      }
    }
    this.viewer3d.entities.add(polyline)
    this.registerEvent()
  }
  registerEvent(){
    this.mouseClick()
    this.mouseMove()
    this.mouseDoubleClick()
  }

  /**
   * 鼠标单击事件：开始绘制
   */
  mouseClick(){
    this.handler.setInputAction(async res => {
      const worldPosition = this.viewer3d.scene.pickPosition(res.position)
      if (Cesium.defined(worldPosition)){
        this.positions.push(worldPosition)
        // 计算距离
        if (this.positions.length > 1){
          await this.calculateDistance()
        }
        const pointEntity = new Cesium.Entity({
          name: "绘制点",
          show: true,
          position: worldPosition,
          point: new Cesium.PointGraphics({
            color: Cesium.Color.AQUA,
            pixelSize: 5,
            outlineColor: Cesium.Color.GHOSTWHITE,
            outlineWidth: 1,
            distanceDisplayCondition: 10,
            heightReference: this.heightMode
          }),
          label: new Cesium.LabelGraphics({
            text: this.distance,
            font: "10px",
            pixelOffset: new Cesium.Cartesian3(0, -40,100000),
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            outlineWidth: 9,
            outlineColor: Cesium.Color.YELLOW
          })
        })
        this.pointEntities.push(pointEntity)
        this.viewer3d.entities.add(pointEntity)
        // 在起点添加Billboard
        if (this.positions.length === 1){
          this.createBillboard(this.pointEntities[0].position,startImage)
        }
      }
    },Cesium.ScreenSpaceEventType.LEFT_CLICK)
  }

  /**
   *  3.鼠标移动事件：绘制临时线段
   */
  mouseMove(){
    this.handler.setInputAction(res => {
      const windowPosition = res.endPosition
      this.infoBox.hidden = false
      this.infoBox.style.top = windowPosition.y - 10 + "px"
      this.infoBox.style.left = windowPosition.x + 20 + "px"

      const worldCoords = this.viewer3d.scene.pickPosition(windowPosition)
      if (this.positions.length >= 1){
        // 移除已经添加线段
        if (this.viewer3d.entities.getById('tempLine')){
          this.viewer3d.entities.remove(this.viewer3d.entities.getById('tempLine'))
        }
        // 构建连接线，绘制临时线
        const tempLine = new Cesium.Entity({
          id: "tempLine",
          name: "临时线段",
          show: true,
          polyline: new Cesium.PolylineGraphics({
            positions: new Cesium.CallbackProperty(() => {
              return [].concat(this.positions[this.positions.length - 1],worldCoords);
            }, false),
            width: 2,
            show: true,
            material: Cesium.Color.BLANCHEDALMOND,
            clampToGround: this.clampToGround
          })
        })
        this.viewer3d.entities.add(tempLine)
      }
    },Cesium.ScreenSpaceEventType.MOUSE_MOVE)
  }

  /**
   * 鼠标双击事件：结束绘制
   */
  mouseDoubleClick(){
    this.handler.setInputAction(async res => {
      // 去除最后一个点位
      if (this.clampToGround){
        setTimeout(() => {
          this.viewer3d.entities.remove(this.pointEntities[this.pointEntities.length - 1])
          // 添加终点 Billboard
          this.createBillboard(this.pointEntities[this.pointEntities.length - 1].position,endImage)
        },1500)
      } else {
        this.viewer3d.entities.remove(this.pointEntities[this.pointEntities.length - 1])
        // 添加终点 Billboard
        this.createBillboard(this.pointEntities[this.pointEntities.length - 1].position,endImage)
      }
      this.positions.pop()

      this.handler.destroy()
      // 关闭提示
      this.infoBox.remove()
      setCursor(this.viewer3d,'pointer')
    },Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK)
  }

  async calculateDistance(){
    const length = this.positions.length
    if (this.clampToGround){
      const singleDistance = this.getTerrainDistance(this.viewer3d,this.positions[length - 2],this.positions[length - 1])
      console.log("单段距离：",singleDistance)
      // singleDistance.then(res=>{
      //   debugger
      // })
      // this.totalDistance += singleDistance
      // console.log("总距离：",this.totalDistance)
      // const distanceValue = +(this.totalDistance.toFixed(2))
      // this.distance = length > 1
      //   ? "总长：" + distanceValue + "公里 \n (+" + singleDistance + "公里)" : ''
      // this.setDistance(distanceValue)
      const singleResult = this.getTerrainDistance(this.viewer3d,this.positions[length - 2],this.positions[length - 1])
      console.log("单段距离singleResult：",singleResult.then(res=>console.log(res)))
      singleDistance.then(res=>{
        debugger
        this.totalDistance += singleDistance
        console.log("总距离：",this.totalDistance)
        const distanceValue = +(this.totalDistance.toFixed(2))
        this.distance = length > 1 ?
          "总长：" + distanceValue + "公里 \n (+" + singleDistance + "公里)" : ''
        this.setDistance(distanceValue)
      })
    } else {
      const singleDistance = this.getDistance(this.positions)
      this.totalDistance += singleDistance
      const distanceValue = +(this.totalDistance.toFixed(2))
      this.distance = length > 1 ?
        "总长：" + distanceValue + "公里 \n (+" + singleDistance + "公里)" : ''
      this.setDistance(distanceValue)
    }
  }

  // 计算空间距离
  getDistance(positions){
    const length = positions.length
    const singleDistance = Cesium.Cartesian3.distance(positions[length - 2],positions[length - 1])
    const distanceValue = singleDistance / 1000
    const tempValue = Number(distanceValue.toFixed(2))
    return tempValue
  }
  /**
   * @description 计算贴地形距离
   * @param {SuperMap3D.Viewer} viewer
   * @param {SuperMap3D.Cartesian3} start
   * @param {SuperMap3D.Cartesian3} end
   * @returns {number}
   */
  getTerrainDistance(viewer, start, end) {
    // 制图坐标插值（不贴附地形）
    const lerpArray = [];
    // 插值数量，我这里根据两点间直线距离来插，每1米插一下，插的越多越精准
    const splitNum = parseInt(Cesium.Cartesian3.distance(start, end));

    const startCartographic = Cesium.Cartographic.fromCartesian(start);
    const startDegrees = [startCartographic.longitude, startCartographic.latitude];
    const endCartographic = Cesium.Cartographic.fromCartesian(end);
    const endDegrees = [endCartographic.longitude, endCartographic.latitude];

    lerpArray.push(new Cesium.Cartographic(startDegrees[0], startDegrees[1]));

    for (let i = 1; i <= splitNum - 1; i++) {
      const num = i / splitNum
      const x = Cesium.Math.lerp(startDegrees[0], endDegrees[0], num);
      const y = Cesium.Math.lerp(startDegrees[1], endDegrees[1], num);
      lerpArray.push(new Cesium.Cartographic(x, y));
    }

    // 地形细节采样：传入 目标地形 和 制图坐标插值组（不贴附地形） 获取 贴地形的制图坐标插值组 再计算距离
    // Cesium.sampleTerrainMostDetailed(viewer.terrainProvider, lerpArray).then(res=>{
    //   console.log("采样点：",res)
    //   debugger
    // })

    var promise = Cesium.sampleTerrainMostDetailed(viewer.terrainProvider, lerpArray);
    return Cesium.when(promise, function(updatedPositions) {
      console.log("采样点：",updatedPositions)
      debugger
      return this.getDetailedTerrainDistance(updatedPositions)
    });

    // const cartographicArr = Cesium.sampleTerrainMostDetailed(viewer.terrainProvider, lerpArray)
    // console.log("采样点：",cartographicArr)


    // return new Promise((resolve,reject)=>{
    //   const result = this.getDetailedTerrainDistance(cartographicArr)
    //   debugger
    //   resolve(result)
    // })

    // const promise = Cesium.sampleTerrainMostDetailed(viewer.terrainProvider, lerpArray)
    // return Cesium.when(promise,(cartographicArr)=>{
    //   debugger
    //   return this.getDetailedTerrainDistance(cartographicArr)
    // })
  }

  /**
   * @description 根据制图坐标计算距离
   * @param {Array.<SuperMap3D.Cartographic>} cartographicArr 制图坐标数组
   * @returns {number} 距离值
   */
  getDetailedTerrainDistance(cartographicArr) {
    let terrainDistance = 0;
    cartographicArr.map((currentCartographic, index) => {
      if (index == cartographicArr.length - 1) {
        return;
      }
      const nextCartographic = cartographicArr[index + 1];
      const currentPosition = Cesium.Cartesian3.fromRadians(currentCartographic.longitude, currentCartographic.latitude, currentCartographic.height);
      const nextPosition = Cesium.Cartesian3.fromRadians(nextCartographic.longitude, nextCartographic.latitude, nextCartographic.height);
      terrainDistance += Cesium.Cartesian3.distance(currentPosition, nextPosition);
    });
    const distanceValue = terrainDistance / 1000
    const tempValue = Number(distanceValue.toFixed(2))
    return tempValue
  }
  setDistance(value){
    // 注册一个自定义事件
    const event = new CustomEvent('valueChange',{
      detail: value
    })
    this.viewer3d.container.dispatchEvent(event)
  }

  /**
   * 创建标签
   * @param position
   */
  createLabel(position){
    this.viewer3d.entities.add({
      name: "label",
      position: position,
      label: {
        text: '',
        scale: 0.5,
        // eyeOffset: new Cartesian3(0.0, 0.0, 1000.0),
        heightReference: this.heightMode,
        font: 'normal 30px MicroSoft YaHei',
        // distanceDisplayCondition: new SuperMap3D.DistanceDisplayCondition(0, 5000),
        // scaleByDistance: new SuperMap3D.NearFarScalar(1000, 1, 3000, 0.4),
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian3(0, 0,100000),
        outlineWidth: 9,
        outlineColor: Cesium.Color.YELLOW
      }
    })
  }

  /**
   * 创建billBoard
   * @position：笛卡尔坐标
   * @imageUrl：图片url
   */
  createBillboard(position,imageUrl){
    this.viewer3d.entities.add({
      name: "billboard",
      description: "标识牌",
      position: position,
      billboard: {
        image: imageUrl,
        width: 50,
        height: 50
        //eyeOffset: new SuperMap3D.Cartesian3(0,0,1000)
      }
    })
  }

  /**
   * 清除绘制
   */
  clearMeasure(){
    const isDestroyed = this.handler.isDestroyed()
    if (!isDestroyed){
      // 若事件未销毁，则销毁；若销毁，则不可用
      this.handler.destroy()
    }
    this.infoBox.remove()
    this.viewer3d.entities.removeAll()
  }
}
