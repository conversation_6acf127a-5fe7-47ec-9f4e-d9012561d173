<template>
  <div id="app" class="drone-dashboard">
    <!-- 顶部标题 -->
    <div class="header" :style="{ backgroundImage: `url(${imagePath})` }"/>

    <div style="width: 100%;display: flex;justify-content: center;align-items: center;height:5vh;gap: 20px">
      <!-- 常规模式按钮 -->
      <el-button class="custom-button" type="primary" :plain="mode !== 'regular'" @click="setMode('regular')" :class="{ active: mode === 'regular' }">
        常规模式
      </el-button>

      <!-- 飞行模式按钮 -->
      <el-button class="custom-button" type="primary" :plain="mode !== 'flight'" @click="setMode('flight')" :class="{ active: mode === 'flight' }">
        飞行模式
      </el-button>
    </div>

    <!-- 根据模式显示不同内容 -->
    <RegularMode v-if="drones.isConnected" />

    <LiveMode v-else />
  </div>
</template>

<script setup>

import { ref } from 'vue'
import { useDroneStore } from '@/store/modules/drone.js'
import RegularMode from '@/views/cockpit/routineIndex.vue'
import LiveMode from '@/views/cockpit/flightIndex.vue'

// 图片资源
import cgztImg from '@/assets/images/drone/cgzt_02.png'
import wrjdpImg from '@/assets/images/drone/wrjdp_02.png'

const drones = useDroneStore()
const boundUpdate = drones.updateIsConnected

// 组件状态
const handleName = ref('飞行模式')
const imagePath = ref(cgztImg)

// 当前模式：'regular' | 'flight'
const mode = ref('regular')

// 切换模式的方法
const setMode = (newMode) => {
  boundUpdate(!drones.isConnected)
  mode.value = newMode
  imagePath.value = newMode === 'regular' ? cgztImg : wrjdpImg
}
/**
 * 模式切换处理
 */
const handleClick = () => {

  handleName.value = drones.isConnected ? '飞行模式' : '常规模式'
  imagePath.value = drones.isConnected ? cgztImg : wrjdpImg
}
</script>

<style scoped>
/* 基础重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 整体布局 */
.drone-dashboard {
  min-height: 100vh;
  background-image: url("@/assets/images/drone/background.png");
  color: #ffffff;
  font-family: 'Roboto', 'PingFang SC', 'Microsoft YaHei', sans-serif;
  display: flex;
  flex-direction: column;
}

.status-bar {
  position: absolute;
  right: 2%;
  left: auto;
}

/* 顶部标题 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 10vh;
  min-height: 10vh;
  max-height: 10vh;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

/* 自定义按钮样式 */
.custom-button {
  display: flex;               /* 使用 Flexbox 布局 */
  justify-content: center;     /* 水平居中 */
  align-items: center;         /* 垂直居中 */
  background-color: transparent;
  border-color: white;
  color: white;
  font-size: 16px;
  padding: 10px 20px;
  border-radius: 20px;
  transition: all 0.3s ease;
  height: 40px;                /* 设置固定高度以保证足够的点击区域，根据实际情况调整 */
}

/* 激活时按钮样式 */
.custom-button.active {
  background-color: white;
  color: #00a0e9;
  border-color: white;
}

/* 悬停效果 */
.custom-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}
</style>
