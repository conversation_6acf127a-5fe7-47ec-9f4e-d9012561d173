import request from '@/utils/request';

/**
 * 查询拟储备项目列表
 * @param query
 * @returns {*}
 */

export const listPlanReserveProject = (query) => {
  return request({
    url: '/patrol/planReserveProject/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询拟储备项目详细
 * @param id
 */
export const getPlanReserveProject = (id) => {
  return request({
    url: '/patrol/planReserveProject/' + id,
    method: 'get'
  });
};

/**
 * 新增拟储备项目
 * @param data
 */
export const addPlanReserveProject = (data) => {
  return request({
    url: '/patrol/planReserveProject',
    method: 'post',
    data: data,
    headers: {
      "Content-Type": "multipart/form-data" // 必须设置
    }
  });
};

/**
 * 修改拟储备项目
 * @param data
 */
export const updatePlanReserveProject = (data) => {
  return request({
    url: '/patrol/planReserveProject',
    method: 'put',
    data: data
  });
};

/**
 * 删除拟储备项目
 * @param id
 */
export const delPlanReserveProject = (id) => {
  return request({
    url: '/patrol/planReserveProject/' + id,
    method: 'delete'
  });
};

/**
 * 导出拟收储项目数据
 */
export const downloadPlanReserveProject = () => {
  return request({
    url: "/patrol/planReserveProject/export",
    method: "post",
    responseType: "blob"
  })
}


/**
 * 统计拟收储项目数量与面积
 */
export const getPlanStoreStatistics = () => {
  return request({
    url: "/patrol/relProject/planProjectSta",
    method: "get"
  })
}
