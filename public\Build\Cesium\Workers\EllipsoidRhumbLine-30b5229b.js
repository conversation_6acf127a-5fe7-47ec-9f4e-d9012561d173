define(["exports","./Cartographic-3309dd0d","./Check-7b2a090c","./when-b60132fc","./Rectangle-dee65d21","./Math-119be1a3"],(function(t,i,e,a,n,s){"use strict";function h(t,i,e){if(0===t)return i*e;var a=t*t,n=a*a,s=n*a,h=s*a,u=h*a,r=u*a,o=e;return i*((1-a/4-3*n/64-5*s/256-175*h/16384-441*u/65536-4851*r/1048576)*o-(3*a/8+3*n/32+45*s/1024+105*h/4096+2205*u/131072+6237*r/524288)*Math.sin(2*o)+(15*n/256+45*s/1024+525*h/16384+1575*u/65536+155925*r/8388608)*Math.sin(4*o)-(35*s/3072+175*h/12288+3675*u/262144+13475*r/1048576)*Math.sin(6*o)+(315*h/131072+2205*u/524288+43659*r/8388608)*Math.sin(8*o)-(693*u/1310720+6237*r/5242880)*Math.sin(10*o)+1001*r/8388608*Math.sin(12*o))}function u(t,i){if(0===t)return Math.log(Math.tan(.5*(s.CesiumMath.PI_OVER_TWO+i)));var e=t*Math.sin(i);return Math.log(Math.tan(.5*(s.CesiumMath.PI_OVER_TWO+i)))-t/2*Math.log((1+e)/(1-e))}var r=new i.Cartesian3,o=new i.Cartesian3;function l(t,e,a,n){i.Cartesian3.normalize(n.cartographicToCartesian(e,o),r),i.Cartesian3.normalize(n.cartographicToCartesian(a,o),o);var l=n.maximumRadius,d=n.minimumRadius,M=l*l,c=d*d;t._ellipticitySquared=(M-c)/M,t._ellipticity=Math.sqrt(t._ellipticitySquared),t._start=i.Cartographic.clone(e,t._start),t._start.height=0,t._end=i.Cartographic.clone(a,t._end),t._end.height=0,t._heading=function(t,i,e,a,n){var h=u(t._ellipticity,e),r=u(t._ellipticity,n);return Math.atan2(s.CesiumMath.negativePiToPi(a-i),r-h)}(t,e.longitude,e.latitude,a.longitude,a.latitude),t._distance=function(t,i,e,a,n,u,r){var o=t._heading,l=u-a,d=0;if(s.CesiumMath.equalsEpsilon(Math.abs(o),s.CesiumMath.PI_OVER_TWO,s.CesiumMath.EPSILON8))if(i===e)d=i*Math.cos(n)*s.CesiumMath.negativePiToPi(l);else{var M=Math.sin(n);d=i*Math.cos(n)*s.CesiumMath.negativePiToPi(l)/Math.sqrt(1-t._ellipticitySquared*M*M)}else{var c=h(t._ellipticity,i,n);d=(h(t._ellipticity,i,r)-c)/Math.cos(o)}return Math.abs(d)}(t,n.maximumRadius,n.minimumRadius,e.longitude,e.latitude,a.longitude,a.latitude)}function d(t,e,n,r,o,l){var d,M,c,g=o*o;if(Math.abs(s.CesiumMath.PI_OVER_TWO-Math.abs(e))>s.CesiumMath.EPSILON8){M=function(t,i,e){var a=t/e;if(0===i)return a;var n=a*a,s=n*a,h=s*a,u=i*i,r=u*u,o=r*u,l=o*u,d=l*u,M=d*u,c=Math.sin(2*a),g=Math.cos(2*a),m=Math.sin(4*a),_=Math.cos(4*a),p=Math.sin(6*a),C=Math.cos(6*a),f=Math.sin(8*a),P=Math.cos(8*a),v=Math.sin(10*a);return a+a*u/4+7*a*r/64+15*a*o/256+579*a*l/16384+1515*a*d/65536+16837*a*M/1048576+(3*a*r/16+45*a*o/256-a*(32*n-561)*l/4096-a*(232*n-1677)*d/16384+a*(399985-90560*n+512*h)*M/5242880)*g+(21*a*o/256+483*a*l/4096-a*(224*n-1969)*d/16384-a*(33152*n-112599)*M/1048576)*_+(151*a*l/4096+4681*a*d/65536+1479*a*M/16384-453*s*M/32768)*C+(1097*a*d/65536+42783*a*M/1048576)*P+8011*a*M/1048576*Math.cos(10*a)+(3*u/8+3*r/16+213*o/2048-3*n*o/64+255*l/4096-33*n*l/512+20861*d/524288-33*n*d/512+h*d/1024+28273*M/1048576-471*n*M/8192+9*h*M/4096)*c+(21*r/256+21*o/256+533*l/8192-21*n*l/512+197*d/4096-315*n*d/4096+584039*M/16777216-12517*n*M/131072+7*h*M/2048)*m+(151*o/6144+151*l/4096+5019*d/131072-453*n*d/16384+26965*M/786432-8607*n*M/131072)*p+(1097*l/131072+1097*d/65536+225797*M/10485760-1097*n*M/65536)*f+(8011*d/2621440+8011*M/1048576)*v+293393*M/251658240*Math.sin(12*a)}(h(o,r,t.latitude)+n*Math.cos(e),o,r);var m=u(o,t.latitude),_=u(o,M);c=Math.tan(e)*(_-m),d=s.CesiumMath.negativePiToPi(t.longitude+c)}else{var p;if(M=t.latitude,0===o)p=r*Math.cos(t.latitude);else{var C=Math.sin(t.latitude);p=r*Math.cos(t.latitude)/Math.sqrt(1-g*C*C)}c=n/p,d=e>0?s.CesiumMath.negativePiToPi(t.longitude+c):s.CesiumMath.negativePiToPi(t.longitude-c)}return a.defined(l)?(l.longitude=d,l.latitude=M,l.height=0,l):new i.Cartographic(d,M,0)}function M(t,e,s){var h=a.defaultValue(s,n.Ellipsoid.WGS84);this._ellipsoid=h,this._start=new i.Cartographic,this._end=new i.Cartographic,this._heading=void 0,this._distance=void 0,this._ellipticity=void 0,this._ellipticitySquared=void 0,a.defined(t)&&a.defined(e)&&l(this,t,e,h)}Object.defineProperties(M.prototype,{ellipsoid:{get:function(){return this._ellipsoid}},surfaceDistance:{get:function(){return this._distance}},start:{get:function(){return this._start}},end:{get:function(){return this._end}},heading:{get:function(){return this._heading}}}),M.fromStartHeadingDistance=function(t,i,e,h,u){var r=a.defaultValue(h,n.Ellipsoid.WGS84),o=r.maximumRadius,l=r.minimumRadius,c=o*o,g=l*l,m=Math.sqrt((c-g)/c),_=d(t,i=s.CesiumMath.negativePiToPi(i),e,r.maximumRadius,m);return!a.defined(u)||a.defined(h)&&!h.equals(u.ellipsoid)?new M(t,_,r):(u.setEndPoints(t,_),u)},M.prototype.setEndPoints=function(t,i){l(this,t,i,this._ellipsoid)},M.prototype.interpolateUsingFraction=function(t,i){return this.interpolateUsingSurfaceDistance(t*this._distance,i)},M.prototype.interpolateUsingSurfaceDistance=function(t,i){return d(this._start,this._heading,t,this._ellipsoid.maximumRadius,this._ellipticity,i)},M.prototype.findIntersectionWithLongitude=function(t,e){var n=this._ellipticity,h=this._heading,u=Math.abs(h),r=this._start;if(t=s.CesiumMath.negativePiToPi(t),s.CesiumMath.equalsEpsilon(Math.abs(t),Math.PI,s.CesiumMath.EPSILON14)&&(t=s.CesiumMath.sign(r.longitude)*Math.PI),a.defined(e)||(e=new i.Cartographic),Math.abs(s.CesiumMath.PI_OVER_TWO-u)<=s.CesiumMath.EPSILON8)return e.longitude=t,e.latitude=r.latitude,e.height=0,e;if(s.CesiumMath.equalsEpsilon(Math.abs(s.CesiumMath.PI_OVER_TWO-u),s.CesiumMath.PI_OVER_TWO,s.CesiumMath.EPSILON8)){if(s.CesiumMath.equalsEpsilon(t,r.longitude,s.CesiumMath.EPSILON12))return;return e.longitude=t,e.latitude=s.CesiumMath.PI_OVER_TWO*s.CesiumMath.sign(s.CesiumMath.PI_OVER_TWO-h),e.height=0,e}var o,l=r.latitude,d=n*Math.sin(l),M=Math.tan(.5*(s.CesiumMath.PI_OVER_TWO+l))*Math.exp((t-r.longitude)/Math.tan(h)),c=(1+d)/(1-d),g=r.latitude;do{o=g;var m=n*Math.sin(o),_=(1+m)/(1-m);g=2*Math.atan(M*Math.pow(_/c,n/2))-s.CesiumMath.PI_OVER_TWO}while(!s.CesiumMath.equalsEpsilon(g,o,s.CesiumMath.EPSILON12));return e.longitude=t,e.latitude=g,e.height=0,e},M.prototype.findIntersectionWithLatitude=function(t,e){var n=this._ellipticity,h=this._heading,r=this._start;if(!s.CesiumMath.equalsEpsilon(Math.abs(h),s.CesiumMath.PI_OVER_TWO,s.CesiumMath.EPSILON8)){var o=u(n,r.latitude),l=u(n,t),d=Math.tan(h)*(l-o),M=s.CesiumMath.negativePiToPi(r.longitude+d);return a.defined(e)?(e.longitude=M,e.latitude=t,e.height=0,e):new i.Cartographic(M,t,0)}},t.EllipsoidRhumbLine=M}));
