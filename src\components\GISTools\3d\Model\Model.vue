<template>
  <div
    v-if="isShow"
    class="tool-wrap"
  >
    <div class="scene-container clearfix">
      <div
        v-for="img in modelImages"
        :key="img.name"
        class="model-image"
        @click="addModel2Viewer(img.path)"
      >
        <h5 class="img-title">{{ img.name }}</h5>
        <el-image
          style="width: 83px; height: 80px"
          :src="img.thumbnail"
          fit="contain"
        />
      </div>
    </div>
    <div
      v-if="isShowAction"
      ref="modelTool" class="model-action"
    >
      <div class="action-item clearfix">
        <span class="action-label">绕X轴旋转</span>
        <el-slider
          id="pitch"
          v-model="xRotate"
          class="action-slider"
          :min="0"
          :max="360"
          :step="1"
          @input="handleRotate('x')"
        />
      </div>
      <div class="action-item clearfix">
        <span class="action-label">绕Y轴旋转</span>
        <el-slider
          v-model="yRotate"
          class="action-slider"
          :min="0"
          :max="360"
          :step="1"
          @input="handleRotate('y')"
        />
      </div>
      <div class="action-item clearfix">
        <span class="action-label">绕Z轴旋转</span>
        <el-slider
          v-model="zRotate"
          class="action-slider"
          :min="0"
          :max="360"
          :step="1"
          @input="handleRotate('z')"
        />
      </div>
    </div>
  </div>
</template>

<script setup name="Model">
import s3mModels from "./ModelJSON/models.json";
import useMapViewStore from "@/store/modules/map/mapView.js"
import { setCursor } from "@/utils/Cesium/CesiumTool.js"

defineProps({
  isShow: {
    type: Boolean,
    default: true
  },
  headerInfo: {
    type: Object
  }
})

const modelImages = ref([])
const isShowAction = ref(false)
// x、y、z轴旋转值
const xRotate = ref( 0)
const yRotate = ref(0)
const zRotate = ref(0)
// x、y、z轴移动值
const xMove = ref(0)
const yMove = ref(0)
const zMove = ref(0)
const viewModel = ref({
  heading: 1.0,
  pitch: 1.0,
  roll: 1.0,
  scale: 1.0,
  material: "#ffffff"
})

const viewer3d = computed(()=>useMapViewStore().viewer3d)

const emits = defineEmits(['closePanel'])

const closePanel = ()=>{
  emits('closePanel')
}

/**
 * 生成模型图片
 */
const generateImages = ()=> {
  const serviceUrl = import.meta.env.VITE_APP_MODEL_URL
  modelImages.value = s3mModels.map((item) => {
    const imgObj = {
      name: item.name,
      thumbnail: serviceUrl + item.thumbnail,
      path: serviceUrl + item.path
    };
    return imgObj;
  })
}
/**
 * 添加模型工具到视图对象
 * @param modelUrl
 */
const addModel2Viewer = (modelUrl)=> {
  initModel(viewer3d.value.scene, modelUrl, viewer3d.value)
  isShowAction.value = true
}

/**
 * 初始化模型数据
 */
const initModel = (scene, modelUrl, viewer3d)=> {
  const tooltip = this.createTooltip(document.body);
  const handlerPoint = new Cesium.DrawHandler(
    this.viewer3d,
    Cesium.DrawMode.Point
  );
  handlerPoint && handlerPoint.activate();
  handlerPoint.activeEvt.addEventListener((isActive) => {
    if (isActive) {
      setCursor(this.viewer3d, "crosshair");
    } else {
      setCursor(this.viewer3d, "pointer");
    }
  });
  handlerPoint.movingEvt.addEventListener(function(windowPosition) {
    if (windowPosition.x < 210 && windowPosition.y < 120) {
      tooltip.setVisible(false);
      return;
    }
    tooltip.showAt(windowPosition, "<p>点击添加模型</p>");
  });
  const s3mInstanceColc = new Cesium.S3MInstanceCollection(
    scene._context
  );
  scene.primitives.add(s3mInstanceColc);
  handlerPoint.drawEvt.addEventListener(function(result) {
    handlerPoint.clear();
    const point = result.object;
    const color = Cesium.Color.WHITE;
    s3mInstanceColc.add(modelUrl, {
      position: point.position,
      hpr: new Cesium.HeadingPitchRoll(0, 0, 0),
      scale: new Cesium.Cartesian3(1, 1, 1),
      color: color
    });
    handlerPoint && handlerPoint.deactivate();
    tooltip.setVisible(false);
  });
}
/**
 * 模型旋转
 */
const handleRotate = (value)=> {
  if (value === "x") {
    const rotationValue = Cesium.Math.toRadians(xRotate.value);
    if (viewer3d.value.selectedEntity) {
      const instance = this.viewer3d.selectedEntity.primitive;
      const index = this.viewer3d.selectedEntity.id;
      instance.updateRotation(
        new Cesium.HeadingPitchRoll(0, 0, rotationValue),
        index
      );
    }
  } else if (value === "y") {
    const rotationValue = Cesium.Math.toRadians(yRotate.value);
    if (viewer3d.value.selectedEntity) {
      const instance = this.viewer3d.selectedEntity.primitive;
      const index = this.viewer3d.selectedEntity.id;
      instance.updateRotation(
        new Cesium.HeadingPitchRoll(0, rotationValue, 0),
        index
      );
    }
  } else {
    const rotationValue = Cesium.Math.toRadians(zRotate.value);
    if (viewer3d.value.selectedEntity) {
      const instance = this.viewer3d.selectedEntity.primitive;
      const index = this.viewer3d.selectedEntity.id;
      instance.updateRotation(
        new Cesium.HeadingPitchRoll(rotationValue, 0, 0),
        index
      );
    }
  }
}
/**
 * 创建提示工具
 */
const createTooltip = (frameDiv)=> {
  var tooltip = function(frameDiv) {
    var div = document.createElement("DIV");
    div.className = "twipsy right";

    var arrow = document.createElement("DIV");
    arrow.className = "twipsy-arrow";
    div.appendChild(arrow);

    var title = document.createElement("DIV");
    title.className = "twipsy-inner";
    div.appendChild(title);

    this._div = div;
    this._title = title;
    this.message = "";

    // add to frame div and display coordinates
    frameDiv.appendChild(div);
    var that = this;
    div.onmousemove = function(evt) {
      that.showAt({ x: evt.clientX, y: evt.clientY }, that.message);
    };
  };

  tooltip.prototype.setVisible = function(visible) {
    this._div.style.display = visible ? "block" : "none";
  };

  tooltip.prototype.showAt = function(position, message) {
    if (position && message) {
      this.setVisible(true);
      this._title.innerHTML = message;
      this._div.style.left = position.x + 10 + "px";
      this._div.style.top = position.y - this._div.clientHeight / 2 + "px";
      this.message = message;
    }
  };

  return new tooltip(frameDiv);
}

onBeforeMount(()=>{
  generateImages()
})
</script>

<style scoped lang="scss">
.tool-wrap {
  color: #fff;
  background-image: url("@/assets/images/map/tool.png");
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-size: cover;
  box-shadow: 0 0 8px 0 #057595;
}
.scene-container {
  max-height: 40vh;
  overflow-y: scroll;
}
.model-image {
  position: relative;
  float: left;
  margin: 10px;
  border: 1px solid #ccc;
  border-radius: 5px;
  .el-image {
    margin-top: 40px;
    padding: 5px;
  }
  &:hover {
    cursor: pointer;
    background-color: #496993a6;
  }
}
.img-title {
  position: absolute;
  background: #00000036;
  display: block;
  width: 100%;
  text-align: center;
  padding: 5px;
  font-size: 14px;
  z-index: 99;
}
.model-action {
  position: absolute;
  padding: 10px;
  top: 50px;
  left: -270px;
  width: 80%;
  background-image: url("@/assets/images/map/tool.png");
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-size: cover;
  border-radius: 5px;
}
.action-item {
  line-height: 40px;
}
.action-slider {
  float: right;
  width: 70%;
}
.action-move {
  margin: 5px 0;
}
</style>
