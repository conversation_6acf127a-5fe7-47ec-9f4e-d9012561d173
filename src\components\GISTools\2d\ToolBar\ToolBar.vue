<template>
  <div class="tool-bar-wrap">
    <div class="tool-bar-container">
      <button
        v-for="(option,index) in options"
        :key="index"
        class="tool-bar-item"
        :class="{active:option.isActive}"
        @click="toogleBar(option)"
        @mouseover="toolTip(option.name,$event)"
        @mouseleave="showTip=false"
      >
        <svg-icon :icon-class="option.iconClass" />
      </button>
    </div>
    <transition name="text-animate">
      <div
        v-show="showTip"
        ref="textArea" class="text-tip"
      >
        <p class="text-span">{{ textContent }}</p>
      </div>
    </transition>
    <AttrQuery
      v-if="attrQueryVisible"
      ref="attrQuery"
    />
    <MeasureDistance
      v-if="showLengthComp"
      :distance="distance" class="measure-container-no-content"
    />
    <MeasureArea
      v-if="showAreaComp"
      class="measure-container-no-content" :area="area"
    />
    <!--    <SplitScreen v-if="splitScreenVisible" class="split-container" />-->
  </div>
</template>

<script setup name="真正的工具条">
import eventBus from '@/utils/eventBus.js'
import OlDraw from '@/utils/OpenLayers/olDraw'
import { removeAllLayer, setCursor } from '@/utils/OpenLayers/olTool'
import { getDistance, PlanarArea } from "@/utils/mapFunction/geometryTool";
import GeoJSON from "ol/format/GeoJSON";
import MeasureArea from '@/components/GISTools/2d/Measure/MeasureArea'
import MeasureDistance from "@/components/GISTools/2d/Measure/MeasureDistance";
import AttrQuery from "@/components/GISTools/2d/MapQuery/AttrQuery.vue";
import useMapViewStore from "@/store/modules/map/mapView.js"

const { proxy } = getCurrentInstance()

const area = ref(0)
const distance = ref(0)
const showAreaComp = ref(false)
const showLengthComp = ref(false)
const olDraw = shallowRef(undefined)
const textContent = ref("")//文本提示内容
const showTip = ref(false)
const targetEle = ref('')
const attrQueryVisible = ref(false)
const options = reactive([
  {
    name: "属性查询",
    isActive: false,
    iconClass: "attribute",
    funcName: "attributeQuery"
  },
  {
    name: "长度测量",
    isActive: false,
    iconClass: "meter",
    funcName: "measureLength"
  },
  {
    name: "面积测量",
    isActive: false,
    iconClass: "mj",
    funcName: "measureArea"
  },
  {
    name: "绘制点",
    isActive: false,
    iconClass: "spot",
    funcName: "drawPoint"
  },
  {
    name: "绘制线",
    isActive: false,
    iconClass: "lines",
    funcName: "drawPolyline"
  },
  {
    name: "绘制多边形",
    isActive: false,
    iconClass: "polygon",
    funcName: "drawPolygon"
  },
  {
    name: "绘制矩形",
    isActive: false,
    iconClass: "jux",
    funcName: "drawRectangular"
  },
  {
    name: "清除",
    isActive: false,
    iconClass: "clear",
    funcName: "clearAll"
  }
])

const map = computed(() => useMapViewStore().map)
const view = computed(() => useMapViewStore().viewer2d)

olDraw.value = new OlDraw(map.value, view.value)
// debugger
// watch(map.value,()=>{
//   olDraw.value = new OlDraw(map.value, view.value)
// })

watch(showTip, (value) => {
  nextTick(() => {
    const textEle = proxy.$refs.textArea
    const boolValue = Boolean(!value || !textEle || !targetEle.value)
    if (boolValue) {
      return
    }
    // 获取元素的绝对位置
    const target = targetEle.value.target
    const left = target.offsetLeft - (textEle.offsetWidth - target.offsetWidth) / 2 + "px"
    const top = (target.offsetTop + 50) + "px"

    textEle.style.left = left
    textEle.style.top = top
  })
})
watch(distance, (value) => {
  showLengthComp.value = Boolean(value)
  showAreaComp.value = false
  attrQueryVisible.value = false
})
watch(area, (value) => {
  showAreaComp.value = Boolean(value)
  showLengthComp.value = false
  attrQueryVisible.value = false
})

const emits = defineEmits(["showImageComp"])

/**
 * 按钮事件函数
 */
const toogleBar = (option) => {
  options.forEach(o => {
    o.isActive = false
  })
  const target = options.find(o => o.name === option.name)
  const isActive = target.isActive = !target.isActive
  showAreaComp.value = showLengthComp.value = attrQueryVisible.value = false
  switch (option.funcName) {
    case "imageSeries":
      imageSeries(isActive)
      emits("showImageComp", isActive)
      break
    case "attributeQuery":
      attributeQuery(isActive)
      break
    case "measureLength":
      measureLength(isActive)
      break
    case "measureArea":
      measureArea(isActive)
      break
    case "drawPoint":
      drawPoint(isActive)
      break
    case "drawPolyline":
      drawPolyline(isActive)
      break
    case "drawPolygon":
      drawPolygon(isActive)
      break
    case "drawRectangular":
      drawRectangular(isActive)
      break
    case "clearAll":
      clearAll()
      break
  }
}

/**
 * @Description:文本提示
 * @param:
 **/
const toolTip = (text, $event) => {
  if ($event.target.tagName !== "BUTTON") {
    return
  }
  textContent.value = text
  targetEle.value = $event
  showTip.value = true
}

/**
 * 影像时间序列
 */
const imageSeries = (isActive) => {
  clearGeometry(isActive)
  setCursor('pointer')
}

/**
 * 属性查询
 */
const attributeQuery = (isActive) => {
  clearGeometry(isActive)
  setCursor('help')
  attrQueryVisible.value = isActive
}

/**
 * 分屏对比
 */
const splitScreen = (isActive) => {
  clearGeometry(isActive)
  setCursor('pointer')
}

/**
 * 长度测量
 */
const measureLength = (isActive) => {
  distance.value = area.value = 0
  if (map.value) {
    clearGeometry(isActive)
    if (isActive) {
      olDraw.value.drawGeometry("LineString")
      const drawAction = olDraw.value.getDrawAction()
      drawAction.on('drawend', evt => {
        const geoJson = new GeoJSON()
        const geometry = geoJson.writeGeometryObject(evt.feature.getGeometry())
        const drawLength = getDistance(geometry.coordinates)
        distance.value = +(drawLength * 1e-3).toFixed(4)
      })
    }
  }
}

/**
 * 面积测量
 */
const measureArea = (isActive) => {
  area.value = distance.value = 0
  if (map.value) {
    clearGeometry(isActive)
    if (isActive) {
      olDraw.value.drawGeometry("Polygon")
      const drawAction = olDraw.value.getDrawAction()
      drawAction.on('drawend', evt => {
        const geoJson = new GeoJSON()
        // 字符串
        const geometry = geoJson.writeGeometryObject(evt.feature.getGeometry())
        const drawArea = PlanarArea(geometry.coordinates, "Polygon")
        area.value = +(drawArea * 1e-4).toFixed(4)
        console.log("测量面积：", area.value);
      })
    }
  }
}

/**
 * 绘制点
 */
const drawPoint = (isActive) => {
  if (map.value) {
    clearGeometry(isActive)
    isActive ? olDraw.value.drawGeometry("Point") : ''
  }
}

/**
 * 绘制线
 */
const drawPolyline = (isActive) => {
  if (map.value) {
    clearGeometry(isActive)
    isActive ? olDraw.value.drawGeometry("LineString") : ''
  }
}

/**
 * 绘制多边形
 */
const drawPolygon = (isActive) => {
  if (map.value) {
    // 每次都制造一个新的对象，你怎么可能获取得到呢？
    clearGeometry(isActive)
    isActive ? olDraw.value.drawGeometry("Polygon") : ''
  }
}

/**
 * 绘制矩形
 */
const drawRectangular = (isActive) => {
  if (map.value) {
    clearGeometry(isActive)
    isActive ? olDraw.value.drawGeometry("Box") : ""
  }
}

/**
 * 清除绘制对象以及移除绘制控件
 * @param isActive
 */
const clearGeometry = (isActive) => {
  if (olDraw.value?.getDrawSource()) {
    // 结束上一次的绘制并清除绘制对象
    const drawSource = olDraw.value.getDrawSource()
    const drawAction = olDraw.value.getDrawAction()
    olDraw.value.clearGeometry(drawSource)
    olDraw.value.deactiveAction(drawAction)
  }
  console.log("drawAction:", olDraw.value.getDrawAction())
  // window._map.un('singleclick', popupListener)
  if (isActive) {
    setCursor('crosshair')
  } else {
    setCursor('default')
  }
}

/**
 * 清除所有绘制对象
 */
const clearAll = () => {
  clearGeometry(false)
  options.forEach(o => {
    o.isActive = false
  })
  removeAllLayer()
  showAreaComp.value = showLengthComp.value = attrQueryVisible.value = false
  eventBus.emit('showLoading', false)
  eventBus.emit('clearCoords')
}
</script>

<style scoped lang="scss">
.tool-bar-wrap {
  position: absolute;
  z-index: 555;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
}

.tool-bar-container {
  background-color: #104f9fd1;
  transition: all .25s;
  border: 1px solid #f0f4f76b;
  border-radius: 3.5px;
  box-shadow: 1px 0px 6px 0px #d9d9d94f;
}

.tool-bar-item {
  width: 40px;
  height: 40px;
  color: #ffff;
  cursor: pointer;
  border-right: 1px solid #f0f4f76b;

  &:hover {
    background-color: #248cfba8;
    transition-delay: .25s;
  }

  &:last-child {
    border-right: none;
  }

  .svg-icon {
    //width: 1.5em;
    //height: 1.5em;
  }
}

.active {
  background: #248cfba8;
}

.text-tip {
  position: absolute;
  padding: 5px 10px;
  background-color: #0f1325;
  color: #fff;
  text-align: center;
  border-radius: 2.5px;
  min-width: 68px;
}

.text-span {
  display: inline-block;
  margin: 0 5px;
  min-width: 80px;
}

.text-span:before {
  position: absolute;
  content: "";
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 10px solid #0f1325;;
  left: 50%;
  transform: translateX(-50%);
  top: -8px;
}

/**开始进入时，顶部动画**/
.text-animate-enter, .text-animate-leave {
  opacity: 1;
  transition: all .1s ease-in-out;
}

.text-animate-enter-active, .text-animate-leave-active {
  opacity: 0;
  transition: all .25s ease-in-out;
}

.measure-div {
  position: absolute;
  top: 550px;
  color: #fff;
  background: #031f30d1;
  z-index: 999;
  font-size: 16px;
  font-weight: bold;
}

.measure-container {
  bottom: 10%;
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  width: 250px;
}

.measure-container-no-content {
  bottom: 10%;
  color: #fff;
  background: #104f9fc9;
  font-size: 16px;
  font-weight: bold;
}

.split-container {
  bottom: 10%;
  color: #fff;
  background: #43709f87;
  font-size: 16px;
  font-weight: bold;
}
</style>
