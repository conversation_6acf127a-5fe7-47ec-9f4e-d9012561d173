<template>
  <div class="container">
    <div class="btn-submit">
      <el-button v-if="linkTitle" @click="back">返回</el-button>
      <el-button>打印</el-button>
      <el-button type="primary" v-loading="showLoding" @click="onSubmit">发送</el-button>
    </div>
    <el-row :gutter="20">
      <el-col :span="18" class="card form" v-loading="showLoding">
        <div class="form-content">
          <h1 style="text-align: center">昆明市土地矿产储备中心发文审签表</h1>
          <el-form ref="receiptFormRef" :model="form" :rules="rules" label-width="80px">
            <div class="flx-justify-between">
              <el-form-item label="案卷号" prop="num">
                <el-input v-model="form.num" placeholder="请输入案卷号" />
              </el-form-item>
              <el-form-item label="文号" prop="docNum">
                <el-input v-model="form.docNum" placeholder="请输入文号" />
              </el-form-item>
            </div>
            <table>
              <tr>
                <td colspan="4">
                  <el-form-item label="发文标题" prop="title" label-width="150px" style="width: 100%">
                    <el-input :rows="2" type="textarea" v-model="form.title" placeholder="请输入标题" />
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td>
                  <el-form-item label="发文类型" prop="type" label-width="150px">
                    <el-select v-model="form.type" placeholder="发文类型" clearable>
                      <el-option v-for="dict in dicts.doc_send_type" :key="dict.dictValue" :label="dict.dictLabel"
                        :value="dict.dictValue" />
                    </el-select>
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="紧急程度" prop="urgentDegree" label-width="150px">
                    <el-select v-model="form.urgentDegree" placeholder="急 缓" clearable>
                      <el-option v-for="dict in dicts.doc_urgency" :key="dict.dictValue" :label="dict.dictLabel"
                        :value="dict.dictValue" />
                    </el-select>
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="文件密级" prop="secretLevel" label-width="150px">
                    <el-select v-model="form.secretLevel" placeholder="密 级" clearable>
                      <el-option v-for="dict in dicts.doc_secret" :key="dict.dictValue" :label="dict.dictLabel"
                        :value="dict.dictValue" />
                    </el-select>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td colspan="1">
                  <el-form-item label="是否有上行文" prop="isWriting" label-width="150px">
                    <el-select v-model="form.isWriting" placeholder="是否有上行文" clearable>
                      <el-option label="否" value="0" />
                      <el-option label="是" value="1" />
                    </el-select>
                  </el-form-item>
                </td>
                <td colspan="3">
                  <el-form-item label="政府信息公开属性" prop="infoAttribute" label-width="150px">
                    <el-select v-model="form.infoAttribute" placeholder="请选择政府信息公开属性" clearable>
                      <el-option v-for="dict in dicts.doc_attribute" :key="dict.dictValue" :label="dict.dictLabel"
                        :value="dict.dictValue" />
                    </el-select>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td colspan="4">
                  <el-form-item label="主送" prop="report" label-width="150px">
                    <el-input v-model="form.report" placeholder="请输入主送" />
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td colspan="4">
                  <el-form-item label="抄送" prop="transcribe" label-width="150px">
                    <el-input v-model="form.transcribe" placeholder="请输入主送" />
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td colspan="4">
                  <el-form-item label="备注" prop="remark" label-width="150px">
                    <el-input type="textarea" v-model="form.remark" placeholder="请输入备注" />
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td colspan="4">
                  <el-form-item label="关联标题" :prop="form.isWriting === '1' ? 'linkTitle' : ''" label-width="150px">
                    <div class="flx-center" style="width: 100%">
                      <el-input v-model="form.linkTitle" disabled placeholder="请输入关联文号" style="flex: 1" />
                      <el-button v-if="!linkTitle" class="ml5" type="primary"
                        @click="showSelectRecipt = true">关联收文</el-button>
                    </div>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td colspan="4">
                  <el-form-item label="关联文号" prop="linkNum" label-width="150px" style="width: 100%">
                    <el-input v-model="form.linkNum" disabled placeholder="请输入关联文号" />
                  </el-form-item>
                </td>
              </tr>
              <tr v-for="(item, index) in tmpJson" :key="index">
                <td v-if="item.docData === '1'" colspan="4">
                  <el-form-item :label="item.nodeName" label-width="150px">
                    <el-input :disabled="true" type="textarea" v-model="item.data[0].auditIdea" placeholder="请输入意见" />
                    <el-form-item>
                      <el-form-item label="签字">
                        <el-input :disabled="true" type="textarea" v-model="item.data[0].auditSign"
                          placeholder="请输入备注" />
                      </el-form-item>
                      <el-form-item label="日期">
                        <el-date-picker :disabled="true" clearable v-model="item.data[0].auditTime" type="datetime"
                          value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择日期">
                        </el-date-picker>
                      </el-form-item>
                    </el-form-item>
                  </el-form-item>
                </td>
                <td v-if="item.docData === '2'" colspan="4">
                  <div class="el-form-item__label" style="width: 150px; border: 0px">
                    {{ item.nodeName }}
                  </div>
                  <el-table :data="item.data" border style="width: 100%" height="200">
                    <el-table-column prop="auditIdea" label="意见" />
                    <el-table-column prop="auditSign" label="签字" width="180" />
                    <el-table-column prop="auditTime" label="日期" width="180" />
                  </el-table>
                </td>
              </tr>
              <tr>
                <td style="text-align: center">拟稿</td>
                <td style="text-align: center">核稿</td>
                <td style="text-align: center">校对</td>
              </tr>
              <tr>
                <td colspan="3">
                  <el-form-item label="拟稿处室" prop="draftDept" label-width="150px" style="width: 100%">
                    <el-input v-model="form.draftDept" type="textarea" disabled />
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td>
                  <el-form-item label="拟稿人" prop="draftUser" label-width="150px" style="width: 100%">
                    <el-input v-model="form.draftUser" disabled type="textarea" />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="核稿人" prop="reviewUser" label-width="150px" style="width: 100%">
                    <el-input v-model="form.reviewUser" disabled type="textarea" />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="校对人" prop="checkUser" label-width="150px" style="width: 100%">
                    <el-input v-model="form.checkUser" disabled type="textarea" />
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td>
                  <el-form-item label="拟稿日期" prop="draftDate" label-width="150px" style="width: 100%">
                    <el-input v-model="form.draftDate" disabled type="textarea" />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="核稿日期" prop="reviewDate" label-width="150px" style="width: 100%">
                    <el-input v-model="form.reviewDate" disabled type="textarea" />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="校对日期" prop="checkDate" label-width="150px" style="width: 100%">
                    <el-input v-model="form.checkDate" disabled type="textarea" />
                  </el-form-item>
                </td>
              </tr>
              <tr v-for="(item, index) in tmpJson1" :key="index">
                <td v-if="item.docData === '1'" colspan="4">
                  <el-form-item :label="item.nodeName" label-width="150px">
                    <el-input :disabled="true" type="textarea" v-model="item.data[0].auditIdea" placeholder="请输入意见" />
                    <el-form-item>
                      <el-form-item label="签字">
                        <el-input :disabled="true" type="textarea" v-model="item.data[0].auditSign"
                          placeholder="请输入备注" />
                      </el-form-item>
                      <el-form-item label="日期">
                        <el-date-picker :disabled="true" clearable v-model="item.data[0].auditTime" type="datetime"
                          value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择日期">
                        </el-date-picker>
                      </el-form-item>
                    </el-form-item>
                  </el-form-item>
                </td>
                <td v-if="item.docData === '2'" colspan="4">
                  <div class="el-form-item__label" style="width: 150px; border: 0px">
                    {{ item.nodeName }}
                  </div>
                  <el-table :data="item.data" border style="width: 100%" height="200">
                    <el-table-column prop="auditIdea" label="意见" />
                    <el-table-column prop="auditSign" label="签字" width="180" />
                    <el-table-column prop="auditTime" label="日期" width="180" />
                  </el-table>
                </td>
              </tr>
            </table>

          </el-form>
        </div>
      </el-col>
      <el-col :span="6" v-loading="showLoding">
        <div class="file-content card">
          <FileTree v-if="dicts.doc_send_file_type.length > 0" :key="formKey" :data="dicts.doc_send_file_type"
            :upload="true" @onSuccess="onUploadSuccess" @onRemove="onUploadRemove" />
        </div>
      </el-col>
    </el-row>
    <component v-if="showSelectRecipt" :is="selectRecipt" @close="handleClose" @selectReciptData="handleData" />
  </div>
</template>

<script setup>
import FileTree from "@/components/FileTree";
import { onMounted, ref } from "vue";
import { getDicts } from "@/api/system/dict/data";
import { getTmpJson } from "@/api/document/common";
import { addSend } from "@/api/document/send";
import { startWorkFlow } from "@/api/document/common";
import useUserStore from "@/store/modules/user";
import { formatDay } from "@/utils/index";
import { ElMessage } from "element-plus";
const { proxy } = getCurrentInstance();

const emit = defineEmits()
const props = defineProps({
  linkTitle: {
    type: String,
  },
  linkNum: {
    type: String,
  },

});
const back = () => {
  emit('back')
}
const userStore = useUserStore();
const formKey = ref(new Date().getTime());
const showLoding = ref(false);

//需要请求的字典类型
const dictsList = [
  "doc_secret",
  "doc_urgency",
  "doc_send_file_type",
  "doc_attribute",
  "doc_send_type",
];
const dicts = ref({});
dictsList.map((item) => (dicts.value[item] = []));

const receiptFormRef = ref(null);
const initFormData = {
  num: "",
  docNum: "",
  title: "",
  type: "",
  urgentDegree: "",
  secretLevel: "0",
  printNumber: 0,
  infoAttribute: "",
  report: "",
  transcribe: "",
  isWriting: "",
  linkTitle: "",
  linkNum: "",
  isOut: "",
  nodeId: "",
  status: "",
  flag: "",
  remark: "",
  affixList: [],
  draftDept: "",
  draftUser: "",
  draftDate: "",
  reviewUser: "",
  reviewDate: "",
  checkUser: "",
  checkDate: "",
};
const data = reactive({
  form: { ...initFormData },
  rules: {
    title: [{ required: true, message: "发文标题不能为空", trigger: "blur" }],
    secretLevel: [{ required: true, message: "密级不能为空", trigger: "blur" }],
    isWriting: [
      { required: true, message: "是否有上行文不能为空", trigger: "blur" },
    ],
    report: [{ required: true, message: "主送不能为空", trigger: "blur" }],
    linkTitle: [
      {
        required: true,
        message: "存在上行文时关联标题不能为空",
        trigger: "blur",
      },
    ],
  },
});

const { form, rules } = toRefs(data);
const tmpJson = ref([]);
const tmpJson1 = ref([]);

//上传成功
function onUploadSuccess(fileList) {
  form.value.affixList.push(fileList);
}
//删除文件
function onUploadRemove(file) {
  form.value.affixList = form.value.affixList.filter(
    (item) => item.name !== file.response.data.url
  );
}
function onSubmit() {
  if (!form.value.affixList.length) {
    proxy.$modal.msgError("请上传附件");
    return
  }
  receiptFormRef.value.validate((valid) => {
    if (valid) {
      showLoding.value = true;
      addSend(form.value).then((res) => {
        let data = {
          businessKey: res.data.id,
          tableName: "doc_send",
          variables: {
            entity: res.data,
          },
        };
        startWorkFlow(data).then(() => {
          proxy.$modal.msgSuccess("提交成功");
          receiptFormRef.value.resetFields();
          formKey.value = new Date().getTime();
          showLoding.value = false;
          if (props.linkTitle) {
            back()
          }
        });
      });
    } else {
      ElMessage({
        message: "请先完善表单信息",
        type: "error",
        plain: true,
      });
    }
  });
}

//关联收文(异步组件)
const showSelectRecipt = ref(false);
const selectRecipt = defineAsyncComponent(() =>
  import("./components/SelectRecipt")
);
const handleClose = (msg) => {
  showSelectRecipt.value = msg;
};
const handleData = (msg) => {
  showSelectRecipt.value = msg.close;
  form.value.linkTitle = msg.list[0].title;
  form.value.linkNum = msg.list[0].num;
};

onMounted(() => {
  form.value.linkTitle = props.linkTitle || "";
  form.value.linkNum = props.linkNum || "";
  dictsList.map((item) => {
    getDicts(item).then((res) => {
      dicts.value[item] = res.data;
    });
  });
  getTmpJson({ type: "3" }).then((res) => {
    let index = res.data.findIndex(
      (item) => item.nodeName === "办公室审核意见"
    );
    tmpJson.value = res.data.slice(0, index + 1);
    tmpJson1.value = res.data.slice(index + 1);
  });
  form.value.draftDept = userStore.user.deptName;
  form.value.draftUser = userStore.user.nickName;
  form.value.draftDate = formatDay(new Date());
});
</script>

<style scoped lang="scss">
@import "@/styles/variables.module.scss";;

.container {
  padding: 10px 20px;
  background-color: #e9eef3;
  height: $contentHeight;
  overflow-x: auto;
}

.form {
  display: flex;
  justify-content: center;
  background: #dce2f1;
}

.form-content {
  width: 770px;
}

table {
  width: 100%;
  border: 1px solid #e2e2e2;
  border-collapse: collapse;

  :deep(.el-form-item) {
    margin-bottom: 0;
  }

  :deep(.el-form-item__label) {
    justify-content: center;
    border-right: 1px solid #e2e2e2;
    margin-right: 1px;
    height: auto;
    padding: 10px 10px;
  }

  :deep(.el-form-item__error) {
    position: static;
  }

  td {
    border: 1px solid #e2e2e2;
    background-color: rgb(246, 246, 246);
  }
}

.btn-submit {
  width: 100%;
  display: flex;
  margin-bottom: 5px;
}
</style>
