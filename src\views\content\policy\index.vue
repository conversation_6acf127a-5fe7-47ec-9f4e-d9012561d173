<template>
  <div>
    <div class="table-box" v-if="!addDialogShow">
      <ProTable
        ref="proTable"
        :columns="columns"
        :request-api="getTableList"
        :init-param="initParam"
        :data-callback="dataCallback"
      >
        <!-- 表格 header 按钮 -->
        <template #tableHeader="">
          <el-button
            v-if="auth.hasPermi('content:polisy:add')"
            type="primary"
            icon="CirclePlus"
            @click="addHadleNews"
            >新增</el-button
          >
        </template>
        <template #flag="scope">
          <el-tag type="primary" v-show="scope.scope.row.flag==='0'">政策</el-tag>
          <el-tag type="success" v-show="scope.scope.row.flag==='1'">法规</el-tag>
        </template>
        <template #operation="scope">
          <el-button
            link
            type="success"
            icon="View"
            @click="viewDetail(scope.scope.row)"
            >查看</el-button
          >
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="editDetail(scope.scope.row)"
            v-if="auth.hasPermi('content:polisy:edit')"
            >修改</el-button
          >
          <el-button
            link
            type="danger"
            icon="Delete"
            @click="delDetail(scope.scope.row)"
            v-if="auth.hasPermi('content:polisy:del')"
            >删除</el-button
          >
        </template>
      </ProTable>
    </div>
    <!-- 组件 -->
    <AddDialog
      v-if="addDialogShow"
      @close="handleClose"
      :id="detailId"
      :type="type"
    ></AddDialog>
  </div>
</template>

<script setup name="content-news">
import { ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import ProTable from "@/components/ProTable/index.vue";
import AddDialog from "./components/addDialog.vue";
import auth from "@/plugins/auth";
import { getPolicyList, dePolicy } from "@/api/content/policy.js";
import { useRoute } from "vue-router";

const proTable = ref();

const route = useRoute()

//

const flagType = [
  {label:'政策', value:'0'},
  {label:'法规', value:'1'}
]

const columns = reactive([
  { type: "index", label: "序号", width: 80 },
  {
    prop: "title",
    label: "标题",
    search: { el: "input" },
  },
  {
    prop: "flag",
    label: "类型",
    // search: { el: "input" },
    enum: flagType,
    search: { el: "select", props: { filterable: true } },
    fieldNames: { label: "label", value: "value" }
  },
  {
    prop: "docNum",
    label: "文号",
    search: { el: "input" },
  },
  {
    prop: "sendTime",
    label: "发布时间",
    search: {
      el: "date-picker",
      props: { type: "daterange", valueFormat: "YYYY-MM-DD" },
    },
  },
  { prop: "operation", label: "操作", fixed: "right", width: 280 },
]);

// 如果表格需要初始化请求参数，直接定义传给 ProTable (之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({
  // flag: route.query.flag?route.query.flag:''
});

// dataCallback 是对于返回的表格数据做处理，如果你后台返回的数据不是 list && total 这些字段，可以在这里进行处理成这些字段
// 或者直接去 hooks/useTable.ts 文件中把字段改为你后端对应的就行
const dataCallback = (data) => {
  return {
    list: data.list,
    total: data.total,
  };
};

const getTableList = (params) => {
  
  let newParams = JSON.parse(JSON.stringify(params));
  console.log('params',newParams);
  newParams.flag = route.query.flag? route.query.flag:newParams.flag
  // newParams.flag = route.query.flag?route.query.flag:newParams.flag
  newParams.sendTime && (newParams.startDay = newParams.sendTime[0]);
  newParams.sendTime && (newParams.endDay = newParams.sendTime[1]);
  delete newParams.sendTime;
  console.log("newParams", newParams);
  
  return getPolicyList(newParams);
};

//
const type = ref("add");
const detailId = ref("");
// 新增数据
const addDialogShow = ref(false);
const addHadleNews = () => {
  addDialogShow.value = true;
  detailId.value = ''
  type.value = ""
};
const handleClose = (e) => {
  addDialogShow.value = e;
  detailId.value = ''
  type.value = ""
};

//view
const viewDetail = (row) => {
  addDialogShow.value = true;
  (type.value = "view"), (detailId.value = row.id);
};
//view
const editDetail = (row) => {
  addDialogShow.value = true;
  (type.value = "edit"), (detailId.value = row.id);
};

//删除
const delDetail = (row) => {
  ElMessageBox.confirm(`您确定删除 ${row.title}?`, "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      dePolicy(row.id).then((res) => {
        if (res.code === 200) {
          ElMessage({
            type: "success",
            message: "删除成功",
          });
          proTable.value?.getTableList();
        } else {
          ElMessage({
            type: "danger",
            message: res.msg,
          });
        }
      });
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "操作已取消",
      });
    });
};

onMounted(() => {
  console.log('route', route.query.flag);
});
</script>

<style scoped lang="scss">
.content {
  padding: 15px;

  .el-row {
    margin-bottom: 15px;
  }
}
</style>