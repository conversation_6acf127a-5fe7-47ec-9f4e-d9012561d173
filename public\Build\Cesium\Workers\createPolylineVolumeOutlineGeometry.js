define(["./when-b60132fc","./Rectangle-dee65d21","./arrayRemoveDuplicates-d2f048c5","./BoundingRectangle-143a34da","./buildModuleUrl-9085faaa","./Cartesian2-db21342c","./Cartographic-3309dd0d","./ComponentDatatype-c140a87d","./PolylineVolumeGeometryLibrary-b276ee2b","./Check-7b2a090c","./GeometryAttribute-c65394ac","./GeometryAttributes-252e9929","./IndexDatatype-8a5eead4","./Math-119be1a3","./PolygonPipeline-d83979ed","./FeatureDetection-806b12f0","./Event-16a2dfbf","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Cartesian4-3ca25aab","./EllipsoidTangentPlane-1dfa0a87","./IntersectionTests-0d6905a3","./Plane-a3d8b3d2","./PolylinePipeline-a5200218","./EllipsoidGeodesic-139a7db9","./EllipsoidRhumbLine-30b5229b","./earcut-2.2.1-20c8012f"],(function(e,a,i,t,n,r,o,l,s,p,d,c,u,y,h,g,f,m,v,E,b,P,_,C,k,L,D){"use strict";function T(i){var t=(i=e.defaultValue(i,e.defaultValue.EMPTY_OBJECT)).polylinePositions,n=i.shapePositions;this._positions=t,this._shape=n,this._ellipsoid=a.Ellipsoid.clone(e.defaultValue(i.ellipsoid,a.Ellipsoid.WGS84)),this._cornerType=e.defaultValue(i.cornerType,s.CornerType.ROUNDED),this._granularity=e.defaultValue(i.granularity,y.CesiumMath.RADIANS_PER_DEGREE),this._workerName="createPolylineVolumeOutlineGeometry";var l=1+t.length*o.Cartesian3.packedLength;l+=1+n.length*r.Cartesian2.packedLength,this.packedLength=l+a.Ellipsoid.packedLength+2}T.pack=function(i,t,n){var l;n=e.defaultValue(n,0);var s=i._positions,p=s.length;for(t[n++]=p,l=0;l<p;++l,n+=o.Cartesian3.packedLength)o.Cartesian3.pack(s[l],t,n);var d=i._shape;for(p=d.length,t[n++]=p,l=0;l<p;++l,n+=r.Cartesian2.packedLength)r.Cartesian2.pack(d[l],t,n);return a.Ellipsoid.pack(i._ellipsoid,t,n),n+=a.Ellipsoid.packedLength,t[n++]=i._cornerType,t[n]=i._granularity,t};var G=a.Ellipsoid.clone(a.Ellipsoid.UNIT_SPHERE),R={polylinePositions:void 0,shapePositions:void 0,ellipsoid:G,height:void 0,cornerType:void 0,granularity:void 0};T.unpack=function(i,t,n){var l;t=e.defaultValue(t,0);var s=i[t++],p=new Array(s);for(l=0;l<s;++l,t+=o.Cartesian3.packedLength)p[l]=o.Cartesian3.unpack(i,t);s=i[t++];var d=new Array(s);for(l=0;l<s;++l,t+=r.Cartesian2.packedLength)d[l]=r.Cartesian2.unpack(i,t);var c=a.Ellipsoid.unpack(i,t,G);t+=a.Ellipsoid.packedLength;var u=i[t++],y=i[t];return e.defined(n)?(n._positions=p,n._shape=d,n._ellipsoid=a.Ellipsoid.clone(c,n._ellipsoid),n._cornerType=u,n._granularity=y,n):(R.polylinePositions=p,R.shapePositions=d,R.cornerType=u,R.granularity=y,new T(R))};var V=new t.BoundingRectangle;return T.createGeometry=function(e){var a=e._positions,r=i.arrayRemoveDuplicates(a,o.Cartesian3.equalsEpsilon),p=e._shape;if(p=s.PolylineVolumeGeometryLibrary.removeDuplicatesFromShape(p),!(r.length<2||p.length<3)){h.PolygonPipeline.computeWindingOrder2D(p)===h.WindingOrder.CLOCKWISE&&p.reverse();var y=t.BoundingRectangle.fromPoints(p,V);return function(e,a){var i=new c.GeometryAttributes;i.position=new d.GeometryAttribute({componentDatatype:l.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:e});var t,r,o=a.length,s=i.position.values.length/3,p=e.length/3/o,y=u.IndexDatatype.createTypedArray(s,2*o*(p+1)),h=0,f=(t=0)*o;for(r=0;r<o-1;r++)y[h++]=r+f,y[h++]=r+f+1;for(y[h++]=o-1+f,y[h++]=f,f=(t=p-1)*o,r=0;r<o-1;r++)y[h++]=r+f,y[h++]=r+f+1;for(y[h++]=o-1+f,y[h++]=f,t=0;t<p-1;t++){var m=o*t,v=m+o;for(r=0;r<o;r++)y[h++]=r+m,y[h++]=r+v}return new d.Geometry({attributes:i,indices:u.IndexDatatype.createTypedArray(s,y),boundingSphere:n.BoundingSphere.fromVertices(e),primitiveType:g.PrimitiveType.LINES})}(s.PolylineVolumeGeometryLibrary.computePositions(r,p,y,e,!1),p)}},function(i,t){return e.defined(t)&&(i=T.unpack(i,t)),i._ellipsoid=a.Ellipsoid.clone(i._ellipsoid),T.createGeometry(i)}}));
