<template>
  <div class="main-content">
    <transition>
      <div
        v-show="showSearch"
        class="mb-[10px]"
      >
        <el-card shadow="hover">
          <el-form
            ref="queryFormRef"
            :model="queryParams" :inline="true"
          >
            <el-form-item
              label="合同编号"
              prop="htbh"
            >
              <el-input
                v-model="queryParams.htbh"
                placeholder="请输入合同编号"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item
              label="乙方单位"
              prop="yfdw"
            >
              <el-input
                v-model="queryParams.yfdw"
                placeholder="请输入乙方单位"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item
              label="是否已收回"
              prop="sfysh"
            >
              <el-input
                v-model="queryParams.sfysh"
                placeholder="请输入是否已收回"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                icon="Search" @click="handleQuery"
              >搜索</el-button>
              <el-button
                icon="Refresh"
                @click="resetQuery"
              >重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card class="result-wrap">
      <!--      <template #header>-->
      <!--        <el-row :gutter="10" class="mb8">-->
      <!--          <el-col :span="1.5">-->
      <!--            <el-button-->
      <!--              type="primary"-->
      <!--              plain-->
      <!--              icon="Plus"-->
      <!--              @click="handleAdd"-->
      <!--              v-hasPermi="['patrol:temporaryManage:add']"-->
      <!--              >新增</el-button-->
      <!--            >-->
      <!--          </el-col>-->
      <!--          <el-col :span="1.5">-->
      <!--            <el-button-->
      <!--              type="success"-->
      <!--              plain-->
      <!--              icon="Edit"-->
      <!--              :disabled="single"-->
      <!--              @click="handleUpdate()"-->
      <!--              v-hasPermi="['patrol:temporaryManage:edit']"-->
      <!--              >修改</el-button-->
      <!--            >-->
      <!--          </el-col>-->
      <!--          <el-col :span="1.5">-->
      <!--            <el-button-->
      <!--              type="danger"-->
      <!--              plain-->
      <!--              icon="Delete"-->
      <!--              :disabled="multiple"-->
      <!--              @click="handleDelete()"-->
      <!--              v-hasPermi="['patrol:temporaryManage:remove']"-->
      <!--              >删除</el-button-->
      <!--            >-->
      <!--          </el-col>-->
      <!--          <el-col :span="1.5">-->
      <!--            <el-button-->
      <!--              type="warning"-->
      <!--              plain-->
      <!--              icon="Download"-->
      <!--              @click="handleExport"-->
      <!--              v-hasPermi="['patrol:temporaryManage:export']"-->
      <!--              >导出</el-button-->
      <!--            >-->
      <!--          </el-col>-->
      <!--          <right-toolbar-->
      <!--            v-model:showSearch="showSearch"-->
      <!--            @queryTable="getList"-->
      <!--          ></right-toolbar>-->
      <!--        </el-row>-->
      <!--      </template>-->

      <el-table
        v-loading="loading"
        :data="temporaryManageList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="55" align="center"
        />
        <el-table-column
          label="临时利用合同编号"
          align="center" prop="htbh"
        />
        <el-table-column
          label="乙方单位"
          align="center" prop="yfdw"
        />
        <el-table-column
          label="临时利用期限起"
          align="center"
          prop="lslyqxq"
          width="180"
        >
          <template #default="scope">
            <span>{{ parseTime(scope.row.lslyqxq, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="临时利用期限止"
          align="center"
          prop="lslyqxz"
          width="180"
        >
          <template #default="scope">
            <span>{{ parseTime(scope.row.lslyqxz, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="合同金额"
          align="center" prop="htje"
        />
        <el-table-column
          label="临时利用用途"
          align="center" prop="lslyyt"
        />
        <el-table-column
          label="是否已收回"
          align="center" prop="sfysh"
        />
        <el-table-column
          label="土地临时利用成本"
          align="center"
          prop="tdlslycb"
        />
        <el-table-column
          label="备注"
          align="center" prop="bz"
        />
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-tooltip
              content="修改"
              placement="top"
            >
              <el-button
                link
                type="primary"
                icon="Edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['patrol:temporaryManage:edit']"
              />
            </el-tooltip>
            <el-tooltip
              content="删除"
              placement="top"
            >
              <el-button
                link
                type="primary"
                icon="Delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['patrol:temporaryManage:remove']"
              />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
    <!-- 添加或修改临时管护对话框 -->
    <el-dialog
      :title="dialog.title"
      v-model="dialog.visible"
      width="500px"
      append-to-body
    >
      <el-form
        ref="temporaryManageFormRef"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item
          label="地块id"
          prop="dkId"
        >
          <el-input
            v-model="form.dkId"
            placeholder="请输入地块id"
          />
        </el-form-item>
        <el-form-item
          label="临时利用合同编号"
          prop="htbh"
        >
          <el-input
            v-model="form.htbh"
            placeholder="请输入临时利用合同编号"
          />
        </el-form-item>
        <el-form-item
          label="乙方单位"
          prop="yfdw"
        >
          <el-input
            v-model="form.yfdw"
            placeholder="请输入乙方单位"
          />
        </el-form-item>
        <el-form-item
          label="临时利用期限起"
          prop="lslyqxq"
        >
          <el-date-picker
            clearable
            v-model="form.lslyqxq"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择临时利用期限起"
          />
        </el-form-item>
        <el-form-item
          label="临时利用期限止"
          prop="lslyqxz"
        >
          <el-date-picker
            clearable
            v-model="form.lslyqxz"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择临时利用期限止"
          />
        </el-form-item>
        <el-form-item
          label="合同金额"
          prop="htje"
        >
          <el-input
            v-model="form.htje"
            placeholder="请输入合同金额"
          />
        </el-form-item>
        <el-form-item
          label="临时利用用途"
          prop="lslyyt"
        >
          <el-input
            v-model="form.lslyyt"
            placeholder="请输入临时利用用途"
          />
        </el-form-item>
        <el-form-item
          label="是否已收回"
          prop="sfysh"
        >
          <el-input
            v-model="form.sfysh"
            placeholder="请输入是否已收回"
          />
        </el-form-item>
        <el-form-item
          label="土地临时利用成本"
          prop="tdlslycb"
        >
          <el-input
            v-model="form.tdlslycb"
            placeholder="请输入土地临时利用成本"
          />
        </el-form-item>
        <el-form-item
          label="备注"
          prop="bz"
        >
          <el-input
            v-model="form.bz"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            :loading="buttonLoading"
            type="primary" @click="submitForm"
          >确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="TemporaryManage">
import {
  listTemporaryManage,
  getTemporaryManage,
  delTemporaryManage,
  addTemporaryManage,
  updateTemporaryManage
} from "@/api/patrol/tempMaintenance.js";

const { proxy } = getCurrentInstance();

const temporaryManageList = ref([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref();
const temporaryManageFormRef = ref();

const dialog = reactive({
  visible: false,
  title: ""
});

const initFormData = {
  id: undefined,
  dkId: undefined,
  htbh: undefined,
  yfdw: undefined,
  lslyqxq: undefined,
  lslyqxz: undefined,
  htje: undefined,
  lslyyt: undefined,
  sfysh: undefined,
  tdlslycb: undefined,
  bz: undefined
};
const data = reactive({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    dkId: undefined,
    htbh: undefined,
    yfdw: undefined,
    lslyqxq: undefined,
    lslyqxz: undefined,
    htje: undefined,
    lslyyt: undefined,
    sfysh: undefined,
    tdlslycb: undefined,
    bz: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: "$comment不能为空", trigger: "blur" }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询临时管护列表 */
const getList = async () => {
  loading.value = true;
  const res = await listTemporaryManage(queryParams.value);
  temporaryManageList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  temporaryManageFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加临时管护";
};

/** 修改按钮操作 */
const handleUpdate = async (row) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getTemporaryManage(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改临时管护";
};

/** 提交按钮 */
const submitForm = () => {
  temporaryManageFormRef.value?.validate(async (valid) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateTemporaryManage(form.value).finally(
          () => (buttonLoading.value = false)
        );
      } else {
        await addTemporaryManage(form.value).finally(
          () => (buttonLoading.value = false)
        );
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal
    .confirm('是否确认删除临时管护编号为"' + _ids + '"的数据项？')
    .finally(() => (loading.value = false));
  await delTemporaryManage(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    "patrol/temporaryManage/export",
    {
      ...queryParams.value
    },
    `temporaryManage_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
<style lang="scss">
.main-content {
  padding: 10px;
}
.result-wrap {
  margin-top: 10px;
}
</style>
