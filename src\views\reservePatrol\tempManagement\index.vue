<template>
  <div class="main-content">
    <div v-if="!showDetail">
      <transition>
        <div
          v-show="showSearch"
          class="mb-[10px]"
        >
          <el-card shadow="hover">
            <el-form
              ref="queryFormRef"
              :model="queryParams" :inline="true" class="query-form"
            >
              <el-form-item
                label="行政区"
                prop="xzqdm"
              >
                <el-tree-select
                  v-model="queryParams.xzqdm" :data="region"
                  node-key="id"
                  :props="props"
                  check-strictly
                  filterable style="width: 200px"
                />
              </el-form-item>
              <el-form-item
                label="地块编号"
                prop="dkbh"
              >
                <el-input
                  v-model="queryParams.dkbh"
                  placeholder="请输入地块编号"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item
                label="地块名称"
                prop="dkmc"
              >
                <el-input
                  v-model="queryParams.dkmc"
                  placeholder="请输入地块名称"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item
                label="详细地址"
                prop="dz"
              >
                <el-input
                  v-model="queryParams.dz"
                  placeholder="请输入详细地址"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  icon="Search" @click="handleQuery"
                >搜索
                </el-button>
                <el-button
                  icon="Refresh"
                  @click="resetQuery"
                >重置</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </div>
      </transition>

      <el-row
        :gutter="10"
        class="mb8 new"
      >
        <el-col :span="1.5">
          <el-button
            type="warning" plain icon=""
            @click="handleExport"
          >导出
          </el-button>
        </el-col>
        <right-toolbar
          v-model:showSearch="showSearch"
          @queryTable="getList"
        />
      </el-row>
      <el-card class="result-wrap">
        <el-table
          v-loading="loading"
          :data="relLandList"
        >
          <el-table-column
            label="行政区名称"
            align="center" prop="xzqmc"
          />
          <el-table-column
            label="地块编号"
            align="center" prop="dkbh"
          />
          <el-table-column
            label="地块名称"
            align="center" prop="dkmc"
          />
          <el-table-column
            label="地块标识码"
            align="center" prop="dkbsm"
          />
          <el-table-column
            label="项目名称"
            align="center" prop="xmmc"
          />
          <el-table-column
            label="地块面积"
            align="center" prop="dkmj"
          />
          <el-table-column
            label="管护方式"
            align="center" prop="jhghfs"
          >
            <template #default="scope">
              <el-tag
                v-if="scope.row.jhghfs === '1'"
                type="success"
              >自行管护</el-tag>
              <el-tag
                v-else-if="scope.row.jhghfs === '2'"
                type="warning"
              >委托管护</el-tag>
              <el-tag
                v-else
                type="primary"
              >其他</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="利用方式"
            align="center" prop="jhlyfs"
          >
            <template #default="scope">
              <el-tag
                v-if="scope.row.jhlyfs === '1'"
                type="primary"
              >有偿</el-tag>
              <el-tag
                v-else
                type="danger"
              >无偿</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="详细地址"
            align="center" prop="dz"
          />
          <el-table-column
            label="操作"
            align="center"
            width="180"
          >
            <template #default="scope">
              <el-button
                plain
                type="primary" size="small" @click="handleUpdate(scope.row)"
              >修改</el-button>
              <el-button
                plain
                type="danger" size="small" @click="handleDelete(scope.row)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>
    <!-- 添加或修改拆迁地块信息对话框 -->
    <div
      v-else
      class="content"
    >
      <div class="add-header-title-land">
        <div class="add-title">{{ ghTitle }}</div>
        <div
          class="add-title-return"
          @click="cancel"
        >
          <img
            src="@/assets/images/img-return.png"
            class="back"
          >
          <div class="backlist">返回列表</div>
        </div>
      </div>
      <div class="add-content-land">
        <el-form
          ref="relLandFormRef"
          :rules="rules"
          label-width="180px"
          style="padding: 10px;"
          :model="form"
        >
          <div>
            <div class="content-title-land">
              <img src="@/assets/images/left.png">
              <p>地块信息</p>
            </div>
            <el-row>
              <el-col :span="12">
                <el-form-item
                  label="地块编号"
                  prop="dkbh"
                >
                  <el-autocomplete
                    v-model="form.dkbh"
                    :fetch-suggestions="querySearch"
                    @select="handleSelect"
                  >
                    <template #default="{ item }">
                      <div class="value">地块编号：{{ item.dkbh }}，项目名称：{{ item.xmmc }}，位置：{{ item.dz }}</div>
                    </template>
                  </el-autocomplete>
                </el-form-item>
                <el-form-item
                  label="地块名称"
                  prop="dkmc"
                >
                  <el-input v-model="form.dkmc"/>
                </el-form-item>
                <el-form-item
                  label="地块面积"
                  prop="dkmj"
                >
                  <el-input
                    v-model="form.dkmj"
                  >
                    <template #append>公顷</template>
                  </el-input>
                </el-form-item>
                <el-form-item
                  label="行政区域"
                  prop="xzqdm"
                >
                  <el-tree-select
                    v-model="form.xzqdm" :data="region"
                    node-key="id"
                    :props="props"
                    check-strictly
                    @change="handleSelectionChange"
                    filterable style="width: 100%"
                  />
                </el-form-item>
                <el-form-item
                  label="储备机构"
                  prop="cbjg"
                >
                  <el-input v-model="form.cbjg"/>
                </el-form-item>
                <el-form-item
                  label="利用方式"
                  prop="jhlyfs"
                >
                  <el-select
                    v-model="form.jhlyfs"
                  >
                    <el-option
                      :key="1"
                      label="有偿" value="1"
                    />
                    <el-option
                      :key="2"
                      label="无偿" value="2"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="地块标识码"
                  prop="dkbsm"
                >
                  <el-input v-model="form.dkbsm"/>
                </el-form-item>
                <el-form-item
                  label="项目名称"
                  prop="xmmc"
                >
                  <el-input v-model="form.xmmc"/>
                </el-form-item>
                <el-form-item
                  label="规划用途"
                  prop="ghyt"
                >
                  <el-tree-select
                    v-model="form.ghyt" :data="ghdldm"
                    show-checkbox
                    check-strictly
                    filterable style="width: 100%"
                  />
                </el-form-item>
                <el-form-item
                  label="来源"
                  prop="ly"
                >
                  <el-select
                    v-model="form.ly"
                  >
                    <el-option
                      :key="1"
                      label="依法收回" value="1"
                    />
                    <el-option
                      :key="2"
                      label="新增用地" value="2"
                    />
                    <el-option
                      :key="3"
                      label="其他" value="3"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item
                  label="管护方式"
                  prop="jhghfs"
                >
                  <el-select
                    v-model="form.jhghfs"
                  >
                    <el-option
                      :key="1"
                      label="自行管护" value="1"
                    />
                    <el-option
                      :key="2"
                      label="委托管护" value="2"
                    />
                    <el-option
                      :key="3"
                      label="其他" value="3"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item
                  label="图斑编号"
                  prop="tbbh"
                >
                  <el-input v-model="form.tbbh"/>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="备注"
                  prop="bz"
                >
                  <el-input
                    v-model="form.bz"
                    :rows="2" type="textarea"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div>
            <div class="content-title-land">
              <img src="@/assets/images/left.png">
              <p>四至范围</p>
            </div>
            <el-row>
              <el-col :span="12">
                <el-form-item
                  label="四至东"
                  prop="zdszd"
                >
                  <el-input v-model="form.zdszd"/>
                </el-form-item>
                <el-form-item
                  label="四至西"
                  prop="zdszx"
                >
                  <el-input v-model="form.zdszx"/>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="四至南"
                  prop="zdszn"
                >
                  <el-input v-model="form.zdszn"/>
                </el-form-item>
                <el-form-item
                  label="四至北"
                  prop="zdszb"
                >
                  <el-input v-model="form.zdszb"/>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="详细地址"
                  prop="dz"
                >
                  <el-input
                    v-model="form.dz"
                    :rows="2" type="textarea"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>
      <div class="footer-button-land">
        <el-button
          :loading="buttonLoading"
          type="primary" @click="submitForm" plain
        >
          <el-icon>
            <CircleCheckFilled/>
          </el-icon>
          提交项目信息
        </el-button>
        <el-button
          @click="cancel"
          plain
        >
          <el-icon>
            <CircleCloseFilled/>
          </el-icon>
          取 消
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup name="RelLand">
import {
  delRelLand
} from "@/api/relocationPatrol/relocationLand.js";
import { getRegionTreeList } from "@/api/gis/layerTree.js";
import useUserStore from "@/store/modules/user.js";
import { reserveLandlist } from "@/api/patrol/reserveProject.js";
import { addLand, getLandDetail, updateLand } from "@/api/patrol/tempMaintenance.js";
import { getGhdldm } from "@/api/system/dict/data.js";

const { proxy } = getCurrentInstance();
const userStore = useUserStore();

const relLandList = ref([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref();
const relLandFormRef = ref();

const land = ref({});

const showDetail = ref(false);

const dialog = reactive({
  visible: false,
  title: ""
});

const initFormData = {
  id: undefined,
  xzqdm: undefined,
  xzqmc: undefined,
  dkbh: undefined,
  tbbh: undefined,
  dkmc: undefined,
  ghyt: undefined,
  dkmj: undefined,
  dz: undefined,
  tdzt: undefined,
  dkbsm: undefined,
  bz: undefined
};
const data = reactive({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    xzqdm: undefined,
    xzqmc: undefined,
    dkbh: undefined,
    tbbh: undefined,
    dkmc: undefined,
    ghyt: undefined,
    dkmj: undefined,
    dz: undefined,
    tdzt: undefined,
    smgeometry: undefined,
    bz: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: "$comment不能为空", trigger: "blur" }],
    xzqdm: [{ required: true, message: "行政区代码不能为空", trigger: "blur" }],
    dkbh: [{ required: true, message: "地块编号不能为空", trigger: "blur" }],
    dkmc: [{ required: true, message: "地块名称不能为空", trigger: "blur" }],
    ghyt: [{ required: true, message: "规划用途不能为空", trigger: "blur" }],
    dkmj: [{ required: true, message: "地块面积不能为空", trigger: "blur" }],
    dz: [{ required: true, message: "地不能为空", trigger: "blur" }],
    tdzt: [{ required: true, message: "土地状态不能为空", trigger: "blur" }]
  }
});

const { queryParams, form, rules } = toRefs(data);

const ghdldm = ref([]);
getGhdldm().then((res) => {
  ghdldm.value = res.data;
});

/** 查询拆迁地块信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await reserveLandlist(queryParams.value);
  relLandList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  showDetail.value = false;
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  land.value = {};
  relLandFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 选中数据 */
const handleSelectionChange = (value) => {
  debugger
  // form.value.xzqmc = region.value.filter((item) => item.xzqdm === selection).map((item) => item.xzqmc)[0];
  const findNodeById = (selection) => {
    let stack = [...region.value];
    while (stack.length) {
      const node = stack.pop();
      if (node.areaCode === selection) {
        form.value.xzqmc = node.name; // 更新label值
        return; // 找到后退出函数
      }
      if (node.children) node.children.forEach(child => stack.push(child));
    }
  };
  findNodeById(value);
};

//标题变量
const ghTitle = ref('新增管护信息')

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  showDetail.value = true;
  ghTitle.value = '新增管护信息'
};

/** 修改按钮操作 */
const handleUpdate = async (row) => {
  reset();
  ghTitle.value = '修改管护信息'
  const _id = row?.id || ids.value[0];
  const res = await getLandDetail(_id);
  Object.assign(form.value, res.data);
  showDetail.value = true;
};

/** 提交按钮 */
const submitForm = () => {
  relLandFormRef.value?.validate(async (valid) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateLand(form.value).finally(
          () => (buttonLoading.value = false)
        );
      } else {
        await addLand(form.value).finally(
          () => (buttonLoading.value = false)
        );
      }
      proxy?.$modal.msgSuccess("操作成功");
      showDetail.value = false;
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal
    .confirm('是否确认删除管护信息编号为"' + _ids + '"的数据项？')
    .finally(() => (loading.value = false));
  await delRelLand(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    "patrol/relLand/export",
    {
      ...queryParams.value
    },
    `relLand_${new Date().getTime()}.xlsx`
  );
};

const handleUploadSuccess = (file, files) => {
  files.forEach((singlefile) => {
    singlefile.scrMc = userStore.user.nickName;
    singlefile.scrId = userStore.user.userId;
    singlefile.scsj = parseTime(new Date(), "{y}-{m}-{d} {h}:{i}:{s}");
    singlefile.wjmc = singlefile.name;
  })
  form.value.files = files;
}

const props = {
  value: 'areaCode',
  children: 'children',
  label: 'name'
}
const region = ref([]);
const regionTree = async () => {
  const tree = await getRegionTreeList();
  region.value = tree.data;
}

const querySearch = async (queryString, callback) => {
  const results = await createFilter(queryString)
  callback(results.rows);
}
const createFilter = async (queryString) => {
  return await reserveLandlist({
    dkbh: queryString,
    pageNum: 1,
    pageSize: 10
  });
}

const handleSelect = async (row) => {
  const landRes = await getLandDetail(row.id);
  Object.assign(form.value, landRes.data);
}

onMounted(() => {
  getList();
  regionTree();
});
</script>
<style scoped lang="scss">
.main-content {
  padding: 10px;
}

.content {
  border: 1px solid rgb(233, 233, 233);
  border-radius: 4px;
}

.add-content-land {
  padding: 0px 10px;
}

.result-wrap {
  margin-top: 10px;
}

.new {
  margin-top: 10px;
}

.add-header-title-land {
  padding: 10px;
  display: flex;
  justify-content: space-between;
  height: 50px;
  background-color: rgb(222, 239, 255);
  box-sizing: border-box;
  border-bottom: 1px solid rgb(233, 233, 233);
  font-weight: 700;
  font-size: 14px;
  line-height: 28px;
}

.add-title-return {
  display: flex;
  align-content: center;
  color: rgb(32, 119, 255);
  cursor: pointer;
  font-weight: normal;
}

.back {
  height: 18px;
  width: 18px;
  margin-top: 5px;
}

.backlist {
  padding-left: 6px;
  font-size: 14px;
}

.content-title-land {
  font-weight: bold;
  display: flex;
  align-items: center;

  p {
    margin-left: 8px;
  }
}

.footer-button-land {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  border-top: 1px solid #e0e0e0;
  padding: 20px 0;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  button {
    height: 40px;
  }
}

@media(max-width: 1000px){
  .query-form{
    :deep(.el-form-item) {
      margin-bottom: 18px !important;
    }
  }
}
@media(min-width: 1500px){
  .query-form{
    :deep(.el-form-item) {
      margin-bottom: 0 !important;
    }
  }
}
</style>
