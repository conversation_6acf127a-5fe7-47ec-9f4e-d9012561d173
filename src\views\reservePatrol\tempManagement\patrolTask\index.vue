<template>
  <div class="main-content">
    <div v-if="!showDetail">
      <transition>
        <div
          v-show="showSearch"
          class="mb-[10px]"
        >
          <el-card shadow="hover">
            <el-form
              ref="queryFormRef"
              :model="queryParams" :inline="true"
              class="query-form"
            >
              <el-form-item
                label="任务名称"
                prop="name"
              >
                <el-input
                  v-model="queryParams.name"
                  placeholder="请输入任务名称"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item
                label="地块号"
                prop="dkbh"
              >
                <el-input
                  v-model="queryParams.dkbh"
                  placeholder="请输入地块号"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item
                label="是否已发布"
                prop="sfyfb"
              >
                <el-select
                  v-model="queryParams.sfyfb"
                  style="width: 200px"
                >
                  <el-option
                    :key="1"
                    label="已发布" :value="true"
                  />
                  <el-option
                    :key="2"
                    label="未发布" :value="false"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                label="任务状态"
                prop="rwzt"
              >
                <el-select
                  v-model="queryParams.rwzt"
                  style="width: 200px"
                >
                  <el-option
                    :key="1"
                    label="未完结" :value="0"
                  />
                  <el-option
                    :key="2"
                    label="已完结" :value="1"
                  />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  icon="Search" @click="handleQuery"
                >搜索
                </el-button>
                <el-button
                  icon="Refresh"
                  @click="resetQuery"
                >重置</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </div>
      </transition>
      <el-row
        :gutter="10"
        class="mb8 searchbar"
      >
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="Upload"
            @click="handleExport"
          >导出
          </el-button>
        </el-col>
        <right-toolbar
          v-model:showSearch="showSearch"
          @queryTable="getList"
        />
      </el-row>
      <el-card class="result-wrap">
        <el-table
          v-loading="loading"
          :data="patrolTaskList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            label="区域"
            align="center" prop="land.xzqmc"
          />
          <el-table-column
            label="年度"
            align="center" prop="releaseTime"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.yqzxsj, "{y}") }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="任务名称"
            align="center" prop="name"
          />
          <el-table-column
            label="合同编号"
            align="center" prop="temp.htbh"
          />
          <el-table-column
            label="临时用途"
            align="center" prop="temp.lslyyt"
          />
          <el-table-column
            label="地块名称"
            align="center" prop="land.dkmc"
          />
          <el-table-column
            label="地块编号"
            align="center" prop="land.dkbh"
          />
          <el-table-column
            label="地块面积(公顷)"
            align="center" prop="land.dkmj"
          />
          <el-table-column
            label="地块位置"
            align="center" prop="land.dz"
          />
          <el-table-column
            label="是否已发布"
            align="center" prop="sfyfb"
          >
            <template #default="scope">
              <el-tag
                v-if="scope.row.sfyfb"
                type="success"
              >已发布</el-tag>
              <el-tag
                v-else
                type="primary"
              >未发布</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="预期执行时间"
            align="center"
            prop="yqzxsj"
            width="180"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.yqzxsj, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="是否已完结"
            align="center" prop="rwzt"
          >
            <template #default="scope">
              <el-tag
                v-if="scope.row.rwzt === '1'"
                type="success"
              >已完结</el-tag>
              <el-tag
                v-else
                type="danger"
              >未完结</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="调查人员"
            align="center" prop="rwjsr"
          />
          <el-table-column
            label="操作"
            align="center"
            width="180"
          >
            <template #default="scope">
              <el-button
                plain
                type="primary" size="small" @click="handleUpdate(scope.row)"
              >修改</el-button>
              <el-button
                plain
                type="danger" size="small" @click="handleDelete(scope.row)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>
    <!-- 添加或修改巡查任务管理对话框 -->
    <div
      v-else
      class="content"
    >
      <div class="add-header-title">
        <div class="add-title">{{ ghxcTitle }}</div>
        <div
          class="add-title-return"
          @click="cancel"
        >
          <img
            src="../../../../assets/images/img-return.png"
            class="back"
          >
          <div class="backlist">返回列表</div>
        </div>
      </div>
      <div class="add-content-task">
        <el-form
          ref="patrolTaskFormRef"
          :model="form"
          :rules="rules"
          label-width="180px"
          style="padding: 10px"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item
                label="合同编号"
                prop="htbh"
              >
                <el-autocomplete
                  v-model="temp.htbh"
                  :fetch-suggestions="querySearch"
                  @select="handleSelect"
                >
                  <template #default="{ item }">
                    <div class="value">合同编号：{{ item.htbh }}，乙方单位：{{ item.yfdw }}</div>
                  </template>
                </el-autocomplete>
              </el-form-item>
              <el-form-item
                label="任务发布时间"
                prop="releaseTime"
              >
                <el-date-picker
                  v-model="form.releaseTime"
                  type="datetime"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  placeholder="请选择任务发布时间"
                  style="width: 100%"
                />
              </el-form-item>
              <el-form-item
                label="是否发布"
                prop="sfyfb"
              >
                <el-switch
                  v-model="form.sfyfb"
                  inline-prompt
                  active-text="是"
                  inactive-text="否"
                  @change="dataChange"
                />
              </el-form-item>
              <el-form-item
                label="地块编号"
                prop="dkbh"
              >
                <el-input
                  v-model="form.dkbh"
                  disabled
                />
              </el-form-item>
              <el-form-item
                label="图斑编号"
                prop="tbbh"
              >
                <el-input
                  v-model="form.tbbh"
                  disabled
                />
              </el-form-item>
              <el-form-item
                label="临时利用期限起"
                prop="lslyqxq"
              >
                <el-date-picker
                  disabled
                  v-model="temp.lslyqxq"
                  type="date"
                  value-format="YYYY-MM-DD"
                  style="width: 100%;"
                />
              </el-form-item>
              <el-form-item
                label="临时利用用途"
                prop="lslyyt"
              >
                <el-input
                  v-model="temp.lslyyt"
                  disabled
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="任务名称"
                prop="name"
              >
                <el-input
                  v-model="form.name"
                  placeholder="请输入任务名称"
                />
              </el-form-item>
              <el-form-item
                label="预期执行时间"
                prop="yqzxsj"
              >
                <el-date-picker
                  v-model="form.yqzxsj"
                  type="datetime"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  placeholder="请选择预期执行时间"
                  style="width: 100%"
                />
              </el-form-item>
              <el-form-item
                label="任务接收人"
                prop="rwjsr"
              >
                <el-input
                  v-model="form.rwjsr"
                  placeholder="请输入任务接收人"
                />
              </el-form-item>
              <el-form-item
                label="地块面积"
                prop="dkmj"
              >
                <el-input
                  v-model="form.dkmj"
                  disabled
                >
                  <template #append>公顷</template>
                </el-input>
              </el-form-item>
              <el-form-item
                label="乙方单位"
                prop="yfdw"
              >
                <el-input
                  v-model="temp.yfdw"
                  disabled
                />
              </el-form-item>
              <el-form-item
                label="临时利用期限止"
                prop="lslyqxz"
              >
                <el-date-picker
                  disabled
                  v-model="temp.lslyqxz"
                  type="date"
                  value-format="YYYY-MM-DD"
                  style="width: 100%;"
                />
              </el-form-item>
              <el-form-item
                label="是否已收回"
                prop="sfysh"
              >
                <el-switch
                  v-model="temp.sfysh"
                  inline-prompt
                  active-text="是"
                  inactive-text="否"
                  disabled
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="地块位置"
                prop="dkzl"
              >
                <el-input
                  v-model="form.dkzl"
                  disabled
                />
              </el-form-item>
              <el-form-item
                label="备注"
                prop="bz"
              >
                <el-input
                  v-model="form.bz"
                  :rows="2" type="textarea" placeholder="请输入备注"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <div class="footer-button-task">
          <el-button
            :loading="buttonLoading"
            type="primary"
            @click="submitForm"
            plain
          >
            <el-icon>
              <CircleCheckFilled/>
            </el-icon>
            提交项目信息
          </el-button>
          <el-button
            @click="cancel"
            type="info" plain
          >
            <el-icon>
              <CircleCloseFilled/>
            </el-icon>
            取 消
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="TempPatrolTask">
/**
 * CHN：临时管护巡查任务
 */
import {
  listPatrolTask,
  getPatrolTask,
  delPatrolTask,
  addPatrolTask,
  updatePatrolTask
} from "@/api/patrol/patrolTask.js";
import {
  getLandDetail,
  getTemporaryManage,
  listTemporaryManage
} from "@/api/patrol/tempMaintenance.js";

const { proxy } = getCurrentInstance();

const showDetail = ref(false);
const patrolTaskList = ref([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref();
const patrolTaskFormRef = ref();

const initFormData = {
  id: undefined,
  dkId: undefined,
  name: undefined,
  releaseTime: undefined,
  dkmj: undefined,
  dkbh: undefined,
  tbbh: undefined,
  dkzl: undefined,
  rwfbr: undefined,
  fbrId: undefined,
  rwjsr: undefined,
  jsrId: undefined,
  yqzxsj: undefined,
  sfyfb: false,
  rwzt: undefined,
  bz: undefined
};
const data = reactive({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    dkId: undefined,
    name: undefined,
    releaseTime: undefined,
    dkmj: undefined,
    dkbh: undefined,
    tbbh: undefined,
    dkzl: undefined,
    rwfbr: undefined,
    fbrId: undefined,
    rwjsr: undefined,
    jsrId: undefined,
    yqzxsj: undefined,
    sfyfb: undefined,
    rwzt: undefined,
    bz: undefined,
    params: {}
  },
  rules: {
    name: [{ required: true, message: "任务名称不能为空", trigger: "blur" }],
    releaseTime: [{ required: true, message: "任务发布时间不能为空", trigger: "blur" }],
    dkbh: [{ required: true, message: "地块编号不能为空", trigger: "blur" }],
    dkzl: [{ required: true, message: "地块坐落不能为空", trigger: "blur" }],
    yqzxsj: [{ required: true, message: "预期执行时间不能为空", trigger: "blur" }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询巡查任务管理列表 */
const getList = async () => {
  loading.value = true;
  const res = await listPatrolTask(queryParams.value);
  patrolTaskList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  showDetail.value = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  temp.value = {};
  patrolTaskFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};
//标题变量
const ghxcTitle = ref('新建管护巡查任务')

/** 多选框选中数据 */
const handleSelectionChange = (selection) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  ghxcTitle.value = '新建管护巡查任务'
  showDetail.value = true;
};

const temp = ref({});
/** 修改按钮操作 */
const handleUpdate = async (row) => {
  reset();
  ghxcTitle.value = '修改管护巡查任务'
  const _id = row?.id || ids.value[0];
  const res = await getPatrolTask(_id);
  const tempRes = await getTemporaryManage(res.data.ghId);
  temp.value = tempRes.data;
  Object.assign(form.value, res.data);
  showDetail.value = true;
};

/** 提交按钮 */
const submitForm = () => {
  patrolTaskFormRef.value?.validate(async (valid) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updatePatrolTask(form.value).finally(
          () => (buttonLoading.value = false)
        );
      } else {
        await addPatrolTask(form.value).finally(
          () => (buttonLoading.value = false)
        );
      }
      proxy?.$modal.msgSuccess("操作成功");
      showDetail.value = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal
    .confirm('是否确认删除巡查任务管理编号为"' + _ids + '"的数据项？')
    .finally(() => (loading.value = false));
  await delPatrolTask(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    "patrol/patrolTask/export",
    {
      ...queryParams.value
    },
    `patrolTask_${new Date().getTime()}.xlsx`
  );
};

const dataChange = (e) => {
  if (e) {
    rules.value.rwjsr = [{ required: true, message: "预期执行时间不能为空", trigger: "blur" }];
  } else {
    rules.value.rwjsr = [];
  }
};

const querySearch = async (queryString, callback) => {
  const results = await createFilter(queryString)
  callback(results.rows);
}
const createFilter = async (queryString) => {
  return await listTemporaryManage({
    htbh: queryString,
    sfysh: false,
    pageNum: 1,
    pageSize: 10
  });
}

const handleSelect = async (row) => {
  const tempRes = await getTemporaryManage(row.id);
  temp.value = tempRes.data;
  form.value.ghId = tempRes.data.id;

  const landRes = await getLandDetail(tempRes.data.dkId);
  form.value.dkbh = landRes.data.dkbh;
  form.value.dkzl = landRes.data.dz;
  form.value.tbbh = landRes.data.tbbh;
  form.value.dkmj = landRes.data.dkmj;
  form.value.dkId = landRes.data.id;
}


onMounted(() => {
  getList();
});
</script>
<style lang="scss" scoped>
.main-content {
  padding: 10px;
}

.result-wrap {
  margin-top: 10px;
}

.content {
  border: 1px solid rgb(233, 233, 233);
  border-radius: 4px;
}

.add-content-task {
  padding: 0px 10px;
}

.add-header-title {
  padding: 10px;
  display: flex;
  justify-content: space-between;
  height: 50px;
  background-color: rgb(222, 239, 255);
  box-sizing: border-box;
  border-bottom: 1px solid rgb(233, 233, 233);
  font-weight: 700;
  font-size: 14px;
  line-height: 28px;
}

.add-title-return {
  display: flex;
  align-content: center;
  color: rgb(32, 119, 255);
  cursor: pointer;
  font-weight: normal;
}

.footer-button-task {
  display: flex;
  justify-content: center;
  margin: 20px -10px 0 -10px;
  border-top: 1px solid #e0e0e0;
  padding: 20px 0;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  button {
    height: 40px;
  }
}

.back {
  height: 18px;
  width: 18px;
  margin-top: 5px;
}

.backlist {
  padding-left: 6px;
  font-size: 14px;
}

.searchbar {
  margin-top: 10px;
}

.el-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
@media(max-width: 1000px){
  .query-form{
    :deep(.el-form-item) {
      margin-bottom: 18px !important;
    }
  }
}
@media(min-width: 1000px){
  .query-form{
    :deep(.el-form-item) {
      margin-bottom: 0 !important;
    }
  }
}
</style>
