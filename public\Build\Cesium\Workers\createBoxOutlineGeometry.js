define(["./arrayFill-4513d7ad","./buildModuleUrl-9085faaa","./Cartographic-3309dd0d","./Check-7b2a090c","./ComponentDatatype-c140a87d","./when-b60132fc","./GeometryAttribute-c65394ac","./GeometryAttributes-252e9929","./GeometryOffsetAttribute-fbeb6f1a","./FeatureDetection-806b12f0","./Rectangle-dee65d21","./Math-119be1a3","./Event-16a2dfbf","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Cartesian2-db21342c","./Cartesian4-3ca25aab"],(function(e,t,a,n,i,r,u,o,m,s,f,c,d,l,b,p,y){"use strict";var C=new a.Cartesian3;function A(e){var t=(e=r.defaultValue(e,r.defaultValue.EMPTY_OBJECT)).minimum,n=e.maximum;this._min=a.Cartesian3.clone(t),this._max=a.Cartesian3.clone(n),this._offsetAttribute=e.offsetAttribute,this._workerName="createBoxOutlineGeometry"}A.fromDimensions=function(e){var t=(e=r.defaultValue(e,r.defaultValue.EMPTY_OBJECT)).dimensions,n=a.Cartesian3.multiplyByScalar(t,.5,new a.Cartesian3);return new A({minimum:a.Cartesian3.negate(n,new a.Cartesian3),maximum:n,offsetAttribute:e.offsetAttribute})},A.fromAxisAlignedBoundingBox=function(e){return new A({minimum:e.minimum,maximum:e.maximum})},A.packedLength=2*a.Cartesian3.packedLength+1,A.pack=function(e,t,n){return n=r.defaultValue(n,0),a.Cartesian3.pack(e._min,t,n),a.Cartesian3.pack(e._max,t,n+a.Cartesian3.packedLength),t[n+2*a.Cartesian3.packedLength]=r.defaultValue(e._offsetAttribute,-1),t};var x=new a.Cartesian3,_=new a.Cartesian3,w={minimum:x,maximum:_,offsetAttribute:void 0};return A.unpack=function(e,t,n){t=r.defaultValue(t,0);var i=a.Cartesian3.unpack(e,t,x),u=a.Cartesian3.unpack(e,t+a.Cartesian3.packedLength,_),o=e[t+2*a.Cartesian3.packedLength];return r.defined(n)?(n._min=a.Cartesian3.clone(i,n._min),n._max=a.Cartesian3.clone(u,n._max),n._offsetAttribute=-1===o?void 0:o,n):(w.offsetAttribute=-1===o?void 0:o,new A(w))},A.createGeometry=function(n){var f=n._min,c=n._max;if(!a.Cartesian3.equals(f,c)){var d=new o.GeometryAttributes,l=new Uint16Array(24),b=new Float64Array(24);b[0]=f.x,b[1]=f.y,b[2]=f.z,b[3]=c.x,b[4]=f.y,b[5]=f.z,b[6]=c.x,b[7]=c.y,b[8]=f.z,b[9]=f.x,b[10]=c.y,b[11]=f.z,b[12]=f.x,b[13]=f.y,b[14]=c.z,b[15]=c.x,b[16]=f.y,b[17]=c.z,b[18]=c.x,b[19]=c.y,b[20]=c.z,b[21]=f.x,b[22]=c.y,b[23]=c.z,d.position=new u.GeometryAttribute({componentDatatype:i.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:b}),l[0]=4,l[1]=5,l[2]=5,l[3]=6,l[4]=6,l[5]=7,l[6]=7,l[7]=4,l[8]=0,l[9]=1,l[10]=1,l[11]=2,l[12]=2,l[13]=3,l[14]=3,l[15]=0,l[16]=0,l[17]=4,l[18]=1,l[19]=5,l[20]=2,l[21]=6,l[22]=3,l[23]=7;var p=a.Cartesian3.subtract(c,f,C),y=.5*a.Cartesian3.magnitude(p);if(r.defined(n._offsetAttribute)){var A=b.length,x=new Uint8Array(A/3),_=n._offsetAttribute===m.GeometryOffsetAttribute.NONE?0:1;e.arrayFill(x,_),d.applyOffset=new u.GeometryAttribute({componentDatatype:i.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:x})}return new u.Geometry({attributes:d,indices:l,primitiveType:s.PrimitiveType.LINES,boundingSphere:new t.BoundingSphere(a.Cartesian3.ZERO,y),offsetAttribute:n._offsetAttribute})}},function(e,t){return r.defined(t)&&(e=A.unpack(e,t)),A.createGeometry(e)}}));
