
/**
 * @name: MeasureHeight.js
 * @description: 量测面积工具类
 * @author: zyc
 * @time: 2024-05-06
 **/

import { setCursor } from "@/utils/Cesium/CesiumTool";
import CesiumMeasureBaseClass from "@/utils/Cesium/CesiumMeasureBaseClass";
export default class CesiumMeasureAreaClass extends CesiumMeasureBaseClass{
  constructor(props) {
    super(props)
    this.positions = []

    this.labelEntity = undefined
    this.area = "0"
  }
  initEvent(){
    /**
     * @params{perPositionHeight}
     * @description：布尔值，指定是否使用每个位置的高度。当值为 true 时，绘制水平面；当值为 false 时，绘制贴地面
     */
    setCursor(this.viewer3d,'crosshair')
    // 注册测量事件
    this.handler = new Cesium.ScreenSpaceEventHandler(this.viewer3d.canvas)
    // 鼠标移动时绘制的临时对象
    const polygon = {
      // id: "polygon",
      name: "绘制面",
      polygon: {
        hierarchy: new Cesium.CallbackProperty(() => {
          return new Cesium.PolygonHierarchy(this.positions);
        }, false),
        perPositionHeight: this.isPerPositionHeight,
        // fill: SuperMap3D.Color.ALICEBLUE,
        // outline: true,
        // outlineColor: SuperMap3D.Color.AQUA,
        // outlineWidth: 2.5,
        // show: true,
        material: new Cesium.ColorMaterialProperty(
          Cesium.Color.WHITE.withAlpha(0.4)
        )
      }
    }
    const polyline = {
      // id: "polyline",
      name: "绘制线段",
      show: true,
      polyline: {
        positions: new Cesium.CallbackProperty(() => {
          return this.positions;
        }, false),
        width: 2.5,
        show: true,
        material: Cesium.Color.CORNSILK,
        clampToGround: this.clampToGround
      }
    }
    this.viewer3d.entities.add(polyline)
    this.viewer3d.entities.add(polygon)
    this.registerEvent()
  }
  registerEvent(){
    this.mouseClick()
    this.mouseMove()
    this.mouseDoubleClick()
  }

  /**
   *  1.鼠标单击事件：开始绘制
   */
  mouseClick(){
    this.handler.setInputAction(async res => {
      const worldPosition = this.viewer3d.scene.pickPosition(res.position)
      if (Cesium.defined(worldPosition)){
        this.positions.push(worldPosition)
        // 计算面积
        if (this.positions.length > 2){
          if (this.labelEntity){
            this.viewer3d.entities.remove(this.labelEntity)
          }
          this.area = this.measureArea(this.positions)
          this.setArea(this.area)
          this.createResultLabel(this.positions)
          console.log("this.area:",this.area)
        }
        // 绘制点
        const entity = new Cesium.Entity({
          name: "绘制点",
          show: true,
          position: worldPosition,
          point: new Cesium.PointGraphics({
            color: Cesium.Color.AQUA,
            pixelSize: 5,
            outlineColor: Cesium.Color.GHOSTWHITE,
            outlineWidth: 1,
            distanceDisplayCondition: 10,
            heightReference: this.heightMode
          }),
          label: new Cesium.LabelGraphics({
            font: "10px",
            pixelOffset: new Cesium.Cartesian2(0, -40)
          })
        })
        this.viewer3d.entities.add(entity)
      }
    },Cesium.ScreenSpaceEventType.LEFT_CLICK)
  }

  /**
   * 2.鼠标移动事件：临时绘制
   */
  mouseMove(){
    this.handler.setInputAction(res => {
      const windowPosition = res.endPosition
      this.infoBox.hidden = false
      this.infoBox.style.top = windowPosition.y - 10 + "px"
      this.infoBox.style.left = windowPosition.x + 40 + "px"

      const worldCoords = this.viewer3d.scene.pickPosition(windowPosition)
      if (this.positions.length >= 1){
        // 移除已经添加线段
        const isTempLine = this.viewer3d.entities.getById('tempLine')
        if (isTempLine){
          this.viewer3d.entities.remove(isTempLine)
        }
        // 构建连接线，绘制临时线
        const tempLine = new Cesium.Entity({
          id: "tempLine",
          name: "临时线段",
          show: true,
          polyline: new Cesium.PolylineGraphics({
            positions: new Cesium.CallbackProperty(() => {
              return [].concat(this.positions[this.positions.length - 1],worldCoords);
            }, false),
            width: 2,
            show: true,
            material: new Cesium.ColorMaterialProperty(Cesium.Color.CORNSILK),
            clampToGround: this.clampToGround
          })
        })
        this.viewer3d.entities.add(tempLine)
      }
    },Cesium.ScreenSpaceEventType.MOUSE_MOVE)
  }

  /**
   * 3.鼠标双击事件：结束绘制
   */
  mouseDoubleClick(){
    this.handler.setInputAction(res => {
      this.positions.pop()
      // 添加最后一段线
      const lastLine = new Cesium.Entity({
        name: "最后一段线",
        show: true,
        polyline: new Cesium.PolylineGraphics({
          positions: new Cesium.CallbackProperty(() => {
            return [].concat(this.positions[this.positions.length - 1],this.positions[0]);
          }, false),
          width: 2,
          show: true,
          material: new Cesium.ColorMaterialProperty(Cesium.Color.CORNSILK),
          clampToGround: this.clampToGround
        })
      })
      this.viewer3d.entities.add(lastLine)
      // 移除绘制
      this.handler.destroy()
      this.infoBox.remove()
      setCursor(this.viewer3d,'pointer')
    },Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK)
  }

  // 测量面积
  measureArea(Cartesian3Arr) {
    let areaAll = 0;
    const cartesianFirst = Cartesian3Arr[0];
    for (let i = 1; i < Cartesian3Arr.length - 1; i++) {
      const cartesianStart = Cartesian3Arr[i];
      const cartesianEnd = Cartesian3Arr[i + 1];
      const vecStart = Cesium.Cartesian3.subtract(
        cartesianStart,
        cartesianFirst,
        new Cesium.Cartesian3()
      );
      const vecEnd = Cesium.Cartesian3.subtract(
        cartesianEnd,
        cartesianFirst,
        new Cesium.Cartesian3()
      );
      const vecCross = Cesium.Cartesian3.cross(
        vecStart,
        vecEnd,
        new Cesium.Cartesian3()
      );
      const areaTriangle = Cesium.Cartesian3.magnitude(vecCross) * 0.5;
      areaAll += areaTriangle;
    }
    const area = areaAll.toFixed(4)
    return area;
  }
  // 创建测量结果标签
  createResultLabel(positions){
    const that = this
    this.labelEntity = ({
      position: new Cesium.CallbackProperty(e => {
        return that.getCenterPosition(positions)
      }, false),
      type: "MeasureAreaResult",
      label: {
        text: new Cesium.CallbackProperty(e => {
          return "面积 " + that.area + "平方米";
        }, false),
        scale: 0.5,
        // eyeOffset: new Cartesian3(0.0, 0.0, 1000.0),
        heightReference: this.heightMode,
        font: 'normal 30px MicroSoft YaHei',
        // distanceDisplayCondition: new SuperMap3D.DistanceDisplayCondition(0, 5000),
        // scaleByDistance: new SuperMap3D.NearFarScalar(1000, 1, 3000, 0.4),
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian3(0, 0,100000),
        outlineWidth: 9,
        outlineColor: Cesium.Color.YELLOW
      }
    })
    this.viewer3d.entities.add(this.labelEntity)
  }
  //获取节点的中心点
  getCenterPosition(positions) {
    const polyCenter = Cesium.BoundingSphere.fromPoints(positions).center;

    const center = Cesium.Ellipsoid.WGS84.scaleToGeodeticSurface(polyCenter);
    return center
  }
  setArea(value){
    this.area = value
    // 注册一个自定义事件
    const event = new CustomEvent('valueChange',{
      detail: this.area
    })
    this.viewer3d.container.dispatchEvent(event)
  }
  // 清除绘制
  clearMeasure(){
    const isDestroyed = this.handler.isDestroyed()
    if (!isDestroyed){
      // 若事件未销毁，则销毁；若销毁，则不可用
      this.handler.destroy()
    }
    this.infoBox.remove()
    // setCursor(this.viewer3d,'pointer')
    this.viewer3d.entities.removeAll()
  }
}
