define(["./when-b60132fc","./Rectangle-dee65d21","./arrayRemoveDuplicates-d2f048c5","./BoundingRectangle-143a34da","./buildModuleUrl-9085faaa","./Cartesian2-db21342c","./Cartographic-3309dd0d","./ComponentDatatype-c140a87d","./PolylineVolumeGeometryLibrary-b276ee2b","./Check-7b2a090c","./GeometryAttribute-c65394ac","./GeometryAttributes-252e9929","./GeometryPipeline-7a733318","./IndexDatatype-8a5eead4","./Math-119be1a3","./PolygonPipeline-d83979ed","./FeatureDetection-806b12f0","./VertexFormat-6446fca0","./Event-16a2dfbf","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Cartesian4-3ca25aab","./EllipsoidTangentPlane-1dfa0a87","./IntersectionTests-0d6905a3","./Plane-a3d8b3d2","./PolylinePipeline-a5200218","./EllipsoidGeodesic-139a7db9","./EllipsoidRhumbLine-30b5229b","./AttributeCompression-0a087f75","./EncodedCartesian3-f1396b05","./earcut-2.2.1-20c8012f"],(function(e,t,a,n,i,r,o,l,s,p,d,u,c,m,y,g,h,f,v,b,C,P,E,_,k,L,V,F,A,G,T){"use strict";function D(a){var n=(a=e.defaultValue(a,e.defaultValue.EMPTY_OBJECT)).polylinePositions,i=a.shapePositions;this._positions=n,this._shape=i,this._ellipsoid=t.Ellipsoid.clone(e.defaultValue(a.ellipsoid,t.Ellipsoid.WGS84)),this._cornerType=e.defaultValue(a.cornerType,s.CornerType.ROUNDED),this._vertexFormat=f.VertexFormat.clone(e.defaultValue(a.vertexFormat,f.VertexFormat.DEFAULT)),this._granularity=e.defaultValue(a.granularity,y.CesiumMath.RADIANS_PER_DEGREE),this._workerName="createPolylineVolumeGeometry",this.enuCenter=e.defaultValue(a.enuCenter,o.Cartesian3.ZERO);var l=1+n.length*o.Cartesian3.packedLength;l+=1+i.length*r.Cartesian2.packedLength+o.Cartesian3.packedLength,this.packedLength=l+t.Ellipsoid.packedLength+f.VertexFormat.packedLength+2}D.pack=function(a,n,i){var l;i=e.defaultValue(i,0);var s=a._positions,p=s.length;for(n[i++]=p,l=0;l<p;++l,i+=o.Cartesian3.packedLength)o.Cartesian3.pack(s[l],n,i);var d=a._shape;for(p=d.length,n[i++]=p,l=0;l<p;++l,i+=r.Cartesian2.packedLength)r.Cartesian2.pack(d[l],n,i);return t.Ellipsoid.pack(a._ellipsoid,n,i),i+=t.Ellipsoid.packedLength,f.VertexFormat.pack(a._vertexFormat,n,i),i+=f.VertexFormat.packedLength,n[i++]=a._cornerType,n[i++]=a._granularity,o.Cartesian3.pack(a.enuCenter,n,i),n};var x=t.Ellipsoid.clone(t.Ellipsoid.UNIT_SPHERE),R=new f.VertexFormat,w={polylinePositions:void 0,shapePositions:void 0,ellipsoid:x,vertexFormat:R,cornerType:void 0,granularity:void 0,enuCenter:void 0};D.unpack=function(a,n,i){var l;n=e.defaultValue(n,0);var s=a[n++],p=new Array(s);for(l=0;l<s;++l,n+=o.Cartesian3.packedLength)p[l]=o.Cartesian3.unpack(a,n);s=a[n++];var d=new Array(s);for(l=0;l<s;++l,n+=r.Cartesian2.packedLength)d[l]=r.Cartesian2.unpack(a,n);var u=t.Ellipsoid.unpack(a,n,x);n+=t.Ellipsoid.packedLength;var c=f.VertexFormat.unpack(a,n,R);n+=f.VertexFormat.packedLength;var m,y=a[n++],g=a[n++];return m=o.Cartesian3.unpack(a,n),e.defined(i)?(i._positions=p,i._shape=d,i._ellipsoid=t.Ellipsoid.clone(u,i._ellipsoid),i._vertexFormat=f.VertexFormat.clone(c,i._vertexFormat),i._cornerType=y,i._granularity=g,i.enuCenter=m,i):(w.polylinePositions=p,w.shapePositions=d,w.cornerType=y,w.granularity=g,w.enuCenter=m,new D(w))};var O=new n.BoundingRectangle;return D.createGeometry=function(t){for(var r=t._positions,p=a.arrayRemoveDuplicates(r,o.Cartesian3.equalsEpsilon),y=p.length,f=new Array(y),v=0;v<y;v++)f[v]=o.Cartesian3.clone(p[v]);var b=t._shape;if(b=s.PolylineVolumeGeometryLibrary.removeDuplicatesFromShape(b),!(p.length<2||b.length<3)){g.PolygonPipeline.computeWindingOrder2D(b)===g.WindingOrder.CLOCKWISE&&b.reverse();var C=n.BoundingRectangle.fromPoints(b,O),P={};if(P.combinedPositions=s.PolylineVolumeGeometryLibrary.computePositions(f,b,C,t,!0),!o.Cartesian3.equals(t.enuCenter,o.Cartesian3.ZERO)){var E=new Array(y);for(v=0;v<y;v++)E[v]=o.Cartesian3.clone(p[v]);P.combinedLocalPositions=s.PolylineVolumeGeometryLibrary.computeLocalPositions(E,b,C,t,!0,t.enuCenter)}return function(t,a,n,r){var o=t.combinedPositions,s=t.combinedLocalPositions,p=new u.GeometryAttributes;r.position&&(p.position=new d.GeometryAttribute({componentDatatype:l.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:o}));var y,f,v,b,C,P,E=a.length,_=o.length/3,k=(_-2*E)/(2*E),L=g.PolygonPipeline.triangulate(a),V=(k-1)*E*6+2*L.length,F=m.IndexDatatype.createTypedArray(_,V),A=2*E,G=0;for(y=0;y<k-1;y++){for(f=0;f<E-1;f++)P=(v=2*f+y*E*2)+A,C=(b=v+1)+A,F[G++]=b,F[G++]=v,F[G++]=C,F[G++]=C,F[G++]=v,F[G++]=P;C=(b=1+(v=2*E-2+y*E*2))+A,P=v+A,F[G++]=b,F[G++]=v,F[G++]=C,F[G++]=C,F[G++]=v,F[G++]=P}if(r.st||r.tangent||r.bitangent){var T,D,x=new Float32Array(2*_),R=1/(k-1),w=1/n.height,O=n.height/2,S=0;for(y=0;y<k;y++){for(T=y*R,D=w*(a[0].y+O),x[S++]=T,x[S++]=D,f=1;f<E;f++)D=w*(a[f].y+O),x[S++]=T,x[S++]=D,x[S++]=T,x[S++]=D;D=w*(a[0].y+O),x[S++]=T,x[S++]=D}for(f=0;f<E;f++)T=0,D=w*(a[f].y+O),x[S++]=T,x[S++]=D;for(f=0;f<E;f++)T=(k-1)*R,D=w*(a[f].y+O),x[S++]=T,x[S++]=D;p.st=new d.GeometryAttribute({componentDatatype:l.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:new Float32Array(x)})}var B=_-2*E;for(y=0;y<L.length;y+=3){var I=L[y]+B,N=L[y+1]+B,U=L[y+2]+B;F[G++]=I,F[G++]=N,F[G++]=U,F[G++]=U+E,F[G++]=N+E,F[G++]=I+E}var W=new d.Geometry({attributes:p,indices:F,boundingSphere:i.BoundingSphere.fromVertices(o),primitiveType:h.PrimitiveType.TRIANGLES});if(r.normal&&(W=c.GeometryPipeline.computeNormal(W)),r.tangent||r.bitangent){try{W=c.GeometryPipeline.computeTangentAndBitangent(W)}catch(e){i.oneTimeWarning("polyline-volume-tangent-bitangent","Unable to compute tangents and bitangents for polyline volume geometry")}r.tangent||(W.attributes.tangent=void 0),r.bitangent||(W.attributes.bitangent=void 0),r.st||(W.attributes.st=void 0)}return e.defined(s)&&(W.attributes.position.values=s,W.attributes.position.componentDatatype=l.ComponentDatatype.FLOAT),W}(P,b,C,t._vertexFormat)}},function(a,n){return e.defined(n)&&(a=D.unpack(a,n)),a._ellipsoid=t.Ellipsoid.clone(a._ellipsoid),D.createGeometry(a)}}));
