<template>
  <div v-if="isShow" class="measure-wrap">
    <div class="measure-content">
      <div class="measure-mode">
        <span>选择单位：</span>
        <el-select v-model="unitValue" placeholder="选择测量单位">
          <el-option
            v-for="item in measureUnit"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
      <div class="measure-result">
        <span class="measure-num">{{ measureValue }}</span>
        <span class="measure-unit">{{ unitValue }}</span>
        <svg-icon icon-class="clear" @click="clearResult" />
      </div>
      <div class="measure-type">
        <div
          v-for="(m, i) in measureOption"
          :key="m.name"
          class="measure-item"
          :class="actionIndex === i ? 'measure-item-active' : ''"
          @click="measureAction(m, i)"
        >
          <svg-icon :icon-class="m.icon" class-name="measure-display" />
          <label class="measure-display">{{ m.name }}</label>
        </div>
      </div>
      <!-- <div class="measure-clear" @click="clearResult">
        <svg-icon icon-class="clear" />
        <span>清除</span>
      </div> -->
    </div>
  </div>
</template>

<script setup lang="js" name="MeasureCesium">
import useMapViewStore from "@/store/modules/map/mapView.js"
import CesiumMeasureDistanceClass from "@/utils/Cesium/CesiumMeasureDistanceClass";
import CesiumMeasureAreaClass from "@/utils/Cesium/CesiumMeasureAreaClass";
import CesiumMeasureHeightClass from "@/utils/Cesium/CesiumMeasureHeightClass";

const store = useMapViewStore()

defineProps({
  isShow: {
    type: Boolean,
    default: true
  },
  headerInfo: {
    type: Object
  }
})

const measureResult = ref(0)
const handlerDistance = ref({})
const handlerArea = ref({})
const handlerHeight = ref({})
const unitValue = ref("")
const distanceUnits = reactive([
  {
    label: "公里",
    value: "Km"
  },
  {
    label: "米",
    value: "m"
  }
]
)
const areaUnits = reactive([
  {
    label: "平方千米",
    value: "Km2"
  },
  {
    label: "公顷",
    value: "h2"
  },
  {
    label: "平方米",
    value: "m2"
  }
])
const measureUnit = ref([])
const measureOption = ref([
  {
    name: "空间距离",
    icon: "measured",
    funcName: "measureSpaceDistance"
  },
  // {
  //   name: "贴地距离",
  //   icon: "measured",
  //   funcName: "measureGroundDistance"
  // },
  {
    name: "贴地面积",
    icon: "measurea",
    funcName: "measureGroundArea"
  },
  {
    name: "水平面积",
    icon: "measurea",
    funcName: "measureSpaceArea"
  },
  {
    name: "量测高度",
    icon: "measureh",
    funcName: "measureHeight"
  }
])
const actionIndex = ref(-1)
const measureFunc = ref(-1)
const measureValue = computed(()=>{
  let oldValue = 0;
  switch (unitValue.value) {
    case "Km":
      oldValue = measureResult.value;
      break;
    case "m":
      oldValue = measureResult.value * 1000;
      break;
    case "m2":
      oldValue = measureResult.value;
      break;
    case "h2":
      oldValue = measureResult.value * 1e-4;
      break;
    case "Km2":
      oldValue = measureResult.value * 1e-6;
      break;
  }
  return oldValue;
})
const viewer3d = computed(()=> useMapViewStore().viewer3d)
const emits = defineEmits({
  closePanel: ()=>{
    return true
  }
})
const measureAction = (m, i)=> {
  measureFunc.value = m;
  actionIndex.value = i;
  switch (m.funcName) {
    case "measureSpaceDistance":
      measureDistance("Space");
      break;
    case "measureGroundDistance":
      measureDistance("Ground");
      break;
    case "measureSpaceArea":
      measureArea("Space");
      break;
    case "measureGroundArea":
      measureArea("Ground");
      break;
    case "measureHeight":
      measureHeight("Space");
      break;
  }
}

const measureDistance = (measureMode)=>{
  if (handlerDistance.value?.clearMeasure) {
    handlerDistance.value.clearMeasure();
  }
  measureUnit.value = distanceUnits;
  unitValue.value = "Km";
  // 鼠标箭头提示信息
  const ele = document.createElement("p");
  ele.textContent = "单击地图绘制点，双击结束绘制！";

  const infoBox = createPopup(viewer3d.value, ele);
  const option = {
    viewer3d: viewer3d.value,
    infoBox: infoBox,
    measureMode: measureMode
  };
  handlerDistance.value = new CesiumMeasureDistanceClass(option);

  handlerDistance.value.initEvent();
  viewer3d.value.container.addEventListener("valueChange", (res) => {
    measureResult.value = res.detail;
  });
}
const measureArea = (measureMode)=>{
  if (handlerArea.value?.clearMeasure) {
    handlerArea.value.clearMeasure();
  }
  measureUnit.value = areaUnits;
  unitValue.value = "m2";
  // 鼠标箭头提示信息
  const ele = document.createElement("p");
  ele.textContent = "单击地图绘制点，双击结束绘制！";
  const infoBox = createPopup(viewer3d.value, ele);
  const option = {
    viewer3d: viewer3d.value,
    infoBox: infoBox,
    measureMode: measureMode
  };
  handlerArea.value = new CesiumMeasureAreaClass(option);

  handlerArea.value.initEvent();
  viewer3d.value.container.addEventListener("valueChange", (res) => {
    measureResult.value = res.detail;
  });
}

const measureHeight = (measureMode)=> {
  if (handlerHeight.value?.clearMeasure) {
    handlerHeight.value.clearMeasure();
  }
  measureUnit.value = distanceUnits;
  unitValue.value = "m";
  // 鼠标箭头提示信息
  const ele = document.createElement("p");
  ele.textContent = "";
  const infoBox = createPopup(viewer3d.value, ele);
  const option = {
    viewer3d: viewer3d.value,
    infoBox: infoBox,
    measureMode: measureMode
  };
  handlerHeight.value = new CesiumMeasureHeightClass(option);

  handlerHeight.value.initEvent();
  viewer3d.value.container.addEventListener("valueChange", (res) => {
    measureResult.value = res.detail;
  });
}

// 创建信息弹窗
const createPopup = (viewer3d, ele)=> {
  const infoBox = document.createElement("div");
  infoBox.className = "SuperMap3D-info-box";
  // infoBox.textContent = "单击开始绘制！"
  infoBox.style.position = "absolute";
  // infoBox.style.top = windowPosition.y + "px"
  // infoBox.style.left = windowPosition.x + 20 + "px"
  infoBox.appendChild(ele);
  infoBox.hidden = true;
  viewer3d.container.appendChild(infoBox);
  return infoBox;
}
const clearResult = ()=> {
  measureResult.value = 0;
  actionIndex.value = -1;
  viewer3d.value.entities.removeAll();
}


</script>

<style scoped lang="scss">
.measure-wrap {
  color: #fff;
  background-image: url("@/assets/images/map/tool.png");
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-size: cover;
  box-shadow: 0 0 8px 0 #057595;
}
.title {
  padding: 12px;
  font-size: 16px;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  &:hover {
    cursor: all-scroll;
  }
  .title-name {
    float: left;
    span {
      margin-left: 10px;
    }
    .svg-icon {
      color: #30fdff;
    }
  }
  .title-close {
    float: right;
    color: #ccc;
    &:hover {
      cursor: pointer;
      filter: brightness(150%);
      transition-delay: 0.25s;
    }
  }
}
.measure-content {
  padding: 10px;
  width: 100%;
  // background-color: #193b51;
  font-size: 16px;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  .measure-result {
    margin: 10px auto;
    padding: 10px;
    text-align: center;
    border-bottom: 1px solid #a9a9a973;
    .measure-num {
      font-weight: bold;
      font-size: 20px;
      color: #dedede;
      margin-right: 5px;
    }
    .svg-icon {
      margin-left: 20px;
      &:hover {
        cursor: pointer;
      }
    }
  }
  .measure-type {
    display: flex;
    margin: 10px 0;
    justify-content: space-between;
    /** 换行 **/
    flex-wrap: wrap;
    align-items: center;
    .measure-display {
      display: inline-block;
    }
    .measure-item {
      background-image: url("@/assets/images/map/measureBtn.png");
      background-position: center center;
      background-repeat: no-repeat;
      background-size: cover;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      // padding: 10px 10px;
      width: 90px;
      height: 54px;
      margin-top: 10px;
      color: #d1d1d1;
      text-align: center;
      font-size: 14px;
      &:hover {
        cursor: pointer;
        color: #fff;
        filter: brightness(110%) opacity(100%);
        transition: all 0.5s ease-in;
        // background: linear-gradient(to bottom right, #00baff, #00cffa);
        border-radius: 4px;
      }
      svg {
        width: 1.25em;
        height: 1.25em;
      }
    }
    .measure-item-active {
      cursor: pointer;
      color: #fff;
      filter: brightness(110%) opacity(100%);
      transition: all 0.5s ease-in;
      // background: linear-gradient(to bottom right, #00baff, #00cffa);
      background-image: url("@/assets/images/map/measureBtnIs.png");
      background-position: center center;
      background-repeat: no-repeat;
      background-size: cover;
      border-radius: 4px;
      box-shadow: 0 0 8px #ffffff;
      width: 90px;
      height: 54px;
    }
  }
  .measure-clear {
    margin-top: 20px;
    padding: 10px;
    background-color: #00cffa;
    border-color: #fff;
    text-align: center;
    border-radius: 4px;
    color: #ffffff;
    &:hover {
      cursor: pointer;
      color: #fff;
      filter: brightness(110%) opacity(100%);
      transition: all 0.5s ease-in;
      background: linear-gradient(to bottom right, #00baff, #00cffa);
    }
    svg {
      margin-right: 10px;
    }
  }
}

.SuperMap3D-info-box {
  font-size: 16px;
}
:deep(.el-input--suffix .el-input__inner) {
  background-color: #0144a0;
  color: #fff;
  border: 1px solid #2aa2ff;
}
</style>
