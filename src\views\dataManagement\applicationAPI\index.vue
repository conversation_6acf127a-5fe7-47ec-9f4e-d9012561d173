<template>
  <div class="container">
    <div v-if="!showDetail" class="project-list">
      <el-card shadow="never" class="result-card">
        <el-row :gutter="10" class="mb8 toolbar-row" justify="space-between">
          <el-col :span="12">
            <el-space>
              <el-button type="success" plain icon="Promotion" @click="handleApplication()">申请</el-button>
              <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
            </el-space>
          </el-col>
          <el-col :span="12" class="text-right">
            <el-tooltip effect="dark" content="刷新" placement="top">
              <el-button circle icon="Refresh" @click="handleQuery()" />
            </el-tooltip>
          </el-col>
        </el-row>
        <div class="card-container">
          <el-card
              v-for="(item, index) in reserveProjectList"
              :key="index"
              shadow="hover"
              class="project-card"
          >
            <div class="card-header">
              <span>{{StatusUtils.photoQueryTypeToChinese(item.type)}}</span>
            </div>
            <div class="card-body">
              <p><strong>appId：</strong>{{ item.appId }}</p>
              <p><strong>有效期至：</strong>{{ item.validDateEnd }}</p>
              <p><strong>创建时间：</strong>{{ item.createTime }}</p>
            </div>
          </el-card>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup name="ReserveProject">
import {currentUser, partApiApplyExport, partApiApplyPartApiApply} from "@/api/uav/partApiApply.js";
import {StatusUtils} from "@/constants/flightTask.js";

const showDetail = ref(false);
const reserveProjectList = ref([]);
const loading = ref(true);
const total = ref(0);
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    params: {},
  },
  rules: {
    id: [{ required: true, message: "$comment不能为空", trigger: "blur" }],
  },
});
const { queryParams, form, rules } = toRefs(data);

/** 查询储备项目列表 */
const getList = async () => {
  try {
    loading.value = true; // 开始加载

    // 调用接口获取数据
    const res = await currentUser();

    // 确保 reserveProjectList 是一个数组
    if (Array.isArray(res.data)) {
      // 如果已经是数组，直接赋值
      reserveProjectList.value = res.data;
    } else if (res.data && typeof res.data === 'object') {
      // 如果是对象，将其封装为数组
      reserveProjectList.value = [res.data];
    } else {
      // 如果既不是数组也不是对象，初始化为空数组
      console.warn('Unexpected data format:', res.data);
      reserveProjectList.value = [];
    }
  } catch (error) {
    // 捕获错误并处理
    console.error('Error fetching data:', error);
    reserveProjectList.value = []; // 确保列表初始化为空数组
  } finally {
    loading.value = false; // 结束加载
  }
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 导出 */
const handleExport = async () => {
    await partApiApplyExport();
};

/** 申请 */
const handleApplication = async () => {
  await partApiApplyPartApiApply(1);
  //try {
  //   const { data } = await this.$modal.confirm('确定要申请吗？', '提示', {confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'});
  //   console.log("data" + data)
  //   if (data === 'confirm') {
  //     const loadingInstance = this.ElLoading.service({
  //       lock: true,
  //       text: '正在提交申请，请稍候...',
  //       background: 'rgba(0, 0, 0, 0.7)',
  //     });
  //
  //     await partApiApplyPartApiApply();
  //
  //     proxy.$modal.msgSuccess("申请提交成功！");
  //     this.getList(); // 重新加载数据
  //   }
  // } catch (error) {
  //   proxy.$modal.msgError("申请失败：" + (error.message || "未知错误"));
  // }
};

onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.container {
  padding: 20px;
  background-color: #f5f7fa;
}

.result-card {
  border-radius: 8px;

  :deep(.el-card__body) {
    padding: 0;
  }
}

.pagination-container {
  padding: 16px;
  text-align: right;
}

.toolbar-row {
  width: 100%;
  padding: 0 16px;

  .text-right {
    text-align: right;
    padding-right: 16px;
  }

  :deep(.el-space) {
    display: flex;
    align-items: center;
  }
}

:deep(.el-table) {
  margin-top: 16px;

  .el-button {
    padding: 5px 12px;
  }
}

.card-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start; /* 靠左对齐 */
  gap: 20px; /* 卡片之间的间距 */
  padding: 20px;
}

.project-card {
  width: 400px; /* 固定卡片宽度，适合响应式设计 */
  border-radius: 12px; /* 圆角 */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* 更柔和的阴影 */
  transition: transform 0.2s ease-in-out;
}

.project-card:hover {
  transform: translateY(-5px); /* 悬停时轻微上移 */
}

.card-header {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #333;
  text-align: center;
}

.card-body p {
  margin: 8px 0;
  color: #555;
}
:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: #fafafa;
}

:deep(.el-table th.el-table__cell) {
  background-color: #f5f7fa;
}
</style>
