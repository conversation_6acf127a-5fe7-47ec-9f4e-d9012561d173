define(["./when-b60132fc","./Rectangle-dee65d21","./arrayFill-4513d7ad","./buildModuleUrl-9085faaa","./Cartesian2-db21342c","./Cartographic-3309dd0d","./Check-7b2a090c","./ComponentDatatype-c140a87d","./GeometryAttribute-c65394ac","./GeometryAttributes-252e9929","./GeometryInstance-6bd4503d","./GeometryOffsetAttribute-fbeb6f1a","./GeometryPipeline-7a733318","./IndexDatatype-8a5eead4","./Math-119be1a3","./FeatureDetection-806b12f0","./PolygonPipeline-d83979ed","./RectangleGeometryLibrary-d0377774","./VertexFormat-6446fca0","./Event-16a2dfbf","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Cartesian4-3ca25aab","./AttributeCompression-0a087f75","./EncodedCartesian3-f1396b05","./IntersectionTests-0d6905a3","./Plane-a3d8b3d2","./earcut-2.2.1-20c8012f","./EllipsoidRhumbLine-30b5229b"],(function(t,e,a,r,n,i,o,s,l,u,c,d,m,p,g,y,f,h,v,b,_,A,x,w,C,R,E,F,G){"use strict";var P=new i.Cartesian3,V=new i.Cartesian3,L=new i.Cartesian3,D=new i.Cartesian3,M=new e.Rectangle,O=new n.Cartesian2,T=new r.BoundingSphere,N=new r.BoundingSphere;function S(t,e){var a=new l.Geometry({attributes:new u.GeometryAttributes,primitiveType:y.PrimitiveType.TRIANGLES});return a.attributes.position=new l.GeometryAttribute({componentDatatype:s.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:e.positions}),t.normal&&(a.attributes.normal=new l.GeometryAttribute({componentDatatype:s.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:e.normals})),t.tangent&&(a.attributes.tangent=new l.GeometryAttribute({componentDatatype:s.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:e.tangents})),t.bitangent&&(a.attributes.bitangent=new l.GeometryAttribute({componentDatatype:s.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:e.bitangents})),a}var I=new i.Cartesian3,k=new i.Cartesian3;function H(t,e){var a=t._vertexFormat,r=t._ellipsoid,n=e.height,o=e.width,u=e.northCap,c=e.southCap,d=0,m=n,g=n,f=0;u&&(d=1,g-=1,f+=1),c&&(m-=1,g-=1,f+=1),f+=o*g;for(var v=a.position?new Float64Array(3*f):void 0,b=a.st?new Float32Array(2*f):void 0,_=0,A=0,x=P,w=O,C=Number.MAX_VALUE,R=Number.MAX_VALUE,E=-Number.MAX_VALUE,F=-Number.MAX_VALUE,G=d;G<m;++G)for(var M=0;M<o;++M)h.RectangleGeometryLibrary.computePosition(e,r,a.st,G,M,x,w),v[_++]=x.x,v[_++]=x.y,v[_++]=x.z,a.st&&(b[A++]=w.x,b[A++]=w.y,C=Math.min(C,w.x),R=Math.min(R,w.y),E=Math.max(E,w.x),F=Math.max(F,w.y));if(u&&(h.RectangleGeometryLibrary.computePosition(e,r,a.st,0,0,x,w),v[_++]=x.x,v[_++]=x.y,v[_++]=x.z,a.st&&(b[A++]=w.x,b[A++]=w.y,C=w.x,R=w.y,E=w.x,F=w.y)),c&&(h.RectangleGeometryLibrary.computePosition(e,r,a.st,n-1,0,x,w),v[_++]=x.x,v[_++]=x.y,v[_]=x.z,a.st&&(b[A++]=w.x,b[A]=w.y,C=Math.min(C,w.x),R=Math.min(R,w.y),E=Math.max(E,w.x),F=Math.max(F,w.y))),a.st&&(C<0||R<0||E>1||F>1))for(var T=0;T<b.length;T+=2)b[T]=(b[T]-C)/(E-C),b[T+1]=(b[T+1]-R)/(F-R);var N=function(t,e,a,r){var n=t.length,o=e.normal?new Float32Array(n):void 0,s=e.tangent?new Float32Array(n):void 0,l=e.bitangent?new Float32Array(n):void 0,u=0,c=D,d=L,m=V;if(e.normal||e.tangent||e.bitangent)for(var p=0;p<n;p+=3){var g=i.Cartesian3.fromArray(t,p,P),f=u+1,h=u+2;m=a.geodeticSurfaceNormal(g,m),(e.tangent||e.bitangent)&&(i.Cartesian3.cross(i.Cartesian3.UNIT_Z,m,d),y.Matrix3.multiplyByVector(r,d,d),i.Cartesian3.normalize(d,d),e.bitangent&&i.Cartesian3.normalize(i.Cartesian3.cross(m,d,c),c)),e.normal&&(o[u]=m.x,o[f]=m.y,o[h]=m.z),e.tangent&&(s[u]=d.x,s[f]=d.y,s[h]=d.z),e.bitangent&&(l[u]=c.x,l[f]=c.y,l[h]=c.z),u+=3}return S(e,{positions:t,normals:o,tangents:s,bitangents:l})}(v,a,r,e.tangentRotationMatrix),I=6*(o-1)*(g-1);u&&(I+=3*(o-1)),c&&(I+=3*(o-1));var k,H=p.IndexDatatype.createTypedArray(f,I),z=0,U=0;for(k=0;k<g-1;++k){for(var B=0;B<o-1;++B){var Y=z,q=Y+o,X=q+1,Q=Y+1;H[U++]=Y,H[U++]=q,H[U++]=Q,H[U++]=Q,H[U++]=q,H[U++]=X,++z}++z}if(u||c){var W,J,j=f-1,Z=f-1;if(u&&c&&(j=f-2),z=0,u)for(k=0;k<o-1;k++)J=(W=z)+1,H[U++]=j,H[U++]=W,H[U++]=J,++z;if(c)for(z=(g-1)*o,k=0;k<o-1;k++)J=(W=z)+1,H[U++]=W,H[U++]=Z,H[U++]=J,++z}return N.indices=H,a.st&&(N.attributes.st=new l.GeometryAttribute({componentDatatype:s.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:b})),N}function z(t,e,a,r,n){return t[e++]=r[a],t[e++]=r[a+1],t[e++]=r[a+2],t[e++]=n[a],t[e++]=n[a+1],t[e]=n[a+2],t}function U(t,e,a,r){return t[e++]=r[a],t[e++]=r[a+1],t[e++]=r[a],t[e]=r[a+1],t}var B=new v.VertexFormat;function Y(e,r){var n,o=e._shadowVolume,u=e._offsetAttribute,y=e._vertexFormat,h=e._extrudedHeight,b=e._surfaceHeight,_=e._ellipsoid,A=r.height,x=r.width;if(o){var w=v.VertexFormat.clone(y,B);w.normal=!0,e._vertexFormat=w}var C=H(e,r);o&&(e._vertexFormat=y);var R=f.PolygonPipeline.scaleToGeodeticHeight(C.attributes.position.values,b,_,!1),E=(R=new Float64Array(R)).length,F=2*E,G=new Float64Array(F);G.set(R);var M=f.PolygonPipeline.scaleToGeodeticHeight(C.attributes.position.values,h,_);G.set(M,E),C.attributes.position.values=G;var O,T,N,Y=y.normal?new Float32Array(F):void 0,q=y.tangent?new Float32Array(F):void 0,X=y.bitangent?new Float32Array(F):void 0,Q=y.st?new Float32Array(F/3*2):void 0;if(y.normal){for(T=C.attributes.normal.values,Y.set(T),n=0;n<E;n++)T[n]=-T[n];Y.set(T,E),C.attributes.normal.values=Y}if(o){T=C.attributes.normal.values,y.normal||(C.attributes.normal=void 0);var W=new Float32Array(F);for(n=0;n<E;n++)T[n]=-T[n];W.set(T,E),C.attributes.extrudeDirection=new l.GeometryAttribute({componentDatatype:s.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:W})}var J=t.defined(u);if(J){var j=E/3*2,Z=new Uint8Array(j);u===d.GeometryOffsetAttribute.TOP?Z=a.arrayFill(Z,1,0,j/2):(N=u===d.GeometryOffsetAttribute.NONE?0:1,Z=a.arrayFill(Z,N)),C.attributes.applyOffset=new l.GeometryAttribute({componentDatatype:s.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:Z})}if(y.tangent){var K=C.attributes.tangent.values;for(q.set(K),n=0;n<E;n++)K[n]=-K[n];q.set(K,E),C.attributes.tangent.values=q}if(y.bitangent){var $=C.attributes.bitangent.values;X.set($),X.set($,E),C.attributes.bitangent.values=X}y.st&&(O=C.attributes.st.values,Q.set(O),Q.set(O,E/3*2),C.attributes.st.values=Q);var tt=C.indices,et=tt.length,at=E/3,rt=p.IndexDatatype.createTypedArray(F/3,2*et);for(rt.set(tt),n=0;n<et;n+=3)rt[n+et]=tt[n+2]+at,rt[n+1+et]=tt[n+1]+at,rt[n+2+et]=tt[n]+at;C.indices=rt;var nt=r.northCap,it=r.southCap,ot=A,st=2,lt=0,ut=4,ct=4;nt&&(st-=1,ot-=1,lt+=1,ut-=2,ct-=1),it&&(st-=1,ot-=1,lt+=1,ut-=2,ct-=1);var dt=2*((lt+=st*x+2*ot-ut)+ct),mt=new Float64Array(3*dt),pt=o?new Float32Array(3*dt):void 0,gt=J?new Uint8Array(dt):void 0,yt=y.st?new Float32Array(2*dt):void 0,ft=u===d.GeometryOffsetAttribute.TOP;J&&!ft&&(N=u===d.GeometryOffsetAttribute.ALL?1:0,gt=a.arrayFill(gt,N));var ht,vt=0,bt=0,_t=0,At=0,xt=x*ot;for(n=0;n<xt;n+=x)mt=z(mt,vt,ht=3*n,R,M),vt+=6,y.st&&(yt=U(yt,bt,2*n,O),bt+=4),o&&(_t+=3,pt[_t++]=T[ht],pt[_t++]=T[ht+1],pt[_t++]=T[ht+2]),ft&&(gt[At++]=1,At+=1);if(it){var wt=nt?xt+1:xt;for(ht=3*wt,n=0;n<2;n++)mt=z(mt,vt,ht,R,M),vt+=6,y.st&&(yt=U(yt,bt,2*wt,O),bt+=4),o&&(_t+=3,pt[_t++]=T[ht],pt[_t++]=T[ht+1],pt[_t++]=T[ht+2]),ft&&(gt[At++]=1,At+=1)}else for(n=xt-x;n<xt;n++)mt=z(mt,vt,ht=3*n,R,M),vt+=6,y.st&&(yt=U(yt,bt,2*n,O),bt+=4),o&&(_t+=3,pt[_t++]=T[ht],pt[_t++]=T[ht+1],pt[_t++]=T[ht+2]),ft&&(gt[At++]=1,At+=1);for(n=xt-1;n>0;n-=x)mt=z(mt,vt,ht=3*n,R,M),vt+=6,y.st&&(yt=U(yt,bt,2*n,O),bt+=4),o&&(_t+=3,pt[_t++]=T[ht],pt[_t++]=T[ht+1],pt[_t++]=T[ht+2]),ft&&(gt[At++]=1,At+=1);if(nt){var Ct=xt;for(ht=3*Ct,n=0;n<2;n++)mt=z(mt,vt,ht,R,M),vt+=6,y.st&&(yt=U(yt,bt,2*Ct,O),bt+=4),o&&(_t+=3,pt[_t++]=T[ht],pt[_t++]=T[ht+1],pt[_t++]=T[ht+2]),ft&&(gt[At++]=1,At+=1)}else for(n=x-1;n>=0;n--)mt=z(mt,vt,ht=3*n,R,M),vt+=6,y.st&&(yt=U(yt,bt,2*n,O),bt+=4),o&&(_t+=3,pt[_t++]=T[ht],pt[_t++]=T[ht+1],pt[_t++]=T[ht+2]),ft&&(gt[At++]=1,At+=1);var Rt=function(t,e,a){var r=t.length,n=e.normal?new Float32Array(r):void 0,o=e.tangent?new Float32Array(r):void 0,s=e.bitangent?new Float32Array(r):void 0,l=0,u=0,c=0,d=!0,m=D,p=L,y=V;if(e.normal||e.tangent||e.bitangent)for(var f=0;f<r;f+=6){var h=i.Cartesian3.fromArray(t,f,P),v=i.Cartesian3.fromArray(t,(f+6)%r,I);if(d){var b=i.Cartesian3.fromArray(t,(f+3)%r,k);i.Cartesian3.subtract(v,h,v),i.Cartesian3.subtract(b,h,b),y=i.Cartesian3.normalize(i.Cartesian3.cross(b,v,y),y),d=!1}i.Cartesian3.equalsEpsilon(v,h,g.CesiumMath.EPSILON10)&&(d=!0),(e.tangent||e.bitangent)&&(m=a.geodeticSurfaceNormal(h,m),e.tangent&&(p=i.Cartesian3.normalize(i.Cartesian3.cross(m,y,p),p))),e.normal&&(n[l++]=y.x,n[l++]=y.y,n[l++]=y.z,n[l++]=y.x,n[l++]=y.y,n[l++]=y.z),e.tangent&&(o[u++]=p.x,o[u++]=p.y,o[u++]=p.z,o[u++]=p.x,o[u++]=p.y,o[u++]=p.z),e.bitangent&&(s[c++]=m.x,s[c++]=m.y,s[c++]=m.z,s[c++]=m.x,s[c++]=m.y,s[c++]=m.z)}return S(e,{positions:t,normals:n,tangents:o,bitangents:s})}(mt,y,_);y.st&&(Rt.attributes.st=new l.GeometryAttribute({componentDatatype:s.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:yt})),o&&(Rt.attributes.extrudeDirection=new l.GeometryAttribute({componentDatatype:s.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:pt})),J&&(Rt.attributes.applyOffset=new l.GeometryAttribute({componentDatatype:s.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:gt}));var Et,Ft,Gt,Pt,Vt=p.IndexDatatype.createTypedArray(dt,6*lt);E=mt.length/3;var Lt=0;for(n=0;n<E-1;n+=2){Pt=((Et=n)+2)%E;var Dt=i.Cartesian3.fromArray(mt,3*Et,I),Mt=i.Cartesian3.fromArray(mt,3*Pt,k);i.Cartesian3.equalsEpsilon(Dt,Mt,g.CesiumMath.EPSILON10)||(Gt=((Ft=(Et+1)%E)+2)%E,Vt[Lt++]=Et,Vt[Lt++]=Ft,Vt[Lt++]=Pt,Vt[Lt++]=Pt,Vt[Lt++]=Ft,Vt[Lt++]=Gt)}return Rt.indices=Vt,(Rt=m.GeometryPipeline.combineInstances([new c.GeometryInstance({geometry:C}),new c.GeometryInstance({geometry:Rt})]))[0]}var q=[new i.Cartesian3,new i.Cartesian3,new i.Cartesian3,new i.Cartesian3],X=new i.Cartographic,Q=new i.Cartographic;function W(t,a,r,n,i){if(0===r)return e.Rectangle.clone(t,i);var o=h.RectangleGeometryLibrary.computeOptions(t,a,r,0,M,X),s=o.height,l=o.width,u=q;return h.RectangleGeometryLibrary.computePosition(o,n,!1,0,0,u[0]),h.RectangleGeometryLibrary.computePosition(o,n,!1,0,l-1,u[1]),h.RectangleGeometryLibrary.computePosition(o,n,!1,s-1,0,u[2]),h.RectangleGeometryLibrary.computePosition(o,n,!1,s-1,l-1,u[3]),e.Rectangle.fromCartesianArray(u,n,i)}function J(a){var r=(a=t.defaultValue(a,t.defaultValue.EMPTY_OBJECT)).rectangle,n=t.defaultValue(a.height,0),i=t.defaultValue(a.extrudedHeight,n);this._rectangle=e.Rectangle.clone(r),this._granularity=t.defaultValue(a.granularity,g.CesiumMath.RADIANS_PER_DEGREE),this._ellipsoid=e.Ellipsoid.clone(t.defaultValue(a.ellipsoid,e.Ellipsoid.WGS84)),this._surfaceHeight=Math.max(n,i),this._rotation=t.defaultValue(a.rotation,0),this._stRotation=t.defaultValue(a.stRotation,0),this._vertexFormat=v.VertexFormat.clone(t.defaultValue(a.vertexFormat,v.VertexFormat.DEFAULT)),this._extrudedHeight=Math.min(n,i),this._shadowVolume=t.defaultValue(a.shadowVolume,!1),this._workerName="createRectangleGeometry",this._offsetAttribute=a.offsetAttribute,this._rotatedRectangle=void 0,this._textureCoordinateRotationPoints=void 0}J.packedLength=e.Rectangle.packedLength+e.Ellipsoid.packedLength+v.VertexFormat.packedLength+7,J.pack=function(a,r,n){return n=t.defaultValue(n,0),e.Rectangle.pack(a._rectangle,r,n),n+=e.Rectangle.packedLength,e.Ellipsoid.pack(a._ellipsoid,r,n),n+=e.Ellipsoid.packedLength,v.VertexFormat.pack(a._vertexFormat,r,n),n+=v.VertexFormat.packedLength,r[n++]=a._granularity,r[n++]=a._surfaceHeight,r[n++]=a._rotation,r[n++]=a._stRotation,r[n++]=a._extrudedHeight,r[n++]=a._shadowVolume?1:0,r[n]=t.defaultValue(a._offsetAttribute,-1),r};var j=new e.Rectangle,Z=e.Ellipsoid.clone(e.Ellipsoid.UNIT_SPHERE),K={rectangle:j,ellipsoid:Z,vertexFormat:B,granularity:void 0,height:void 0,rotation:void 0,stRotation:void 0,extrudedHeight:void 0,shadowVolume:void 0,offsetAttribute:void 0};J.unpack=function(a,r,n){r=t.defaultValue(r,0);var i=e.Rectangle.unpack(a,r,j);r+=e.Rectangle.packedLength;var o=e.Ellipsoid.unpack(a,r,Z);r+=e.Ellipsoid.packedLength;var s=v.VertexFormat.unpack(a,r,B);r+=v.VertexFormat.packedLength;var l=a[r++],u=a[r++],c=a[r++],d=a[r++],m=a[r++],p=1===a[r++],g=a[r];return t.defined(n)?(n._rectangle=e.Rectangle.clone(i,n._rectangle),n._ellipsoid=e.Ellipsoid.clone(o,n._ellipsoid),n._vertexFormat=v.VertexFormat.clone(s,n._vertexFormat),n._granularity=l,n._surfaceHeight=u,n._rotation=c,n._stRotation=d,n._extrudedHeight=m,n._shadowVolume=p,n._offsetAttribute=-1===g?void 0:g,n):(K.granularity=l,K.height=u,K.rotation=c,K.stRotation=d,K.extrudedHeight=m,K.shadowVolume=p,K.offsetAttribute=-1===g?void 0:g,new J(K))},J.computeRectangle=function(a,r){var n=(a=t.defaultValue(a,t.defaultValue.EMPTY_OBJECT)).rectangle,i=t.defaultValue(a.granularity,g.CesiumMath.RADIANS_PER_DEGREE),o=t.defaultValue(a.ellipsoid,e.Ellipsoid.WGS84);return W(n,i,t.defaultValue(a.rotation,0),o,r)};var $=new y.Matrix3,tt=new l.Quaternion,et=new i.Cartographic;J.createGeometry=function(n){if(!g.CesiumMath.equalsEpsilon(n._rectangle.north,n._rectangle.south,g.CesiumMath.EPSILON10)&&!g.CesiumMath.equalsEpsilon(n._rectangle.east,n._rectangle.west,g.CesiumMath.EPSILON10)){var i=n._rectangle,o=n._ellipsoid,u=n._rotation,c=n._stRotation,m=n._vertexFormat,p=h.RectangleGeometryLibrary.computeOptions(i,n._granularity,u,c,M,X,Q),v=$;if(0!==c||0!==u){var b=e.Rectangle.center(i,et),_=o.geodeticSurfaceNormalCartographic(b,I);l.Quaternion.fromAxisAngle(_,-c,tt),y.Matrix3.fromQuaternion(tt,v)}else y.Matrix3.clone(y.Matrix3.IDENTITY,v);var A,x,w=n._surfaceHeight,C=n._extrudedHeight,R=!g.CesiumMath.equalsEpsilon(w,C,0,g.CesiumMath.EPSILON2);if(p.lonScalar=1/n._rectangle.width,p.latScalar=1/n._rectangle.height,p.tangentRotationMatrix=v,i=n._rectangle,R){A=Y(n,p);var E=r.BoundingSphere.fromRectangle3D(i,o,w,N),F=r.BoundingSphere.fromRectangle3D(i,o,C,T);x=r.BoundingSphere.union(E,F)}else{if((A=H(n,p)).attributes.position.values=f.PolygonPipeline.scaleToGeodeticHeight(A.attributes.position.values,w,o,!1),t.defined(n._offsetAttribute)){var G=A.attributes.position.values.length,P=new Uint8Array(G/3),V=n._offsetAttribute===d.GeometryOffsetAttribute.NONE?0:1;a.arrayFill(P,V),A.attributes.applyOffset=new l.GeometryAttribute({componentDatatype:s.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:P})}x=r.BoundingSphere.fromRectangle3D(i,o,w)}return m.position||delete A.attributes.position,new l.Geometry({attributes:A.attributes,indices:A.indices,primitiveType:A.primitiveType,boundingSphere:x,offsetAttribute:n._offsetAttribute})}},J.createShadowVolume=function(t,e,a){var r=t._granularity,n=t._ellipsoid,i=e(r,n),o=a(r,n);return new J({rectangle:t._rectangle,rotation:t._rotation,ellipsoid:n,stRotation:t._stRotation,granularity:r,extrudedHeight:o,height:i,vertexFormat:v.VertexFormat.POSITION_ONLY,shadowVolume:!0})};var at=new e.Rectangle,rt=[new n.Cartesian2,new n.Cartesian2,new n.Cartesian2],nt=new l.Matrix2,it=new i.Cartographic;return Object.defineProperties(J.prototype,{rectangle:{get:function(){return t.defined(this._rotatedRectangle)||(this._rotatedRectangle=W(this._rectangle,this._granularity,this._rotation,this._ellipsoid)),this._rotatedRectangle}},textureCoordinateRotationPoints:{get:function(){return t.defined(this._textureCoordinateRotationPoints)||(this._textureCoordinateRotationPoints=function(t){if(0===t._stRotation)return[0,0,0,1,1,0];var a=e.Rectangle.clone(t._rectangle,at),r=t._granularity,i=t._ellipsoid,o=W(a,r,t._rotation-t._stRotation,i,at),s=rt;s[0].x=o.west,s[0].y=o.south,s[1].x=o.west,s[1].y=o.north,s[2].x=o.east,s[2].y=o.south;for(var u=t.rectangle,c=l.Matrix2.fromRotation(t._stRotation,nt),d=e.Rectangle.center(u,it),m=0;m<3;++m){var p=s[m];p.x-=d.longitude,p.y-=d.latitude,l.Matrix2.multiplyByVector(c,p,p),p.x+=d.longitude,p.y+=d.latitude,p.x=(p.x-u.west)/u.width,p.y=(p.y-u.south)/u.height}var g=s[0],y=s[1],f=s[2],h=new Array(6);return n.Cartesian2.pack(g,h),n.Cartesian2.pack(y,h,2),n.Cartesian2.pack(f,h,4),h}(this)),this._textureCoordinateRotationPoints}}}),function(a,r){return t.defined(r)&&(a=J.unpack(a,r)),a._ellipsoid=e.Ellipsoid.clone(a._ellipsoid),a._rectangle=e.Rectangle.clone(a._rectangle),J.createGeometry(a)}}));
