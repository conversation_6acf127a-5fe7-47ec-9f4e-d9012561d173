<template>
  <div class="navbar">
    <h2 class="title">储备项目与地块巡查管理系统</h2>
    <top-nav
      id="topmenu-container"
      class="topmenu-container"
      v-if="settingsStore.topNav"
    />

    <div class="right-menu cursor-default">
      <el-dropdown
        class="flx-center mr30 right-menu-drop"
        trigger="click"
      >
        <span class="el-dropdown-link">
          <!--            <el-icon color="#fff">-->
          <!--              <User />-->
          <!--            </el-icon>-->
          {{ userStore.user.nickName }}
          <el-icon class="el-icon--right el-icon--right-down">
            <arrow-down />
          </el-icon>
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item>
              <router-link
                to="/user/profile"
                class="flx-center"
              >
                <el-icon><Setting /></el-icon>
                <p class="shezhi">设置</p>
              </router-link>
            </el-dropdown-item>
            <el-dropdown-item>
              <div
                class="flx-center"
                @click="logout"
              >
                <el-icon class="icon">
                  <CloseBold />
                </el-icon>
                <p class="shezhi">注销</p>
              </div>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup>
import { ElMessageBox } from "element-plus";
import TopNav from "@/components/TopNav";
import useAppStore from "@/store/modules/app";
import useUserStore from "@/store/modules/user";
import useSettingsStore from "@/store/modules/settings";
import { initSSE } from "@/utils/sse";

const appStore = useAppStore();
const userStore = useUserStore();
const settingsStore = useSettingsStore();

function toggleSideBar() {
  appStore.toggleSideBar();
}

function handleCommand(command) {
  switch (command) {
    case "setLayout":
      setLayout();
      break;
    case "logout":
      logout();
      break;
    default:
      break;
  }
}

function logout() {
  ElMessageBox.confirm("确定注销并退出系统吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      // initSSE(import.meta.env.VITE_APP_BASE_API + '/resource/sse/close');
      userStore.logOut().then(() => {
        // location.href = import.meta.env.VITE_APP_CONTEXT_PATH + 'index';
        location.href = import.meta.env.VITE_APP_CONTEXT_PATH + "login";
      });
    })
    .catch(() => {});
}

const emits = defineEmits(["setLayout"]);
function setLayout() {
  emits("setLayout");
}
</script>

<style lang="scss" scoped>
@import "@/styles/variables.module.scss";

.navbar {
  width: 100%;
  height: $topBarHeight;
  overflow: hidden;
  position: relative;
  //background: url("@/assets/images/header.png") no-repeat;
  background: url("@/assets/images/top.png") no-repeat;
  background-size: cover;
  // background: rgb(51.2, 126.4, 204);
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  display: flex;
  align-items: center;

  .title {
    margin-left: 30px;
    color: #fff;
    letter-spacing: 2px;
    //margin-right: 170px;
    // font-size: 18px;
  }

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    // float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }
  .shezhi {
    color: #ffffff;
    margin: 10px;
  }
  .icon {
    color: #fff;
  }
  :deep(.topmenu-container.el-menu--horizontal > .el-menu-item) {
    float: left;
    height: 70px !important;
    width: 162px !important;
    line-height: 35px !important;
    padding: 5px 5px !important;
    display: flex;
    flex-direction: column;
    justify-content: end;
    font-size: 16px;
    margin: 0 !important;
    border-bottom: 0px;
    background: url(/src/assets/images/toop_02.png) no-repeat;
  }
  :deep(.topmenu-container.el-menu--horizontal > .el-menu-item.is-active) {
    //background: url(/src/assets/images/tope.png) no-repeat;
    background: #2077ff;
    font-size: bold;
  }
  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    margin-left: 50px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    position: absolute;
    right: 0;
    height: 100%;
    display: flex;
    margin-right: 15px;
    &:focus {
      outline: none;
    }
    .right-menu-drop{
      font-size: 25px;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }
    .el-icon--right-down{
      vertical-align: middle;
    }
    .avatar-container {
      margin-right: 40px;

      .avatar-wrapper {
        margin-top: 5px;
        cursor: pointer;
        display: flex;
        height: 70px;
        align-items: end;
        i {
          margin-bottom: 10px;
        }
      }
    }
  }
}
:deep(.el-dropdown-menu__item){
  line-height: 10px!important;
}
.el-dropdown-menu a {
  display: inherit;
}
.el-dropdown-link{
  font-size: 22px;
}
.el-dropdown .el-dropdown-link {
    color: #91c5fb !important;
}
</style>
