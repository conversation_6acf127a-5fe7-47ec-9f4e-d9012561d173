<p align="center">
	<img alt="logo" src="https://oscimg.oschina.net/oscnet/up-d3d0a9303e11d522a06cd263f3079027715.png">
</p>
<h1 align="center" style="margin: 30px 0 30px; font-weight: bold;">RuoYi v3.8.8</h1>
<h4 align="center">基于SpringBoot+Vue3前后端分离的Java快速开发框架</h4>
<p align="center">
	<a href="https://gitee.com/y_project/RuoYi-Vue/stargazers"><img src="https://gitee.com/y_project/RuoYi-Vue/badge/star.svg?theme=dark"></a>
	<a href="https://gitee.com/y_project/RuoYi-Vue"><img src="https://img.shields.io/badge/RuoYi-v3.8.8-brightgreen.svg"></a>
	<a href="https://gitee.com/y_project/RuoYi-Vue/blob/master/LICENSE"><img src="https://img.shields.io/github/license/mashape/apistatus.svg"></a>
</p>

## 平台简介

* 本仓库为前端技术栈 [Vue3](https://v3.cn.vuejs.org) + [Element Plus](https://element-plus.org/zh-CN) + [Vite](https://cn.vitejs.dev) 版本。
* 配套后端代码仓库地址[RuoYi-Vue](https://gitee.com/y_project/RuoYi-Vue) 或 [RuoYi-Vue-fast](https://github.com/yangzongzhuan/RuoYi-Vue-fast) 版本。
* 前端技术栈（[Vue2](https://cn.vuejs.org) + [Element](https://github.com/ElemeFE/element) + [Vue CLI](https://cli.vuejs.org/zh)），请移步[RuoYi-Vue](https://gitee.com/y_project/RuoYi-Vue/tree/master/ruoyi-ui)。
* 阿里云折扣场：[点我进入](http://aly.ruoyi.vip)，腾讯云秒杀场：[点我进入](http://txy.ruoyi.vip)&nbsp;&nbsp;
* 阿里云优惠券：[点我领取](https://www.aliyun.com/minisite/goods?userCode=brki8iof&share_source=copy_link)，腾讯云优惠券：[点我领取](https://cloud.tencent.com/redirect.php?redirect=1025&cps_key=198c8df2ed259157187173bc7f4f32fd&from=console)&nbsp;&nbsp;

## 前端运行

Node：`18.20.4`

Vue：`3.4.31`

Vite：`5.4.0`

```bash
# 克隆项目
git clone https://github.com/yangzongzhuan/RuoYi-Vue3.git

# 进入项目目录
cd RuoYi-Vue3

# 安装依赖
npm --registry=https://registry.npmmirror.com

# 启动服务
npm run dev

# 构建测试环境 npm build:stage
# 构建生产环境 npm build:prod
# 前端访问地址 http://localhost:80
```

## 数据目录

```angular2html

+---api
+---assets
+---components
+---constants
+---directive
+---hooks
+---layout
+---plugins
+---router
+---store
+---styles
+---utils
\---views
    +---cockpit------------------------------------无人机驾驶舱                        
    +---company------------------------------------公司名录
    +---content------------------------------------内容信息
    +---dataManagement-----------------------------无人机数据管理       
    +---document-----------------------------------文档管理
    +---droneCockpit-------------------------------无人机飞行
    +---error--------------------------------------错误处理
    +---index--------------------------------------······
    +---landSupply---------------------------------土地供应
    +---mailList-----------------------------------······
    +---maps---------------------------------------地图展示
    +---message------------------------------------······
    +---planStorageProject-------------------------拟收储项目
    +---redirect-----------------------------------重定向
    +---relocationPatrol---------------------------征拆项目管理
    +---reservePatrol------------------------------土地储备项目管理
```
