<template>
  <div class="main-content">
    <div v-if="!showDetail">
      <transition>
        <div
          v-show="showSearch"
          class="mb-10"
        >
          <el-card
            shadow="hover"
            class="search"
          >
            <el-form
              ref="queryFormRef"
              :model="queryParams" :inline="true"
              class="query-form"
            >
              <el-form-item
                label="项目名称"
                prop="xmmc"
              >
                <el-input
                  v-model="queryParams.xmmc"
                  placeholder="请输入项目名称"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item
                label="项目编号"
                prop="xmbh"
              >
                <el-input
                  v-model="queryParams.xmbh"
                  placeholder="请输入项目编号"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  icon="Search" @click="handleQuery"
                >搜索
                </el-button>
                <el-button
                  icon="Refresh"
                  @click="resetQuery"
                >重置</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </div>
      </transition>
      <el-row
        :gutter="20"
        class="mb8"
      >
        <el-col :span="1.5">
          <el-button
            type="primary" plain icon="Plus"
            @click="handleAdd"
          >项目录入
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success" plain icon="Upload"
            @click="handleImport"
          > 批量导入（Shape）
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="Upload"
            @click="handleExport"
          >导出
          </el-button>
        </el-col>
        <right-toolbar
          v-model:showSearch="showSearch"
          @queryTable="getList"
        />
      </el-row>
      <el-card class="result-wrap">
        <el-table
          v-loading="loading"
          :data="reserveProjectList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            label="项目名称"
            align="center" prop="xmmc"
          />
          <el-table-column
            label="项目编号"
            align="center" prop="xmbh"
          />
          <el-table-column
            label="项目总面积(公顷)"
            align="center"
            prop="xmzmj"
          />
          <el-table-column
            label="项目坐落"
            align="center" prop="xmzl"
          />
          <el-table-column
            label="行政区名称"
            align="center" prop="xzqmc"
          />
          <el-table-column
            label="项目涉及地块数"
            align="center"
            prop="xmsjdks"
          />
          <el-table-column
            label="收储面积(公顷)"
            align="center" prop="scmj"
          />
          <el-table-column
            label="收储日期"
            align="center"
            prop="scrq"
            width="180"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.scrq, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="是否跨机构联动收储"
            align="center"
            prop="sfkjgldsc"
          />
          <el-table-column
            label="备注"
            align="center" prop="bz"
          />
          <el-table-column
            label="操作"
            align="center"
            width="250"
          >
            <template #default="scope">
              <el-button
                plain
                type="primary" size="small" @click="handleUpdate(scope.row)"
              >查看</el-button>
              <el-button
                plain
                type="primary" size="small" @click="handleUpdate(scope.row)"
              >修改</el-button>
              <el-button
                plain
                type="danger" size="small" @click="handleDelete(scope.row)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>
    <div
      v-else
      class="content"
    >
      <!-- 添加或修改储备项目对话框 -->
      <div class="add-header-title">
        <div class="add-title">{{xmxiTitle}}</div>
        <div
          class="add-title-return"
          @click="cancel"
        >
          <img
            src="../../../assets/images/img-return.png"
            class="listimage"
          >
          <div class="return">返回列表</div>
        </div>
      </div>
      <div class="add-content">
        <!--项目基本信息-->
        <div>
          <div class="content-project">
            <img src="../../../assets/images/left.png" >
            <p>项目基本信息</p>
          </div>
          <el-form
            ref="reserveProjectFormRef"
            :model="form"
            :rules="rules"
            label-width="180px"
            class="information"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item
                  label="项目名称"
                  prop="xmmc"
                >
                  <el-input
                    v-model="form.xmmc"
                    placeholder="请输入项目名称"
                  />
                </el-form-item>
                <el-form-item
                  label="项目编号"
                  prop="xmbh"
                >
                  <el-input
                    v-model="form.xmbh"
                    placeholder="请输入项目编号"
                  />
                </el-form-item>
                <el-form-item
                  label="项目总面积(公顷)"
                  prop="xmzmj"
                >
                  <el-input
                    v-model="form.xmzmj"
                    placeholder="请输入项目总面积(公顷)"
                  >
                    <template #append>公顷</template>
                  </el-input>
                </el-form-item>

                <el-form-item
                  label="项目坐落"
                  prop="xmzl"
                >
                  <el-input
                    v-model="form.xmzl"
                    placeholder="请输入项目坐落"
                  />
                </el-form-item>
                <el-form-item
                  label="收储面积"
                  prop="scmj"
                >
                  <el-input
                    v-model="form.scmj"
                    placeholder="请输入收储面积"
                  >
                    <template #append>公顷</template>
                  </el-input>
                </el-form-item>
                <el-form-item
                  label="项目涉及地块数"
                  prop="xmsjdks"
                >
                  <el-input
                    v-model="form.xmsjdks"
                    placeholder="请输入项目涉及地块数"
                  >
                    <template #append>&nbsp&nbsp&nbsp块</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="行政区"
                  prop="xzqdm"
                >
                  <el-input
                    v-model="form.xzqdm"
                    placeholder="请输入行政区代码"
                    style="width: 100%"
                  />
                </el-form-item>
                <el-form-item
                  label="收储日期"
                  prop="scrq"
                >
                  <el-date-picker
                    clearable
                    v-model="form.scrq"
                    type="datetime"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    placeholder="请选择收储日期"
                    style="width: 100%"
                  />
                </el-form-item>
                <el-form-item
                  label="批准日期"
                  prop="pzrq"
                >
                  <el-date-picker
                    clearable
                    v-model="form.pzrq"
                    type="datetime"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    placeholder="请选择批准日期"
                    style="width: 100%"
                  />
                </el-form-item>
                <el-form-item
                  label="批准文号"
                  prop="pzwh"
                >
                  <el-input
                    v-model="form.pzwh"
                    placeholder="请输入批准文号"
                  />
                </el-form-item>
                <el-form-item
                  label="批准文件名"
                  prop="pzwjm"
                >
                  <el-input
                    v-model="form.pzwjm"
                    placeholder="请输入批准文件名"
                  />
                </el-form-item>
                <el-form-item
                  label="是否跨机构联动收储"
                  prop="sfkjgldsc"
                >
                  <el-input
                    v-model="form.sfkjgldsc"
                    placeholder="请输入是否跨机构联动收储"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="备注"
                  prop="bz"
                >
                  <el-input
                    v-model="form.bz"
                    :rows="2"
                    type="textarea"
                    placeholder="请输入备注"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <!--地块信息-->
        <div>
          <div class="content-title">
            <div class="content-project">
              <img src="../../../assets/images/left.png" >
              <p>地块信息</p>
            </div>
            <div>
              <el-button
                type="primary"
                plain
              >
                <el-icon>
                  <Upload />
                </el-icon>
                导入
              </el-button>
            </div>
          </div>
        </div>

      </div>
      <div class="footer-button">
        <el-button
          :loading="buttonLoading"
          type="primary"
          @click="submitForm"
          plain
        >
          <el-icon>
            <CircleCheckFilled />
          </el-icon>
          提交项目信息
        </el-button>
        <el-button
          @click="cancel"
          type="info" plain
        >
          <el-icon>
            <CircleCloseFilled />
          </el-icon>
          取 消
        </el-button>
      </div>
    </div>
    <!--  项目模板数据导入对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="导入项目范围数据"
      width="500"
      :before-close="handleDialogClose"
    >
      <el-upload
        ref="projectUploadRef"
        class="upload-demo"
        drag
        multiple
        accept=".zip"
        :action="uploadModelActionUrl"
        :headers="headers"
        :before-upload="beforeUploadProject"
        :on-success="handleProjectSuccess"
        :on-error="handleProjectError"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          拖动文件 或者<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            请上传 Shapefile <span class="upload-tip">【.zip】</span> 压缩文件 <span class="upload-tip">（坐标系：CGCS2000）</span>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="submitProjectModel"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ReserveProject">
import {
  listReserveProject,
  getReserveProject,
  delReserveProject,
  addReserveProject,
  updateReserveProject,
  reserveLandlist,
  listReserveRight
} from "@/api/patrol/reserveProject.js";
import { getGhdldm } from "@/api/system/dict/data.js";
import { ElMessage } from "element-plus"

const { proxy } = getCurrentInstance();
const showDetail = ref(false);

//数据表格
const tableDataCQ = ref([]);
const tableDataDK = ref([]);

const { tdcb_zjlx, tdcb_lxfs, sys_user_sex } = proxy.useDict(
  "tdcb_zjlx",
  "tdcb_lxfs",
  "sys_user_sex"
);
const ghdldm = ref([]);
getGhdldm().then((res) => {
  ghdldm.value = res.data;
});

//数据列
const columnsCQ = ref([
  {
    name: "qlrmc",
    label: "权利人名称",
    type: "String",
    default: ""
  },
  {
    name: "zjlx",
    label: "证件类型",
    type: "Select",
    default: "",
    data: tdcb_zjlx
  },
  {
    name: "qlrzjh",
    label: "证件号码",
    type: "String",
    default: ""
  },
  {
    name: "lxfs",
    label: "联系方式",
    type: "Select",
    default: "",
    data: tdcb_lxfs
  },
  {
    name: "lxdh",
    label: "联系电话",
    type: "String",
    default: ""
  },
  {
    name: "xb",
    label: "性别",
    type: "Select",
    default: "",
    data: sys_user_sex
  },
  {
    name: "lxdz",
    label: "联系地址",
    type: "String",
    default: ""
  },
  {
    name: "dzyx",
    label: "邮箱",
    type: "String",
    default: ""
  },
  {
    name: "gj",
    label: "国籍",
    type: "String",
    default: ""
  },
  {
    name: "bz",
    label: "备注",
    type: "String",
    default: ""
  }
]);
const columnsDK = ref([
  {
    name: "dkbh",
    label: "地块编号",
    type: "String",
    default: ""
  },
  {
    name: "tbbh",
    label: "图斑编号",
    type: "String",
    default: ""
  },
  {
    name: "dkmc",
    label: "地块名称",
    type: "String",
    default: ""
  },
  {
    name: "ghyt",
    label: "规划用途",
    type: "Tree",
    default: "",
    data: ghdldm
  },
  {
    name: "dkmj",
    label: "地块面积",
    type: "String",
    default: ""
  },
  {
    name: "dz",
    label: "地址",
    type: "String",
    default: ""
  },
  {
    name: "bz",
    label: "备注",
    type: "String",
    default: ""
  },
  {
    name: "file",
    label: "坐标文件",
    type: "File",
    default: {}
  }
]);

const reserveProjectList = ref([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref();
const reserveProjectFormRef = ref();
const dialogVisible = ref(false)
const projectUploadRef = ref()

const initFormData = {
  id: undefined,
  xmmc: undefined,
  xmbh: undefined,
  xmzmj: undefined,
  xmzl: undefined,
  xzqdm: undefined,
  xzqmc: undefined,
  xmsjdks: undefined,
  scmj: undefined,
  scrq: undefined,
  pzrq: undefined,
  pzwh: undefined,
  pzwjm: undefined,
  sfkjgldsc: undefined,
  xmjddm: undefined,
  bz: undefined,
  rightBos: undefined,
  landBos: undefined
};
const data = reactive({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    xmmc: undefined,
    xmbh: undefined,
    xzqdm: undefined,
    scrq: undefined,
    params: {}
  },
  rules: {
  }
});

const { queryParams, form, rules } = toRefs(data);

/**
 * 项目Shape数据批量导入
 */
const handleImport = ()=>{
  dialogVisible.value = true
}

/** 查询储备项目列表 */
const getList = async () => {
  loading.value = true;
  const res = await listReserveProject(queryParams.value);

  const newRes = res.rows.map(item=>{
    item.xmzmj = Number(item.xmzmj).toFixed(2);
    item.scmj = Number(item.scmj).toFixed(2);
    return item
  })
  reserveProjectList.value = newRes;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  showDetail.value = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  reserveProjectFormRef.value?.resetFields();
  tableDataCQ.value = [];
  tableDataDK.value = [];
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};
//标题变量
const xmxiTitle = ref('项目信息录入')
/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  xmxiTitle.value = '项目信息录入'
  showDetail.value = true;
};

/** 修改按钮操作 */
const handleUpdate = async (row) => {
  reset();
  xmxiTitle.value = '修改项目信息'
  const _id = row?.id || ids.value[0];
  const res = await getReserveProject(_id);
  const land = await reserveLandlist(_id);
  const right = await listReserveRight(_id);
  tableDataCQ.value = right.data || [];
  tableDataDK.value = land.data || [];
  Object.assign(form.value, res.data);
  showDetail.value = true;
};

/** 提交按钮 */
const submitForm = () => {
  form.value.rightBos = tableDataCQ;
  form.value.landBos = tableDataDK;
  form.value.sfkjgldsc = Number(form.value.sfkjgldsc)

  reserveProjectFormRef.value?.validate(async (valid) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateReserveProject(form.value).finally(
          () => (buttonLoading.value = false)
        );
      } else {
        await addReserveProject(form.value).finally(
          () => (buttonLoading.value = false)
        );
      }
      proxy?.$modal.msgSuccess("操作成功");
      showDetail.value = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal
    .confirm('是否确认删除储备项目为"' + _ids + '"的数据项？')
    .finally(() => (loading.value = false));
  await delReserveProject(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    "patrol/reserveProject/export",
    {
      ...queryParams.value
    },
    `reserveProject_${new Date().getTime()}.xlsx`
  );
};

const beforeUploadProject = (file)=>{
  return true;
}

const handleProjectSuccess = (response,uploadFile)=>{
  projectUploadRef.value.clearFiles()
  ElMessage.success("项目数据上传城功")
  getList()
}

const handleProjectError = ()=>{
  projectUploadRef.value.clearFiles()
  ElMessage.error("项目数据上传失败")
}

const submitProjectModel = ()=>{
  dialogVisible.value = false
}
// 关闭导入对话框
const handleDialogClose = ()=>{
  projectUploadRef.value.clearFiles()
  dialogVisible.value = false
}


onMounted(() => {
  getList();
});
</script>
<style lang="scss" scoped>
.main-content {
  padding: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}

.result-wrap {
  margin-top: 10px;
}

.content {
  border: 1px solid rgb(233, 233, 233);
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.add-header-title {
  padding: 10px;
  display: flex;
  justify-content: space-between;
  height: 50px;
  background-color: rgb(222, 239, 255);
  box-sizing: border-box;
  border-bottom: 1px solid rgb(233, 233, 233);
  font-weight: 700;
  font-size: 14px;
  line-height: 28px;
}

.add-title-return {
  display: flex;
  align-content: center;
  color: rgb(32, 119, 255);
  cursor: pointer;
  font-weight: normal;
  &:hover{
    cursor: pointer;
    font-size: 16px;
    transform: scale(1.15);
    transition: all ease-in .25s;
  }
}

.add-content {
  padding: 0px 10px;
}

.content-title {
  line-height: 70px;
  font-weight: bold;
  display: flex;
  justify-content: space-between;
}

.footer-button {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  border-top: 1px solid #e0e0e0;
  padding: 20px 0;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  button {
    height: 40px;
  }
}
.listimage {
  height: 18px;
  width: 18px;
  margin-top: 5px;
}
.return {
  padding-left: 6px;
  font-size: 14px;
}
.information {
  // border: 1px solid #dcdfe6;
  padding: 10px;
}
.content-project {
  display: flex;
  align-items: center;
  p {
    color: #333333;
    font-weight: bold;
    margin-left: 8px;
  }
}

.el-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
.pagination-container {
  margin: 10px 0 30px !important;
}
.el-card__header {
  border: none;
}
.mb8 {
    margin: 20px 0 9px 0 !important;
}
:deep(.search){
  margin: 8px 0 3px 0;
}
.el-form--inline .el-form-item {
    margin-bottom: 1px;
}
.upload-tip{
  color:red;
}
.query-form{
  /**防止输入框出现清除按钮时输入框产生宽度变化**/
  :deep(.el-input--suffix) {
    // 固定宽度
    width: 200px !important;
  }
}
@media(max-width: 1000px){
  .query-form{
    :deep(.el-form-item) {
      margin-bottom: 18px !important;
    }
  }
}
@media(min-width: 1500px){
  .query-form{
    :deep(.el-form-item) {
      margin-bottom: 0 !important;
    }
  }
}
</style>
