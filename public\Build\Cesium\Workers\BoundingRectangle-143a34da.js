define(["exports","./Cartesian2-db21342c","./Cartographic-3309dd0d","./Check-7b2a090c","./when-b60132fc","./buildModuleUrl-9085faaa","./Rectangle-dee65d21"],(function(t,e,i,n,h,r,a){"use strict";function d(t,e,i,n){this.x=h.defaultValue(t,0),this.y=h.defaultValue(e,0),this.width=h.defaultValue(i,0),this.height=h.defaultValue(n,0)}d.packedLength=4,d.pack=function(t,e,i){return i=h.defaultValue(i,0),e[i++]=t.x,e[i++]=t.y,e[i++]=t.width,e[i]=t.height,e},d.unpack=function(t,e,i){return e=h.defaultValue(e,0),h.defined(i)||(i=new d),i.x=t[e++],i.y=t[e++],i.width=t[e++],i.height=t[e],i},d.fromPoints=function(t,e){if(h.defined(e)||(e=new d),!h.defined(t)||0===t.length)return e.x=0,e.y=0,e.width=0,e.height=0,e;for(var i=t.length,n=t[0].x,r=t[0].y,a=t[0].x,u=t[0].y,c=1;c<i;c++){var o=t[c],f=o.x,x=o.y;n=Math.min(f,n),a=Math.max(f,a),r=Math.min(x,r),u=Math.max(x,u)}return e.x=n,e.y=r,e.width=a-n,e.height=u-r,e};var u=new r.GeographicProjection,c=new i.Cartographic,o=new i.Cartographic;d.fromRectangle=function(t,i,n){if(h.defined(n)||(n=new d),!h.defined(t))return n.x=0,n.y=0,n.width=0,n.height=0,n;var r=(i=h.defaultValue(i,u)).project(a.Rectangle.southwest(t,c)),f=i.project(a.Rectangle.northeast(t,o));return e.Cartesian2.subtract(f,r,f),n.x=r.x,n.y=r.y,n.width=f.x,n.height=f.y,n},d.clone=function(t,e){if(h.defined(t))return h.defined(e)?(e.x=t.x,e.y=t.y,e.width=t.width,e.height=t.height,e):new d(t.x,t.y,t.width,t.height)},d.union=function(t,e,i){h.defined(i)||(i=new d);var n=Math.min(t.x,e.x),r=Math.min(t.y,e.y),a=Math.max(t.x+t.width,e.x+e.width),u=Math.max(t.y+t.height,e.y+e.height);return i.x=n,i.y=r,i.width=a-n,i.height=u-r,i},d.expand=function(t,e,i){i=d.clone(t,i);var n=e.x-i.x,h=e.y-i.y;return n>i.width?i.width=n:n<0&&(i.width-=n,i.x=e.x),h>i.height?i.height=h:h<0&&(i.height-=h,i.y=e.y),i},d.intersect=function(t,e){var i=t.x,n=t.y,h=e.x,a=e.y;return i>h+e.width||i+t.width<h||n+t.height<a||n>a+e.height?r.Intersect.OUTSIDE:r.Intersect.INTERSECTING},d.equals=function(t,e){return t===e||h.defined(t)&&h.defined(e)&&t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height},d.prototype.clone=function(t){return d.clone(this,t)},d.prototype.intersect=function(t){return d.intersect(this,t)},d.prototype.equals=function(t){return d.equals(this,t)},t.BoundingRectangle=d}));
