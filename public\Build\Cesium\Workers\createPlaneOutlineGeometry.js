define(["./when-b60132fc","./buildModuleUrl-9085faaa","./Cartographic-3309dd0d","./Check-7b2a090c","./ComponentDatatype-c140a87d","./GeometryAttribute-c65394ac","./GeometryAttributes-252e9929","./FeatureDetection-806b12f0","./Rectangle-dee65d21","./Math-119be1a3","./Event-16a2dfbf","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Cartesian2-db21342c","./Cartesian4-3ca25aab"],(function(e,t,n,r,a,i,o,c,u,d,y,p,s,b,m){"use strict";function f(){this._workerName="createPlaneOutlineGeometry"}f.packedLength=0,f.pack=function(e,t){return t},f.unpack=function(t,n,r){return e.defined(r)?r:new f};var w=new n.Cartesian3(-.5,-.5,0),C=new n.Cartesian3(.5,.5,0);return f.createGeometry=function(){var e=new o.GeometryAttributes,r=new Uint16Array(8),u=new Float64Array(12);return u[0]=w.x,u[1]=w.y,u[2]=w.z,u[3]=C.x,u[4]=w.y,u[5]=w.z,u[6]=C.x,u[7]=C.y,u[8]=w.z,u[9]=w.x,u[10]=C.y,u[11]=w.z,e.position=new i.GeometryAttribute({componentDatatype:a.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:u}),r[0]=0,r[1]=1,r[2]=1,r[3]=2,r[4]=2,r[5]=3,r[6]=3,r[7]=0,new i.Geometry({attributes:e,indices:r,primitiveType:c.PrimitiveType.LINES,boundingSphere:new t.BoundingSphere(n.Cartesian3.ZERO,Math.sqrt(2))})},function(t,n){return e.defined(n)&&(t=f.unpack(t,n)),f.createGeometry(t)}}));
