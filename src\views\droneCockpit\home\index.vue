<template>
  <div class="common-layout">
    <el-container>
      <!-- Header -->
      <el-header class="header">
        <el-card class="header-card" >
          <template #header>
            <div class="card-header">
              <div class="header-left">
                <img
                  src="@/assets/images/drone/tc3.png"
                  alt="status" class="header-icon"
                >
                <span class="header-text">系统状态</span>
              </div>

            </div>
          </template>
          <!-- 使用自定义容器替代 el-row -->
          <div class="stats-grid-container">
            <el-card
              v-for="(item, index) in stats"
              :key="index"
              shadow="hover"
              class="stat-card"
              :body-style="{ padding: '16px', display: 'flex', alignItems: 'center' }"
            >
              <div class="stat-content">
                <div class="stat-title">{{ item.title }}</div>
                <div class="stat-value">{{ item.value }} <p class="stat-unit">{{ item.unit }}</p></div>
                <!-- <div class="stat-value">{{ item.value }} {{ item.unit }}</div> -->
              </div>
              <el-image
                :src="item.icon"
                fit="contain"
                class="stat-icon"
              />
            </el-card>
          </div>
        </el-card>
      </el-header>

      <!-- Main -->
      <el-main class="main">
        <div class="main-card">
          <el-card class="main-card-left">
            <template #header>
              <div class="card-header">
                <div class="header-left">
                  <img
                    src="@/assets/images/drone/tc3.png"
                    alt="status" class="header-icon"
                  >
                  <span class="header-text">执行飞行次数统计</span>
                </div>
              </div>
            </template>

            <div
              id="flight-chart"
              style="height: 400px"
            />
          </el-card>
          <el-card class="main-card-right">
            <template
              #header
              class=""
            >
              <div class="card-header">
                <div class="header-left">
                  <img
                    src="@/assets/images/drone/tc3.png"
                    alt="status" class="header-icon"
                  >
                  <span class="header-text">飞行日志</span>
                </div>
                <div class="header-right">
                  <el-button
                    type="text"
                    size="small" @click="refreshLogs"
                  >
                    <i class="el-icon-refresh"/> 刷新
                  </el-button>
                </div>
              </div>
            </template>
            <el-table
              :data="logs"
              height="450" stripe v-loading="loading"
            >
              <el-table-column
                prop="name"
                label="任务名称" align="center" width="200"
              />
              <el-table-column
                prop="beginAt"
                label="开始时间" align="center" width="180"
                sortable
              >
                <template #default="{row}">
                  {{ formatDates(row.beginAt) }}
                </template>
              </el-table-column>
              <el-table-column
                prop="completedAt"
                label="完成的时间" align="center" width="180"
                sortable
              >
                <template #default="{row}">
                  {{ formatDates(row.completedAt) }}
                </template>
              </el-table-column>
              <el-table-column
                prop="currentWaypointIndex"
                align="center" label="航点数量"
              />
              <el-table-column
                prop="status"
                label="飞行状态" align="center" width="180"
              />
              <el-table-column
                prop="endAt"
                label="结束时间" align="center" width="180"
              >
                <template #default="{row}">
                  {{ formatDates(row.endAt) }}
                </template>
              </el-table-column>
              <el-table-column
                prop="runAt"
                label="实际执行的时间" align="center" width="180"
              >
                <template #default="{row}">
                  {{ formatDates(row.runAt) }}
                </template>
              </el-table-column>
              <el-table-column
                prop="resumableStatus"
                label="断点续飞状态" align="center" width="180"
              />
              <el-table-column
                prop="taskType"
                align="center" label="任务类型"
              />
              <el-table-column
                prop="totalWaypoints"
                label="总航点数量" align="center" width="180"
              />
            </el-table>
            <pagination
              v-show="total > 0"
              :total="total"
              v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize"
              @pagination="getList"
            />
          </el-card>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script >
import * as echarts from 'echarts';
import { statisticFlightLog, statisticHome, flightNumMonthOfYear } from "@/api/uav/statistic.js";
import { formatDate } from "@/constants/flightUtils.js"
export default {
  name: 'HomePageOfCockpit',
  data() {
    return {
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dz: undefined,
        bz: undefined,
        params: {}
      },
      stats: [
        { title: '系统状态', value: '加载中...', unit: '', icon: new URL('@/assets/images/drone/tc5.png', import.meta.url).href },
        { title: '设备总数', value: '0', unit: '台', icon: new URL('@/assets/images/drone/tc1.png', import.meta.url).href },
        { title: '项目总数', value: '0', unit: '个', icon: new URL('@/assets/images/drone/tc2.png', import.meta.url).href },
        { title: '航线总数', value: '0', unit: '条', icon: new URL('@/assets/images/drone/tc6.png', import.meta.url).href },
        { title: '飞行任务', value: '0', unit: '次', icon: new URL('@/assets/images/drone/tc4.png', import.meta.url).href }
      ],
      year: new Date().getFullYear().toString(),
      years: [],
      logs: [],
      loading: false,
      chart: null
    };
  },
  watch: {
    year() {
      this.fetchChartData();
    }
  },
  mounted() {
    this.initChart();
  },
  created() {
    this.initializeYears();
    this.getList();
  },
  methods: {
    initializeYears() {
      const currentYear = new Date().getFullYear();
      this.years = Array.from({ length: 5 }, (_, i) => ({
        value: (currentYear - i).toString(),
        label: (currentYear - i).toString()
      }));
    },
    async getList() {
      await Promise.all([
        this.fetchChartData(),
        this.fetchLogs(),
        this.fetchStatsData()
      ]);
    },
    async fetchChartData() {
      try {
        const response = await flightNumMonthOfYear({ year: this.year });
        const months = Array.from({ length: 12 }, (_, i) => i + 1);
        const flightData = months.map(month => {
          return parseInt(response.data[month]?.flightNum || 0);
        });

        if (this.chart) {
          this.chart.setOption({
            xAxis: {
              data: months.map(m => `${m}月`)
            },
            series: [{
              data: flightData
            }]
          });
        }
      } catch (error) {
        console.error('获取飞行数据失败:', error);
        this.$message.error('飞行数据加载失败');
      }
    },
    initChart() {
      const chartDom = document.getElementById('flight-chart');
      if (!chartDom) return;

      this.chart = echarts.init(chartDom);
      const option = {
        // 保持原有的option配置
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' },
          formatter: '{b}<br/>{a}: {c} 次'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value',
          name: '飞行次数',
          axisLabel: { formatter: '{value} 次' }
        },
        series: [{
          name: '飞行次数',
          type: 'line',
          data: [],
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(64, 158, 255, 0.8)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
            ])
          },
          itemStyle: { color: '#409EFF' },
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          lineStyle: { width: 3 },
          label: {
            show: true,
            position: 'top',
            formatter: '{c} 次'
          }
        }]
      };
      this.chart.setOption(option);
    },
    async fetchStatsData() {
      try {
        const response = await statisticHome();
        const statsMapping = {
          '设备总数': 'deviceNum',
          '项目总数': 'projectNum',
          '飞行任务': 'taskNum',
          '航线总数': 'waylineNum',
          '系统状态': 'systemStatus'
        };

        this.stats = this.stats.map(statItem => {
          const apiKey = statsMapping[statItem.title];
          return {
            ...statItem,
            value: response.data[apiKey] ?? statItem.value,
            unit: statItem.unit
          };
        });
      } catch (error) {
        console.error('获取统计数据失败:', error);
        this.$message.error('统计数据加载失败');
      }
    },
    async fetchLogs() {
      this.loading = true;
      try {
        const response = await statisticFlightLog(this.queryParams);
        this.logs = response.data;
      } catch (error) {
        console.error('获取日志失败:', error);
        this.$message.error('日志加载失败');
      } finally {
        this.loading = false;
      }
    },
    refreshLogs() {
      this.queryParams.pageNum = 1;
      this.fetchLogs();
      this.$message.success('日志已刷新');
    },
    navigateToDroneDashboard() {
      const routePath = 'droneHome';
      const fullUrl = window.location.origin + import.meta.env.VITE_APP_CONTEXT_PATH + routePath;
      window.open(fullUrl, '_blank');
    },
    getStatusType(status) {
      const types = {
        '待执行': 'info',
        '执行中': 'warning',
        '已完成': 'success',
        '执行失败': 'danger'
      };
      return types[status] || '';
    },
    filterStatus(value, row) {
      return row.status === value;
    },
    formatDates(Time){
      return formatDate(Time);
    }
  }
};
</script>

<style scoped lang="scss">
html, body {
  margin: 0;
  padding: 0;
  height: 100%; /* 确保根元素占满整个视口 */
  overflow: hidden; /* 禁止页面滚动 */
}

.common-layout {
  display: flex;
  flex-direction: column;
  height: 88vh; /* 占满整个视口高度 */

  .el-container {
    display: flex;
    flex-direction: column;
    flex: 1; /* 让容器占满父级空间 */
    overflow: hidden; /* 禁止内部滚动 */
  }

  /* Header 样式 */
  .header {
    flex: 0 0 30%; /* 高度为窗口高度的 30% */
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Main 样式 */
  .main {
    flex: 0 0 70%; /* 高度为窗口高度的 70% */
    display: flex;
    flex-direction: row;  /* 关键修改：改为横向排列 */
    align-items: stretch; /* 让子元素撑满高度 */
    overflow: hidden;     /* 防止内容溢出 */
  }
}

.header-card{
  // padding: 20px;
  width: 100%;
  height: 100%;
}

.main-card {
  display: flex;
  flex-direction: row;  /* 明确指定横向排列 */
  gap: 10px;           /* 添加间距 */
  width: 100%;
  height: 100%;
}

.main-card-left,
.main-card-right {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.header-icon {
  width: 16px;
  height: 16px;
  margin-right: 10px;
}

.card-header {
  display: flex;
  justify-content: space-between; /* 标题靠左，时间选择器靠右 */
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
}

.header-text {
  font-size: 16px;
  font-weight: bold;
}

.stat-content {
  flex: 1;
  min-width: 0;
}

.stat-title {
  font-size: 18px;
  color: #333333;
  margin-bottom: 8px;
  font-weight: bold;
  white-space: nowrap; /* 标题不换行 */
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #2077FF;
  margin-bottom: 4px;
  display: flex;
  white-space: nowrap; /* 数值不换行 */
}
.stat-unit {
  font-size: 12px;
  font-weight: 200;
  color: #605F5F;
  margin-left: 5px;
}
.stat-icon {
  width: 105px;
  height: 105px;
  // margin-left: 16px;
  flex-shrink: 0; /* 防止图标被压缩 */
}

/* 主容器样式 */
.stats-grid-container {
  display: grid;
  grid-auto-flow: column; /* 横向排列 */
  grid-auto-columns: 1fr; /* 自动等分宽度 */
  gap: 20px; /* 卡片间距 */
  overflow-x: auto; /* 允许横向滚动 */
  padding-bottom: 10px; /* 为滚动条预留空间 */
  scrollbar-width: thin; /* 更细的滚动条 */
}
/* 卡片样式 */
.stat-card {
  min-width: 0; /* 防止内容溢出 */
  height: 100%; /* 统一高度 */
  border: 1px solid #E1F0FF;
  box-shadow: 3px 3px 5px rgba(176, 215, 251, 0.8);
}
/* 响应式调整 */
@media (max-width: 768px) {
  .stat-card {
    min-width: 180px; /* 移动端最小宽度 */
  }

  .stat-icon {
    width: 40px;
    height: 40px;
  }
}

/* 美化滚动条 */
.stats-grid-container::-webkit-scrollbar {
  height: 6px;
}

.stats-grid-container::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  border-radius: 3px;
}

@media (max-width: 768px) {
  .home-page {
    padding: 10px;

    .stat-icon {
      width: 40px;
      height: 40px;
    }
  }
}
.common-layout .header {
    margin-top: 15px;
}
:deep(.el-card__header) {
  border-bottom: 1px solid #B0D7FB;
  padding: 14px 10px 14px 14px !important;
}
:deep(.el-card__header){
  position: sticky;
  top: 0;
}
</style>
