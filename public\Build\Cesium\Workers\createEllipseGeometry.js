define(["./Cartographic-3309dd0d","./when-b60132fc","./EllipseGeometry-204f6df4","./Rectangle-dee65d21","./Check-7b2a090c","./Math-119be1a3","./arrayFill-4513d7ad","./buildModuleUrl-9085faaa","./FeatureDetection-806b12f0","./Cartesian4-3ca25aab","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Event-16a2dfbf","./Cartesian2-db21342c","./ComponentDatatype-c140a87d","./EllipseGeometryLibrary-a39b75ad","./GeometryAttribute-c65394ac","./GeometryAttributes-252e9929","./GeometryInstance-6bd4503d","./GeometryOffsetAttribute-fbeb6f1a","./GeometryPipeline-7a733318","./AttributeCompression-0a087f75","./EncodedCartesian3-f1396b05","./IndexDatatype-8a5eead4","./IntersectionTests-0d6905a3","./Plane-a3d8b3d2","./VertexFormat-6446fca0"],(function(e,t,a,r,n,i,d,o,c,l,s,b,f,m,p,u,y,G,C,E,h,A,_,D,F,I,g){"use strict";return function(n,i){return t.defined(i)&&(n=a.EllipseGeometry.unpack(n,i)),n._center=e.Cartesian3.clone(n._center),n._ellipsoid=r.Ellipsoid.clone(n._ellipsoid),a.EllipseGeometry.createGeometry(n)}}));
