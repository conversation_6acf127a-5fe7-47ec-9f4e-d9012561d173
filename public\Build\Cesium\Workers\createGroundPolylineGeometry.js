define(["./buildModuleUrl-9085faaa","./Cartesian2-db21342c","./Cartographic-3309dd0d","./Check-7b2a090c","./when-b60132fc","./Rectangle-dee65d21","./Math-119be1a3","./ArcType-29cf2197","./arrayRemoveDuplicates-d2f048c5","./ComponentDatatype-c140a87d","./EllipsoidGeodesic-139a7db9","./EllipsoidRhumbLine-30b5229b","./EncodedCartesian3-f1396b05","./GeometryAttribute-c65394ac","./IntersectionTests-0d6905a3","./FeatureDetection-806b12f0","./Plane-a3d8b3d2","./WebMercatorProjection-a4b885f9","./Event-16a2dfbf","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Cartesian4-3ca25aab"],(function(e,a,t,i,n,r,s,o,l,u,c,h,d,C,p,g,f,m,v,w,y,_){"use strict";function T(a){a=n.defaultValue(a,{}),this._ellipsoid=n.defaultValue(a.ellipsoid,r.Ellipsoid.WGS84),this._rectangle=n.defaultValue(a.rectangle,r.Rectangle.MAX_VALUE),this._projection=new e.GeographicProjection(this._ellipsoid),this._numberOfLevelZeroTilesX=n.defaultValue(a.numberOfLevelZeroTilesX,2),this._numberOfLevelZeroTilesY=n.defaultValue(a.numberOfLevelZeroTilesY,1),this._customDPI=a.customDPI,this._scaleDenominators=a.scaleDenominators,this._tileWidth=n.defaultValue(a.tileWidth,256),this._tileHeight=n.defaultValue(a.tileHeight,256),this._beginLevel=n.defaultValue(a.beginLevel,0)}Object.defineProperties(T.prototype,{ellipsoid:{get:function(){return this._ellipsoid}},rectangle:{get:function(){return this._rectangle}},projection:{get:function(){return this._projection}},beginLevel:{get:function(){return this._beginLevel}}}),T.prototype.getNumberOfXTilesAtLevel=function(e){if(n.defined(this._customDPI)&&n.defined(this._scaleDenominators)){var a=this.calculateResolution(e),t=this._tileWidth*a.x;return Math.ceil(this._rectangle.width/t)}return this._numberOfLevelZeroTilesX<<e-this._beginLevel},T.prototype.getNumberOfYTilesAtLevel=function(e){if(n.defined(this._customDPI)&&n.defined(this._scaleDenominators)){var a=this.calculateResolution(e),t=this._tileHeight*a.y;return Math.ceil(this._rectangle.height/t)}return this._numberOfLevelZeroTilesY<<e-this._beginLevel},T.prototype.rectangleToNativeRectangle=function(e,a){var t=s.CesiumMath.toDegrees(e.west),i=s.CesiumMath.toDegrees(e.south),o=s.CesiumMath.toDegrees(e.east),l=s.CesiumMath.toDegrees(e.north);return n.defined(a)?(a.west=t,a.south=i,a.east=o,a.north=l,a):new r.Rectangle(t,i,o,l)},T.prototype.tileXYToNativeRectangle=function(e,a,t,i){var n=this.tileXYToRectangle(e,a,t,i);return n.west=s.CesiumMath.toDegrees(n.west),n.south=s.CesiumMath.toDegrees(n.south),n.east=s.CesiumMath.toDegrees(n.east),n.north=s.CesiumMath.toDegrees(n.north),n},T.prototype.tileXYToRectangle=function(e,a,t,i){var s=this._rectangle;if(n.defined(this._customDPI)&&n.defined(this._scaleDenominators)){var o=this.calculateResolution(t),l=s.west+e*this._tileWidth*o.x,u=s.west+(e+1)*this._tileWidth*o.x,c=s.north-a*this._tileHeight*o.y,h=s.north-(a+1)*this._tileHeight*o.y;return n.defined(i)?(i.west=l,i.south=h,i.east=u,i.north=c,i):new r.Rectangle(l,h,u,c)}var d=this.getNumberOfXTilesAtLevel(t),C=this.getNumberOfYTilesAtLevel(t),p=s.width/d,g=(l=e*p+s.west,u=(e+1)*p+s.west,s.height/C);c=s.north-a*g,h=s.north-(a+1)*g;return n.defined(i)||(i=new r.Rectangle(l,h,u,c)),i.west=l,i.south=h,i.east=u,i.north=c,i},T.prototype.positionToTileXY=function(e,t,i){var o=this._rectangle;if(r.Rectangle.contains(o,e)){var l=this.getNumberOfXTilesAtLevel(t),u=this.getNumberOfYTilesAtLevel(t),c=o.width/l,h=o.height/u;if(n.defined(this._customDPI)&&n.defined(this._scaleDenominators)){var d=this.calculateResolution(t);c=this._tileWidth*d.x,h=this._tileHeight*d.y}var C=e.longitude;o.east<o.west&&(C+=s.CesiumMath.TWO_PI);var p=(C-o.west)/c|0;p>=l&&(p=l-1);var g=(o.north-e.latitude)/h|0;return g>=u&&(g=u-1),n.defined(i)?(i.x=p,i.y=g,i):new a.Cartesian2(p,g)}},T.prototype.calculateResolution=function(e){var t=.0254*this._scaleDenominators[e-this._beginLevel]/this._customDPI.x,i=.0254*this._scaleDenominators[e-this._beginLevel]/this._customDPI.y,n=r.Ellipsoid.WGS84.maximumRadius;return new a.Cartesian2(t/n,i/n)};var M=new t.Cartesian3,E=new t.Cartesian3,b=new t.Cartographic,P=new t.Cartesian3,O=new t.Cartesian3,I=new e.BoundingSphere,L=new T,D=[new t.Cartographic,new t.Cartographic,new t.Cartographic,new t.Cartographic],S=new a.Cartesian2,k={};function A(e){t.Cartographic.fromRadians(e.east,e.north,0,D[0]),t.Cartographic.fromRadians(e.west,e.north,0,D[1]),t.Cartographic.fromRadians(e.east,e.south,0,D[2]),t.Cartographic.fromRadians(e.west,e.south,0,D[3]);var a,i=0,n=0,r=0,s=0,o=k._terrainHeightsMaxLevel;for(a=0;a<=o;++a){for(var l=!1,u=0;u<4;++u){var c=D[u];if(L.positionToTileXY(c,a,S),0===u)r=S.x,s=S.y;else if(r!==S.x||s!==S.y){l=!0;break}}if(l)break;i=r,n=s}if(0!==a)return{x:i,y:n,level:a>o?o:a-1}}k.initialize=function(){var a=k._initPromise;return n.defined(a)||(a=e.Resource.fetchJson(e.buildModuleUrl("Assets/approximateTerrainHeights.json")).then((function(e){k._terrainHeights=e})),k._initPromise=a),a},k.getMinimumMaximumHeights=function(e,a){a=n.defaultValue(a,r.Ellipsoid.WGS84);var i=A(e),s=k._defaultMinTerrainHeight,o=k._defaultMaxTerrainHeight;if(n.defined(i)){var l=i.level+"-"+i.x+"-"+i.y,u=k._terrainHeights[l];n.defined(u)&&(s=u[0],o=u[1]),a.cartographicToCartesian(r.Rectangle.northeast(e,b),M),a.cartographicToCartesian(r.Rectangle.southwest(e,b),E),t.Cartesian3.midpoint(E,M,P);var c=a.scaleToGeodeticSurface(P,O);if(n.defined(c)){var h=t.Cartesian3.distance(P,c);s=Math.min(s,-h)}else s=k._defaultMinTerrainHeight}return{minimumTerrainHeight:s=Math.max(k._defaultMinTerrainHeight,s),maximumTerrainHeight:o}},k.getBoundingSphere=function(a,t){t=n.defaultValue(t,r.Ellipsoid.WGS84);var i=A(a),s=k._defaultMaxTerrainHeight;if(n.defined(i)){var o=i.level+"-"+i.x+"-"+i.y,l=k._terrainHeights[o];n.defined(l)&&(s=l[1])}var u=e.BoundingSphere.fromRectangle3D(a,t,0);return e.BoundingSphere.fromRectangle3D(a,t,s,I),e.BoundingSphere.union(u,I,u)},k._terrainHeightsMaxLevel=6,k._defaultMaxTerrainHeight=9e3,k._defaultMinTerrainHeight=-1e5,k._terrainHeights=void 0,k._initPromise=void 0,Object.defineProperties(k,{initialized:{get:function(){return n.defined(k._terrainHeights)}}});var x=[e.GeographicProjection,m.WebMercatorProjection],N=x.length,R=Math.cos(s.CesiumMath.toRadians(30)),H=Math.cos(s.CesiumMath.toRadians(150));function z(e){var a=(e=n.defaultValue(e,n.defaultValue.EMPTY_OBJECT)).positions;this.width=n.defaultValue(e.width,1),this._positions=a,this.granularity=n.defaultValue(e.granularity,9999),this.loop=n.defaultValue(e.loop,!1),this.arcType=n.defaultValue(e.arcType,o.ArcType.GEODESIC),this._ellipsoid=n.defaultValue(e.ellipsoid,r.Ellipsoid.WGS84),this._projectionIndex=0,this._workerName="createGroundPolylineGeometry",this._scene3DOnly=!1}Object.defineProperties(z.prototype,{packedLength:{get:function(){return 1+3*this._positions.length+1+1+1+r.Ellipsoid.packedLength+1+1}}}),z.setProjectionAndEllipsoid=function(e,a){for(var t=0,i=0;i<N;i++)if(a instanceof x[i]){t=i;break}e._projectionIndex=t,e._ellipsoid=a.ellipsoid};var V=new t.Cartesian3,j=new t.Cartesian3,G=new t.Cartesian3;function B(e,a,i,n,r){var s=Z(n,e,0,V),o=Z(n,e,i,j),l=Z(n,a,0,G),u=Q(o,s,j),c=Q(l,s,G);return t.Cartesian3.cross(c,u,r),t.Cartesian3.normalize(r,r)}var W=new t.Cartographic,F=new t.Cartesian3,Y=new t.Cartesian3,X=new t.Cartesian3;function q(e,a,i,n,r,s,l,u,d,C,p){if(0!==r){var g;s===o.ArcType.GEODESIC?g=new c.EllipsoidGeodesic(e,a,l):s===o.ArcType.RHUMB&&(g=new h.EllipsoidRhumbLine(e,a,l));var f=g.surfaceDistance;if(!(f<r))for(var m=B(e,a,n,l,X),v=Math.ceil(f/r),w=f/v,y=w,_=v-1,T=u.length,M=0;M<_;M++){var E=g.interpolateUsingSurfaceDistance(y,W),b=Z(l,E,i,F),P=Z(l,E,n,Y);t.Cartesian3.pack(m,u,T),t.Cartesian3.pack(b,d,T),t.Cartesian3.pack(P,C,T),p.push(E.latitude),p.push(E.longitude),T+=3,y+=w}}}var U=new t.Cartographic;function Z(e,a,i,n){return t.Cartographic.clone(a,U),U.height=i,t.Cartographic.toCartesian(U,e,n)}function Q(e,a,i){return t.Cartesian3.subtract(e,a,i),t.Cartesian3.normalize(i,i),i}z.pack=function(e,a,i){var s=n.defaultValue(i,0),o=e._positions,l=o.length;a[s++]=l;for(var u=0;u<l;++u){var c=o[u];t.Cartesian3.pack(c,a,s),s+=3}return a[s++]=e.granularity,a[s++]=e.loop?1:0,a[s++]=e.arcType,r.Ellipsoid.pack(e._ellipsoid,a,s),s+=r.Ellipsoid.packedLength,a[s++]=e._projectionIndex,a[s++]=e._scene3DOnly?1:0,a},z.unpack=function(e,a,i){for(var s=n.defaultValue(a,0),o=e[s++],l=new Array(o),u=0;u<o;u++)l[u]=t.Cartesian3.unpack(e,s),s+=3;var c=e[s++],h=1===e[s++],d=e[s++],C=r.Ellipsoid.unpack(e,s);s+=r.Ellipsoid.packedLength;var p=e[s++],g=1===e[s++];if(!n.defined(i)){var f=new z({positions:l,granularity:c,loop:h,arcType:d,ellipsoid:C});return f._projectionIndex=p,f._scene3DOnly=g,f}return i._positions=l,i.granularity=c,i.loop=h,i.arcType=d,i._ellipsoid=C,i._projectionIndex=p,i._scene3DOnly=g,i};var J=new t.Cartesian3,K=new t.Cartesian3,$=new t.Cartesian3,ee=new t.Cartesian3,ae=new f.Plane(t.Cartesian3.UNIT_X,0),te=new t.Cartesian3;function ie(e,a,i,n,r){var o=Q(i,a,te),l=Q(e,a,J),u=Q(n,a,K),c=t.Cartesian3.cross(o,l,ee);c=t.Cartesian3.normalize(c,c);var h=f.Plane.fromPointNormal(a,c,ae),d=f.Plane.getPointDistance(h,n);if(s.CesiumMath.equalsEpsilon(d,0,s.CesiumMath.EPSILON7))return t.Cartesian3.clone(c,r),r;r=t.Cartesian3.add(u,l,r),r=t.Cartesian3.normalize(r,r);var C=t.Cartesian3.cross(o,r,$);return t.Cartesian3.normalize(C,C),t.Cartesian3.cross(C,o,r),t.Cartesian3.normalize(r,r),t.Cartesian3.dot(u,C)<0&&(r=t.Cartesian3.negate(r,r)),r}var ne=f.Plane.fromPointNormal(t.Cartesian3.ZERO,t.Cartesian3.UNIT_Y),re=new t.Cartesian3,se=new t.Cartesian3,oe=new t.Cartesian3,le=new t.Cartesian3,ue=new t.Cartesian3,ce=new t.Cartesian3,he=new t.Cartographic,de=new t.Cartographic,Ce=new t.Cartographic;z.createGeometry=function(a){var i,c,g,f,m,v,w=!a._scene3DOnly,y=a.loop,_=a._ellipsoid,T=a.granularity,M=a.arcType,E=new x[a._projectionIndex](_),b=1e3,P=a._positions,O=P.length;2===O&&(y=!1);var I,L,D,S=new h.EllipsoidRhumbLine(void 0,void 0,_),A=[P[0]];for(c=0;c<O-1;c++)g=P[c],f=P[c+1],I=p.IntersectionTests.lineSegmentPlane(g,f,ne,ce),!n.defined(I)||t.Cartesian3.equalsEpsilon(I,g,s.CesiumMath.EPSILON7)||t.Cartesian3.equalsEpsilon(I,f,s.CesiumMath.EPSILON7)||(a.arcType===o.ArcType.GEODESIC?A.push(t.Cartesian3.clone(I)):a.arcType===o.ArcType.RHUMB&&(D=_.cartesianToCartographic(I,he).longitude,m=_.cartesianToCartographic(g,he),v=_.cartesianToCartographic(f,de),S.setEndPoints(m,v),L=S.findIntersectionWithLongitude(D,Ce),I=_.cartographicToCartesian(L,ce),!n.defined(I)||t.Cartesian3.equalsEpsilon(I,g,s.CesiumMath.EPSILON7)||t.Cartesian3.equalsEpsilon(I,f,s.CesiumMath.EPSILON7)||A.push(t.Cartesian3.clone(I)))),A.push(f);y&&(g=P[O-1],f=P[0],I=p.IntersectionTests.lineSegmentPlane(g,f,ne,ce),!n.defined(I)||t.Cartesian3.equalsEpsilon(I,g,s.CesiumMath.EPSILON7)||t.Cartesian3.equalsEpsilon(I,f,s.CesiumMath.EPSILON7)||(a.arcType===o.ArcType.GEODESIC?A.push(t.Cartesian3.clone(I)):a.arcType===o.ArcType.RHUMB&&(D=_.cartesianToCartographic(I,he).longitude,m=_.cartesianToCartographic(g,he),v=_.cartesianToCartographic(f,de),S.setEndPoints(m,v),L=S.findIntersectionWithLongitude(D,Ce),I=_.cartographicToCartesian(L,ce),!n.defined(I)||t.Cartesian3.equalsEpsilon(I,g,s.CesiumMath.EPSILON7)||t.Cartesian3.equalsEpsilon(I,f,s.CesiumMath.EPSILON7)||A.push(t.Cartesian3.clone(I)))));var N=A.length,H=new Array(N);for(c=0;c<N;c++){var z=t.Cartographic.fromCartesian(A[c],_);z.height=0,H[c]=z}if(!((N=(H=l.arrayRemoveDuplicates(H,t.Cartographic.equalsEpsilon)).length)<2)){var V=[],j=[],G=[],W=[],F=re,Y=se,X=oe,U=le,J=ue,K=H[0],$=H[1];for(F=Z(_,H[N-1],0,F),U=Z(_,$,0,U),Y=Z(_,K,0,Y),X=Z(_,K,b,X),J=y?ie(F,Y,X,U,J):B(K,$,b,_,J),t.Cartesian3.pack(J,j,0),t.Cartesian3.pack(Y,G,0),t.Cartesian3.pack(X,W,0),V.push(K.latitude),V.push(K.longitude),q(K,$,0,b,T,M,_,j,G,W,V),c=1;c<N-1;++c){F=t.Cartesian3.clone(Y,F),Y=t.Cartesian3.clone(U,Y);var ee=H[c];Z(_,ee,b,X),Z(_,H[c+1],0,U),ie(F,Y,X,U,J),i=j.length,t.Cartesian3.pack(J,j,i),t.Cartesian3.pack(Y,G,i),t.Cartesian3.pack(X,W,i),V.push(ee.latitude),V.push(ee.longitude),q(H[c],H[c+1],0,b,T,M,_,j,G,W,V)}var ae=H[N-1],te=H[N-2];if(Y=Z(_,ae,0,Y),X=Z(_,ae,b,X),y){var pe=H[0];J=ie(F=Z(_,te,0,F),Y,X,U=Z(_,pe,0,U),J)}else J=B(te,ae,b,_,J);if(i=j.length,t.Cartesian3.pack(J,j,i),t.Cartesian3.pack(Y,G,i),t.Cartesian3.pack(X,W,i),V.push(ae.latitude),V.push(ae.longitude),y){for(q(ae,K,0,b,T,M,_,j,G,W,V),i=j.length,c=0;c<3;++c)j[i+c]=j[c],G[i+c]=G[c],W[i+c]=W[c];V.push(K.latitude),V.push(K.longitude)}return function(a,i,n,o,l,c,h){var p,g,f,m,v,w,y=i._ellipsoid,_=n.length/3-1,T=8*_,M=4*T,E=36*_,b=T>65535?new Uint32Array(E):new Uint16Array(E),P=new Float64Array(3*T),O=new Float32Array(M),I=new Float32Array(M),L=new Float32Array(M),D=new Float32Array(M),S=new Float32Array(M);h&&(f=new Float32Array(M),m=new Float32Array(M),v=new Float32Array(M),w=new Float32Array(2*T));var A=c.length/2,x=0,N=Ie;N.height=0;var H=Le;H.height=0;var z=De,V=Se;if(h)for(g=0,p=1;p<A;p++)N.latitude=c[g],N.longitude=c[g+1],H.latitude=c[g+2],H.longitude=c[g+3],z=i.project(N,z),V=i.project(H,V),x+=t.Cartesian3.distance(z,V),g+=2;var j=o.length/3;V=t.Cartesian3.unpack(o,0,V);var G,B=0;for(g=3,p=1;p<j;p++)z=t.Cartesian3.clone(V,z),V=t.Cartesian3.unpack(o,g,V),B+=t.Cartesian3.distance(z,V),g+=3;g=3;var W=0,F=0,Y=0,X=0,q=!1,U=t.Cartesian3.unpack(n,0,Ae),Z=t.Cartesian3.unpack(o,0,Se),J=t.Cartesian3.unpack(l,0,Ne);if(a){me(J,t.Cartesian3.unpack(n,n.length-6,ke),U,Z)&&(J=t.Cartesian3.negate(J,J))}var K=0,$=0,ee=0;for(p=0;p<_;p++){var ae,te,ie,ne,re=t.Cartesian3.clone(U,ke),se=t.Cartesian3.clone(Z,De),oe=t.Cartesian3.clone(J,xe);if(q&&(oe=t.Cartesian3.negate(oe,oe)),U=t.Cartesian3.unpack(n,g,Ae),Z=t.Cartesian3.unpack(o,g,Se),q=me(J=t.Cartesian3.unpack(l,g,Ne),re,U,Z),N.latitude=c[W],N.longitude=c[W+1],H.latitude=c[W+2],H.longitude=c[W+3],h){var le=Oe(N,H);ae=i.project(N,Be);var ue=Q(te=i.project(H,We),ae,ea);ue.y=Math.abs(ue.y),ie=Fe,ne=Ye,0===le||t.Cartesian3.dot(ue,t.Cartesian3.UNIT_Y)>R?(ie=_e(i,N,oe,ae,Fe),ne=_e(i,H,J,te,Ye)):1===le?(ne=_e(i,H,J,te,Ye),ie.x=0,ie.y=s.CesiumMath.sign(N.longitude-Math.abs(H.longitude)),ie.z=0):(ie=_e(i,N,oe,ae,Fe),ne.x=0,ne.y=s.CesiumMath.sign(N.longitude-H.longitude),ne.z=0)}var ce=t.Cartesian3.distance(se,Z),he=d.EncodedCartesian3.fromCartesian(re,Ke),de=t.Cartesian3.subtract(U,re,Xe),Ce=t.Cartesian3.normalize(de,Ze),pe=t.Cartesian3.subtract(se,re,qe);pe=t.Cartesian3.normalize(pe,pe);var ge=t.Cartesian3.cross(Ce,pe,Ze);ge=t.Cartesian3.normalize(ge,ge);var fe=t.Cartesian3.cross(pe,oe,Qe);fe=t.Cartesian3.normalize(fe,fe);var ve=t.Cartesian3.subtract(Z,U,Ue);ve=t.Cartesian3.normalize(ve,ve);var we=t.Cartesian3.cross(J,ve,Je);we=t.Cartesian3.normalize(we,we);var ye,Te,Me,be=ce/B,oa=K/B,la=0,ua=0,ca=0;if(h){la=t.Cartesian3.distance(ae,te),ye=d.EncodedCartesian3.fromCartesian(ae,$e),Te=t.Cartesian3.subtract(te,ae,ea);var ha=(Me=t.Cartesian3.normalize(Te,aa)).x;Me.x=Me.y,Me.y=-ha,ua=la/x,ca=$/x}for(G=0;G<8;G++){var da=X+4*G,Ca=F+2*G,pa=da+3,ga=G<4?1:-1,fa=2===G||3===G||6===G||7===G?1:-1;t.Cartesian3.pack(he.high,O,da),O[pa]=de.x,t.Cartesian3.pack(he.low,I,da),I[pa]=de.y,t.Cartesian3.pack(fe,L,da),L[pa]=de.z,t.Cartesian3.pack(we,D,da),D[pa]=be*ga,t.Cartesian3.pack(ge,S,da);var ma=oa*fa;0===ma&&fa<0&&(ma=Number.POSITIVE_INFINITY),S[pa]=ma,h&&(f[da]=ye.high.x,f[da+1]=ye.high.y,f[da+2]=ye.low.x,f[da+3]=ye.low.y,v[da]=-ie.y,v[da+1]=ie.x,v[da+2]=ne.y,v[da+3]=-ne.x,m[da]=Te.x,m[da+1]=Te.y,m[da+2]=Me.x,m[da+3]=Me.y,w[Ca]=ua*ga,0===(ma=ca*fa)&&fa<0&&(ma=Number.POSITIVE_INFINITY),w[Ca+1]=ma)}var va=je,wa=Ge,ya=ze,_a=Ve,Ta=r.Rectangle.fromCartographicArray(Re,He),Ma=k.getMinimumMaximumHeights(Ta,y),Ea=Ma.minimumTerrainHeight,ba=Ma.maximumTerrainHeight;ee+=Ea,ee+=ba,Ee(re,se,Ea,ba,va,ya),Ee(U,Z,Ea,ba,wa,_a);var Pa=t.Cartesian3.multiplyByScalar(ge,s.CesiumMath.EPSILON5,ta);t.Cartesian3.add(va,Pa,va),t.Cartesian3.add(wa,Pa,wa),t.Cartesian3.add(ya,Pa,ya),t.Cartesian3.add(_a,Pa,_a),Pe(va,wa),Pe(ya,_a),t.Cartesian3.pack(va,P,Y),t.Cartesian3.pack(wa,P,Y+3),t.Cartesian3.pack(_a,P,Y+6),t.Cartesian3.pack(ya,P,Y+9),Pa=t.Cartesian3.multiplyByScalar(ge,-2*s.CesiumMath.EPSILON5,ta),t.Cartesian3.add(va,Pa,va),t.Cartesian3.add(wa,Pa,wa),t.Cartesian3.add(ya,Pa,ya),t.Cartesian3.add(_a,Pa,_a),Pe(va,wa),Pe(ya,_a),t.Cartesian3.pack(va,P,Y+12),t.Cartesian3.pack(wa,P,Y+15),t.Cartesian3.pack(_a,P,Y+18),t.Cartesian3.pack(ya,P,Y+21),W+=2,g+=3,F+=16,Y+=24,X+=32,K+=ce,$+=la}g=0;var Oa=0;for(p=0;p<_;p++){for(G=0;G<ra;G++)b[g+G]=na[G]+Oa;Oa+=8,g+=ra}var Ia=ia;e.BoundingSphere.fromVertices(n,t.Cartesian3.ZERO,3,Ia[0]),e.BoundingSphere.fromVertices(o,t.Cartesian3.ZERO,3,Ia[1]);var La=e.BoundingSphere.fromBoundingSpheres(Ia);La.radius+=ee/(2*_);var Da={position:new C.GeometryAttribute({componentDatatype:u.ComponentDatatype.DOUBLE,componentsPerAttribute:3,normalize:!1,values:P}),startHiAndForwardOffsetX:sa(O),startLoAndForwardOffsetY:sa(I),startNormalAndForwardOffsetZ:sa(L),endNormalAndTextureCoordinateNormalizationX:sa(D),rightNormalAndTextureCoordinateNormalizationY:sa(S)};h&&(Da.startHiLo2D=sa(f),Da.offsetAndRight2D=sa(m),Da.startEndNormals2D=sa(v),Da.texcoordNormalization2D=new C.GeometryAttribute({componentDatatype:u.ComponentDatatype.FLOAT,componentsPerAttribute:2,normalize:!1,values:w}));return new C.Geometry({attributes:Da,indices:b,boundingSphere:La})}(y,E,G,W,j,V,w)}};var pe=new t.Cartesian3,ge=new g.Matrix3,fe=new C.Quaternion;function me(e,a,i,n){var r=Q(i,a,pe),o=t.Cartesian3.dot(r,e);if(o>R||o<H){var l=Q(n,i,te),u=o<H?s.CesiumMath.PI_OVER_TWO:-s.CesiumMath.PI_OVER_TWO,c=C.Quaternion.fromAxisAngle(l,u,fe),h=g.Matrix3.fromQuaternion(c,ge);return g.Matrix3.multiplyByVector(h,e,e),!0}return!1}var ve=new t.Cartographic,we=new t.Cartesian3,ye=new t.Cartesian3;function _e(e,a,i,n,r){var o=t.Cartographic.toCartesian(a,e._ellipsoid,we),l=t.Cartesian3.add(o,i,ye),u=!1,c=e._ellipsoid,h=c.cartesianToCartographic(l,ve);Math.abs(a.longitude-h.longitude)>s.CesiumMath.PI_OVER_TWO&&(u=!0,l=t.Cartesian3.subtract(o,i,ye),h=c.cartesianToCartographic(l,ve)),h.height=0;var d=e.project(h,r);return(r=t.Cartesian3.subtract(d,n,r)).z=0,r=t.Cartesian3.normalize(r,r),u&&t.Cartesian3.negate(r,r),r}var Te=new t.Cartesian3,Me=new t.Cartesian3;function Ee(e,a,i,n,r,s){var o=t.Cartesian3.subtract(a,e,Te);t.Cartesian3.normalize(o,o);var l=i-0,u=t.Cartesian3.multiplyByScalar(o,l,Me);t.Cartesian3.add(e,u,r);var c=n-1e3;u=t.Cartesian3.multiplyByScalar(o,c,Me),t.Cartesian3.add(a,u,s)}var be=new t.Cartesian3;function Pe(e,a){var i=f.Plane.getPointDistance(ne,e),n=f.Plane.getPointDistance(ne,a),r=be;s.CesiumMath.equalsEpsilon(i,0,s.CesiumMath.EPSILON2)?(r=Q(a,e,r),t.Cartesian3.multiplyByScalar(r,s.CesiumMath.EPSILON2,r),t.Cartesian3.add(e,r,e)):s.CesiumMath.equalsEpsilon(n,0,s.CesiumMath.EPSILON2)&&(r=Q(e,a,r),t.Cartesian3.multiplyByScalar(r,s.CesiumMath.EPSILON2,r),t.Cartesian3.add(a,r,a))}function Oe(e,a){var t=Math.abs(e.longitude),i=Math.abs(a.longitude);if(s.CesiumMath.equalsEpsilon(t,s.CesiumMath.PI,s.CesiumMath.EPSILON11)){var n=s.CesiumMath.sign(a.longitude);return e.longitude=n*(t-s.CesiumMath.EPSILON11),1}if(s.CesiumMath.equalsEpsilon(i,s.CesiumMath.PI,s.CesiumMath.EPSILON11)){var r=s.CesiumMath.sign(e.longitude);return a.longitude=r*(i-s.CesiumMath.EPSILON11),2}return 0}var Ie=new t.Cartographic,Le=new t.Cartographic,De=new t.Cartesian3,Se=new t.Cartesian3,ke=new t.Cartesian3,Ae=new t.Cartesian3,xe=new t.Cartesian3,Ne=new t.Cartesian3,Re=[Ie,Le],He=new r.Rectangle,ze=new t.Cartesian3,Ve=new t.Cartesian3,je=new t.Cartesian3,Ge=new t.Cartesian3,Be=new t.Cartesian3,We=new t.Cartesian3,Fe=new t.Cartesian3,Ye=new t.Cartesian3,Xe=new t.Cartesian3,qe=new t.Cartesian3,Ue=new t.Cartesian3,Ze=new t.Cartesian3,Qe=new t.Cartesian3,Je=new t.Cartesian3,Ke=new d.EncodedCartesian3,$e=new d.EncodedCartesian3,ea=new t.Cartesian3,aa=new t.Cartesian3,ta=new t.Cartesian3,ia=[new e.BoundingSphere,new e.BoundingSphere],na=[0,2,1,0,3,2,0,7,3,0,4,7,0,5,4,0,1,5,5,7,4,5,6,7,5,2,6,5,1,2,3,6,2,3,7,6],ra=na.length;function sa(e){return new C.GeometryAttribute({componentDatatype:u.ComponentDatatype.FLOAT,componentsPerAttribute:4,normalize:!1,values:e})}return z._projectNormal=_e,function(e,a){return k.initialize().then((function(){return n.defined(a)&&(e=z.unpack(e,a)),z.createGeometry(e)}))}}));
