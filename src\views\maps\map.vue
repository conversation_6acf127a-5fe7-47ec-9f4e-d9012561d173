<template>
  <div class="map-container">
    <Map3d  v-if="showWhichMap === '3D'"/>
    <Map2d
      v-else
      :layer-params="layerParams"
    />
  </div>
</template>

<script setup name="Map">
// import Map2d from "@/views/maps/map2d";
// import Map3d from "@/views/maps/map3d";

// 使用异步方式加载组件
// const Map2d = defineAsyncComponent(() =>
//   import('@/views/maps/map2d')
// )
// const Map3d = defineAsyncComponent(() =>
//   import('@/views/maps/map3d')
// )
import LoadingComp from "@/components/LoadingComp.vue"
// 在空闲时激活
const Map2d = defineAsyncComponent({
  loader: () =>import('@/views/maps/map2d'),
  loadingComponent: LoadingComp,
  delay: 200
})
const Map3d = defineAsyncComponent({
  loader: () =>import('@/views/maps/map3d'),
  loadingComponent: LoadingComp
})


import useMapViewStore from "@/store/modules/map/mapView.js"
const layerParams = ref()

const showWhichMap = computed(()=>{
  return useMapViewStore().showWhichMap
});
</script>

<style scoped lang="scss">
@import "@/styles/variables.module.scss";
.map-container {
  position: absolute;
  width: 100vw;
  height: $resourceContentHeight;
}
</style>
