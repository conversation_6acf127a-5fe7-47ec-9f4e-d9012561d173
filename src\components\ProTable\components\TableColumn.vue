<template>
  <el-table-column
    v-if="column.isShow"
    v-bind="column"
    :align="column.align ?? 'center'"
    :showOverflowTooltip="column.showOverflowTooltip ?? column.prop !== 'operation'"
  >
    <template #default="scope">
      <!-- 递归渲染子列 -->
      <template v-if="column._children">
        <!-- <component
          v-for="child in column._children"
          :key="child.prop"
          is="RenderTableColumn"
          :column="child"
        /> -->
      </template>
      <!-- 使用用户提供的渲染函数 -->
      <template v-else-if="column.render">
        {{ column.render(scope) }}
      </template>
      <!-- 使用插槽 -->
      <template v-else-if="column.prop && slots[handleProp(column.prop)]">
        <slot :name="handleProp(column.prop)" :scope="scope"></slot>
      </template>
      <!-- 使用el-tag展示数据 -->
      <template v-else-if="column.tag">
        <el-tag :type="getTagType(column, scope)">
          {{ renderCellData(column, scope) }}
        </el-tag>
      </template>
      <!-- 默认渲染方案 -->
      <template v-else>
        {{ renderCellData(column, scope) }}
      </template>
    </template>

    <template #header="scope">
      <template v-if="column.headerRender">
        {{ column.headerRender(scope) }}
      </template>
      <template v-else-if="column.prop && slots[`${handleProp(column.prop)}Header`]">
        <slot :name="`${handleProp(column.prop)}Header`" :scope="scope"></slot>
      </template>
      <template v-else>
        {{ column.label }}
      </template>
    </template>
  </el-table-column>
</template>

<script setup>
import { inject, ref, useSlots } from 'vue';
import { filterEnum, formatValue, handleProp, handleRowAccordingToProp } from '@/utils/proTable';

const props = defineProps({
  column: {
    type: Object,
    required: true,
    default: () => ({ isShow: true }) // 设置默认值
  },
});
const slots = useSlots();
const enumMap = inject('enumMap', ref(new Map()));

const renderCellData = (item, scope) => {
  return enumMap.value.get(item.prop) && item.isFilterEnum
    ? filterEnum(
        handleRowAccordingToProp(scope.row, item.prop),
        enumMap.value.get(item.prop),
        item.fieldNames
      )
    : formatValue(handleRowAccordingToProp(scope.row, item.prop));
};

const getTagType = (item, scope) => {
  return (
    filterEnum(
      handleRowAccordingToProp(scope.row, item.prop),
      enumMap.value.get(item.prop),
      item.fieldNames,
      'tag'
    ) || 'primary'
  );
};
</script>