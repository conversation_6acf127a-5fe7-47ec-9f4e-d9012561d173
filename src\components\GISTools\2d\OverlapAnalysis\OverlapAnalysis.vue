<template>
  <div class="overlap-container">
    <div class="header">
      <div class="title-overlap" :class="isOverlapMenu?'title-active':''" @click="overLapMenu">
        <span>叠加分析</span>
      </div>
      <div class="title-overlap" :class="isTxtMenu?'title-active':''" @click="txtMenu">
        <span>分析情况</span>
      </div>
      <div class="title-overlap" :class="isChartsMenu?'title-active':''" @click="chartsMenu">
        <span>图表展示</span>
      </div>
    </div>
    <div class="overlap-main">
      <div class="overlap-box">
        <div v-show="isOverlapMenu" class="overlap-content">
          <p class="describe">注：点击地图打开卷帘窗口</p>
          <div
            v-for="(layer, index) in overLayers"
            :key="index"
            :class="currentIndex===index?'active':''"
            class="over-div"
          >
            <div class="map-title">{{ layerTitleAndArea(layer) }}</div>
            <div class="over-map">
              <div :id="layer.value" class="map" @click="openSwipeWindow(layer,index)" />
              <div v-if="!layer.value || !layer.mapUrl.indexOf('iserver')<0" class="no-server-tip">
                <i class="iconfont icon-zanwushuju" />
                <span>无图层服务</span>
              </div>
            </div>
          </div>
        </div>
        <div v-show="isTxtMenu" class="txt-content">
          <txt-list :prj-info="prjInfo" :draw-area="drawArea" :classify-list="classifyList" />
        </div>
        <div v-show="isChartsMenu" class="charts-content">
          <Echarts :classify-list="classifyList" />
        </div>
      </div>
      <!--  神之操作  -->
      <div v-for="(layer, index) in overLayers" :key="index">
        <div v-if="currentIndex === index" class="map-window">
          <SwipeWindow
            :map-id="layer.value+'-swiper'"
            :over-layer="layer"
            :classify="classify"
            :geometry="geometry"
            @closeWindow="currentIndex=-1"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="叠加分析组件">
import TxtList from '@/components/GISTools/2d/OverlapAnalysis/TxtList'
import SwipeWindow from '@/components/GISTools/2d/OverlapAnalysis/SwipeWindow'
import Echarts from '@/components/GISTools/2d/OverlapAnalysis/EchartList'


import useMapInitStore from "@/store/modules/map/mapInit.js"
import { Map, View } from "ol";
import { defaults } from "ol/interaction"
import { addGeoJSON2Map, addLayer } from "@/utils/OpenLayers/olLayer.js"

const props = defineProps({
// 分析几何对象
  geometry: {
    type: Object,
    required: true
  },
  // 叠加分析图层
  overLayers: {
    type: Array,
    required: true
  },
  classifyList: {
    type: Array,
    required: true
  },
  drawArea: {
    type: Number,
    required: true,
    default: 0
  },
  prjInfo: {
    type: Object
  }
})

const { geometry,overLayers,classifyList,drawArea,prjInfo } = toRefs(props)

const activeName = ref('overlap')
// 卷帘新窗口图层
const swipeLayer = ref(undefined)
// 小地图图层
const layers = ref([])
const classify = ref({})
const currentIndex = ref(-1)
const index = ref(-1)
const isOverlapMenu = ref(true)
const isTxtMenu = ref(false)
const isChartsMenu = ref(false)

const mapInfo = computed(()=>useMapInitStore().mapInfo)

const layerTitleAndArea = (layer)=> {
  // console.log(this.classifyList)
  const targetLayer = this.classifyList.find(classifyItem => classifyItem.layerName === layer.label)
  const layerArea = targetLayer?.layerArea ? ' ( ' + (targetLayer?.layerArea * 1e-4).toFixed(4) + ' 公顷 )' : ' ( 0 )'
  return layer.label + layerArea
}

const initMap = ()=> {
  overLayers.value.forEach(layer => {
    // 需在循环中生成绘制，如果在循环外，则绘制对象无法正常显示
    const map = new Map({
      target: layer.value,
      view: new View({
        center: mapInfo.value.center,
        zoom: mapInfo.value.zoom,
        maxZoom: 18,
        minZoom: 7,
        projection: mapInfo.value.projection // 定义坐标系
      }),
      controls: [],
      interactions: defaults({
        mouseWheelZoom: false,
        dragPan: false
      })
    })
    const serviceUrl = layer.mapUrl

    const wmtsOptions = {
      url: serviceUrl,
      layerName: serviceUrl,
      isBaseMap: false,
      layerId: layer.id
    }
    // console.log("wmtsOptions配置：",wmtsOptions)
    addLayer(mapInfo.value.serviceType,wmtsOptions,map)
    addGeoJSON2Map(geometry.value,undefined,map)
  })
}
const openSwipeWindow = (layer, index)=> {
  currentIndex.value = index
  const obj = {
    layerName: layer.label
  }
  classify.value = classifyList.value.find(classify => classify.layerName === layer.label) || obj
}
const overLapMenu = ()=> {
  isOverlapMenu.value = true
  isTxtMenu.value = false
  isChartsMenu.value = false
}
const txtMenu = ()=> {
  isTxtMenu.value = true
  isOverlapMenu.value = false
  isChartsMenu.value = false
}
const chartsMenu = ()=> {
  isChartsMenu.value = true
  isOverlapMenu.value = false
  isTxtMenu.value = false
}

onMounted(()=>{
  initMap()
})
</script>

<style scoped lang="scss">
.header {
  text-align: center;
  margin-top: 20px;
  font-size: 18px;
  border-bottom: 1px solid #90939994;

  .title-overlap {
    cursor: pointer;
    display: inline-block;
    margin: 0 15px;
    padding-bottom: 15px;;
  }
}

.title-active {
  color: #0167cc;
  border-bottom: 2px solid #0167cc;
}

/** 地图激活样式 **/
.active {
  border: 2px solid yellow;
  box-sizing: border-box;
}

/* 初始定位 */
.overlap-main {
  padding: 10px;
  .tip {
    //background: #eeeeee96;
    height: 760px;
    text-align: center;
    padding-top: 300px;
    color: #fff;

    .icon-zanwushuju {
      font-size: 100px;
    }

    span {
      display: block;
      font-size: 16px;
      margin-top: 20px;
      color: #0167cc;
      font-weight: bold;
    }
  }

  .no-server-tip {
    display: flex;
    flex-direction: column;
    text-align: center;
    align-items: center;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    margin-left: 50%;
    margin-top: -25%;

    .iconfont {
      color: #ffffff;
      font-size: 100px;
      display: block;
    }

    span {
      margin-top: 50px;
      display: block;
      color: #3838387a;
      font-size: 14px;
    }
  }

  .overlap-content {
    max-height: 760px;
    overflow-y: auto;

    .describe {
      margin: 6px;
      font-size: 15px;
      font-weight: bold;
      background: #023464f7;
      padding: 10px;
      color: #f1a319;
    }

    .over-div {
      margin: 6px;
      box-sizing: border-box;
    }

    .map-title {
      text-align: center;
      font-size: 16px;
      color: #ffffff;
      background-color: #3184d6;
      height: 30px;
      line-height: 30px;
    }

    .over-map {
      position: relative;
      height: 200px;

      .map {
        width: 100%;
        height: 100%;
      }
    }
  }

  .txt-content {
    padding: 6px;
  }

  .charts-content {
    padding: 6px;
    max-height: 760px;
    overflow-y: auto;
    overflow-x: hidden;
  }

}

.info-div {
  position: absolute;
  top: 150px;
  right: 380px;
  padding: 10px;
  background: #fff;
}

.table-wrap {
  width: 250px;
  border: 1px solid #999999;

  td {
    border: 1px solid #733333;
    border-left: none;
  }
}

/** 小地图窗口 **/
.map-window {
  position: absolute;
  z-index: 1000;
  width: 528%;
  height: 100%;
  top: 1px;
  right: 4px;
  border: 2px solid #f6f8f9d1;
  box-shadow: 2px 1px 10px 6px #3b3232c9;
}

</style>
