define(["./Cartographic-3309dd0d","./when-b60132fc","./EllipseOutlineGeometry-877eb997","./Rectangle-dee65d21","./Check-7b2a090c","./Math-119be1a3","./arrayFill-4513d7ad","./buildModuleUrl-9085faaa","./FeatureDetection-806b12f0","./Cartesian4-3ca25aab","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Event-16a2dfbf","./ComponentDatatype-c140a87d","./EllipseGeometryLibrary-a39b75ad","./GeometryAttribute-c65394ac","./Cartesian2-db21342c","./GeometryAttributes-252e9929","./GeometryOffsetAttribute-fbeb6f1a","./IndexDatatype-8a5eead4"],(function(e,t,a,r,i,n,l,d,o,c,b,s,u,f,p,y,m,G,C,E){"use strict";return function(i,n){return t.defined(n)&&(i=a.EllipseOutlineGeometry.unpack(i,n)),i._center=e.Cartesian3.clone(i._center),i._ellipsoid=r.Ellipsoid.clone(i._ellipsoid),a.EllipseOutlineGeometry.createGeometry(i)}}));
