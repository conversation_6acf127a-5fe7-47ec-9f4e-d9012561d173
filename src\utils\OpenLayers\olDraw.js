/**
 * @description：ol绘制类
 * @author：zyc
 * @date:2023.11.27
 */
import VectorSource from "ol/source/Vector";
import VectorLayer from "ol/layer/Vector";
import Draw,{ createBox } from "ol/interaction/Draw";
import DoubleClickZoom from "ol/interaction/DoubleClickZoom";
import { geometryDefaultStyle } from "@/utils/OpenLayers/olStyles.js";

export default class OlDraw {
  constructor(map,view) {
    this.map = map
    this.view = view
    this._source = undefined
    this._draw = undefined
    this._feature = undefined
    // 禁用双击地图缩放事件
    const doubleClickZoom = new DoubleClickZoom()
    const doubleClickZoomArr = this.map.getInteractions().array_.filter(
      item => {
        return item.constructor.name === doubleClickZoom.constructor.name
      }
    )
    this.map.removeInteraction(doubleClickZoomArr[0])
  }
  /**
   * @description：绘制几何对象
   * @geometryType：Point-点、Polyline-线、Polygon-面
   * @isFreehand：是否开启手写绘制模式，默认为false，
   */
  drawGeometry(geometryType,isFreehand = false){
    // 返回要素
    const feature = ''
    let type = geometryType
    const style = geometryDefaultStyle[geometryType]

    const source = new VectorSource()
    this._source = source
    const layer = new VectorLayer({
      source,
      style,
      layerName: "drawLayer"
    })
    this.map.addLayer(layer)
    let geometryFunction
    if (geometryType === 'Box'){
      geometryFunction = createBox()
      type = "Circle"
    }
    const draw = new Draw({
      source: source,
      style: style,
      type: type,
      snapTolerance: 20, // 容差大小
      freehand: isFreehand, // 设置手写绘制
      geometryFunction
    })
    this.map.addInteraction(draw)
    this.setDrawAction(draw)
    this.setDrawSource(source)
    /**
     * 监听绘制开始事件
     */
    draw.on('drawstart',evt => {
      // write code here
    })
    /**
     * 监听绘制完成事件
     */
    draw.on('drawend',evt => {
      // write code here

      // const geoJson = new GeoJSON()
      // feature = geoJson.writeFeature(evt.feature)
      // console.log("要素：",feature);
      // console.log("area:",typeof feature)
      // // console.log("area:",typeof feature.geometry.coordinates)
      // const geometry = geoJson.writeGeometryObject(evt.feature.getGeometry())
      // const area = PlanarArea(geometry.coordinates,"Polygon")
      // console.log("area",area);
      // this.setFeature(feature)
    })
  }

  getDrawAction(){
    return this._draw
  }
  setDrawAction(drawAction){
    this._draw = drawAction
  }
  getDrawSource(){
    return this._source
  }
  setDrawSource(source){
    this._source = source
  }
  getFeature(){
    return this._feature
  }
  setFeature(feature){
    this._feature = feature
  }
  /**
   * @description：清除绘制对象
   * @param drawSource：绘制对象
   */
  clearGeometry(drawSource){
    drawSource.clear()
  }

  /**
   * @description：关闭绘制事件
   * @param drawAction：绘制事件
   */
  deactiveAction(drawAction){
    this.map.removeInteraction(drawAction)
    // 因为定义了window._map全局对象，还是考虑从window上移除绘制控件
    window._map.removeInteraction(drawAction)
  }
}
