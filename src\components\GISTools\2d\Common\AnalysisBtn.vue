<template>
  <div class="analyse-wrap">
    <el-button :disabled="isDisabled" :class="isDisabled?'disable-btn':'no-disable-btn'" class="start-analyse"
               @click="$emit('startAnalyse')"
    >
      <el-icon v-if="showIcon" :class="iconClass" >
        <DataAnalysis />
      </el-icon>
      <span>{{ analyseTitle }}</span>
    </el-button>
  </div>
</template>

<script setup name="公共按钮组件">
defineProps({
  analyseTitle: {
    type: String,
    require: true
  },
  isDisabled: {
    type: Boolean,
    default: true
  },
  iconClass: {
    type: String,
    default: 'el-icon-data-analysis'
  },
  showIcon: {
    type: Boolean,
    default: () => true
  }
})
</script>

<style scoped lang="scss">
.analyse-wrap {
  color: #fff;
  font-size: 14px;

  .start-analyse {
    display: block;
    padding: 10px;
    border-radius: 5px;
    color: rgb(96, 98, 102);
    text-align: center;
    width: 100%;
  }

  .disable-btn {
    background-color: #c8e7ff;
    border-color: #9e9e9e3d;
  }

  .no-disable-btn {
    color: #e9e9e9;
    background-color: #067af2e0;
    border-color: #00baff;

    &:hover {
      color: #fff;
      filter: brightness(110%) opacity(100%);
      transition: all .5s ease-in;
      background: linear-gradient(to bottom right, #0f7dff, #00cffa);
    }

    &:focus {
      filter: brightness(120%);
      transition: all .5s ease-in;
      background: radial-gradient(circle at center, #0f7dff, #00cffa);
    }
  }
}
</style>
