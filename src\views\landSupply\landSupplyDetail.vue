<template>
  <div class="project-detail">
    <div class="header">
      <div class="add-header-title">
        <div class="add-title">{{ headerTitle }}</div>
        <div
          class="add-title-return"
          @click="closeDetail"
        >
          <img
            src="@/assets/images/img-return.png"
            class="back"
          >
          <div class="backlist">返回列表</div>
        </div>
      </div>
    </div>
    <div class="project-content">
      <el-row>
        <el-col
          :span="11"
          class="project-info"
        >
          <el-descriptions
            class="margin-top"
            :column="1"
            size="default"
            border
          >
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">
                  土地用途
                </div>
              </template>
              {{projectForm.tdyt }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">
                  年份
                </div>
              </template>
              {{projectForm.dataYear }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">
                  行政区域
                </div>
              </template>
              {{projectForm.xzqmc }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">
                  总面积
                </div>
              </template>
              {{projectForm.zmj }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">
                  批准文号
                </div>
              </template>
              {{projectForm.pzwh }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">
                  批准日期
                </div>
              </template>
              {{projectForm.pzrq }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">
                  批准机关
                </div>
              </template>
              {{projectForm.pzjg }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">
                  上报人
                </div>
              </template>
              {{projectForm.sbr }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">
                  上报时间
                </div>
              </template>
              {{projectForm.sbsj }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">
                  实际交地时间
                </div>
              </template>
              {{projectForm.sjjdsj }}
            </el-descriptions-item>
          </el-descriptions>
        </el-col>
        <el-col
          :span="12"
          class="viewer-project"
        >
          <Viewer2d
            :viewerId="viewerProjectId"
            @onCreateViewer2d="createViewer2d"
          />
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup name="landSupplyDetail">
import { useRoute, useRouter } from "vue-router"
import { getTdgy } from "@/api/landSupply/landSuplly.js"
import UseViewer2d from "@/components/GISTools/Viewer/UseViewer2d.js"

const router = useRouter()
const route = useRoute()

const headerTitle = ref("查看项目详情")
const projectId = route.query.id
const projectForm = ref({})
const viewerProjectId = ref("viewerProjectId")

const getProjectById = async (id) => {
  const response = await getTdgy(id)
  projectForm.value = response.data
}

/**
 * 创建2d视图
 * @param viewer2dId
 * @param mapInitStore
 * @param mapViewStore
 */
const createViewer2d = (viewer2dId,mapInitStore,mapViewStore)=>{
  const useViewer2d = new UseViewer2d(viewer2dId,mapInitStore,mapViewStore)
  useViewer2d.createViewer2d()
}


const closeDetail = ()=>{
  router.push({ path: "/planStorage/planStorageProject" })
}

onBeforeMount(()=>{
  getProjectById(projectId)
})
</script>

<style scoped lang="scss">
.project-detail {
  padding: 10px;
}
.header {
  border: 1px solid rgb(233, 233, 233);
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  .add-header-title {
    padding: 10px;
    display: flex;
    justify-content: space-between;
    height: 50px;
    background-color: rgb(222, 239, 255);
    box-sizing: border-box;
    border-bottom: 1px solid rgb(233, 233, 233);
    font-weight: 700;
    font-size: 14px;
    line-height: 28px;
  }
  .add-title-return {
    display: flex;
    align-content: center;
    color: rgb(32, 119, 255);
    cursor: pointer;
    font-weight: normal;
    &:hover{
      cursor: pointer;
      font-size: 16px;
      transform: scale(1.15);
      transition: all ease-in .25s;
    }
  }
  .back-icon{
    margin-right: 5px;
  }
}

.project-content{
  margin-top: 20px;
}

.project-info{
  margin-right: 5px;
}
.viewer-project{
  border: 1px solid rgb(233, 233, 233);
}

</style>
