<template>
    <div>
        <el-dialog @closed="closed" v-model="dialogVisible" width="1000">
            <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
                <el-tab-pane label="批转记录" name="0" class="box">
                    <el-table :span-method="merge" border :data="tableData" stripe style="width: 100%">
                        <el-table-column prop="nodeName" label="处理阶段" width="200" />
                        <el-table-column prop="userName" label="处理人" />
                        <el-table-column prop="taskStatus" label="任务状态" width="200" />
                        <el-table-column prop="claimTime" label="签收时间" />
                        <el-table-column prop="finishTime" label="完成时间" />
                    </el-table>
                </el-tab-pane>
                <el-tab-pane label="办理流程图" name="1" class="box">
                    <imageView :imageUrl="imgSrc" />
                    <!-- <img :src="imgSrc" style="max-width: 100%;;height: auto;"> -->
                </el-tab-pane>

            </el-tabs>
        </el-dialog>
    </div>
</template>

<script setup>
import { getHistoryRecord, getHistoryImage } from '@/api/document/common'
import imageView from "@/components/History/imageView.vue";

import { onMounted, ref } from 'vue';
const perviewUrl = import.meta.env.VITE_APP_FILE_SERVICE_URL

const props = defineProps({
    businessId: {
        type: String,
    },
    activeName: {
        type: String
    }
});
const emit = defineEmits();
const closed = () => {
    emit('back')
}

const handleClick = (pane) => {

    if (pane.index === '0') {
        getHistoryRecord({ businessId: props.businessId }).then(res => {
            // let arr = []
            // res.data.map(item => {
            //     item.comments.map(it => {
            //         arr.push({
            //             ...item,
            //             msg: it.comment.msg,
            //             userName: it.comment.name,
            //             sign: it.comment.sign,
            //             createTime: it.createTime
            //         })
            //     })
            // })
            tableData.value = res.data
        })
    } else {
        getHistoryImage(props.businessId).then(res => {
            let base64 = "data:image/png;base64," + res.data

            imgSrc.value = base64


        })
    }
}

const imgSrc = ref('')
const dialogVisible = ref(false);
const activeName = ref('0');
const tableData = ref([])
function merge({ row, column, rowIndex, columnIndex }) {
    //根据列索引判断是否需要合并 
    // 可向后拼接(从0开始)，即2是第三列
    if (columnIndex === 0) {
        const currentValue = row.nodeName;
        // 获取上一行相同列的值
        const preRow = tableData.value[rowIndex - 1];
        const preValue = preRow ? preRow.nodeName : null;
        // 如果当前值和上一行的值相同，则将当前单元格隐藏
        if (currentValue === preValue) {
            return { 'rowspan': 0, 'colspan': 0 };
        } else {
            // 否则计算当前单元格应该跨越多少行
            let rowspan = 1;
            for (let i = rowIndex + 1; i < tableData.value.length; i++) {
                const nextRow = tableData.value[i];
                const nextValue = nextRow.nodeName;
                if (nextValue === currentValue) {
                    rowspan++;
                } else {
                    break;
                }
            }
            return { 'rowspan': rowspan, 'colspan': 1 };
        }
    }
    return {
        rowspan: 1,
        colspan: 1,
    };
}
onMounted(() => {
    dialogVisible.value = true
    activeName.value = props.activeName
    if (props.activeName === '0') {
        getHistoryRecord({ businessId: props.businessId }).then(res => {
            // let arr = []
            // res.data.map(item => {
            //     item.comments.map(it => {
            //         arr.push({
            //             ...item,
            //             msg: it.comment.msg,
            //             userName: it.comment.name,
            //             sign: it.comment.sign,
            //             createTime: it.createTime
            //         })
            //     })
            // })
            tableData.value = res.data
        })
    } else {
        getHistoryImage(props.businessId).then(res => {
            let base64 = "data:image/png;base64," + res.data

            imgSrc.value = base64


        })
    }

})
</script>
<style lang="scss">
.box {
    height: 600px;
    overflow-y: auto;
}
</style>