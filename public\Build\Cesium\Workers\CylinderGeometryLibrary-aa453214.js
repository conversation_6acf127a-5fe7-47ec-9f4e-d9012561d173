define(["exports","./Math-119be1a3"],(function(r,t){"use strict";var e={computePositions:function(r,e,a,i,n){var o,s=.5*r,u=-s,c=i+i,f=new Float64Array(3*(n?2*c:c)),h=0,y=0,M=n?3*c:0,m=n?3*(c+i):3*i;for(o=0;o<i;o++){var v=o/i*t.CesiumMath.TWO_PI,b=Math.cos(v),d=Math.sin(v),l=b*a,p=d*a,C=b*e,P=d*e;f[y+M]=l,f[y+M+1]=p,f[y+M+2]=u,f[y+m]=C,f[y+m+1]=P,f[y+m+2]=s,y+=3,n&&(f[h++]=l,f[h++]=p,f[h++]=u,f[h++]=C,f[h++]=P,f[h++]=s)}return f}};r.<PERSON>eo<PERSON>ib<PERSON>=e}));
