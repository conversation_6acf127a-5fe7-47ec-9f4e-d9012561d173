import request from "@/utils/request";

/**
 * 查询储备项目列表
 * @param query
 * @returns {*}
 */

export const listReserveProject = (query) => {
  return request({
    url: "/patrol/reserveProject/list",
    method: "get",
    params: query
  });
};

/**
 * 查询储备地块列表
 * @param id
 */

export const getReserveLandById = (id) => {
  return request({
    url: "/patrol/land/" + id,
    method: "get"
  });
};

/**
 * 查询储备地块列表
 * @param id
 */

export const reserveLandlist = (query) => {
  return request({
    url: "/patrol/land/list",
    method: "get",
    params: query
  });
};

/**
 * 删除储备地块信息
 */
export const deleteReserveLand = (id) => {
  return request({
    url: "/patrol/land/" + id,
    method: "delete"
  })
}


/**
 * 查询产权列表
 * @param id
 */

export const listReserveRight = (id) => {
  return request({
    url: "/patrol/reserveProject/rightList/" + id,
    method: "get"
  });
};

/**
 * 查询储备项目详细
 * @param id
 */
export const getReserveProject = (id) => {
  return request({
    url: "/patrol/reserveProject/" + id,
    method: "get"
  });
};

/**
 * 新增储备项目
 * @param data
 */
export const addReserveProject = (data) => {
  return request({
    url: "/patrol/reserveProject",
    method: "post",
    data: data
  });
};

/**
 * 修改储备项目
 * @param data
 */
export const updateReserveProject = (data) => {
  return request({
    url: "/patrol/reserveProject",
    method: "put",
    data: data
  });
};

/**
 * 删除储备项目
 * @param id
 */
export const delReserveProject = (id) => {
  return request({
    url: "/patrol/reserveProject/" + id,
    method: "delete"
  });
};
