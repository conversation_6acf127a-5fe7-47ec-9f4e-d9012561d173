<template>
  <div
    class="loading-container"
    :style="{ '--primary-color': primaryColor }"
  >
    <div class="loading-spinner">
      <div class="spinner-inner"/>
    </div>
    <div
      v-if="text"
      class="loading-text"
    >{{ text }}</div>
  </div>
</template>

<script setup>
const props = defineProps({
  text: {
    type: String,
    default: '加载中...'
  },
  primaryColor: {
    type: String,
    default: '#4f46e5' // 默认紫色
  }
})
</script>

<style scoped lang="scss">
.loading-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  position: relative;
}

.spinner-inner {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s linear infinite;
}

.loading-text {
  color: var(--primary-color);
  font-size: 14px;
  font-weight: 500;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>