import vue from "@vitejs/plugin-vue";
import legacy from "@vitejs/plugin-legacy";
import zipPack from "vite-plugin-zip-pack";

import createAutoImport from "./auto-import";
import createSvgIcon from "./svg-icon";
import createCompression from "./compression";
import createSetupExtend from "./setup-extend";
import { visualizer } from "rollup-plugin-visualizer";

export default function createVitePlugins(viteEnv, isBuild = false) {
  const vitePlugins = [vue()];
  vitePlugins.push(createAutoImport());
  vitePlugins.push(createSetupExtend());
  vitePlugins.push(createSvgIcon(isBuild));

  // 添加打包可视化插件
  vitePlugins.push(visualizer({
    // 打包完成后自动打开浏览器，显示产物体积报告
    open: true
  }));
  isBuild && vitePlugins.push(...createCompression(viteEnv))
  isBuild &&
    vitePlugins.push(
      zipPack({
        inDir: "dist",
        // outDir: 'archive',
        outFileName: `dist.zip`
        // pathPrefix: ''
      })
    );
  return vitePlugins;
}
