import vue from "@vitejs/plugin-vue";
import legacy from "@vitejs/plugin-legacy";
import zipPack from "vite-plugin-zip-pack";

import createAutoImport from "./auto-import";
import createSvgIcon from "./svg-icon";
import createCompression from "./compression";
import createSetupExtend from "./setup-extend";

export default function createVitePlugins(viteEnv, isBuild = false) {
  const vitePlugins = [vue()];
  vitePlugins.push(createAutoImport());
  vitePlugins.push(createSetupExtend());
  vitePlugins.push(createSvgIcon(isBuild));

  // isBuild && vitePlugins.push(...createCompression(viteEnv))
  isBuild &&
    vitePlugins.push(
      zipPack({
        inDir: "dist",
        // outDir: 'archive',
        outFileName: `dist.zip`
        // pathPrefix: ''
      })
    );
  return vitePlugins;
}
