<template>
  <div class="control-container">
    <el-row>
      <el-button type="primary" @click="proceed">继续</el-button>
      <el-button type="warning" @click="pause">暂停</el-button>
      <el-button type="danger" @click="stop">停止</el-button>
    </el-row>
  </div>
</template>

<script setup>

const { proxy } = getCurrentInstance()

const proceed = ()=> {
  proxy.$emit("flyLineProceed")
}
const pause = ()=> {
  proxy.$emit("flyLinePause")
}
const stop = ()=> {
  proxy.$emit("flyLineStop")
}
</script>

<style scoped lang="scss">
.control-container {
  position: fixed;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  box-shadow: 0 0 8px 0 #057595;
  border-radius: 8px;
  background-color: #223A4F;
  display: flex;
  flex-direction: row;
  padding: 10px;
}

.control-compare-position {
  position: absolute;
  bottom: 40px;
  right: calc(25vw - 110px);
  transform: translateX(50%);

  .el-row {
    display: flex;
    flex-direction: row;
  }
}

.control {
  position: fixed;
  bottom: 40px;
  right: 50%;
  transform: translateX(50%);
}
</style>
