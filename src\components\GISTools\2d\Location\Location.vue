<template>
  <div
    class="location-warp"
  >
    <div class="map-bg">
      <AnalysisHeader
        ref="Header"
        :title="locationTitle"
        :header-clicked="isLocationHeader"
        @click="isLocationHeader = !isLocationHeader"
      />
    </div>
    <transition name="fade">
      <div
        v-show="!isLocationHeader"
        class="location-div"
      >
        <div class="coords longitude">
          <label class="fontstyle">输入经度:</label>
          <el-input
            v-model="longitude"
            clearable
            onkeyup="value=value.replace(/[^0-9.]/g,'')"
          />
        </div>
        <div class="coords latitude">
          <label class="fontstyle">输入纬度:</label>
          <el-input
            v-model="latitude"
            clearable
            type="text"
            onkeyup="value=value.replace(/[^0-9.]/g,'')"
          />
        </div>
        <div class="location-btn">
          <AnalyseBtn
            class="start-analyse"
            :icon-class="'el-icon-location'"
            :is-disabled="false"
            :analyse-title="'定位'"
            @startAnalyse="location"
          />
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup name="地图定位">
import AnalyseBtn from "@/components/GISTools/2d/Common/AnalysisBtn";
import { createPulseEffect, removeLayerByName } from "@/utils/OpenLayers/olTool";
import { addGeoJSON2Map } from "@/utils/OpenLayers/olLayer";
import useMapInitStore from "@/store/modules/map/mapInit.js";
import { ElMessage } from "element-plus";
import AnalysisHeader from "@/components/GISTools/2d/Common/AnalysisHeader.vue"
import Style from "ol/style/Style";
import { RegularShape } from "ol/style.js";
import { Fill, Stroke } from "ol/style";

const longitude = ref(undefined);
const latitude = ref(undefined);
const locationTitle = ref("地图定位");
const isLocationHeader = ref(false);

const mapInfo = computed(() => useMapInitStore().mapInfo);

const setLocationInit = () => {
  longitude.value = mapInfo.value.center[0];
  latitude.value = mapInfo.value.center[1];
};

const location = () => {
  removeLayerByName("tempMark");
  const lng = Number(longitude.value);
  const lat = Number(latitude.value);
  if (isNaN(lng) || isNaN(lat)) {
    ElMessage.error("经纬度坐标输入格式不正确！");
    return;
  }
  // 设置闪烁点样式
  const geometry = {
    coordinates: [longitude.value, latitude.value],
    type: "Point",
    properties: {}
  };

  // const pointGeometry = new GeoJSON().readGeometry(geometry);

  // const style = createSinglePointStyle(pointGeometry)
  createPulseEffect(window._map,[longitude.value, latitude.value])

  const marker = addGeoJSON2Map(geometry,"");


  marker.setProperties({ layerName: "tempMark" });
};

const createSinglePointStyle = (geometry)=>{
  return new Style({
    geometry: geometry,
    fill: new Fill({}),
    image: new RegularShape({
      radius: 10,
      radius2: 3,
      points: 5,
      angle: Math.PI,
      fill: new Fill({
        // color: 'rgba(255,153,0,0.8)'
        color: `rgba(34, 255, 136, 0.8)`
      }),
      stroke: new Stroke({
        color: 'rgba(255,204,0,0.2)',
        width: 1
      })
    })
  })
}

onBeforeMount(() => {
  setLocationInit();
});
</script>

<style scoped lang="scss">
@import "@/styles/variables.module";
@import "@/styles/map2d.scss";
.location-warp {
  color: $mapMenuText;
  width: $functionContentWidth;
  .location-div {
    position: relative;
    border: 1px solid #ffffff42;
    padding: 10px;
    background: #409eff21;
    .coords {
      margin-bottom: 10px;
      label {
        line-height: 35px;
      }
    }
    .location-btn {
      padding: 0 10px;
      margin: 10px 0;
      width: 100%;
      color: #fff;
      font-size: 14px;
      .start-analyse {
        display: block;
        color: #fff;
        margin-bottom: 10px;
        border-radius: 5px;
        padding: 10px;
        text-align: center;
        width: 100%;
      }
      .disable-btn {
        background: #fff;
        border: 1px solid #dcdfe6;
        color: #606266;
      }
      .no-disable-btn {
        background: #fff;
        border: 1px solid #dcdfe6;
        color: #606266;
      }
      .el-button:hover,
      .el-button:focus {
        color: #fff;
        background-color: #0167cc;
      }
    }
  }
}
:deep(.el-input__inner) {
  color: #000;
  width: 150px;
}
:deep(.el-input__suffix) {
  margin-top: -5px;
}
.map-bg {
  background: url(/src/assets/icons/svg/u218.svg) no-repeat center center /cover;
}
.fontstyle {
  color: #ffffff;
  font-size: 14px;
}
</style>
