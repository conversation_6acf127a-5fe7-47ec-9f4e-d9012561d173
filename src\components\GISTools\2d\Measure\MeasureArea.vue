<template>
  <div class="measure">
    <div v-if="area>0" class="measure-div">
      <div class="unit">
        <p>单位</p>
        <el-select v-model="areaUnit" placeholder="请选择">
          <el-option
            v-for="item in areaOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
      <div class="show-res">
        <p>面积</p>
        <p>{{ Number(showArea).toFixed(4)+" "+areaUnit }}</p>
      </div>
    </div>
  </div>
</template>

<script setup name="面积测量">

const props = defineProps({
  area: {
    type: Number,
    require: true,
    default: 0
  }
})
const { area } = toRefs(props)

const areaUnit = ref('hec')
const areaOptions = reactive([
  {
    value: 'm²',
    label: '平方米'
  },
  {
    value: 'km²',
    label: '平方千米'
  },
  {
    value: 'mu',
    label: '亩'
  },
  {
    value: 'hec',
    label: '公顷'
  }
])
const isArea = ref(false)

const showArea = computed(()=>{
  if (areaUnit.value !== '') {
    switch (areaUnit.value) {
      case 'm²':
        return area.value * 1e4
      case 'km²':
        return area.value * 1e-2
      case 'mu':
        return area.value * 15
      case 'hec':
        return area.value
    }
  }
  return ''
})

</script>

<style scoped lang="scss">
.measure-div{
  .unit{
    display: flex;
    justify-content: space-between;
    padding: 10px;
    margin: 10px 0;
    .el-select{
      width: 180px;
    }
    p{
      margin-top: 0;
      margin-bottom: 10px;
    }
  }
  .show-res{
    margin: 10px 0;
    padding: 10px;
    p:first-child{
      margin-bottom: 10px;
    }
  }
}
:deep(.el-input--suffix){
  width: 250px;
}
</style>
