/**
 * 消息管理
 */

import request from '@/utils/request'

/** 
 * 发件箱
 * @param {string} pageSize 分页大小
  * @param {string} pageNum 当前页数
  * @param {string} orderByColumn 排序列
  * @param {string} isAsc 排序的方向desc或者asc
  * @param {string} id 主键
  * @param {string} receiveUserId 收件人ID
  * @param {string} receiveUserName 收件人姓名
  * @param {string} title 主题
  * @param {string} readFlag 阅读标记：0-未阅 1-已阅
  * @param {string} sendTime 发送时间
  * @returns
 */
export function getOutboxQuery(query) {
    return request({
        url: '/document/message/outbox/page',
        method: 'get',
        params: query
    })
}

/** 
 * 发送消息
 * @param {object} params 消息管理Bo
 * @param {string} params.id id
 * @param {array} params.receivers 选择人员列表
 * @param {string} params.title 主题
 * @param {array} params.affixList 附件标题
 * @returns
 */
export function addMessage(data) {
    return request({
        url: '/document/message',
        method: 'post',
        data: data
    })
}

/** 
 * 查询消息详情
 * @param {string} id 
  * @returns
 */
export function getMessageDetail(id) {
    return request.get(`/document/message/${id}`);
  }

/** 
 * 收件箱
 * @param {string} pageSize 分页大小
  * @param {string} pageNum 当前页数
  * @param {string} orderByColumn 排序列
  * @param {string} isAsc 排序的方向desc或者asc
  * @param {string} id 主键
  * @param {string} title 主题
  * @param {string} content 内容
  * @param {string} readFlag 阅读标记：0-未阅 1-已阅
  * @param {string} receivers 简单用户DTO
  * @param {string} sendTime 发送时间
  * @param {string} sendTimeStart 
  * @param {string} sendTimeEnd 查询结束时间
  * @returns
 */
export function getInboxQuery(query) {
    return request({
        url: '/document/message/inbox/page',
        method: 'get',
        params: query
    })
}

/** 
 * 未读消息
 * @param {string} pageSize 分页大小
  * @param {string} pageNum 当前页数
  * @param {string} orderByColumn 排序列
  * @param {string} isAsc 排序的方向desc或者asc
  * @param {string} id 主键
  * @param {string} title 主题
  * @param {string} content 内容
  * @param {string} readFlag 阅读标记：0-未阅 1-已阅
  * @param {string} receivers 简单用户DTO
  * @param {string} sendTime 发送时间
  * @param {string} sendTimeStart 
  * @param {string} sendTimeEnd 查询结束时间
  * @returns
 */
export function getUnreadQuery(query) {
    return request({
        url: '/document/message/inbox/unread/page',
        method: 'get',
        params: query
    })
}

/** 
 * 已读消息
 * @param {string} pageSize 分页大小
  * @param {string} pageNum 当前页数
  * @param {string} orderByColumn 排序列
  * @param {string} isAsc 排序的方向desc或者asc
  * @param {string} id 主键
  * @param {string} title 主题
  * @param {string} content 内容
  * @param {string} readFlag 阅读标记：0-未阅 1-已阅
  * @param {string} receivers 简单用户DTO
  * @param {string} sendTime 发送时间
  * @param {string} sendTimeStart 
  * @param {string} sendTimeEnd 查询结束时间
  * @returns
 */
export function getReadQuery(query) {
    return request({
        url: '/document/message/inbox/read/page',
        method: 'get',
        params: query
    })
}

/** 
 * 阅读消息
 * @param {string} id 
  * @returns
 */
export function messageRead(id) {
    return request.get(`/document/message/read?id=${id}`);
  }

/** 
 * 获取未读消息数
 * @returns
 */
export function getUnReadNum() {
    return request.get(`/document/message/unReadNum`);
  }

/** 
 * 删除自己发送的消息
 * @param {string} ids 
  * @returns
 */
export function delMessage(ids) {
    return request.delete(`/document/message?ids=${ids}`);
  }