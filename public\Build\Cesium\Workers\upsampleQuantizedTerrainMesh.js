define(["./AttributeCompression-0a087f75","./buildModuleUrl-9085faaa","./Cartesian2-db21342c","./Cartographic-3309dd0d","./when-b60132fc","./Rectangle-dee65d21","./TerrainEncoding-bc895579","./IndexDatatype-8a5eead4","./Check-7b2a090c","./Math-119be1a3","./OrientedBoundingBox-3b145304","./createTaskProcessorWorker","./FeatureDetection-806b12f0","./Cartesian4-3ca25aab","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Event-16a2dfbf","./ComponentDatatype-c140a87d","./EllipsoidTangentPlane-1dfa0a87","./IntersectionTests-0d6905a3","./Plane-a3d8b3d2","./GeometryAttribute-c65394ac","./PolygonPipeline-d83979ed","./earcut-2.2.1-20c8012f","./EllipsoidRhumbLine-30b5229b"],(function(e,i,t,n,s,r,h,u,a,o,d,p,l,f,g,c,m,x,v,C,w,B,y,b,A){"use strict";var I={clipTriangleAtAxisAlignedThreshold:function(e,i,t,n,r,h){var u,a,o;s.defined(h)?h.length=0:h=[],i?(u=t<e,a=n<e,o=r<e):(u=t>e,a=n>e,o=r>e);var d,p,l,f,g,c,m=u+a+o;return 1===m?u?(d=(e-t)/(n-t),p=(e-t)/(r-t),h.push(1),h.push(2),1!==p&&(h.push(-1),h.push(0),h.push(2),h.push(p)),1!==d&&(h.push(-1),h.push(0),h.push(1),h.push(d))):a?(l=(e-n)/(r-n),f=(e-n)/(t-n),h.push(2),h.push(0),1!==f&&(h.push(-1),h.push(1),h.push(0),h.push(f)),1!==l&&(h.push(-1),h.push(1),h.push(2),h.push(l))):o&&(g=(e-r)/(t-r),c=(e-r)/(n-r),h.push(0),h.push(1),1!==c&&(h.push(-1),h.push(2),h.push(1),h.push(c)),1!==g&&(h.push(-1),h.push(2),h.push(0),h.push(g))):2===m?u||t===e?a||n===e?o||r===e||(p=(e-t)/(r-t),l=(e-n)/(r-n),h.push(2),h.push(-1),h.push(0),h.push(2),h.push(p),h.push(-1),h.push(1),h.push(2),h.push(l)):(c=(e-r)/(n-r),d=(e-t)/(n-t),h.push(1),h.push(-1),h.push(2),h.push(1),h.push(c),h.push(-1),h.push(0),h.push(1),h.push(d)):(f=(e-n)/(t-n),g=(e-r)/(t-r),h.push(0),h.push(-1),h.push(1),h.push(0),h.push(f),h.push(-1),h.push(2),h.push(0),h.push(g)):3!==m&&(h.push(0),h.push(1),h.push(2)),h},computeBarycentricCoordinates:function(e,i,t,r,h,u,a,o,d){var p=t-a,l=a-h,f=u-o,g=r-o,c=1/(f*p+l*g),m=i-o,x=e-a,v=(f*x+l*m)*c,C=(-g*x+p*m)*c,w=1-v-C;return s.defined(d)?(d.x=v,d.y=C,d.z=w,d):new n.Cartesian3(v,C,w)},computeLineSegmentLineSegmentIntersection:function(e,i,n,r,h,u,a,o,d){var p=(o-u)*(n-e)-(a-h)*(r-i);if(0!==p){var l=((a-h)*(i-u)-(o-u)*(e-h))/p,f=((n-e)*(i-u)-(r-i)*(e-h))/p;return l>=0&&l<=1&&f>=0&&f<=1?(s.defined(d)||(d=new t.Cartesian2),d.x=e+l*(n-e),d.y=i+l*(r-i),d):void 0}}},M=32767,H=16383,T=[],z=[],E=[],N=new n.Cartographic,V=new n.Cartesian3,R=[],O=[],U=[],P=[],F=[],S=new n.Cartesian3,D=new i.BoundingSphere,L=new d.OrientedBoundingBox,X=new t.Cartesian2,k=new n.Cartesian3;function W(){this.vertexBuffer=void 0,this.index=void 0,this.first=void 0,this.second=void 0,this.ratio=void 0}W.prototype.clone=function(e){return s.defined(e)||(e=new W),e.uBuffer=this.uBuffer,e.vBuffer=this.vBuffer,e.heightBuffer=this.heightBuffer,e.normalBuffer=this.normalBuffer,e.index=this.index,e.first=this.first,e.second=this.second,e.ratio=this.ratio,e},W.prototype.initializeIndexed=function(e,i,t,n,s){this.uBuffer=e,this.vBuffer=i,this.heightBuffer=t,this.normalBuffer=n,this.index=s,this.first=void 0,this.second=void 0,this.ratio=void 0},W.prototype.initializeFromClipResult=function(e,i,t){var n=i+1;return-1!==e[i]?t[e[i]].clone(this):(this.vertexBuffer=void 0,this.index=void 0,this.first=t[e[n]],++n,this.second=t[e[n]],++n,this.ratio=e[n],++n),n},W.prototype.getKey=function(){return this.isIndexed()?this.index:JSON.stringify({first:this.first.getKey(),second:this.second.getKey(),ratio:this.ratio})},W.prototype.isIndexed=function(){return s.defined(this.index)},W.prototype.getH=function(e,i){if(s.defined(this.index))return this.heightBuffer[this.index];var t=this.first.getH(e,i),n=this.second.getH(e,i);return 0===i+t/M*e||0===i+n/M*e?0:o.CesiumMath.lerp(this.first.getH(),this.second.getH(),this.ratio)},W.prototype.getU=function(){return s.defined(this.index)?this.uBuffer[this.index]:o.CesiumMath.lerp(this.first.getU(),this.second.getU(),this.ratio)},W.prototype.getV=function(){return s.defined(this.index)?this.vBuffer[this.index]:o.CesiumMath.lerp(this.first.getV(),this.second.getV(),this.ratio)};var K=new t.Cartesian2,Y=-1,_=[new n.Cartesian3,new n.Cartesian3],G=[new n.Cartesian3,new n.Cartesian3];function J(i,t){++Y;var s=_[Y],r=G[Y];return s=e.AttributeCompression.octDecode(i.first.getNormalX(),i.first.getNormalY(),s),r=e.AttributeCompression.octDecode(i.second.getNormalX(),i.second.getNormalY(),r),V=n.Cartesian3.lerp(s,r,i.ratio,V),n.Cartesian3.normalize(V,V),e.AttributeCompression.octEncode(V,t),--Y,t}W.prototype.getNormalX=function(){return s.defined(this.index)?this.normalBuffer[2*this.index]:(K=J(this,K)).x},W.prototype.getNormalY=function(){return s.defined(this.index)?this.normalBuffer[2*this.index+1]:(K=J(this,K)).y};var Z=[];function j(e,i,t,n,r,h,u,a,o,d,p){if(0!==u.length){for(var l=0,f=0;f<u.length;)f=Z[l++].initializeFromClipResult(u,f,a);for(var g=0;g<l;++g){var c=Z[g];if(c.isIndexed())c.newIndex=h[c.index],c.uBuffer=e,c.vBuffer=i,c.heightBuffer=t,o&&(c.normalBuffer=n);else{var m=c.getKey();if(s.defined(h[m]))c.newIndex=h[m];else{var x=e.length;e.push(c.getU()),i.push(c.getV()),t.push(c.getH(d,p)),o&&(n.push(c.getNormalX()),n.push(c.getNormalY())),c.newIndex=x,h[m]=x}}}3===l?(r.push(Z[0].newIndex),r.push(Z[1].newIndex),r.push(Z[2].newIndex)):4===l&&(r.push(Z[0].newIndex),r.push(Z[1].newIndex),r.push(Z[2].newIndex),r.push(Z[0].newIndex),r.push(Z[2].newIndex),r.push(Z[3].newIndex))}}return Z.push(new W),Z.push(new W),Z.push(new W),Z.push(new W),p((function(e,t){var a=e.isEastChild,p=e.isNorthChild,l=a?H:0,f=a?M:H,g=p?H:0,c=p?M:H,m=R,x=O,v=U,C=F;m.length=0,x.length=0,v.length=0,C.length=0;var w=P;w.length=0;var B={},y=e.vertices,b=e.indices;b=b.subarray(0,e.indexCountWithoutSkirts);var A,K,Y,_,G,J=h.TerrainEncoding.clone(e.encoding),Z=J.hasVertexNormals,q=e.exaggeration,Q=0,$=e.vertexCountWithoutSkirts,ee=e.minimumHeight,ie=e.maximumHeight,te=s.defined(e.validMinimumHeight)?e.validMinimumHeight:e.minimumHeight,ne=s.defined(e.validMaximumHeight)?e.validMaximumHeight:e.maximumHeight,se=new Array($),re=new Array($),he=new Array($),ue=Z?new Array(2*$):void 0;for(K=0,Y=0;K<$;++K,Y+=2){var ae=J.decodeTextureCoordinates(y,K,X);if(A=J.decodeHeight(y,K)/q,_=o.CesiumMath.clamp(ae.x*M|0,0,M),G=o.CesiumMath.clamp(ae.y*M|0,0,M),he[K]=o.CesiumMath.clamp((A-ee)/(ie-ee)*M|0,0,M),_<20&&(_=0),G<20&&(G=0),M-_<20&&(_=M),M-G<20&&(G=M),se[K]=_,re[K]=G,Z){var oe=J.getOctEncodedNormal(y,K,k);ue[Y]=oe.x,ue[Y+1]=oe.y}(a&&_>=H||!a&&_<=H)&&(p&&G>=H||!p&&G<=H)&&(B[K]=Q,m.push(_),x.push(G),v.push(he[K]),Z&&(C.push(ue[Y]),C.push(ue[Y+1])),++Q)}var de=[];de.push(new W),de.push(new W),de.push(new W);var pe,le=[];for(le.push(new W),le.push(new W),le.push(new W),K=0;K<b.length;K+=3){var fe=b[K],ge=b[K+1],ce=b[K+2],me=se[fe],xe=se[ge],ve=se[ce];de[0].initializeIndexed(se,re,he,ue,fe),de[1].initializeIndexed(se,re,he,ue,ge),de[2].initializeIndexed(se,re,he,ue,ce);var Ce=I.clipTriangleAtAxisAlignedThreshold(H,a,me,xe,ve,T);(pe=0)>=Ce.length||((pe=le[0].initializeFromClipResult(Ce,pe,de))>=Ce.length||(pe=le[1].initializeFromClipResult(Ce,pe,de))>=Ce.length||(pe=le[2].initializeFromClipResult(Ce,pe,de),j(m,x,v,C,w,B,I.clipTriangleAtAxisAlignedThreshold(H,p,le[0].getV(),le[1].getV(),le[2].getV(),z),le,Z,ie,ee),pe<Ce.length&&(le[2].clone(le[1]),le[2].initializeFromClipResult(Ce,pe,de),j(m,x,v,C,w,B,I.clipTriangleAtAxisAlignedThreshold(H,p,le[0].getV(),le[1].getV(),le[2].getV(),z),le,Z,ie,ee))))}var we=a?-32767:0,Be=p?-32767:0,ye=[],be=[],Ae=[],Ie=[],Me=Number.MAX_VALUE,He=-Me,Te=Number.MAX_VALUE,ze=-Me,Ee=E;Ee.length=0;var Ne=r.Ellipsoid.clone(e.ellipsoid),Ve=r.Rectangle.clone(e.childRectangle),Re=Ve.north,Oe=Ve.south,Ue=Ve.east,Pe=Ve.west;for(Ue<Pe&&(Ue+=o.CesiumMath.TWO_PI),K=0;K<m.length;++K){(_=Math.round(m[K]))<=l?(ye.push(K),_=0):_>=f?(Ae.push(K),_=M):_=2*_+we,m[K]=_,(G=Math.round(x[K]))<=g?(be.push(K),G=0):G>=c?(Ie.push(K),G=M):G=2*G+Be,x[K]=G,(A=o.CesiumMath.lerp(ee,ie,v[K]/M))<Me&&(Me=A),A>He&&(He=A);var Fe=A;(Fe=o.CesiumMath.clamp(Fe,te,ne))<Te&&(Te=Fe),Fe>ze&&(ze=Fe),v[K]=A,N.longitude=o.CesiumMath.lerp(Pe,Ue,_/M),N.latitude=o.CesiumMath.lerp(Oe,Re,G/M),N.height=A,Ne.cartographicToCartesian(N,V),Ee.push(V.x),Ee.push(V.y),Ee.push(V.z)}var Se=i.BoundingSphere.fromVertices(Ee,n.Cartesian3.ZERO,3,D),De=d.OrientedBoundingBox.fromRectangle(Ve,Me,He,Ne,L),Le=d.OrientedBoundingBox.fromRectangle(Ve,Te,ze,Ne,L),Xe=new h.EllipsoidalOccluder(Ne).computeHorizonCullingPointFromVerticesPossiblyUnderEllipsoid(Se.center,Ee,3,Se.center,Me,S),ke=He-Me,We=new Uint16Array(m.length+x.length+v.length);for(K=0;K<m.length;++K)We[K]=m[K];var Ke=m.length;for(K=0;K<x.length;++K)We[Ke+K]=x[K];for(Ke+=x.length,K=0;K<v.length;++K)We[Ke+K]=M*(v[K]-Me)/ke;var Ye,_e=u.IndexDatatype.createTypedArray(m.length,w);if(Z){var Ge=new Uint8Array(C);t.push(We.buffer,_e.buffer,Ge.buffer),Ye=Ge.buffer}else t.push(We.buffer,_e.buffer);return{vertices:We.buffer,encodedNormals:Ye,indices:_e.buffer,minimumHeight:Me,maximumHeight:He,westIndices:ye,southIndices:be,eastIndices:Ae,northIndices:Ie,boundingSphere:Se,orientedBoundingBox:De,horizonOcclusionPoint:Xe,validMinimumHeight:Te,validMaximumHeight:ze,validOrientedBoundingBox:Le}}))}));
