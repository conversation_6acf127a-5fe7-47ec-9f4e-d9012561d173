define(["./when-b60132fc","./Rectangle-dee65d21","./ArcType-29cf2197","./arrayFill-4513d7ad","./BoundingRectangle-143a34da","./buildModuleUrl-9085faaa","./Cartesian2-db21342c","./Cartographic-3309dd0d","./Check-7b2a090c","./ComponentDatatype-c140a87d","./EllipsoidGeodesic-139a7db9","./EllipsoidTangentPlane-1dfa0a87","./GeometryAttribute-c65394ac","./GeometryInstance-6bd4503d","./GeometryOffsetAttribute-fbeb6f1a","./GeometryPipeline-7a733318","./IndexDatatype-8a5eead4","./Math-119be1a3","./FeatureDetection-806b12f0","./PolygonGeometryLibrary-8b220fb0","./PolygonPipeline-d83979ed","./VertexFormat-6446fca0","./Event-16a2dfbf","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Cartesian4-3ca25aab","./IntersectionTests-0d6905a3","./Plane-a3d8b3d2","./AttributeCompression-0a087f75","./EncodedCartesian3-f1396b05","./arrayRemoveDuplicates-d2f048c5","./EllipsoidRhumbLine-30b5229b","./GeometryAttributes-252e9929","./earcut-2.2.1-20c8012f"],(function(e,t,o,r,a,i,n,s,l,u,d,p,c,m,g,y,h,f,b,_,v,C,x,P,T,w,A,E,I,G,V,F,H,N){"use strict";var O=new s.Cartographic,R=new s.Cartographic;function D(e,t,o,r){var a=r.cartesianToCartographic(e,O).height,i=r.cartesianToCartographic(t,R);i.height=a,r.cartographicToCartesian(i,t);var n=r.cartesianToCartographic(o,R);n.height=a-100,r.cartographicToCartesian(n,o)}var L=new a.BoundingRectangle,M=new s.Cartesian3,B=new s.Cartesian3,S=new s.Cartesian3,k=new s.Cartesian3,z=new s.Cartesian3,W=new s.Cartesian3,Y=new s.Cartesian3,U=new s.Cartesian3,j=new s.Cartesian3,Q=new n.Cartesian2,q=new n.Cartesian2,K=new s.Cartesian3,Z=new c.Quaternion,J=new b.Matrix3,X=new b.Matrix3;function $(t){var o=t.vertexFormat,a=t.geometry,i=t.shadowVolume,l=a.attributes.position.values,d=l.length,p=t.wall,m=t.top,y=t.bottom;if(o.st||o.normal||o.tangent||o.bitangent||i){var h=t.boundingRectangle,_=t.tangentPlane,v=t.ellipsoid,C=t.stRotation,x=t.perPositionHeight,P=Q;P.x=h.x,P.y=h.y;var T,w=t.isComputeTexCoord?new Float32Array(d):new Float32Array(d/3*2),A=o.st?w:void 0;o.normal&&(T=x&&m&&!p?a.attributes.normal.values:new Float32Array(d));var E=o.tangent?new Float32Array(d):void 0,I=o.bitangent?new Float32Array(d):void 0,G=i?new Float32Array(d):void 0,V=0,F=0,H=B,N=S,O=k,R=!0,L=J,$=X;if(0!==C){var ee=c.Quaternion.fromAxisAngle(_._plane.normal,C,Z);L=b.Matrix3.fromQuaternion(ee,L),ee=c.Quaternion.fromAxisAngle(_._plane.normal,-C,Z),$=b.Matrix3.fromQuaternion(ee,$)}else L=b.Matrix3.clone(b.Matrix3.IDENTITY,L),$=b.Matrix3.clone(b.Matrix3.IDENTITY,$);var te=0;(m&&y||p)&&(te=d/2,d/=2);var oe=1,re=[];if(o.st&&p&&t.isComputeTexCoord){let e=[...t.outerPositions,t.outerPositions[0]];for(let t=1;t<e.length;t++){oe+=s.Cartesian3.distance(e[t-1],e[t]),re.push(oe)}}for(var ae=0;ae<d;ae+=3){var ie=s.Cartesian3.fromArray(l,ae,K);if(o.st){var ne=b.Matrix3.multiplyByVector(L,ie,M);ne=v.scaleToGeodeticSurface(ne,ne);var se=_.projectPointOntoPlane(ne,q);n.Cartesian2.subtract(se,P,se);var le=f.CesiumMath.clamp(se.x/h.width,0,1),ue=f.CesiumMath.clamp(se.y/h.height,0,1);if(p&&oe>1){let e=Math.ceil(ae/6)-1;le=re[e]?re[e]/oe:0,A[V]=1-le,A[V+1]=1,A[V+2]=0,A[V+te]=1-le,A[V+1+te]=0,A[V+2+te]=0}y&&(A[V+te]=le,A[V+1+te]=ue,t.isComputeTexCoord&&(A[V+2+te]=-1)),m&&(A[V]=le,A[V+1]=ue,t.isComputeTexCoord&&(A[V+2]=1)),t.isComputeTexCoord?V+=3:V+=2}if(o.normal||o.tangent||o.bitangent||i){var de=F+1,pe=F+2;if(p){if(ae+3<d){var ce=s.Cartesian3.fromArray(l,ae+3,z);if(R){var me=s.Cartesian3.fromArray(l,ae+d,W);x&&D(ie,ce,me,v),s.Cartesian3.subtract(ce,ie,ce),s.Cartesian3.subtract(me,ie,me),H=s.Cartesian3.normalize(s.Cartesian3.cross(me,ce,H),H),R=!1}s.Cartesian3.equalsEpsilon(ce,ie,f.CesiumMath.EPSILON10)&&(R=!0)}(o.tangent||o.bitangent)&&(O=v.geodeticSurfaceNormal(ie,O),o.tangent&&(N=s.Cartesian3.normalize(s.Cartesian3.cross(O,H,N),N)))}else H=v.geodeticSurfaceNormal(ie,H),(o.tangent||o.bitangent)&&(x&&(Y=s.Cartesian3.fromArray(T,F,Y),U=s.Cartesian3.cross(s.Cartesian3.UNIT_Z,Y,U),U=s.Cartesian3.normalize(b.Matrix3.multiplyByVector($,U,U),U),o.bitangent&&(j=s.Cartesian3.normalize(s.Cartesian3.cross(Y,U,j),j))),N=s.Cartesian3.cross(s.Cartesian3.UNIT_Z,H,N),N=s.Cartesian3.normalize(b.Matrix3.multiplyByVector($,N,N),N),o.bitangent&&(O=s.Cartesian3.normalize(s.Cartesian3.cross(H,N,O),O)));o.normal&&(t.wall?(T[F+te]=H.x,T[de+te]=H.y,T[pe+te]=H.z):y&&(T[F+te]=-H.x,T[de+te]=-H.y,T[pe+te]=-H.z),(m&&!x||p)&&(T[F]=H.x,T[de]=H.y,T[pe]=H.z)),i&&(p&&(H=v.geodeticSurfaceNormal(ie,H)),G[F+te]=-H.x,G[de+te]=-H.y,G[pe+te]=-H.z),o.tangent&&(t.wall?(E[F+te]=N.x,E[de+te]=N.y,E[pe+te]=N.z):y&&(E[F+te]=-N.x,E[de+te]=-N.y,E[pe+te]=-N.z),m&&(x?(E[F]=U.x,E[de]=U.y,E[pe]=U.z):(E[F]=N.x,E[de]=N.y,E[pe]=N.z))),o.bitangent&&(y&&(I[F+te]=O.x,I[de+te]=O.y,I[pe+te]=O.z),m&&(x?(I[F]=j.x,I[de]=j.y,I[pe]=j.z):(I[F]=O.x,I[de]=O.y,I[pe]=O.z))),F+=3}}o.st&&(a.attributes.st=new c.GeometryAttribute({componentDatatype:u.ComponentDatatype.FLOAT,componentsPerAttribute:t.isComputeTexCoord?3:2,values:A})),o.normal&&(a.attributes.normal=new c.GeometryAttribute({componentDatatype:u.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:T})),o.tangent&&(a.attributes.tangent=new c.GeometryAttribute({componentDatatype:u.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:E})),o.bitangent&&(a.attributes.bitangent=new c.GeometryAttribute({componentDatatype:u.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:I})),i&&(a.attributes.extrudeDirection=new c.GeometryAttribute({componentDatatype:u.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:G}))}if(t.extrude&&e.defined(t.offsetAttribute)){var ge=l.length/3,ye=new Uint8Array(ge);if(t.offsetAttribute===g.GeometryOffsetAttribute.TOP)m&&y||p?ye=r.arrayFill(ye,1,0,ge/2):m&&(ye=r.arrayFill(ye,1));else{var he=t.offsetAttribute===g.GeometryOffsetAttribute.NONE?0:1;ye=r.arrayFill(ye,he)}a.attributes.applyOffset=new c.GeometryAttribute({componentDatatype:u.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:ye})}return a}var ee=new s.Cartographic,te=new s.Cartographic,oe={west:0,east:0},re=new d.EllipsoidGeodesic;function ae(r,a,i,n,s){if(s=e.defaultValue(s,new t.Rectangle),!e.defined(r)||r.length<3)return s.west=0,s.north=0,s.south=0,s.east=0,s;if(i===o.ArcType.RHUMB)return t.Rectangle.fromCartesianArray(r,a,s);re.ellipsoid.equals(a)||(re=new d.EllipsoidGeodesic(void 0,void 0,a)),s.west=Number.POSITIVE_INFINITY,s.east=Number.NEGATIVE_INFINITY,s.south=Number.POSITIVE_INFINITY,s.north=Number.NEGATIVE_INFINITY,oe.west=Number.POSITIVE_INFINITY,oe.east=Number.NEGATIVE_INFINITY;for(var l,u=1/f.CesiumMath.chordLength(n,a.maximumRadius),p=r.length,c=a.cartesianToCartographic(r[0],te),m=ee,g=1;g<p;g++)l=m,m=c,c=a.cartesianToCartographic(r[g],l),re.setEndPoints(m,c),ne(re,u,s,oe);return l=m,m=c,c=a.cartesianToCartographic(r[0],l),re.setEndPoints(m,c),ne(re,u,s,oe),s.east-s.west>oe.west-oe.east&&(s.east=oe.east,s.west=oe.west),s}var ie=new s.Cartographic;function ne(e,t,o,r){for(var a=e.surfaceDistance,i=Math.ceil(a*t),n=i>0?a/(i-1):Number.POSITIVE_INFINITY,s=0,l=0;l<i;l++){var u=e.interpolateUsingSurfaceDistance(s,ie);s+=n;var d=u.longitude,p=u.latitude;o.west=Math.min(o.west,d),o.east=Math.max(o.east,d),o.south=Math.min(o.south,p),o.north=Math.max(o.north,p),r.west=d>0?Math.min(d,r.west):r.west,r.east=d<0?Math.max(d,r.east):r.east}}var se=[];function le(e,t,o,r,a,i,n,s,l,u,d){var c,g={walls:[]};if(i||n){var y,f,b=_.PolygonGeometryLibrary.createGeometryFromPositions(e,t,o,a,s,l),C=b.attributes.position.values,x=b.indices;if(i&&n){var P=C.concat(C);y=P.length/3,(f=h.IndexDatatype.createTypedArray(y,2*x.length)).set(x);var T=x.length,w=y/2;for(c=0;c<T;c+=3){var A=f[c]+w,E=f[c+1]+w,I=f[c+2]+w;f[c+T]=I,f[c+1+T]=E,f[c+2+T]=A}if(b.attributes.position.values=P,a&&s.normal){var G=b.attributes.normal.values;b.attributes.normal.values=new Float32Array(P.length),b.attributes.normal.values.set(G)}b.indices=f}else if(n){for(y=C.length/3,f=h.IndexDatatype.createTypedArray(y,x.length),c=0;c<x.length;c+=3)f[c]=x[c+2],f[c+1]=x[c+1],f[c+2]=x[c];b.indices=f}g.topAndBottom=new m.GeometryInstance({geometry:b})}var V,F=r.outerRing,H=p.EllipsoidTangentPlane.fromPoints(F,e),N=H.projectPointsOntoPlane(F,se),O=v.PolygonPipeline.computeWindingOrder2D(N);O===v.WindingOrder.CLOCKWISE&&(F=F.slice().reverse()),u&&(V=_.PolygonGeometryLibrary.computeWallGeometry(F,e,o,a,l,d),g.walls.push(new m.GeometryInstance({geometry:V})));var R=r.holes;for(c=0;c<R.length;c++){var D=R[c];N=(H=p.EllipsoidTangentPlane.fromPoints(D,e)).projectPointsOntoPlane(D,se),(O=v.PolygonPipeline.computeWindingOrder2D(N))===v.WindingOrder.COUNTER_CLOCKWISE&&(D=D.slice().reverse()),V=_.PolygonGeometryLibrary.computeWallGeometry(D,e,o,a,l),g.walls.push(new m.GeometryInstance({geometry:V}))}return g}function ue(r){var a=r.polygonHierarchy,i=e.defaultValue(r.vertexFormat,C.VertexFormat.DEFAULT),n=e.defaultValue(r.ellipsoid,t.Ellipsoid.WGS84),s=e.defaultValue(r.granularity,f.CesiumMath.RADIANS_PER_DEGREE),l=e.defaultValue(r.stRotation,0),u=e.defaultValue(r.perPositionHeight,!1),d=u&&e.defined(r.extrudedHeight),p=e.defaultValue(r.height,0),c=e.defaultValue(r.extrudedHeight,p);if(!d){var m=Math.max(p,c);c=Math.min(p,c),p=m}this._vertexFormat=C.VertexFormat.clone(i),this._ellipsoid=t.Ellipsoid.clone(n),this._granularity=s,this._stRotation=l,this._height=p,this._extrudedHeight=c,this._closeTop=e.defaultValue(r.closeTop,!0),this._closeBottom=e.defaultValue(r.closeBottom,!0),this._extrudeOutering=e.defaultValue(r.extrudeOutering,!0),this._polygonHierarchy=a,this._perPositionHeight=u,this._perPositionHeightExtrude=d,this._shadowVolume=e.defaultValue(r.shadowVolume,!1),this._workerName="createPolygonGeometry",this._offsetAttribute=r.offsetAttribute,this._arcType=e.defaultValue(r.arcType,o.ArcType.GEODESIC),this._groundBottomAltitude=e.defaultValue(r.groundBottomAltitude,void 0),this._groundExtrudedHeight=e.defaultValue(r.groundExtrudedHeight,0),this._rectangle=void 0,this._textureCoordinateRotationPoints=void 0,this._isComputeTexCoord=r.isComputeTexCoord,this._isWall=e.defaultValue(r.isWall,!1),this.packedLength=_.PolygonGeometryLibrary.computeHierarchyPackedLength(a)+t.Ellipsoid.packedLength+C.VertexFormat.packedLength+12}ue.fromPositions=function(t){return new ue({polygonHierarchy:{positions:(t=e.defaultValue(t,e.defaultValue.EMPTY_OBJECT)).positions},height:t.height,extrudedHeight:t.extrudedHeight,vertexFormat:t.vertexFormat,stRotation:t.stRotation,ellipsoid:t.ellipsoid,granularity:t.granularity,perPositionHeight:t.perPositionHeight,closeTop:t.closeTop,closeBottom:t.closeBottom,offsetAttribute:t.offsetAttribute,arcType:t.arcType,isComputeTexCoord:t.isComputeTexCoord,isWall:t.isWall})},ue.pack=function(o,r,a){return a=e.defaultValue(a,0),a=_.PolygonGeometryLibrary.packPolygonHierarchy(o._polygonHierarchy,r,a),t.Ellipsoid.pack(o._ellipsoid,r,a),a+=t.Ellipsoid.packedLength,C.VertexFormat.pack(o._vertexFormat,r,a),a+=C.VertexFormat.packedLength,r[a++]=o._height,r[a++]=o._extrudedHeight,r[a++]=o._granularity,r[a++]=o._stRotation,r[a++]=o._perPositionHeightExtrude?1:0,r[a++]=o._perPositionHeight?1:0,r[a++]=o._closeTop?1:0,r[a++]=o._closeBottom?1:0,r[a++]=o._shadowVolume?1:0,r[a++]=e.defaultValue(o._offsetAttribute,-1),r[a++]=o._arcType,r[a]=o.packedLength,r};var de=t.Ellipsoid.clone(t.Ellipsoid.UNIT_SPHERE),pe=new C.VertexFormat,ce={polygonHierarchy:{}};return ue.unpack=function(o,r,a){r=e.defaultValue(r,0);var i=_.PolygonGeometryLibrary.unpackPolygonHierarchy(o,r);r=i.startingIndex,delete i.startingIndex;var n=t.Ellipsoid.unpack(o,r,de);r+=t.Ellipsoid.packedLength;var s=C.VertexFormat.unpack(o,r,pe);r+=C.VertexFormat.packedLength;var l=o[r++],u=o[r++],d=o[r++],p=o[r++],c=1===o[r++],m=1===o[r++],g=1===o[r++],y=1===o[r++],h=1===o[r++],f=o[r++],b=o[r++],v=o[r];return e.defined(a)||(a=new ue(ce)),a._polygonHierarchy=i,a._ellipsoid=t.Ellipsoid.clone(n,a._ellipsoid),a._vertexFormat=C.VertexFormat.clone(s,a._vertexFormat),a._height=l,a._extrudedHeight=u,a._granularity=d,a._stRotation=p,a._perPositionHeightExtrude=c,a._perPositionHeight=m,a._closeTop=g,a._closeBottom=y,a._shadowVolume=h,a._offsetAttribute=-1===f?void 0:f,a._arcType=b,a.packedLength=v,a},ue.computeRectangle=function(r,a){var i=e.defaultValue(r.granularity,f.CesiumMath.RADIANS_PER_DEGREE),n=e.defaultValue(r.arcType,o.ArcType.GEODESIC),s=r.polygonHierarchy,l=e.defaultValue(r.ellipsoid,t.Ellipsoid.WGS84);return ae(s.positions,l,n,i,a)},ue.createGeometry=function(t){var o=t._vertexFormat,a=t._ellipsoid,n=t._granularity,s=t._stRotation,l=t._polygonHierarchy,d=t._perPositionHeight,b=t._closeTop,C=t._closeBottom,x=t._arcType,P=l.positions;if(!(P.length<3)){var T=p.EllipsoidTangentPlane.fromPoints(P,a),w=_.PolygonGeometryLibrary.polygonsFromHierarchy(l,T.projectPointsOntoPlane.bind(T),!d,a),A=w.hierarchy,E=w.polygons;if(0!==A.length){P=A[0].outerRing;var I,G=_.PolygonGeometryLibrary.computeBoundingRectangle(T.plane.normal,T.projectPointOntoPlane.bind(T),P,s,L),V=[],F=t._height,H=t._extrudedHeight,N=t._perPositionHeightExtrude||!f.CesiumMath.equalsEpsilon(F,H,0,f.CesiumMath.EPSILON2),O={perPositionHeight:d,vertexFormat:o,geometry:void 0,tangentPlane:T,boundingRectangle:G,ellipsoid:a,stRotation:s,bottom:!1,top:!0,wall:!1,extrude:!1,arcType:x,outerPositions:P,isComputeTexCoord:t._isComputeTexCoord};if(N)for(O.extrude=!0,O.top=b,O.bottom=C,O.shadowVolume=t._shadowVolume,O.offsetAttribute=t._offsetAttribute,I=0;I<E.length;I++){var R,D=le(a,E[I],n,A[I],d,b,C,o,x,t._extrudeOutering,t._isWall);b&&C?(R=D.topAndBottom,O.geometry=_.PolygonGeometryLibrary.scaleToGeodeticHeightExtruded(R.geometry,F,H,a,d)):b?((R=D.topAndBottom).geometry.attributes.position.values=v.PolygonPipeline.scaleToGeodeticHeight(R.geometry.attributes.position.values,F,a,!d),O.geometry=R.geometry):C&&((R=D.topAndBottom).geometry.attributes.position.values=v.PolygonPipeline.scaleToGeodeticHeight(R.geometry.attributes.position.values,H,a,!0),O.geometry=R.geometry),(b||C)&&(O.wall=!1,R.geometry=$(O),V.push(R));var M=D.walls;O.wall=!0;for(var B=0;B<M.length;B++){var S=M[B];O.top=!1,O.bottom=!1,O.geometry=_.PolygonGeometryLibrary.scaleToGeodeticHeightExtruded(S.geometry,F,H,a,d),S.geometry=$(O),V.push(S)}}else for(I=0;I<E.length;I++){var k=new m.GeometryInstance({geometry:_.PolygonGeometryLibrary.createGeometryFromPositions(a,E[I],n,d,o,x)});if(k.geometry.attributes.position.values=v.PolygonPipeline.scaleToGeodeticHeight(k.geometry.attributes.position.values,F,a,!d),O.geometry=k.geometry,k.geometry=$(O),e.defined(t._offsetAttribute)){var z=k.geometry.attributes.position.values.length,W=new Uint8Array(z/3),Y=t._offsetAttribute===g.GeometryOffsetAttribute.NONE?0:1;r.arrayFill(W,Y),k.geometry.attributes.applyOffset=new c.GeometryAttribute({componentDatatype:u.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:W})}V.push(k)}var U=y.GeometryPipeline.combineInstances(V)[0];U.attributes.position.values=new Float64Array(U.attributes.position.values),U.indices=h.IndexDatatype.createTypedArray(U.attributes.position.values.length/3,U.indices);var j=U.attributes,Q=i.BoundingSphere.fromVertices(j.position.values);return o.position||delete j.position,new c.Geometry({attributes:j,indices:U.indices,primitiveType:U.primitiveType,boundingSphere:Q,offsetAttribute:t._offsetAttribute})}}},ue.createShadowVolume=function(e,t,o){var r=e._granularity,a=e._ellipsoid,i=e._groundBottomAltitude+e._groundExtrudedHeight,n=e._groundBottomAltitude?e._groundBottomAltitude:t(r,a),s=i||o(r,a);return new ue({polygonHierarchy:e._polygonHierarchy,ellipsoid:a,stRotation:e._stRotation,granularity:r,perPositionHeight:!1,extrudedHeight:n,height:s,vertexFormat:C.VertexFormat.POSITION_ONLY,shadowVolume:!0,arcType:e._arcType})},Object.defineProperties(ue.prototype,{rectangle:{get:function(){if(!e.defined(this._rectangle)){var t=this._polygonHierarchy.positions;this._rectangle=ae(t,this._ellipsoid,this._arcType,this._granularity)}return this._rectangle}},textureCoordinateRotationPoints:{get:function(){return e.defined(this._textureCoordinateRotationPoints)||(this._textureCoordinateRotationPoints=function(e){var t=-e._stRotation;if(0===t)return[0,0,0,1,1,0];var o=e._ellipsoid,r=e._polygonHierarchy.positions,a=e.rectangle;return c.Geometry._textureCoordinateRotationPoints(r,t,o,a)}(this)),this._textureCoordinateRotationPoints}}}),function(o,r){return e.defined(r)&&(o=ue.unpack(o,r)),o._ellipsoid=t.Ellipsoid.clone(o._ellipsoid),ue.createGeometry(o)}}));
