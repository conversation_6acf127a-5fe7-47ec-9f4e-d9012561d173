export const useDroneStore = defineStore('drone', { // 修改 store 名称为 'drone' 避免冲突
    state: () => ({
        droneConverterId: JSON.parse(localStorage.getItem('layout-setting-oa'))?.droneConverterId || null,
        airportConverterId: JSON.parse(localStorage.getItem('layout-setting-oa'))?.airportConverterId || null,
        isConnected: JSON.parse(localStorage.getItem('layout-setting-oa'))?.isConnected || true,
    }),
    actions: {
        updateDroneConverterId(newConverterId) {
            this.droneConverterId = newConverterId;
            const currentSettings = JSON.parse(localStorage.getItem('layout-setting-oa')) || {};
            currentSettings.droneConverterId = newConverterId;
            localStorage.setItem('layout-setting-oa', JSON.stringify(currentSettings));
        },
        updateAirportConverterId(newConverterId) {
            this.airportConverterId = newConverterId;
            const currentSettings = JSON.parse(localStorage.getItem('layout-setting-oa')) || {};
            currentSettings.airportConverterId = newConverterId;
            localStorage.setItem('layout-setting-oa', JSON.stringify(currentSettings));
        },
        updateIsConnected(newConverterId) {
            this.isConnected = newConverterId;
            const currentSettings = JSON.parse(localStorage.getItem('layout-setting-oa')) || {};
            currentSettings.isConnected = newConverterId;
            localStorage.setItem('layout-setting-oa', JSON.stringify(currentSettings));
        },
    },
});
