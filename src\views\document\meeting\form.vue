<template>
    <div class="container">
        <div class="btn-submit">
            <el-button>打印</el-button>
            <el-button type="primary" v-loading="showLoding" @click="onSubmit">发送</el-button>
        </div>
        <el-row :gutter="20">
            <el-col :span="18" class="card form" v-loading="showLoding">
                <div class="form-content">

                    <el-form ref="receiptFormRef" :model="form" :rules="rules" label-width="100px">
                        <table>
                            <tr>
                                <td colspan="4">
                                    <h1 style="text-align: center">会议通知单</h1>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <el-form-item label="编制单位" prop="workUnit">
                                        <el-input v-model="form.workUnit" placeholder="请输入编制单位" />
                                    </el-form-item>
                                </td>
                                <td>
                                    <el-form-item label="编制时间" prop="workDate">
                                        <el-date-picker style="width: 100%;" clearable v-model="form.workDate"
                                            type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择编制时间">
                                        </el-date-picker>
                                    </el-form-item>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <el-form-item label="会议编号" prop="meetNum">
                                        <el-input v-model="form.meetNum" placeholder="请输入会议编号" />
                                    </el-form-item>
                                </td>
                                <td>
                                    <el-form-item label="办理状态" prop="handleStatus">
                                        <el-input v-model="form.handleStatus" placeholder="请输入办理状态" />
                                    </el-form-item>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="4">
                                    <el-form-item label="会议名称" prop="meetName">
                                        <el-input v-model="form.meetName" placeholder="请输入会议名称" />
                                    </el-form-item>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="4">
                                    <el-form-item label="会议要求" prop="meetAsk">
                                        <el-input type="textarea" v-model="form.meetAsk" placeholder="请输入会议要求" />
                                    </el-form-item>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="4">
                                    <el-form-item label="会议地点" prop="meetSite">
                                        <el-input type="textarea" v-model="form.meetSite" placeholder="请输入会议地点" />
                                    </el-form-item>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="4">
                                    <el-form-item label="组会单位" prop="meetUnit">
                                        <el-input type="textarea" v-model="form.meetUnit" placeholder="请输入组会单位" />
                                    </el-form-item>
                                </td>
                            </tr>
                            <tr v-for="(item, index) in tmpJson" :key="index">
                                <td v-if="item.docData === '1'" colspan="4">
                                    <el-form-item :label="item.nodeName" label-width="150px">
                                        <el-input :disabled="true" type="textarea" v-model="item.data[0].auditIdea"
                                            placeholder="请输入意见" />
                                        <el-form-item>
                                            <el-form-item label="签字">
                                                <el-input :disabled="true" type="textarea"
                                                    v-model="item.data[0].auditSign" placeholder="请输入备注" />
                                            </el-form-item>
                                            <el-form-item label="日期">
                                                <el-date-picker :disabled="true" clearable
                                                    v-model="item.data[0].auditTime" type="datetime"
                                                    value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择日期">
                                                </el-date-picker>
                                            </el-form-item>
                                        </el-form-item>
                                    </el-form-item>
                                </td>
                                <td v-if="item.docData === '2'" colspan="4">
                                    <div class="el-form-item__label" style="width: 150px;border: 0px;">{{ item.nodeName
                                        }}
                                    </div>
                                    <el-table :data="item.data" border style="width: 100%" height="200">
                                        <el-table-column prop="auditIdea" label="意见" />
                                        <el-table-column prop="auditSign" label="签字" width="180" />
                                        <el-table-column prop="auditTime" label="日期" width="180" />
                                    </el-table>
                                </td>
                            </tr>
                        </table>

                    </el-form>
                </div>
            </el-col>
            <el-col :span="6" v-loading="showLoding">
                <div class="file-content card">
                    <FileTree v-if="dicts.doc_receipt_file_type.length > 0" :key="formKey"
                        :data="dicts.doc_receipt_file_type" :upload="true" @onSuccess="onUploadSuccess"
                        @onRemove="onUploadRemove" />
                </div>
            </el-col>
        </el-row>
    </div>
</template>

<script setup>
import FileTree from '@/components/FileTree'
import { onMounted, ref } from 'vue';
import { getDicts } from '@/api/system/dict/data';
import { getTmpJson } from '@/api/document/common';
import { addForm, getDetail } from '@/api/document/meeting';
import { startWorkFlow } from '@/api/document/common';
import { parseTime } from '@/utils/ruoyi';

const { proxy } = getCurrentInstance();

const formKey = ref(new Date().getTime())
const showLoding = ref(false)

const dictsList = ['doc_receipt_file_type']
const dicts = ref({})
dictsList.map(item => dicts.value[item] = [])

const receiptFormRef = ref(null)
const initFormData = {
    workUnit: undefined,
    workDate: undefined,
    handleStatus: undefined,
    meetName: undefined,
    meetAsk: undefined,
    meetSite: undefined,
    meetUnit: undefined,
    affixList: [],
}
const data = reactive({
    form: { ...initFormData },
    rules: {
        meetNum: [
            { required: true, message: "请输入会议编号", trigger: "blur" }
        ],
        meetName: [
            { required: true, message: "请输入会议名称", trigger: "blur" }
        ],
    }
});

const { form, rules } = toRefs(data);
const tmpJson = ref([])

//上传成功
function onUploadSuccess(fileList) {
    form.value.affixList.push(fileList)
}
//删除文件
function onUploadRemove(file) {
    form.value.affixList = form.value.affixList.filter(item => item.name !== file.response.data.url)
}
function onSubmit() {
    if (!form.value.affixList.length) {
        proxy.$modal.msgError("请上传附件");
        return
    }
    receiptFormRef.value.validate(valid => {
        if (valid) {
            showLoding.value = true
            addForm(form.value).then(res => {
                let data = {
                    businessKey: res.data.id,
                    tableName: 'doc_notice',
                    variables: {
                        entity: res.data
                    }
                }
                startWorkFlow(data).then(() => {
                    proxy.$modal.msgSuccess('提交成功');
                    receiptFormRef.value.resetFields()
                    formKey.value = new Date().getTime()
                    showLoding.value = false
                })
            })
        }
    })
}
onMounted(() => {
    let date = parseTime(new Date().getTime())
    form.value.workDate = date
    dictsList.map(item => {
        getDicts(item).then(res => {
            dicts.value[item] = res.data
        })
    })
    getTmpJson({ type: '5' }).then(res => {
        tmpJson.value = res.data
    })
})
</script>

<style scoped lang="scss">
@import "@/styles/variables.module.scss";;

.container {
    padding: 10px 20px;
    background-color: #E9EEF3;
    height: $contentHeight;
    overflow-x: auto;
}

.form {
    display: flex;
    justify-content: center;
}

.form-content {
    width: 770px;
}

table,
th,
td {
    border: none;
}

table {
    width: 100%;
    // border: 1px solid #e2e2e2;
    border-collapse: collapse;

    :deep(.el-form-item) {
        margin-bottom: 0;
    }

    :deep(.el-form-item__label) {
        justify-content: center;
        // border-right: 1px solid #e2e2e2;
        margin-right: 1px;
        height: auto;
        padding: 10px 10px;
    }

    :deep(.el-form-item__error) {
        position: static;
    }

    td {
        // border: 1px solid #e2e2e2;
        background-color: rgb(246, 246, 246);
    }
}

.btn-submit {
    width: 100%;
    display: flex;
    margin-bottom: 5px;
}
</style>
