define(["exports","./buildModuleUrl-9085faaa","./Cartographic-3309dd0d","./Cartesian4-3ca25aab","./Check-7b2a090c","./ComponentDatatype-c140a87d","./when-b60132fc","./GeometryAttribute-c65394ac","./GeometryAttributes-252e9929","./FeatureDetection-806b12f0","./Math-119be1a3","./Plane-a3d8b3d2","./VertexFormat-6446fca0"],(function(t,e,a,i,r,n,o,s,f,u,l,h,p){"use strict";function c(t){this.planes=o.defaultValue(t,[])}var d=[new a.Cartesian3,new a.Cartesian3,new a.Cartesian3];a.Cartesian3.clone(a.Cartesian3.UNIT_X,d[0]),a.Cartesian3.clone(a.<PERSON><PERSON>3.UNIT_Y,d[1]),a.Cartesian3.clone(a.Cartesian3.UNIT_Z,d[2]);var m=new a.Cartesian3,C=new a.Cartesian3,_=new h.Plane(new a.Cartesian3(1,0,0),0);function y(t){t=o.defaultValue(t,o.defaultValue.EMPTY_OBJECT),this.left=t.left,this._left=void 0,this.right=t.right,this._right=void 0,this.top=t.top,this._top=void 0,this.bottom=t.bottom,this._bottom=void 0,this.near=o.defaultValue(t.near,1),this._near=this.near,this.far=o.defaultValue(t.far,5e8),this._far=this.far,this._cullingVolume=new c,this._orthographicMatrix=new u.Matrix4}function v(t){t.top===t._top&&t.bottom===t._bottom&&t.left===t._left&&t.right===t._right&&t.near===t._near&&t.far===t._far||(t._left=t.left,t._right=t.right,t._top=t.top,t._bottom=t.bottom,t._near=t.near,t._far=t.far,t._orthographicMatrix=u.Matrix4.computeOrthographicOffCenter(t.left,t.right,t.bottom,t.top,t.near,t.far,t._orthographicMatrix))}c.fromBoundingSphere=function(t,e){o.defined(e)||(e=new c);var r=d.length,n=e.planes;n.length=2*r;for(var s=t.center,f=t.radius,u=0,l=0;l<r;++l){var h=d[l],p=n[u],_=n[u+1];o.defined(p)||(p=n[u]=new i.Cartesian4),o.defined(_)||(_=n[u+1]=new i.Cartesian4),a.Cartesian3.multiplyByScalar(h,-f,m),a.Cartesian3.add(s,m,m),p.x=h.x,p.y=h.y,p.z=h.z,p.w=-a.Cartesian3.dot(h,m),a.Cartesian3.multiplyByScalar(h,f,m),a.Cartesian3.add(s,m,m),_.x=-h.x,_.y=-h.y,_.z=-h.z,_.w=-a.Cartesian3.dot(a.Cartesian3.negate(h,C),m),u+=2}return e},c.prototype.computeVisibility=function(t){for(var a=this.planes,i=!1,r=0,n=a.length;r<n;++r){var o=t.intersectPlane(h.Plane.fromCartesian4(a[r],_));if(o===e.Intersect.OUTSIDE)return e.Intersect.OUTSIDE;o===e.Intersect.INTERSECTING&&(i=!0)}return i?e.Intersect.INTERSECTING:e.Intersect.INSIDE},c.prototype.computeVisibilityWithPlaneMask=function(t,a){if(a===c.MASK_OUTSIDE||a===c.MASK_INSIDE)return a;for(var i=c.MASK_INSIDE,r=this.planes,n=0,o=r.length;n<o;++n){var s=n<31?1<<n:0;if(!(n<31&&0==(a&s))){var f=t.intersectPlane(h.Plane.fromCartesian4(r[n],_));if(f===e.Intersect.OUTSIDE)return c.MASK_OUTSIDE;f===e.Intersect.INTERSECTING&&(i|=s)}}return i},c.MASK_OUTSIDE=4294967295,c.MASK_INSIDE=0,c.MASK_INDETERMINATE=2147483647,Object.defineProperties(y.prototype,{projectionMatrix:{get:function(){return v(this),this._orthographicMatrix}}});var w=new a.Cartesian3,g=new a.Cartesian3,x=new a.Cartesian3,M=new a.Cartesian3;function b(t){t=o.defaultValue(t,o.defaultValue.EMPTY_OBJECT),this._offCenterFrustum=new y,this.width=t.width,this._width=void 0,this.aspectRatio=t.aspectRatio,this._aspectRatio=void 0,this.near=o.defaultValue(t.near,1),this._near=this.near,this.far=o.defaultValue(t.far,5e8),this._far=this.far}function V(t){var e=t._offCenterFrustum;if(t.width!==t._width||t.aspectRatio!==t._aspectRatio||t.near!==t._near||t.far!==t._far){t._aspectRatio=t.aspectRatio,t._width=t.width,t._near=t.near,t._far=t.far;var a=1/t.aspectRatio;e.right=.5*t.width,e.left=-e.right,e.top=a*e.right,e.bottom=-e.top,e.near=t.near,e.far=t.far}}function F(t){t=o.defaultValue(t,o.defaultValue.EMPTY_OBJECT),this.left=t.left,this._left=void 0,this.right=t.right,this._right=void 0,this.top=t.top,this._top=void 0,this.bottom=t.bottom,this._bottom=void 0,this.near=o.defaultValue(t.near,1),this._near=this.near,this.far=o.defaultValue(t.far,5e8),this._far=this.far,this._cullingVolume=new c,this._perspectiveMatrix=new u.Matrix4,this._infinitePerspective=new u.Matrix4}function P(t){var e=t.top,a=t.bottom,i=t.right,r=t.left,n=t.near,o=t.far;e===t._top&&a===t._bottom&&r===t._left&&i===t._right&&n===t._near&&o===t._far||(t._left=r,t._right=i,t._top=e,t._bottom=a,t._near=n,t._far=o,t._perspectiveMatrix=u.Matrix4.computePerspectiveOffCenter(r,i,a,e,n,o,t._perspectiveMatrix),t._infinitePerspective=u.Matrix4.computeInfinitePerspectiveOffCenter(r,i,a,e,n,t._infinitePerspective))}y.prototype.computeCullingVolume=function(t,e,r){var n=this._cullingVolume.planes,s=this.top,f=this.bottom,u=this.right,l=this.left,h=this.near,p=this.far,c=a.Cartesian3.cross(e,r,w);a.Cartesian3.normalize(c,c);var d=g;a.Cartesian3.multiplyByScalar(e,h,d),a.Cartesian3.add(t,d,d);var m=x;a.Cartesian3.multiplyByScalar(c,l,m),a.Cartesian3.add(d,m,m);var C=n[0];return o.defined(C)||(C=n[0]=new i.Cartesian4),C.x=c.x,C.y=c.y,C.z=c.z,C.w=-a.Cartesian3.dot(c,m),a.Cartesian3.multiplyByScalar(c,u,m),a.Cartesian3.add(d,m,m),C=n[1],o.defined(C)||(C=n[1]=new i.Cartesian4),C.x=-c.x,C.y=-c.y,C.z=-c.z,C.w=-a.Cartesian3.dot(a.Cartesian3.negate(c,M),m),a.Cartesian3.multiplyByScalar(r,f,m),a.Cartesian3.add(d,m,m),C=n[2],o.defined(C)||(C=n[2]=new i.Cartesian4),C.x=r.x,C.y=r.y,C.z=r.z,C.w=-a.Cartesian3.dot(r,m),a.Cartesian3.multiplyByScalar(r,s,m),a.Cartesian3.add(d,m,m),C=n[3],o.defined(C)||(C=n[3]=new i.Cartesian4),C.x=-r.x,C.y=-r.y,C.z=-r.z,C.w=-a.Cartesian3.dot(a.Cartesian3.negate(r,M),m),C=n[4],o.defined(C)||(C=n[4]=new i.Cartesian4),C.x=e.x,C.y=e.y,C.z=e.z,C.w=-a.Cartesian3.dot(e,d),a.Cartesian3.multiplyByScalar(e,p,m),a.Cartesian3.add(t,m,m),C=n[5],o.defined(C)||(C=n[5]=new i.Cartesian4),C.x=-e.x,C.y=-e.y,C.z=-e.z,C.w=-a.Cartesian3.dot(a.Cartesian3.negate(e,M),m),this._cullingVolume},y.prototype.getPixelDimensions=function(t,e,a,i,r){v(this);var n=i*(this.right-this.left)/t,o=i*(this.top-this.bottom)/e;return r.x=n,r.y=o,r},y.prototype.clone=function(t){return o.defined(t)||(t=new y),t.left=this.left,t.right=this.right,t.top=this.top,t.bottom=this.bottom,t.near=this.near,t.far=this.far,t._left=void 0,t._right=void 0,t._top=void 0,t._bottom=void 0,t._near=void 0,t._far=void 0,t},y.prototype.equals=function(t){return o.defined(t)&&t instanceof y&&this.right===t.right&&this.left===t.left&&this.top===t.top&&this.bottom===t.bottom&&this.near===t.near&&this.far===t.far},y.prototype.equalsEpsilon=function(t,e,a){return t===this||o.defined(t)&&t instanceof y&&l.CesiumMath.equalsEpsilon(this.right,t.right,e,a)&&l.CesiumMath.equalsEpsilon(this.left,t.left,e,a)&&l.CesiumMath.equalsEpsilon(this.top,t.top,e,a)&&l.CesiumMath.equalsEpsilon(this.bottom,t.bottom,e,a)&&l.CesiumMath.equalsEpsilon(this.near,t.near,e,a)&&l.CesiumMath.equalsEpsilon(this.far,t.far,e,a)},b.packedLength=4,b.pack=function(t,e,a){return a=o.defaultValue(a,0),e[a++]=t.width,e[a++]=t.aspectRatio,e[a++]=t.near,e[a]=t.far,e},b.unpack=function(t,e,a){return e=o.defaultValue(e,0),o.defined(a)||(a=new b),a.width=t[e++],a.aspectRatio=t[e++],a.near=t[e++],a.far=t[e],a},Object.defineProperties(b.prototype,{projectionMatrix:{get:function(){return V(this),this._offCenterFrustum.projectionMatrix}}}),b.prototype.computeCullingVolume=function(t,e,a){return V(this),this._offCenterFrustum.computeCullingVolume(t,e,a)},b.prototype.getPixelDimensions=function(t,e,a,i,r){return V(this),this._offCenterFrustum.getPixelDimensions(t,e,a,i,r)},b.prototype.clone=function(t){return o.defined(t)||(t=new b),t.aspectRatio=this.aspectRatio,t.width=this.width,t.near=this.near,t.far=this.far,t._aspectRatio=void 0,t._width=void 0,t._near=void 0,t._far=void 0,this._offCenterFrustum.clone(t._offCenterFrustum),t},b.prototype.equals=function(t){return!!(o.defined(t)&&t instanceof b)&&(V(this),V(t),this.width===t.width&&this.aspectRatio===t.aspectRatio&&this._offCenterFrustum.equals(t._offCenterFrustum))},b.prototype.equalsEpsilon=function(t,e,a){return!!(o.defined(t)&&t instanceof b)&&(V(this),V(t),l.CesiumMath.equalsEpsilon(this.width,t.width,e,a)&&l.CesiumMath.equalsEpsilon(this.aspectRatio,t.aspectRatio,e,a)&&this._offCenterFrustum.equalsEpsilon(t._offCenterFrustum,e,a))},Object.defineProperties(F.prototype,{projectionMatrix:{get:function(){return P(this),this._perspectiveMatrix}},infiniteProjectionMatrix:{get:function(){return P(this),this._infinitePerspective}}});var E=new a.Cartesian3,O=new a.Cartesian3,z=new a.Cartesian3,R=new a.Cartesian3;function S(t){t=o.defaultValue(t,o.defaultValue.EMPTY_OBJECT),this._offCenterFrustum=new F,this.fov=t.fov,this._fov=void 0,this._fovy=void 0,this._sseDenominator=void 0,this.aspectRatio=t.aspectRatio,this._aspectRatio=void 0,this.near=o.defaultValue(t.near,1),this._near=this.near,this.far=o.defaultValue(t.far,5e8),this._far=this.far,this.xOffset=o.defaultValue(t.xOffset,0),this._xOffset=this.xOffset,this.yOffset=o.defaultValue(t.yOffset,0),this._yOffset=this.yOffset,this.reflect=!1}function T(t){var e=t._offCenterFrustum;t.fov===t._fov&&t.aspectRatio===t._aspectRatio&&t.near===t._near&&t.far===t._far&&t.xOffset===t._xOffset&&t.yOffset===t._yOffset||(t._aspectRatio=t.aspectRatio,t._fov=t.fov,t._fovy=t.aspectRatio<=1?t.fov:2*Math.atan(Math.tan(.5*t.fov)/t.aspectRatio),t._near=t.near,t._far=t.far,t._sseDenominator=2*Math.tan(.5*t._fovy),t._xOffset=t.xOffset,t._yOffset=t.yOffset,e.top=t.near*Math.tan(.5*t._fovy),e.bottom=-e.top,e.right=t.aspectRatio*e.top,e.left=-e.right,e.near=t.near,e.far=t.far,e.right+=t.xOffset,e.left+=t.xOffset,e.top+=t.yOffset,e.bottom+=t.yOffset)}F.prototype.resetProjectionMatrix=function(){var t=this.top,e=this.bottom,a=this.right,i=this.left,r=this.near,n=this.far;this._left=i,this._right=a,this._top=t,this._bottom=e,this._near=r,this._far=n,this._perspectiveMatrix=u.Matrix4.computePerspectiveOffCenter(i,a,e,t,r,n,this._perspectiveMatrix),this._infinitePerspective=u.Matrix4.computeInfinitePerspectiveOffCenter(i,a,e,t,r,this._infinitePerspective)},F.prototype.computeCullingVolume=function(t,e,r,n){var s=this._cullingVolume.planes,f=o.defaultValue(n,0);f=Math.min(f,.5),f=Math.max(f,0);var u=this.top+this.top*f,l=this.bottom-this.top*f,h=this.right+this.right*f,p=this.left-this.right*f,c=this.near,d=this.far,m=a.Cartesian3.cross(e,r,E),C=O;a.Cartesian3.multiplyByScalar(e,c,C),a.Cartesian3.add(t,C,C);var _=z;a.Cartesian3.multiplyByScalar(e,d,_),a.Cartesian3.add(t,_,_);var y=R;a.Cartesian3.multiplyByScalar(m,p,y),a.Cartesian3.add(C,y,y),a.Cartesian3.subtract(y,t,y),a.Cartesian3.normalize(y,y),a.Cartesian3.cross(y,r,y),a.Cartesian3.normalize(y,y);var v=s[0];return o.defined(v)||(v=s[0]=new i.Cartesian4),v.x=y.x,v.y=y.y,v.z=y.z,v.w=-a.Cartesian3.dot(y,t),a.Cartesian3.multiplyByScalar(m,h,y),a.Cartesian3.add(C,y,y),a.Cartesian3.subtract(y,t,y),a.Cartesian3.cross(r,y,y),a.Cartesian3.normalize(y,y),v=s[1],o.defined(v)||(v=s[1]=new i.Cartesian4),v.x=y.x,v.y=y.y,v.z=y.z,v.w=-a.Cartesian3.dot(y,t),a.Cartesian3.multiplyByScalar(r,l,y),a.Cartesian3.add(C,y,y),a.Cartesian3.subtract(y,t,y),a.Cartesian3.cross(m,y,y),a.Cartesian3.normalize(y,y),v=s[2],o.defined(v)||(v=s[2]=new i.Cartesian4),v.x=y.x,v.y=y.y,v.z=y.z,v.w=-a.Cartesian3.dot(y,t),a.Cartesian3.multiplyByScalar(r,u,y),a.Cartesian3.add(C,y,y),a.Cartesian3.subtract(y,t,y),a.Cartesian3.cross(y,m,y),a.Cartesian3.normalize(y,y),v=s[3],o.defined(v)||(v=s[3]=new i.Cartesian4),v.x=y.x,v.y=y.y,v.z=y.z,v.w=-a.Cartesian3.dot(y,t),v=s[4],o.defined(v)||(v=s[4]=new i.Cartesian4),v.x=e.x,v.y=e.y,v.z=e.z,v.w=-a.Cartesian3.dot(e,C),a.Cartesian3.negate(e,y),v=s[5],o.defined(v)||(v=s[5]=new i.Cartesian4),v.x=y.x,v.y=y.y,v.z=y.z,v.w=-a.Cartesian3.dot(y,_),this._cullingVolume},F.prototype.getPixelDimensions=function(t,e,a,i,r){P(this);var n=1/this.near,o=this.top*n,s=2*i*a*o/e,f=2*i*a*(o=this.right*n)/t;return r.x=f,r.y=s,r},F.prototype.clone=function(t){return o.defined(t)||(t=new F),t.right=this.right,t.left=this.left,t.top=this.top,t.bottom=this.bottom,t.near=this.near,t.far=this.far,t._left=void 0,t._right=void 0,t._top=void 0,t._bottom=void 0,t._near=void 0,t._far=void 0,t},F.prototype.equals=function(t){return o.defined(t)&&t instanceof F&&this.right===t.right&&this.left===t.left&&this.top===t.top&&this.bottom===t.bottom&&this.near===t.near&&this.far===t.far},F.prototype.equalsEpsilon=function(t,e,a){return t===this||o.defined(t)&&t instanceof F&&l.CesiumMath.equalsEpsilon(this.right,t.right,e,a)&&l.CesiumMath.equalsEpsilon(this.left,t.left,e,a)&&l.CesiumMath.equalsEpsilon(this.top,t.top,e,a)&&l.CesiumMath.equalsEpsilon(this.bottom,t.bottom,e,a)&&l.CesiumMath.equalsEpsilon(this.near,t.near,e,a)&&l.CesiumMath.equalsEpsilon(this.far,t.far,e,a)},S.packedLength=6,S.pack=function(t,e,a){return a=o.defaultValue(a,0),e[a++]=t.fov,e[a++]=t.aspectRatio,e[a++]=t.near,e[a++]=t.far,e[a++]=t.xOffset,e[a]=t.yOffset,e},S.unpack=function(t,e,a){return e=o.defaultValue(e,0),o.defined(a)||(a=new S),a.fov=t[e++],a.aspectRatio=t[e++],a.near=t[e++],a.far=t[e++],a.xOffset=t[e++],a.yOffset=t[e],a},Object.defineProperties(S.prototype,{projectionMatrix:{get:function(){return T(this),this.reflect&&function(t){if(!o.defined(t.clipPlane)||!o.defined(t.currentViewMatrix))return;var e=t.currentViewMatrix,a=t._offCenterFrustum.projectionMatrix;u.Matrix4.multiplyByPlane(e,t.clipPlane,k),A.x=(l.CesiumMath.sign(k.normal.x)+a[8])/a[0],A.y=(l.CesiumMath.sign(k.normal.y)+a[9])/a[5],A.z=-1,A.w=(1+a[10])/a[14],I.x=k.normal.x,I.y=k.normal.y,I.z=k.normal.z,I.w=k.distance,i.Cartesian4.multiplyByScalar(I,2/i.Cartesian4.dot(I,A),D),a[2]=D.x,a[6]=D.y,a[10]=D.z+1,a[14]=D.w}(this),this._offCenterFrustum.projectionMatrix}},infiniteProjectionMatrix:{get:function(){return T(this),this._offCenterFrustum.infiniteProjectionMatrix}},fovy:{get:function(){return T(this),this._fovy}},sseDenominator:{get:function(){return T(this),this._sseDenominator}}}),S.prototype.resetProjectionMatrix=function(){return this._offCenterFrustum.resetProjectionMatrix()},S.prototype.computeCullingVolume=function(t,e,a,i){return T(this),this._offCenterFrustum.computeCullingVolume(t,e,a,i)},S.prototype.getPixelDimensions=function(t,e,a,i,r){return T(this),this._offCenterFrustum.getPixelDimensions(t,e,a,i,r)},S.prototype.clone=function(t){return o.defined(t)||(t=new S),t.aspectRatio=this.aspectRatio,t.fov=this.fov,t.near=this.near,t.far=this.far,t.reflect=this.reflect,t.clipPlane=this.clipPlane,t.currentViewMatrix=this.currentViewMatrix,t._aspectRatio=void 0,t._fov=void 0,t._near=void 0,t._far=void 0,this._offCenterFrustum.clone(t._offCenterFrustum),t},S.prototype.equals=function(t){return!!(o.defined(t)&&t instanceof S)&&(T(this),T(t),this.fov===t.fov&&this.aspectRatio===t.aspectRatio&&this._offCenterFrustum.equals(t._offCenterFrustum))},S.prototype.equalsEpsilon=function(t,e,a){return!!(o.defined(t)&&t instanceof S)&&(T(this),T(t),l.CesiumMath.equalsEpsilon(this.fov,t.fov,e,a)&&l.CesiumMath.equalsEpsilon(this.aspectRatio,t.aspectRatio,e,a)&&this._offCenterFrustum.equalsEpsilon(t._offCenterFrustum,e,a))};var k=new h.Plane(a.Cartesian3.UNIT_Z,1),A=new i.Cartesian4,I=new i.Cartesian4,D=new i.Cartesian4;function q(t){var e,i,r=t.frustum,n=t.orientation,f=t.origin,u=o.defaultValue(t.vertexFormat,p.VertexFormat.DEFAULT),l=o.defaultValue(t._drawNearPlane,!0);r instanceof S?(e=0,i=S.packedLength):r instanceof b&&(e=1,i=b.packedLength),this._frustumType=e,this._frustum=r.clone(),this._origin=a.Cartesian3.clone(f),this._orientation=s.Quaternion.clone(n),this._drawNearPlane=l,this._vertexFormat=u,this._workerName="createFrustumGeometry",this.packedLength=2+i+a.Cartesian3.packedLength+s.Quaternion.packedLength+p.VertexFormat.packedLength}q.pack=function(t,e,i){i=o.defaultValue(i,0);var r=t._frustumType,n=t._frustum;return e[i++]=r,0===r?(S.pack(n,e,i),i+=S.packedLength):(b.pack(n,e,i),i+=b.packedLength),a.Cartesian3.pack(t._origin,e,i),i+=a.Cartesian3.packedLength,s.Quaternion.pack(t._orientation,e,i),i+=s.Quaternion.packedLength,p.VertexFormat.pack(t._vertexFormat,e,i),e[i+=p.VertexFormat.packedLength]=t._drawNearPlane?1:0,e};var B=new S,N=new b,L=new s.Quaternion,j=new a.Cartesian3,G=new p.VertexFormat;function U(t,e,a,i,r,n,s,f){for(var u=t/3*2,l=0;l<4;++l)o.defined(e)&&(e[t]=n.x,e[t+1]=n.y,e[t+2]=n.z),o.defined(a)&&(a[t]=s.x,a[t+1]=s.y,a[t+2]=s.z),o.defined(i)&&(i[t]=f.x,i[t+1]=f.y,i[t+2]=f.z),t+=3;r[u]=0,r[u+1]=0,r[u+2]=1,r[u+3]=0,r[u+4]=1,r[u+5]=1,r[u+6]=0,r[u+7]=1}q.unpack=function(t,e,i){e=o.defaultValue(e,0);var r,n=t[e++];0===n?(r=S.unpack(t,e,B),e+=S.packedLength):(r=b.unpack(t,e,N),e+=b.packedLength);var f=a.Cartesian3.unpack(t,e,j);e+=a.Cartesian3.packedLength;var u=s.Quaternion.unpack(t,e,L);e+=s.Quaternion.packedLength;var l=p.VertexFormat.unpack(t,e,G),h=1===t[e+=p.VertexFormat.packedLength];if(!o.defined(i))return new q({frustum:r,origin:f,orientation:u,vertexFormat:l,_drawNearPlane:h});var c=n===i._frustumType?i._frustum:void 0;return i._frustum=r.clone(c),i._frustumType=n,i._origin=a.Cartesian3.clone(f,i._origin),i._orientation=s.Quaternion.clone(u,i._orientation),i._vertexFormat=p.VertexFormat.clone(l,i._vertexFormat),i._drawNearPlane=h,i};var Q=new u.Matrix3,K=new u.Matrix4,Y=new u.Matrix4,J=new a.Cartesian3,Z=new a.Cartesian3,W=new a.Cartesian3,X=new a.Cartesian3,H=new a.Cartesian3,$=new a.Cartesian3,tt=new Array(3),et=new Array(4);et[0]=new i.Cartesian4(-1,-1,1,1),et[1]=new i.Cartesian4(1,-1,1,1),et[2]=new i.Cartesian4(1,1,1,1),et[3]=new i.Cartesian4(-1,1,1,1);for(var at=new Array(4),it=0;it<4;++it)at[it]=new i.Cartesian4;q._computeNearFarPlanes=function(t,e,r,n,s,f,l,h){var p=u.Matrix3.fromQuaternion(e,Q),c=o.defaultValue(f,J),d=o.defaultValue(l,Z),m=o.defaultValue(h,W);c=u.Matrix3.getColumn(p,0,c),d=u.Matrix3.getColumn(p,1,d),m=u.Matrix3.getColumn(p,2,m),a.Cartesian3.normalize(c,c),a.Cartesian3.normalize(d,d),a.Cartesian3.normalize(m,m),a.Cartesian3.negate(c,c);var C,_,y=u.Matrix4.computeView(t,m,d,c,K);if(0===r){var v=n.projectionMatrix,w=u.Matrix4.multiply(v,y,Y);_=u.Matrix4.inverse(w,Y)}else C=u.Matrix4.inverseTransformation(y,Y);o.defined(_)?(tt[0]=n.near,tt[1]=n.far):(tt[0]=0,tt[1]=n.near,tt[2]=n.far);for(var g=0;g<2;++g)for(var x=0;x<4;++x){var M=i.Cartesian4.clone(et[x],at[x]);if(o.defined(_)){var b=1/(M=u.Matrix4.multiplyByVector(_,M,M)).w;a.Cartesian3.multiplyByScalar(M,b,M),a.Cartesian3.subtract(M,t,M),a.Cartesian3.normalize(M,M);var V=a.Cartesian3.dot(m,M);a.Cartesian3.multiplyByScalar(M,tt[g]/V,M),a.Cartesian3.add(M,t,M)}else{o.defined(n._offCenterFrustum)&&(n=n._offCenterFrustum);var F=tt[g],P=tt[g+1];M.x=.5*(M.x*(n.right-n.left)+n.left+n.right),M.y=.5*(M.y*(n.top-n.bottom)+n.bottom+n.top),M.z=.5*(M.z*(F-P)-F-P),M.w=1,u.Matrix4.multiplyByVector(C,M,M)}s[12*g+3*x]=M.x,s[12*g+3*x+1]=M.y,s[12*g+3*x+2]=M.z}},q.createGeometry=function(t){var i=t._frustumType,r=t._frustum,l=t._origin,h=t._orientation,p=t._drawNearPlane,c=t._vertexFormat,d=p?6:5,m=new Float64Array(72);q._computeNearFarPlanes(l,h,i,r,m);var C=24;m[C]=m[12],m[C+1]=m[13],m[C+2]=m[14],m[C+3]=m[0],m[C+4]=m[1],m[C+5]=m[2],m[C+6]=m[9],m[C+7]=m[10],m[C+8]=m[11],m[C+9]=m[21],m[C+10]=m[22],m[C+11]=m[23],m[C+=12]=m[15],m[C+1]=m[16],m[C+2]=m[17],m[C+3]=m[3],m[C+4]=m[4],m[C+5]=m[5],m[C+6]=m[0],m[C+7]=m[1],m[C+8]=m[2],m[C+9]=m[12],m[C+10]=m[13],m[C+11]=m[14],m[C+=12]=m[3],m[C+1]=m[4],m[C+2]=m[5],m[C+3]=m[15],m[C+4]=m[16],m[C+5]=m[17],m[C+6]=m[18],m[C+7]=m[19],m[C+8]=m[20],m[C+9]=m[6],m[C+10]=m[7],m[C+11]=m[8],m[C+=12]=m[6],m[C+1]=m[7],m[C+2]=m[8],m[C+3]=m[18],m[C+4]=m[19],m[C+5]=m[20],m[C+6]=m[21],m[C+7]=m[22],m[C+8]=m[23],m[C+9]=m[9],m[C+10]=m[10],m[C+11]=m[11],p||(m=m.subarray(12));var _=new f.GeometryAttributes({position:new s.GeometryAttribute({componentDatatype:n.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:m})});if(o.defined(c.normal)||o.defined(c.tangent)||o.defined(c.bitangent)||o.defined(c.st)){var y=o.defined(c.normal)?new Float32Array(12*d):void 0,v=o.defined(c.tangent)?new Float32Array(12*d):void 0,w=o.defined(c.bitangent)?new Float32Array(12*d):void 0,g=o.defined(c.st)?new Float32Array(8*d):void 0,x=J,M=Z,b=W,V=a.Cartesian3.negate(x,X),F=a.Cartesian3.negate(M,H),P=a.Cartesian3.negate(b,$);C=0,p&&(U(C,y,v,w,g,P,x,M),C+=12),U(C,y,v,w,g,b,V,M),U(C+=12,y,v,w,g,V,P,M),U(C+=12,y,v,w,g,F,P,V),U(C+=12,y,v,w,g,x,b,M),U(C+=12,y,v,w,g,M,b,V),o.defined(y)&&(_.normal=new s.GeometryAttribute({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:y})),o.defined(v)&&(_.tangent=new s.GeometryAttribute({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:v})),o.defined(w)&&(_.bitangent=new s.GeometryAttribute({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:w})),o.defined(g)&&(_.st=new s.GeometryAttribute({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:g}))}for(var E=new Uint16Array(6*d),O=0;O<d;++O){var z=6*O,R=4*O;E[z]=R,E[z+1]=R+1,E[z+2]=R+2,E[z+3]=R,E[z+4]=R+2,E[z+5]=R+3}return new s.Geometry({attributes:_,indices:E,primitiveType:u.PrimitiveType.TRIANGLES,boundingSphere:e.BoundingSphere.fromVertices(m)})},t.FrustumGeometry=q,t.OrthographicFrustum=b,t.PerspectiveFrustum=S}));
