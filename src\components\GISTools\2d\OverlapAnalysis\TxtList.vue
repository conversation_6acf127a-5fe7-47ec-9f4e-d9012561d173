<template>
  <div class="txt-warp">
    <div class="analyse analyse-warp">
      <div>
        <p class="title">
          分析结果
        </p>
      </div>
      <div class="analyse-des">
        <p class="analyse-top">
          叠加分析范围面积为
          <span>{{ +drawArea.toFixed(4) }}</span>
          公顷，其中：
        </p>
        <ul class="analyse-detail">
          <li v-for="(classify, index) in classifyList" :key="index">
            <span>{{ `${classify.layerName}` }}</span>:
            <div class="area">
              <span>{{ classify.layerArea>0?(classify.layerArea*1e-4).toFixed(4):0 }}</span>
              <span>公顷</span>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <div class="analyse analyse-container">
      <p class="title">
        分析详情
      </p>
      <div class="export-word" @click="exportOverlapAnalyseResult">
        <span>导出报告</span>
      </div>
      <el-collapse v-model="activeNames" class="analyse-div">
        <el-collapse-item v-for="(layer, index) in classifyList" :key="index" :name="index"
                          :title="layer.layerName"
        >
          <div class="content">
            <div class="table-scroll">
              <el-table class="table-div" :data="layer.areaList">
                <el-table-column prop="label" label="地类" min-width="50%" />
                <el-table-column prop="value" label="面积(公顷)" min-width="35%">
                  <template v-slot="scope">
                    {{ `${(scope.row.value*1e-4).toFixed(4)}` }}
                  </template>
                </el-table-column>
                <el-table-column prop="percentage" label="占比" min-width="30%">
                  <template v-slot="scope">
                    {{ `${
                      ((scope.row.value)/
                        layer.layerArea * 100).toFixed(2)
                      +"%"}` }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
      <div v-if="!classifyList.length" class="tip">
        <i class="iconfont icon-zanwushuju" />
        <span>暂无数据</span>
      </div>
    </div>
  </div>
</template>

<script setup name="文本分析描述">
import { downloadOnanalysis } from "@/api/gis/export.js"
import { saveAs } from 'file-saver'

const props = defineProps({
  classifyList: {
    type: Array,
    required: true
  },
  drawArea: {
    type: Number,
    required: true,
    default: 0
  },
  prjInfo: {
    type: Object,
    required: true,
    default: () => {
      return {
        'prjName': "对比分析",
        'region': "晋宁区"
      }
    }
  }
})

const { classifyList,drawArea,prjInfo } = toRefs(props)
const activeNames = ref([])

// 导出叠加分析结果
const exportOverlapAnalyseResult = ()=> {
  const filename = prjInfo.value.filename
  downloadOnanalysis(filename).then(res => {
    saveAs(res, prjInfo.value.filename)
  })
}

</script>

<style scoped lang="scss">

</style>
