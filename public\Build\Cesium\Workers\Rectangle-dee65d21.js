define(["exports","./Cartographic-3309dd0d","./Check-7b2a090c","./when-b60132fc","./Math-119be1a3"],(function(e,t,a,i,n){"use strict";function r(e,a,r,u){a=i.defaultValue(a,0),r=i.defaultValue(r,0),u=i.defaultValue(u,0),n.CesiumMath.equalsEpsilon(u,6356752.314245179,n.CesiumMath.EPSILON10)&&(n.CesiumMath.Radius=u),e._radii=new t.Cartesian3(a,r,u),e._radiiSquared=new t.Cartesian3(a*a,r*r,u*u),e._radiiToTheFourth=new t.Cartesian3(a*a*a*a,r*r*r*r,u*u*u*u),e._oneOverRadii=new t.Cartesian3(0===a?0:1/a,0===r?0:1/r,0===u?0:1/u),e._oneOverRadiiSquared=new t.Cartesian3(0===a?0:1/(a*a),0===r?0:1/(r*r),0===u?0:1/(u*u)),e._minimumRadius=Math.min(a,r,u),e._maximumRadius=Math.max(a,r,u),e._centerToleranceSquared=n.CesiumMath.EPSILON1,0!==e._radiiSquared.z&&(e._squaredXOverSquaredZ=e._radiiSquared.x/e._radiiSquared.z)}function u(e,t,a){this._radii=void 0,this._radiiSquared=void 0,this._radiiToTheFourth=void 0,this._oneOverRadii=void 0,this._oneOverRadiiSquared=void 0,this._minimumRadius=void 0,this._maximumRadius=void 0,this._centerToleranceSquared=void 0,this._squaredXOverSquaredZ=void 0,r(this,e,t,a)}Object.defineProperties(u.prototype,{radii:{get:function(){return this._radii}},radiiSquared:{get:function(){return this._radiiSquared}},radiiToTheFourth:{get:function(){return this._radiiToTheFourth}},oneOverRadii:{get:function(){return this._oneOverRadii}},oneOverRadiiSquared:{get:function(){return this._oneOverRadiiSquared}},minimumRadius:{get:function(){return this._minimumRadius}},maximumRadius:{get:function(){return this._maximumRadius}}}),u.clone=function(e,a){if(i.defined(e)){var n=e._radii;return i.defined(a)?(t.Cartesian3.clone(n,a._radii),t.Cartesian3.clone(e._radiiSquared,a._radiiSquared),t.Cartesian3.clone(e._radiiToTheFourth,a._radiiToTheFourth),t.Cartesian3.clone(e._oneOverRadii,a._oneOverRadii),t.Cartesian3.clone(e._oneOverRadiiSquared,a._oneOverRadiiSquared),a._minimumRadius=e._minimumRadius,a._maximumRadius=e._maximumRadius,a._centerToleranceSquared=e._centerToleranceSquared,a):new u(n.x,n.y,n.z)}},u.fromCartesian3=function(e,t){return i.defined(t)||(t=new u),i.defined(e)?(r(t,e.x,e.y,e.z),t):t},u.WGS84=Object.freeze(new u(6378137,6378137,n.CesiumMath.Radius)),u.XIAN80=Object.freeze(new u(6378140,6378140,6356755.29)),u.CGCS2000=Object.freeze(new u(6378137,6378137,6356752.31)),u.UNIT_SPHERE=Object.freeze(new u(1,1,1)),u.MOON=Object.freeze(new u(n.CesiumMath.LUNAR_RADIUS,n.CesiumMath.LUNAR_RADIUS,n.CesiumMath.LUNAR_RADIUS)),u.prototype.clone=function(e){return u.clone(this,e)},u.packedLength=t.Cartesian3.packedLength,u.pack=function(e,a,n){return n=i.defaultValue(n,0),t.Cartesian3.pack(e._radii,a,n),a},u.unpack=function(e,a,n){a=i.defaultValue(a,0);var r=t.Cartesian3.unpack(e,a);return u.fromCartesian3(r,n)},u.prototype.geocentricSurfaceNormal=t.Cartesian3.normalize,u.prototype.geodeticSurfaceNormalCartographic=function(e,a){var n=e.longitude,r=e.latitude,u=Math.cos(r),o=u*Math.cos(n),s=u*Math.sin(n),h=Math.sin(r);return i.defined(a)||(a=new t.Cartesian3),a.x=o,a.y=s,a.z=h,t.Cartesian3.normalize(a,a)},u.prototype.geodeticSurfaceNormal=function(e,a){return i.defined(a)||(a=new t.Cartesian3),a=t.Cartesian3.multiplyComponents(e,this._oneOverRadiiSquared,a),t.Cartesian3.normalize(a,a)};var o=new t.Cartesian3,s=new t.Cartesian3;u.prototype.cartographicToCartesian=function(e,a){var n=o,r=s;this.geodeticSurfaceNormalCartographic(e,n),t.Cartesian3.multiplyComponents(this._radiiSquared,n,r);var u=Math.sqrt(t.Cartesian3.dot(n,r));return t.Cartesian3.divideByScalar(r,u,r),t.Cartesian3.multiplyByScalar(n,e.height,n),i.defined(a)||(a=new t.Cartesian3),t.Cartesian3.add(r,n,a)},u.prototype.cartographicArrayToCartesianArray=function(e,t){var a=e.length;i.defined(t)?t.length=a:t=new Array(a);for(var n=0;n<a;n++)t[n]=this.cartographicToCartesian(e[n],t[n]);return t};var h=new t.Cartesian3,d=new t.Cartesian3,c=new t.Cartesian3;function l(e,t,a,n){this.west=i.defaultValue(e,0),this.south=i.defaultValue(t,0),this.east=i.defaultValue(a,0),this.north=i.defaultValue(n,0)}u.prototype.cartesianToCartographic=function(e,a){var r=this.scaleToGeodeticSurface(e,d);if(i.defined(r)){var u=this.geodeticSurfaceNormal(r,h),o=t.Cartesian3.subtract(e,r,c),s=Math.atan2(u.y,u.x),l=Math.asin(u.z),m=n.CesiumMath.sign(t.Cartesian3.dot(o,e))*t.Cartesian3.magnitude(o);return i.defined(a)?(a.longitude=s,a.latitude=l,a.height=m,a):new t.Cartographic(s,l,m)}},u.prototype.cartesianArrayToCartographicArray=function(e,t){var a=e.length;i.defined(t)?t.length=a:t=new Array(a);for(var n=0;n<a;++n)t[n]=this.cartesianToCartographic(e[n],t[n]);return t},u.prototype.scaleToGeodeticSurface=function(e,a){return t.scaleToGeodeticSurface(e,this._oneOverRadii,this._oneOverRadiiSquared,this._centerToleranceSquared,a)},u.prototype.scaleToGeocentricSurface=function(e,a){i.defined(a)||(a=new t.Cartesian3);var n=e.x,r=e.y,u=e.z,o=this._oneOverRadiiSquared,s=1/Math.sqrt(n*n*o.x+r*r*o.y+u*u*o.z);return t.Cartesian3.multiplyByScalar(e,s,a)},u.prototype.transformPositionToScaledSpace=function(e,a){return i.defined(a)||(a=new t.Cartesian3),t.Cartesian3.multiplyComponents(e,this._oneOverRadii,a)},u.prototype.transformPositionFromScaledSpace=function(e,a){return i.defined(a)||(a=new t.Cartesian3),t.Cartesian3.multiplyComponents(e,this._radii,a)},u.prototype.equals=function(e){return this===e||i.defined(e)&&t.Cartesian3.equals(this._radii,e._radii)},u.prototype.toString=function(){return this._radii.toString()},u.prototype.getSurfaceNormalIntersectionWithZAxis=function(e,a,n){a=i.defaultValue(a,0);var r=this._squaredXOverSquaredZ;if(i.defined(n)||(n=new t.Cartesian3),n.x=0,n.y=0,n.z=e.z*(1-r),!(Math.abs(n.z)>=this._radii.z-a))return n},Object.defineProperties(l.prototype,{width:{get:function(){return l.computeWidth(this)}},height:{get:function(){return l.computeHeight(this)}}}),l.packedLength=4,l.pack=function(e,t,a){return a=i.defaultValue(a,0),t[a++]=e.west,t[a++]=e.south,t[a++]=e.east,t[a]=e.north,t},l.unpack=function(e,t,a){return t=i.defaultValue(t,0),i.defined(a)||(a=new l),a.west=e[t++],a.south=e[t++],a.east=e[t++],a.north=e[t],a},l.computeWidth=function(e){var t=e.east,a=e.west;return t<a&&(t+=n.CesiumMath.TWO_PI),t-a},l.computeHeight=function(e){return e.north-e.south},l.fromDegrees=function(e,t,a,r,u){return e=n.CesiumMath.toRadians(i.defaultValue(e,0)),t=n.CesiumMath.toRadians(i.defaultValue(t,0)),a=n.CesiumMath.toRadians(i.defaultValue(a,0)),r=n.CesiumMath.toRadians(i.defaultValue(r,0)),i.defined(u)?(u.west=e,u.south=t,u.east=a,u.north=r,u):new l(e,t,a,r)},l.fromRadians=function(e,t,a,n,r){return i.defined(r)?(r.west=i.defaultValue(e,0),r.south=i.defaultValue(t,0),r.east=i.defaultValue(a,0),r.north=i.defaultValue(n,0),r):new l(e,t,a,n)},l.fromCartographicArray=function(e,t){for(var a=Number.MAX_VALUE,r=-Number.MAX_VALUE,u=Number.MAX_VALUE,o=-Number.MAX_VALUE,s=Number.MAX_VALUE,h=-Number.MAX_VALUE,d=0,c=e.length;d<c;d++){var m=e[d];a=Math.min(a,m.longitude),r=Math.max(r,m.longitude),s=Math.min(s,m.latitude),h=Math.max(h,m.latitude);var f=m.longitude>=0?m.longitude:m.longitude+n.CesiumMath.TWO_PI;u=Math.min(u,f),o=Math.max(o,f)}return r-a>o-u&&(a=u,(r=o)>n.CesiumMath.PI&&(r-=n.CesiumMath.TWO_PI),a>n.CesiumMath.PI&&(a-=n.CesiumMath.TWO_PI)),i.defined(t)?(t.west=a,t.south=s,t.east=r,t.north=h,t):new l(a,s,r,h)},l.fromCartesianArray=function(e,t,a){t=i.defaultValue(t,u.WGS84);for(var r=Number.MAX_VALUE,o=-Number.MAX_VALUE,s=Number.MAX_VALUE,h=-Number.MAX_VALUE,d=Number.MAX_VALUE,c=-Number.MAX_VALUE,m=0,f=e.length;m<f;m++){var C=t.cartesianToCartographic(e[m]);r=Math.min(r,C.longitude),o=Math.max(o,C.longitude),d=Math.min(d,C.latitude),c=Math.max(c,C.latitude);var M=C.longitude>=0?C.longitude:C.longitude+n.CesiumMath.TWO_PI;s=Math.min(s,M),h=Math.max(h,M)}return o-r>h-s&&(r=s,(o=h)>n.CesiumMath.PI&&(o-=n.CesiumMath.TWO_PI),r>n.CesiumMath.PI&&(r-=n.CesiumMath.TWO_PI)),i.defined(a)?(a.west=r,a.south=d,a.east=o,a.north=c,a):new l(r,d,o,c)},l.clone=function(e,t){if(i.defined(e))return i.defined(t)?(t.west=e.west,t.south=e.south,t.east=e.east,t.north=e.north,t):new l(e.west,e.south,e.east,e.north)},l.equalsEpsilon=function(e,t,a){return e===t||i.defined(e)&&i.defined(t)&&Math.abs(e.west-t.west)<=a&&Math.abs(e.south-t.south)<=a&&Math.abs(e.east-t.east)<=a&&Math.abs(e.north-t.north)<=a},l.prototype.clone=function(e){return l.clone(this,e)},l.prototype.equals=function(e){return l.equals(this,e)},l.equals=function(e,t){return e===t||i.defined(e)&&i.defined(t)&&e.west===t.west&&e.south===t.south&&e.east===t.east&&e.north===t.north},l.prototype.equalsEpsilon=function(e,t){return l.equalsEpsilon(this,e,t)},l.validate=function(e){},l.southwest=function(e,a){return i.defined(a)?(a.longitude=e.west,a.latitude=e.south,a.height=0,a):new t.Cartographic(e.west,e.south)},l.northwest=function(e,a){return i.defined(a)?(a.longitude=e.west,a.latitude=e.north,a.height=0,a):new t.Cartographic(e.west,e.north)},l.northeast=function(e,a){return i.defined(a)?(a.longitude=e.east,a.latitude=e.north,a.height=0,a):new t.Cartographic(e.east,e.north)},l.southeast=function(e,a){return i.defined(a)?(a.longitude=e.east,a.latitude=e.south,a.height=0,a):new t.Cartographic(e.east,e.south)},l.center=function(e,a){var r=e.east,u=e.west;r<u&&(r+=n.CesiumMath.TWO_PI);var o=n.CesiumMath.negativePiToPi(.5*(u+r)),s=.5*(e.south+e.north);return i.defined(a)?(a.longitude=o,a.latitude=s,a.height=0,a):new t.Cartographic(o,s)},l.intersection=function(e,t,a){var r=e.east,u=e.west,o=t.east,s=t.west;r<u&&o>0?r+=n.CesiumMath.TWO_PI:o<s&&r>0&&(o+=n.CesiumMath.TWO_PI),r<u&&s<0?s+=n.CesiumMath.TWO_PI:o<s&&u<0&&(u+=n.CesiumMath.TWO_PI);var h=n.CesiumMath.negativePiToPi(Math.max(u,s)),d=n.CesiumMath.negativePiToPi(Math.min(r,o));if(!((e.west<e.east||t.west<t.east)&&d<=h)){var c=Math.max(e.south,t.south),m=Math.min(e.north,t.north);if(!(c>=m))return i.defined(a)?(a.west=h,a.south=c,a.east=d,a.north=m,a):new l(h,c,d,m)}},l.simpleIntersection=function(e,t,a){var n=Math.max(e.west,t.west),r=Math.max(e.south,t.south),u=Math.min(e.east,t.east),o=Math.min(e.north,t.north);if(!(r>=o||n>=u))return i.defined(a)?(a.west=n,a.south=r,a.east=u,a.north=o,a):new l(n,r,u,o)},l.union=function(e,t,a){i.defined(a)||(a=new l);var r=e.east,u=e.west,o=t.east,s=t.west;r<u&&o>0?r+=n.CesiumMath.TWO_PI:o<s&&r>0&&(o+=n.CesiumMath.TWO_PI),r<u&&s<0?s+=n.CesiumMath.TWO_PI:o<s&&u<0&&(u+=n.CesiumMath.TWO_PI);var h=n.CesiumMath.convertLongitudeRange(Math.min(u,s)),d=n.CesiumMath.convertLongitudeRange(Math.max(r,o));return a.west=h,a.south=Math.min(e.south,t.south),a.east=d,a.north=Math.max(e.north,t.north),a},l.expand=function(e,t,a){return i.defined(a)||(a=new l),a.west=Math.min(e.west,t.longitude),a.south=Math.min(e.south,t.latitude),a.east=Math.max(e.east,t.longitude),a.north=Math.max(e.north,t.latitude),a},l.contains=function(e,t){var a=t.longitude,i=t.latitude,r=e.west,u=e.east;return u<r&&(u+=n.CesiumMath.TWO_PI,a<0&&(a+=n.CesiumMath.TWO_PI)),(a>r||n.CesiumMath.equalsEpsilon(a,r,n.CesiumMath.EPSILON14))&&(a<u||n.CesiumMath.equalsEpsilon(a,u,n.CesiumMath.EPSILON14))&&i>=e.south&&i<=e.north};var m=new t.Cartographic;l.subsample=function(e,t,a,r){t=i.defaultValue(t,u.WGS84),a=i.defaultValue(a,0),i.defined(r)||(r=[]);var o=0,s=e.north,h=e.south,d=e.east,c=e.west,f=m;f.height=a,f.longitude=c,f.latitude=s,r[o]=t.cartographicToCartesian(f,r[o]),o++,f.longitude=d,r[o]=t.cartographicToCartesian(f,r[o]),o++,f.latitude=h,r[o]=t.cartographicToCartesian(f,r[o]),o++,f.longitude=c,r[o]=t.cartographicToCartesian(f,r[o]),o++,f.latitude=s<0?s:h>0?h:0;for(var C=1;C<8;++C)f.longitude=-Math.PI+C*n.CesiumMath.PI_OVER_TWO,l.contains(e,f)&&(r[o]=t.cartographicToCartesian(f,r[o]),o++);return 0===f.latitude&&(f.longitude=c,r[o]=t.cartographicToCartesian(f,r[o]),o++,f.longitude=d,r[o]=t.cartographicToCartesian(f,r[o]),o++),r.length=o,r};var f=new t.Cartographic;l.prototype.contains=function(e){return l.contains(this,l.southwest(e,f))&&l.contains(this,l.northwest(e,f))&&l.contains(this,l.southeast(e,f))&&l.contains(this,l.northeast(e,f))},l.MAX_VALUE=Object.freeze(new l(-Math.PI,-n.CesiumMath.PI_OVER_TWO,Math.PI,n.CesiumMath.PI_OVER_TWO)),e.Ellipsoid=u,e.Rectangle=l}));
