<template>
  <div class="project-detail">
    <div class="header">
      <div class="add-header-title">
        <div class="add-title">{{ `【${headerTitle}】项目详情` }}</div>
        <div
          class="add-title-return"
          @click="closeDetail"
        >
          <img
            src="@/assets/images/img-return.png"
            class="back"
          >
          <div class="backlist">返回列表</div>
        </div>
      </div>
    </div>
    <div class="project-content">
      <el-row>
        <el-col
          :span="12"
          class="project-info"
        >
          <el-descriptions
            class="margin-top"
            :column="1"
            size="default"
            border
          >
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">
                  项目名称
                </div>
              </template>
              {{projectForm.xmmc }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">
                  收储面积（公顷）
                </div>
              </template>
              {{projectForm.scmj }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">
                  性质
                </div>
              </template>
              {{projectForm.xz }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">
                  项目坐落
                </div>
              </template>
              {{projectForm.xmzl }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">
                  已征地总面积
                </div>
              </template>
              {{projectForm.yzdzmj }}
            </el-descriptions-item>
            <!--            <el-descriptions-item>-->
            <!--              <template #label>-->
            <!--                <div class="cell-item">-->
            <!--                  总批复金额-->
            <!--                </div>-->
            <!--              </template>-->
            <!--              {{projectForm.zpfje }}-->
            <!--            </el-descriptions-item>-->
            <!--            <el-descriptions-item>-->
            <!--              <template #label>-->
            <!--                <div class="cell-item">-->
            <!--                  收到资金-->
            <!--                </div>-->
            <!--              </template>-->
            <!--              {{projectForm.sdzj }}-->
            <!--            </el-descriptions-item>-->
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">
                  项目开始期限
                </div>
              </template>
              {{projectForm.xmksqx }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">
                  已拆迁总户数
                </div>
              </template>
              {{projectForm.ycqzhs }}
            </el-descriptions-item>
            <!--            <el-descriptions-item>-->
            <!--              <template #label>-->
            <!--                <div class="cell-item">-->
            <!--                  剩余资金-->
            <!--                </div>-->
            <!--              </template>-->
            <!--              {{projectForm.syzj }}-->
            <!--            </el-descriptions-item>-->
            <!--            <el-descriptions-item>-->
            <!--              <template #label>-->
            <!--                <div class="cell-item">-->
            <!--                  征迁批复金额-->
            <!--                </div>-->
            <!--              </template>-->
            <!--              {{projectForm.zqpfje }}-->
            <!--            </el-descriptions-item>-->
            <!--            <el-descriptions-item>-->
            <!--              <template #label>-->
            <!--                <div class="cell-item">-->
            <!--                  支出资金-->
            <!--                </div>-->
            <!--              </template>-->
            <!--              {{projectForm.zczj }}-->
            <!--            </el-descriptions-item>-->
            <!--            <el-descriptions-item>-->
            <!--              <template #label>-->
            <!--                <div class="cell-item">-->
            <!--                  通车时间-->
            <!--                </div>-->
            <!--              </template>-->
            <!--              {{projectForm.tcsj }}-->
            <!--            </el-descriptions-item>-->
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">
                  项目结束期限
                </div>
              </template>
              {{projectForm.xmjsqx }}
            </el-descriptions-item>
          </el-descriptions>
        </el-col>
        <el-col :span="11">
          <div class="attachment">
            <div>
              <div class="related_accessories"/>
              <div class="related">相关附件</div>
              <div
                class="attachment-content"
                v-loading="isFileLoading"
                element-loading-text="正在上传..."
                :element-loading-spinner="svg"
                element-loading-svg-view-box="-10, -10, 50, 50"
                element-loading-background="rgba(122, 122, 122, 0.25)"
              >
                <ul>
                  <li
                    v-for="(item,index) in dictList"
                    :key="index"
                    class="attachment-item"
                  >
                    <div class="attachment-item-header">
                      <span
                        class="arrow-icon"
                        @click="getFiles(item,false)"
                      >
                        <el-icon v-if="item.isArrowUp"><ArrowDownBold /></el-icon>
                        <el-icon v-else><ArrowUpBold /></el-icon>
                      </span>
                      <span>{{ item.dictLabel }}</span>
                    </div>
                    <div
                      v-if="item.isArrowUp"
                      class="attachment-item-content"
                    >
                      <ul>
                        <li
                          class="file-item clearfix"
                          v-for="(file,i) in allFiles[item.dictValue]" :key="i"
                        >
                          <span v-if="file.wjmc.length<20">{{file.wjmc}}</span>
                          <el-popover
                            v-else
                            placement="top-start"
                            width="500"
                            trigger="hover"
                            :content="file.wjmc"
                          >
                            <template #reference>
                              <span>{{file.wjmc.substring(0,20)+"···"}}</span>
                            </template>
                          </el-popover>
                          <span class="action-btn">
                            <el-button
                              type="primary"
                              plain
                              @click="previewFile(file)"
                            >预览</el-button>
                            <el-button
                              type="success"
                              plain
                              @click="downloadFile(file)"
                            >下载</el-button>
                          </span>
                        </li>
                      </ul>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-row class="viewer-row">
        <el-col
          :span="24"
          class="viewer-project"
        >
          <Viewer2d
            v-if="showMap"
            :viewerId="viewerProjectId"
            @onCreateViewer2d="createViewer2d"
          />
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup name="PlanStorageDetail">
import { useRoute, useRouter } from "vue-router"
import { getPlanReserveProject } from "@/api/planReserveProject/planReserveProject.js"
import UseViewer2d from "@/components/GISTools/Viewer/UseViewer2d.js"
import { getDicts } from "@/api/system/dict/data.js"
import { getAttachments } from "@/api/relocationPatrol/relocationLand.js"
import { fetchAndDownload, filePreview } from "@/utils/index.js"
import { addGeoJSON2Map } from "@/utils/OpenLayers/olLayer.js"
import Viewer2d from "@/components/GISTools/Viewer/Viewer2d.vue"
const router = useRouter()
const route = useRoute()

const headerTitle = ref("")
const projectId = route.query.id
const projectForm = ref({})
const viewerProjectId = ref("viewerProjectId")
const showMap = ref(false)
const viewer2d = ref(null)


const dictList = ref()
const svg = `
        <path class="path" d="
          M 30 15
          L 28 17
          M 25.61 25.61
          A 15 15, 0, 0, 1, 15 30
          A 15 15, 0, 1, 1, 27.99 7.5
          L 15 15
        "style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
      `

const isFileLoading = ref(false)
const allFiles = reactive({
  province: [],
  approve: [],
  other: []
})

const isAddGeometry = computed(()=>{
  return projectForm.value && viewer2d.value
})

watch(isAddGeometry,(value)=>{
  if(value){
    const geoJson = JSON.parse(projectForm.value.smgeometry)
    addGeoJSON2Map(geoJson, undefined, viewer2d.value)
    // viewer2d.value.getView().setZoom(15)
  }
})

const getDictData = ()=>{
  getDicts('tdcb_file_type').then((res) => {
    dictList.value = res.data.map(item=>{
      item.isArrowUp = false
      return item
    })
  })
}

/**
 * 获取附件数据
 */
const getFiles = (item,isArrowUp)=>{
  // isFileLoading.value = true
  item.isArrowUp = !item.isArrowUp
  if(isArrowUp){
    item.isArrowUp = true
  }
  item.wjflId = item.dictCode
  item.createTime = new Date(item.createTime)
  item.zbId = projectForm.value.id
  getAttachments(item).then(res=>{
    allFiles[item.dictValue] = res.rows
    console.log(allFiles)
    isFileLoading.value = false
  })
}

/**
 * 文件预览
 */
const previewFile = (file)=>{
  filePreview(file.wjlj,"_blank")
}

/**
 * 文件下载
 */
const downloadFile = (file)=>{
  fetchAndDownload(file.wjlj, file.wjmc);
}

// 根据id获取项目信息
const getProjectById = async (id) => {
  const response = await getPlanReserveProject(id)
  const transformData = {}
  Object.keys(response.data).forEach(key=>{
    if(key === "scmj"){
      // 转换面积单位：平方米->公顷
      transformData[key] = ((response.data[key]) * 1e-4).toFixed(4);
      return
    }
    transformData[key] = response.data[key]
  })
  projectForm.value = transformData
  headerTitle.value = projectForm.value.xmmc
  showMap.value = true
}

/**
 * 创建2d视图
 * @param viewer2dId
 * @param mapInitStore
 * @param mapViewStore
 */
const createViewer2d = async (viewer2dId,mapInitStore,mapViewStore)=>{
  const useViewer2d = new UseViewer2d(viewer2dId,mapInitStore,mapViewStore)
  await useViewer2d.createViewer2d()
  viewer2d.value = useViewer2d.map
}


const closeDetail = ()=>{
  router.push({ path: "/planStorage/planStorageProject" })
}

onBeforeMount(()=>{
  getProjectById(projectId)
  getDictData()
})
</script>

<style scoped lang="scss">
.project-detail {
  padding: 10px;
}
.header {
  border: 1px solid rgb(233, 233, 233);
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  .add-header-title {
    padding: 10px;
    display: flex;
    justify-content: space-between;
    height: 50px;
    background-color: rgb(222, 239, 255);
    box-sizing: border-box;
    border-bottom: 1px solid rgb(233, 233, 233);
    font-weight: 700;
    font-size: 14px;
    line-height: 28px;
  }
  .add-title-return {
    display: flex;
    align-content: center;
    color: rgb(32, 119, 255);
    cursor: pointer;
    font-weight: normal;
    &:hover{
      cursor: pointer;
      font-size: 16px;
      transform: scale(1.15);
      transition: all ease-in .25s;
    }
    .backlist {
      padding-left: 6px;
      font-size: 14px;
    }
    .back{
      height: 18px;
      width: 18px;
      margin-top: 5px;
    }
  }
  .back-icon{
    margin-right: 5px;
  }
}

.project-content{
  margin-top: 20px;
  .viewer-row{
    margin-top: 20px;
    height: 500px;
  }
}

.project-info{
  margin-right: 20px;
}
.viewer-project{
  border: 1px solid rgb(233, 233, 233);
}

.attachment {
  border: 1px solid #e6e6e6;
  border-radius: 4px;
}
.attachment-content {
  min-height: 300px;
  max-height: 350px;
  overflow: auto;
}

.upload-icon{
  display: inline;
  margin-left: 5px;
  color: rgba(68, 68, 68, 0.7411764706);
  .el-icon:hover{
    cursor: pointer;
    color: #0d84ff;
    transform: scale(1.5);
    margin-left: 5px;
    transition: all ease-in .25s;
  }
}
.upload-picture{
  font-size: 30px;
}
.attachment-item{
  font-weight: bold;
  margin: 5px 0;
  color: #444444bd;
  .arrow-icon:hover{
    cursor: pointer;
  }
}
.attachment-item-content{
  font-weight: normal;
  .file-item{
    padding-bottom: 2.5px;
    margin: 5px 0;
    border-bottom: 1px solid #eee;
  }
}
.action-btn{
  float: right;
  margin-right: 5px;
}
.download-btn{
  margin-bottom: 10px;
}
.related_accessories {
  height: 8px;
  background-color: rgba(33, 120, 255, 1);
  border-radius: 4px 4px 0 0;
}
.related_accessories {
  height: 8px;
  background-color: rgba(33, 120, 255, 1);
  border-radius: 4px 4px 0 0;
}

.related {
  border-bottom: 1px solid rgb(230, 230, 230);
  height: 40px;
  line-height: 40px;
  align-content: center;
  padding-left: 10px;
  font-size: small;
  font-weight: bold;
}
.patrol-info{
  width: 60%;
}

.add-content-wrap {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  border-top: 1px solid #e0e0e0;
  padding: 20px 0;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
:deep(.el-select){
  width: 100%!important;
}
:deep(.el-descriptions__label) {
  width: 35%;
}
</style>
