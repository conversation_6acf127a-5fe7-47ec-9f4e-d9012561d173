import request from '@/utils/request';

/**
 * 查询土地供应列表
 * @param query
 * @returns {*}
 */

export const listTdgy = (query) => {
  return request({
    url: '/patrol/tdgy/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询土地供应详细
 * @param id
 */
export const getTdgy = (id) => {
  return request({
    url: '/patrol/tdgy/' + id,
    method: 'get'
  });
};

/**
 * 新增土地供应
 * @param data
 */
export const addTdgy = (data) => {
  return request({
    url: '/patrol/tdgy',
    method: 'post',
    data: data
  });
};

/**
 * 修改土地供应
 * @param data
 */
export const updateTdgy = (data) => {
  return request({
    url: '/patrol/tdgy',
    method: 'put',
    data: data
  });
};

/**
 * 删除土地供应
 * @param id
 */
export const delTdgy = (id) => {
  return request({
    url: '/patrol/tdgy/' + id,
    method: 'delete'
  });
};
