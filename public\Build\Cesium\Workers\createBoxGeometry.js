define(["./BoxGeometry-97759c2d","./when-b60132fc","./arrayFill-4513d7ad","./Check-7b2a090c","./buildModuleUrl-9085faaa","./Cartographic-3309dd0d","./Math-119be1a3","./Rectangle-dee65d21","./FeatureDetection-806b12f0","./Cartesian4-3ca25aab","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Event-16a2dfbf","./ComponentDatatype-c140a87d","./GeometryAttribute-c65394ac","./Cartesian2-db21342c","./GeometryAttributes-252e9929","./GeometryOffsetAttribute-fbeb6f1a","./VertexFormat-6446fca0"],(function(e,t,a,r,n,o,c,d,b,i,f,u,m,y,s,G,l,C,h){"use strict";return function(a,r){return t.defined(r)&&(a=e.BoxGeometry.unpack(a,r)),e.BoxGeometry.createGeometry(a)}}));
