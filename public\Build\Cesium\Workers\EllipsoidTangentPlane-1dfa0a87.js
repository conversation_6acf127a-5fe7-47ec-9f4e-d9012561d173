define(["exports","./Cartographic-3309dd0d","./Check-7b2a090c","./when-b60132fc","./buildModuleUrl-9085faaa","./Cartesian2-db21342c","./Cartesian4-3ca25aab","./Rectangle-dee65d21","./IntersectionTests-0d6905a3","./FeatureDetection-806b12f0","./Plane-a3d8b3d2","./GeometryAttribute-c65394ac"],(function(e,n,t,i,a,r,s,o,c,m,l,u){"use strict";function d(e,t,a){this.minimum=n.Cartesian3.clone(i.defaultValue(e,n.Cartesian3.ZERO)),this.maximum=n.Cartesian3.clone(i.defaultValue(t,n.Cartesian3.ZERO)),a=i.defined(a)?n.Cartesian3.clone(a):n.Cartesian3.midpoint(this.minimum,this.maximum,new n.Cartesian3),this.center=a}d.fromPoints=function(e,t){if(i.defined(t)||(t=new d),!i.defined(e)||0===e.length)return t.minimum=n.Cartesian3.clone(n.Cartesian3.ZERO,t.minimum),t.maximum=n.Cartesian3.clone(n.Cartesian3.ZERO,t.maximum),t.center=n.Cartesian3.clone(n.Cartesian3.ZERO,t.center),t;for(var a=e[0].x,r=e[0].y,s=e[0].z,o=e[0].x,c=e[0].y,m=e[0].z,l=e.length,u=1;u<l;u++){var f=e[u],h=f.x,C=f.y,p=f.z;a=Math.min(h,a),o=Math.max(h,o),r=Math.min(C,r),c=Math.max(C,c),s=Math.min(p,s),m=Math.max(p,m)}var x=t.minimum;x.x=a,x.y=r,x.z=s;var y=t.maximum;return y.x=o,y.y=c,y.z=m,t.center=n.Cartesian3.midpoint(x,y,t.center),t},d.clone=function(e,t){if(i.defined(e))return i.defined(t)?(t.minimum=n.Cartesian3.clone(e.minimum,t.minimum),t.maximum=n.Cartesian3.clone(e.maximum,t.maximum),t.center=n.Cartesian3.clone(e.center,t.center),t):new d(e.minimum,e.maximum,e.center)},d.equals=function(e,t){return e===t||i.defined(e)&&i.defined(t)&&n.Cartesian3.equals(e.center,t.center)&&n.Cartesian3.equals(e.minimum,t.minimum)&&n.Cartesian3.equals(e.maximum,t.maximum)};var f=new n.Cartesian3;d.intersectPlane=function(e,t){f=n.Cartesian3.subtract(e.maximum,e.minimum,f);var i=n.Cartesian3.multiplyByScalar(f,.5,f),r=t.normal,s=i.x*Math.abs(r.x)+i.y*Math.abs(r.y)+i.z*Math.abs(r.z),o=n.Cartesian3.dot(e.center,r)+t.distance;return o-s>0?a.Intersect.INSIDE:o+s<0?a.Intersect.OUTSIDE:a.Intersect.INTERSECTING},d.prototype.clone=function(e){return d.clone(this,e)},d.prototype.intersectPlane=function(e){return d.intersectPlane(this,e)},d.prototype.equals=function(e){return d.equals(this,e)};var h=new s.Cartesian4;function C(e,t){e=(t=i.defaultValue(t,o.Ellipsoid.WGS84)).scaleToGeodeticSurface(e);var a=u.Transforms.eastNorthUpToFixedFrame(e,t);this._ellipsoid=t,this._origin=e,this._xAxis=n.Cartesian3.fromCartesian4(m.Matrix4.getColumn(a,0,h)),this._yAxis=n.Cartesian3.fromCartesian4(m.Matrix4.getColumn(a,1,h));var r=n.Cartesian3.fromCartesian4(m.Matrix4.getColumn(a,2,h));this._plane=l.Plane.fromPointNormal(e,r)}Object.defineProperties(C.prototype,{ellipsoid:{get:function(){return this._ellipsoid}},origin:{get:function(){return this._origin}},plane:{get:function(){return this._plane}},xAxis:{get:function(){return this._xAxis}},yAxis:{get:function(){return this._yAxis}},zAxis:{get:function(){return this._plane.normal}}});var p=new d;C.fromPoints=function(e,n){return new C(d.fromPoints(e,p).center,n)};var x=new c.Ray,y=new n.Cartesian3;C.prototype.projectPointOntoPlane=function(e,t){var a=x;a.origin=e,n.Cartesian3.normalize(e,a.direction);var s=c.IntersectionTests.rayPlane(a,this._plane,y);if(i.defined(s)||(n.Cartesian3.negate(a.direction,a.direction),s=c.IntersectionTests.rayPlane(a,this._plane,y)),i.defined(s)){var o=n.Cartesian3.subtract(s,this._origin,s),m=n.Cartesian3.dot(this._xAxis,o),l=n.Cartesian3.dot(this._yAxis,o);return i.defined(t)?(t.x=m,t.y=l,t):new r.Cartesian2(m,l)}},C.prototype.projectPointsOntoPlane=function(e,n){i.defined(n)||(n=[]);for(var t=0,a=e.length,r=0;r<a;r++){var s=this.projectPointOntoPlane(e[r],n[t]);i.defined(s)&&(n[t]=s,t++)}return n.length=t,n},C.prototype.projectPointToNearestOnPlane=function(e,t){i.defined(t)||(t=new r.Cartesian2);var a=x;a.origin=e,n.Cartesian3.clone(this._plane.normal,a.direction);var s=c.IntersectionTests.rayPlane(a,this._plane,y);i.defined(s)||(n.Cartesian3.negate(a.direction,a.direction),s=c.IntersectionTests.rayPlane(a,this._plane,y));var o=n.Cartesian3.subtract(s,this._origin,s),m=n.Cartesian3.dot(this._xAxis,o),l=n.Cartesian3.dot(this._yAxis,o);return t.x=m,t.y=l,t},C.prototype.projectPointsToNearestOnPlane=function(e,n){i.defined(n)||(n=[]);var t=e.length;n.length=t;for(var a=0;a<t;a++)n[a]=this.projectPointToNearestOnPlane(e[a],n[a]);return n};var g=new n.Cartesian3;C.prototype.projectPointOntoEllipsoid=function(e,t){i.defined(t)||(t=new n.Cartesian3);var a=this._ellipsoid,r=this._origin,s=this._xAxis,o=this._yAxis,c=g;return n.Cartesian3.multiplyByScalar(s,e.x,c),t=n.Cartesian3.add(r,c,t),n.Cartesian3.multiplyByScalar(o,e.y,c),n.Cartesian3.add(t,c,t),a.scaleToGeocentricSurface(t,t),t},C.prototype.projectPointsOntoEllipsoid=function(e,n){var t=e.length;i.defined(n)?n.length=t:n=new Array(t);for(var a=0;a<t;++a)n[a]=this.projectPointOntoEllipsoid(e[a],n[a]);return n},e.AxisAlignedBoundingBox=d,e.EllipsoidTangentPlane=C}));
