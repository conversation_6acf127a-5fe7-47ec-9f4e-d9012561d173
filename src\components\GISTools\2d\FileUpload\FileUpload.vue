<template>
  <div class="upload-wrap">
    <div class="upload-div">
      <el-upload
        ref="upload"
        class="upload-file"
        action="#"
        accept=".shp,.zip"
        :file-list="fileList"
        show-file-list
        :limit="1"
        :before-upload="handleBefore"
        :on-exceed="handleExceed"
        :on-error="handleUploadError"
        :on-change="handleChange"
        :http-request="httpRequest"
      >
        <el-button
          size="small"
          type="primary"
        >{{ title }}</el-button>
      </el-upload>
    </div>
  </div>
</template>

<script setup name="文件上传">
import { read } from 'shapefile'
import { ElMessage } from "element-plus"
import JSZip from "jszip"
import GeoJSON from "ol/format/GeoJSON"
import { addGeoJSON2Map } from "@/utils/OpenLayers/olLayer.js"
import { PlanarArea } from "@/utils/mapFunction/geometryTool.js"
import { getCenter } from "ol/extent"
import eventBus from "@/utils/eventBus.js"
import { removeAllLayer } from "@/utils/OpenLayers/olTool.js"

const { proxy } = getCurrentInstance()

defineProps({
  title: {
    type: String,
    require: true,
    default: '点击上传文件'
  },
  uploadMap: {
    type: Object,
    default: () => window._map ? window._map : ''
  }
})

const fileList = ref([])
const operatePolygon = ref({})
const coords = ref([])
const shapeArea = ref(0)
const geometry = ref({})

/**
 * 限制用户上传文件格式和大小
 * 文件上传之前处理程序,若返回false或者Promise且被reject,则停止上传
 * @param file
 * @returns {boolean}
 */
const handleBefore = (file)=> {
  removeAllLayer()
  // 判断文件大小是否超过100KB，JS文件size大小默认单位为字节，需将Byte转换为KB
  const isLt100KB = file.size / 1024 < 100
  const isFileFormat = file.name.indexOf('.shp') > -1 || file.name.indexOf('.zip') > -1
  if (!isLt100KB) {
    ElMessage.error('上传文件大小不能超过100KB!')
  }
  if (!isFileFormat){
    ElMessage.error('上传文件只能是.shp 或者 .zip格式')
  }
  return isLt100KB && isFileFormat
}

// 文件大小超出处理程序
const handleExceed = (files, fileList)=> {
  if (fileList.length > 10) {
    // 取消上传
    proxy.$refs.upload.abort()
    ElMessage.error('上传文件最多不能超过10个！')
  }
}

// 文件上传出错处理程序
const handleUploadError = (error, file)=> {
  ElMessage.notify.error({
    title: 'error',
    message: file.name + '上传出错:' + error,
    position: 'bottom-right'
  })
}

// 文件改变处理程序
const handleChange = (file, fileList)=> {
  fileList.value = fileList
}

// 自定义文件文件上传
const httpRequest = (data)=> {
  if (fileList.value.length) {
    fileList.value = []
    proxy.$refs.upload.clearFiles()
  }
  const file = data.file
  const fileName = file.name
  const isZip = fileName.indexOf('.zip') > -1
  isZip ? openZip(file) : openShp(file)
}
// 转ArrayBuffer
const openShp = (data)=> {
  const reader = new FileReader()
  reader.readAsArrayBuffer(data)
  reader.onload = (e) => {
    openShapefile(e.target.result)
  }
}

// 读取压缩包
const openZip = (data)=> {
  const zip = new JSZip()
  zip.loadAsync(data).then(file => {
    const fileList = Object.keys(file.files)
    const regExp = new RegExp(/\S\.shp$/)
    const shpFile = fileList.find(file => regExp.test(file))
    zip.file(shpFile).async('arraybuffer')
      .then(content => {
        openShapefile(content)
      })
  }).catch(err => {
    console.log(err)
    ElMessage.error('请上传正确格式的文件！')
  })
}

const openShapefile = async (content)=> {
  const featureCollection = await read(content)
  console.log(featureCollection);
  const geoType = featureCollection.features[0].geometry.type
  const isPolygon = geoType === 'Polygon' || geoType === 'MultiPolygon'
  if (!isPolygon){
    if (geoType === 'Point'){
      ElMessage.error('当前上传的文件类型为 "Point"，请上传面对象！')
    } else if (geoType === 'LineString'){
      ElMessage.error('当前上传的文件类型为 "LineString"，请上传面对象！')
    }
    return
  }

  const geoJson = new GeoJSON()
  const features = geoJson.readFeatures(featureCollection)
  // 添加对象
  addGeoJSON2Map(featureCollection)

  if (features.length === 1){
    geometry.value = geoJson.writeGeometryObject(features[0].getGeometry())
    coords.value = geometry.value.coordinates
    shapeArea.value = PlanarArea(coords.value,'Polygon') * 1e-4
  } else {
    let area = 0
    const coords = []
    features.forEach(feature => {
      geometry.value = geoJson.writeGeometryObject(feature.getGeometry())
      area += PlanarArea(geometry.value.coordinates,'Polygon') * 1e-4
      coords.push(geometry.value.coordinates)
    })
    coords.value = coords
    shapeArea.value = area
  }

  if (shapeArea.value > 50) {
    ElMessage({
      message: `上传文件面积不能超过50公顷，请重新上传范围！`,
      type: 'error'
    })
    removeAllLayer()
    return
  }

  eventBus.emit('getCoords', { geometry: geometry.value, drawArea: shapeArea.value })
  const nc = getCenter(features[0].getGeometry().getExtent())
  // polygon.getCenter()
  ElMessage.success("文件上传成功。")
  window._map.getView().animate({ center: nc },{ zoom: 15 })
}


</script>

<style scoped lang="scss">

</style>
