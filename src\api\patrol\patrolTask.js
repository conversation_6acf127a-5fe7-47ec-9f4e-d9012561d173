import request from "@/utils/request";

/**
 * 查询巡查任务管理列表
 * @param query
 * @returns {*}
 */

export const listPatrolTask = (query) => {
  return request({
    url: "/patrol/relPatrolTask/list",
    method: "get",
    params: query
  });
};

/**
 * 查询巡查任务管理详细
 * @param id
 */
export const getPatrolTask = (id) => {
  return request({
    url: "/patrol/patrolTask/" + id,
    method: "get"
  });
};

/**
 * 新增巡查任务管理
 * @param data
 */
export const addPatrolTask = (data) => {
  return request({
    url: "/patrol/patrolTask",
    method: "post",
    data: data
  });
};

/**
 * 修改巡查任务管理
 * @param data
 */
export const updatePatrolTask = (data) => {
  return request({
    url: "/patrol/patrolTask",
    method: "put",
    data: data
  });
};

/**
 * 删除巡查任务管理
 * @param id
 */
export const delPatrolTask = (id) => {
  return request({
    url: "/patrol/patrolTask/" + id,
    method: "delete"
  });
};

export const getPatrolLand = (id) => {
  return request({
    url: "/patrol/land/" + id,
    method: "get"
  })
}
