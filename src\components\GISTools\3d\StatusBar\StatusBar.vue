<template>
  <div class="">
    <div class="side-tool">
      <button class="zoom-bar-item" title="切换地图" @click="switchView">
        <svg-icon :icon-class="viewIconName" />
      </button>
      <button class="zoom-bar-item" title="放大" @click="zoomIn">
        <svg-icon icon-class="zoomin" />
      </button>
      <button
        class="zoom-bar-item"
        title="缩放到起始位置"
        @click="resetPosition"
      >
        <svg-icon icon-class="homepage" />
      </button>
      <button class="zoom-bar-item" title="缩小" @click="zoomOut">
        <svg-icon icon-class="zoomout" />
      </button>
    </div>
  </div>
</template>

<script setup name="StatusBar">
import useMapViewStore from "@/store/modules/map/mapView.js"
import { flyToPoint, flyToS3MLayerCenter } from "@/utils/Cesium/CesiumTool.js"

const store = useMapViewStore()

const viewIconName = ref("2D")

/**
 * 切换视图
 */
function switchView(){
  useMapViewStore().switchMap("2D");
}


/**
 * 放大
 */
function zoomIn() {
  if (store.viewer3d) {
    const viewer3d = useMapViewStore().viewer3d
    const amount = Math.ceil(
      viewer3d.camera.positionCartographic.height / 3
    );
    viewer3d.camera.zoomIn(amount);
  }
}
/**
 * 缩小
 */
function zoomOut() {
  if (store.viewer3d) {
    const viewer3d = useMapViewStore().viewer3d
    const amount = Math.ceil(
      viewer3d.camera.positionCartographic.height / 3
    );
    viewer3d.camera.zoomOut(amount);
  }
}
/**
 * 复位
 */
function resetPosition() {
  if (store.viewer3d) {
    const viewer3d = useMapViewStore().viewer3d
    const s3mLayer = viewer3d.scene.layers.find("fengceng") || viewer3d.scene.layers.find("fenghu")
    if (s3mLayer){
      flyToS3MLayerCenter(viewer3d,s3mLayer)
    } else {
      flyToPoint(viewer3d);
    }
  }
}
</script>

<style scoped lang="scss">
@import "@/styles/variables.module.scss";
$sideBarWeight: 50px;
$sideContentWeight: 325px;
.side-bar {
  width: $sideBarWeight;
  position: absolute;
  bottom: 20px;
  right: 5px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  .side-tool {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
}
.zoom-bar-item {
  position: relative;
  width: 40px;
  height: 40px;
  background: rgba(0, 19, 46, 0.686274509803922);
  color: #fff;
  cursor: pointer;
  transition: all 0.25s; /* 定义过渡效果 */
  border-radius: 2.5px;
  border: 1px solid #cccccc6b;
  margin: 5px 0;
  &:hover {
    transition-delay: 0.25s;
    background-color: #248cfba8
  }
  .svg-icon {
    width: 1.5em;
    height: 1.5em;
  }
}
</style>
