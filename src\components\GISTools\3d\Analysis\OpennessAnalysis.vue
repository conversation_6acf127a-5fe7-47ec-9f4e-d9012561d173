<template>
  <div v-if="isShow" class="analyse-wrap">
    <!-- <Header :header-info="headerInfo" @closePanel="closePanel" /> -->
    <div class="tab-container">
      <div class="tool-bottom">
        <div class="tool-btn" @click="startAnalyse">
          <svg-icon icon-class="start_analyse" />
          <span>绘制分析点</span>
        </div>
        <div class="tool-btn" @click="clearResult">
          <svg-icon icon-class="clear" />
          <span>清除</span>
        </div>
      </div>
    </div>

    <div v-if="isShowAction" ref="modelTool" class="model-action">
      <div class="action-title">
        <h5>属性编辑</h5>
      </div>
      <div class="action-panel">
        <div class="action-item clearfix">
          <h3 class="action-header">观察点信息</h3>
          <div>
            <label for="" class="action-label">经度: </label>
            <el-input
              v-model="coordinates.longitude"
              placeholder="填入经度"
              :disabled="true"
              size="small"
            />
            <!--            <el-input-number key="longitude" v-model="coordinates.longitude" size="medium" :precision="6" :step="0.0001" @change="handleAction('long')" />-->
          </div>
          <div>
            <label for="" class="action-label">维度: </label>
            <el-input
              v-model="coordinates.latitude"
              placeholder="填入维度"
              size="small"
              :disabled="true"
            />
            <!--            <el-input-number key="latitude" v-model="coordinates.latitude" size="medium" :precision="6" :step="0.0001" @change="handleAction('lat')" />-->
          </div>
          <div>
            <label for="" class="action-label">高度: </label>
            <el-input
              v-model="coordinates.height"
              placeholder="填入高度"
              size="small"
              :disabled="true"
            />
            <!--            <el-input-number key="height" v-model="coordinates.height" size="medium" :precision="6" :step="0.0001" @change="handleAction('height')" />-->
          </div>
        </div>
        <div class="action-item clearfix">
          <h3 class="action-header">参数设置</h3>
          <div>
            <label for="" class="action-label">观察半径</label>
            <el-slider
              v-model="viewDomeModel.distance"
              class="action-slider"
              :min="0"
              :max="1000"
              :step="10"
              @change="handleAction('distance')"
            />
          </div>
          <div>
            <label for="" class="action-label">起始角度</label>
            <el-slider
              v-model="viewDomeModel.startAngle"
              class="action-slider"
              :min="0"
              :max="360"
              :step="1"
              @change="handleAction('startAngle')"
            />
          </div>
          <div>
            <label for="" class="action-label">终止角度</label>
            <el-slider
              v-model="viewDomeModel.endAngle"
              class="action-slider"
              :min="0"
              :max="360"
              :step="1"
              @change="handleAction('endAngle')"
            />
          </div>
        </div>
        <div class="action-item clearfix">
          <label for="" class="action-label">显示类型</label>
          <el-select v-model="viewDomeModel.domeType" placeholder="请选择显示类型" @change="handleAction('domeType')">
            <el-option
              v-for="item in viewDomeModel.domeTypes"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="action-item action-move clearfix">
          <label for="" class="action-label">可见区域颜色</label>
          <el-color-picker
            v-model="viewDomeModel.visibleColor"
            class="color-picker"
            @change="handleAction('visible')"
          />
        </div>
        <div class="action-item action-move clearfix">
          <label for="" class="action-label">不可见区域颜色</label>
          <el-color-picker
            v-model="viewDomeModel.hiddenColor"
            class="color-picker"
            @change="handleAction('invisible')"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="OpennessAnalysis" text="开敞度分析">
import useMapViewStore from "@/store/modules/map/mapView.js"
import { setCursor, setLayerSelectStatus } from "@/utils/Cesium/CesiumTool.js"

defineProps({
  isShow: {
    type: Boolean,
    default: true
  },
  headerInfo: {
    type: Object
  }
})


const viewDomeModel = ref({
  visibleColor: "#00B7EF",
  hiddenColor: "#E36C09",
  distance: 100,
  startAngle: 0,
  endAngle: 360,
  isClosed: false, // 是否封口
  domeType: 2, // 开敞度类型,分为可视部分、不可视部分, 全部显示
  domeTypes: [
    {
      label: "全部显示",
      value: 2
    },
    {
      label: "显示可视区域",
      value: 0
    },
    {
      label: "显示隐藏区域",
      value: 1
    }
  ]
})

const coordinates = ref({
  longitude: 0,
  latitude: 0,
  height: 0
})
const longitude = ref(0)
const latitude = ref( 0)
const viewDomeArray = ref([])
const isShowAction = ref(false)



const viewer3d = computed(()=>useMapViewStore().viewer3d)


const emits = defineEmits(['closePanel'])
const closePanel = ()=>{
  emits("closePanel")
}


/**
 * 分析初始化参数
 */
const initViewDome = ()=>{
  const visibleColor = Cesium.Color.fromCssColorString(viewDomeModel.value.visibleColor);
  const hiddenColor = Cesium.Color.fromCssColorString(viewDomeModel.value.hiddenColor);

  // 开敞度分析对象
  const viewDome = new Cesium.ViewDome(viewer3d.value.scene);
  // 可视距离
  viewDome.distance = Number(viewDomeModel.value.distance);
  // 开敞度类型：可视部分、不可视部分、完全可视
  // viewDome.domeType = SuperMap3D.ViewDomeType.ALLDOME
  viewDome.domeType = 2

  viewDome.visibleAreaColor = Cesium.Color.fromAlpha(visibleColor, 0.5); //可视部颜色
  viewDome.hiddenAreaColor = Cesium.Color.fromAlpha(hiddenColor, 0.5); //隐藏部分颜色

  viewDome.startAngle = Number(viewDomeModel.value.startAngle); //起始角度
  viewDome.endAngle = Number(viewDomeModel.value.endAngle); //终止角度

  viewDome.isClosed = viewDomeModel.value.isClosed
  viewDomeArray.value.push(viewDome)
}

/**
 * 开始分析
 */
const startAnalyse = ()=> {
  const scene = viewer3d.value.scene;
  setLayerSelectStatus(viewer3d.value,false)

  initViewDome()
  // 事件处理器
  const polygonHandler = new Cesium.ScreenSpaceEventHandler(scene.canvas);
  polygonHandler.setInputAction(() => {
    setCursor(viewer3d.value,'crosshair')
  },Cesium.ScreenSpaceEventType.MOUSE_MOVE)
  // 获取点击位置笛卡尔坐标
  polygonHandler.setInputAction((e) => {
    setCursor(viewer3d.value,'pointer')
    const position = scene.pickPosition(e.position)
    // 将笛卡尔坐标转换为经纬度坐标
    const positions = []
    const cartographic = Cesium.Cartographic.fromCartesian(position)
    longitude.value = Cesium.Math.toDegrees(cartographic.longitude)
    latitude.value = Cesium.Math.toDegrees(cartographic.latitude)
    let height = cartographic.height + 0.5
    if (height < 0){
      height = 0
    }
    const coords = { longitude: longitude.value,latitude: latitude.value,height: height }
    coordinates.value = coords
    addPoint(coords)

    if (positions.indexOf(longitude.value) === -1 && positions.indexOf(latitude) === -1){
      positions.push(longitude.value)
      positions.push(latitude.value)
      positions.push(height)
    }
    opennessAnalyse(positions)

    polygonHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK)
    polygonHandler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE)
    isShowAction.value = true
  },Cesium.ScreenSpaceEventType.LEFT_CLICK);
}

/**
 * 执行开敞度分析
 * @param positions
 */
const opennessAnalyse = (positions)=> {
  const length = viewDomeArray.value.length;
  viewDomeArray.value[length - 1].viewPosition = positions;
  // 执行开敞度分析
  viewDomeArray.value[length - 1].build();
}


/**
 * 添加观察点
 * @param coords
 */
const addPoint = (coords)=>{
  // console.log(coords);
  if (coords.longitude == undefined || coords.latitude == undefined || coords.height == undefined) {
    return
  }
  // 首先移除之前添加的点
  viewer3d.value.entities.removeAll();
  // 在位置添加对应点
  viewer3d.value.entities.add(new Cesium.Entity({
    point: new Cesium.PointGraphics({
      color: new Cesium.Color(1, 0, 0),
      pixelSize: 6,
      outlineColor: new Cesium.Color(0, 1, 1),
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
    }),
    position: Cesium.Cartesian3.fromDegrees(coords.longitude,coords.latitude,coords.height)
  }));
}

// 参数change事件
const handleAction = (type,value)=>{
  const domeLength = viewDomeArray.value.length
  if (domeLength == 0) {
    return
  }
  switch (type){
    case "long":
      break
    case "lat":
      break
    case "height":
      break
    case "distance":
      if (viewDomeModel.value.distance === 0) {
        return
      }
      viewDomeArray.value[domeLength - 1].distance = viewDomeModel.value.distance
      break
    case "startAngle":
      viewDomeArray.value[domeLength - 1].startAngle = viewDomeModel.value.startAngle
      break
    case "endAngle":
      viewDomeArray.value[domeLength - 1].endAngle = viewDomeModel.value.endAngle
      break
    case "domeType":
      viewDomeArray.value[domeLength - 1].domeType = viewDomeModel.value.domeType
      break
    case "visible":
      {
        const visibleColor = Cesium.Color.fromCssColorString(viewDomeModel.value.visibleColor)
        viewDomeArray.value[domeLength - 1].visibleAreaColor = Cesium.Color.fromAlpha(visibleColor, 0.5); //可视部颜色
      }
      break
    case "invisible":
      {
        const hiddenColor = Cesium.Color.fromCssColorString(viewDomeModel.value.hiddenColor)
        viewDomeArray.value[domeLength - 1].hiddenAreaColor = Cesium.Color.fromAlpha(hiddenColor, 0.5); //可视部颜色
      }
      break
  }
}

const clearResult = ()=> {
  viewer3d.value.entities.removeAll();
  isShowAction.value = false
  // 释放对象
  if (viewDomeArray.value.length){
    for (let viewDome of viewDomeArray.value){
      viewDome.clear()
      viewDome.destroy();
      viewDome = null
    }
    viewDomeArray.value.length = 0
  }
}

onBeforeUnmount(()=>{
  clearResult();
  setLayerSelectStatus(viewer3d.value,true)
})


</script>

<style scoped lang="scss">
.analyse-wrap {
  color: #fff;
  background-image: url("@/assets/images/map/tool.png");
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-size: cover;
  box-shadow: 0 0 8px 0 #057595;
}
.tool-tabs {
  display: flex;
  justify-items: center;
  justify-content: space-between;
  background-color: #4f7287;
  span {
    display: inline-block;
    width: 50%;
    padding: 10px 5px;
    text-align: center;
    color: #fff;
    &:hover {
      cursor: pointer;
      background-color: #0f7dff;
      filter: brightness(110%);
      color: #fff;
      transition: background-color 0.25s;
    }
  }
}
.tab-active {
  color: #fff;
  background: linear-gradient(to bottom right, #00baff, #0f7dff);
}
.tool-tab-content {
  padding-top: 20px;
}
:deep(.el-form-item__label) {
  width: 60px !important;
  color: #b5b5b5;
}
.tab-container {
  display: flex;
  padding: 10px;
  flex-direction: column;
  column-count: 2;
}
.height-value {
  margin: 10px;
  label {
    line-height: 40px;
  }
}
.input-value {
  float: right;
  width: 80%;
}
.tool-bottom {
  display: flex;
  justify-content: space-between;
  justify-items: center;
}
.tool-btn {
  margin-top: 20px;
  padding: 10px;
  width: 48%;
  background-color: #0f7dff;
  border-color: #fff;
  text-align: center;
  border-radius: 4px;
  color: #ffffff;
  &:hover {
    cursor: pointer;
    color: #fff;
    filter: brightness(110%) opacity(100%);
    transition: all 0.5s ease-in;
    background: linear-gradient(to bottom right, #00baff, #0f7dff);
  }
  svg {
    margin-right: 10px;
  }
}
.label {
  display: inline;
  padding: 0.01em 3.5em 0.01em 3.5em;
  font-size: 150%;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25em;
  line-height: 1.1;
}

.model-action {
  position: absolute;
  top: 50px;
  left: -270px;
  width: 80%;
  background-color: #001d3bdb;
  border-radius: 5px;
  z-index: 999;
}
.action-title {
  padding: 15px;
  background-image: url("@/assets/images/map/queryResultTitle2.png");
  background-repeat: no-repeat;
  font-size: 14px;
}
.action-panel {
  padding: 10px;
}
.action-item {
  line-height: 40px;
}
.action-slider {
  float: right;
  width: 60%;
}
.action-move {
  margin: 5px 0;
}
.color-picker {
  float: right;
  margin-right: 44px;
}
.action-header{
  padding: 5px;
  margin: 10px 0;
  background-color: #113c56;
}
:deep(.el-select){
  width: 75%;
  float: right;
}
:deep(.el-input) {
  width: 80%;
  float: right;
}
</style>
