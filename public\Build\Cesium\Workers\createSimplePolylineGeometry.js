define(["./when-b60132fc","./Rectangle-dee65d21","./ArcType-29cf2197","./buildModuleUrl-9085faaa","./Cartographic-3309dd0d","./Color-2a095a27","./ComponentDatatype-c140a87d","./Check-7b2a090c","./GeometryAttribute-c65394ac","./GeometryAttributes-252e9929","./IndexDatatype-8a5eead4","./Math-119be1a3","./PolylinePipeline-a5200218","./FeatureDetection-806b12f0","./Event-16a2dfbf","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Cartesian2-db21342c","./Cartesian4-3ca25aab","./EllipsoidGeodesic-139a7db9","./EllipsoidRhumbLine-30b5229b","./IntersectionTests-0d6905a3","./Plane-a3d8b3d2"],(function(e,o,t,r,a,l,i,n,s,p,d,c,y,u,f,h,C,g,T,m,b,v,P){"use strict";function _(e,o,t,r,a,i,n){var s,p=y.PolylinePipeline.numberOfPoints(e,o,a),d=t.red,c=t.green,u=t.blue,f=t.alpha,h=r.red,C=r.green,g=r.blue,T=r.alpha;if(l.Color.equals(t,r)){for(s=0;s<p;s++)i[n++]=l.Color.floatToByte(d),i[n++]=l.Color.floatToByte(c),i[n++]=l.Color.floatToByte(u),i[n++]=l.Color.floatToByte(f);return n}var m=(h-d)/p,b=(C-c)/p,v=(g-u)/p,P=(T-f)/p,_=n;for(s=0;s<p;s++)i[_++]=l.Color.floatToByte(d+s*m),i[_++]=l.Color.floatToByte(c+s*b),i[_++]=l.Color.floatToByte(u+s*v),i[_++]=l.Color.floatToByte(f+s*P);return _}function B(r){var i=(r=e.defaultValue(r,e.defaultValue.EMPTY_OBJECT)).positions,n=r.colors,s=e.defaultValue(r.colorsPerVertex,!1);this._positions=i,this._colors=n,this._colorsPerVertex=s,this._arcType=e.defaultValue(r.arcType,t.ArcType.GEODESIC),this._granularity=e.defaultValue(r.granularity,c.CesiumMath.RADIANS_PER_DEGREE),this._ellipsoid=e.defaultValue(r.ellipsoid,o.Ellipsoid.WGS84),this._workerName="createSimplePolylineGeometry";var p=1+i.length*a.Cartesian3.packedLength;p+=e.defined(n)?1+n.length*l.Color.packedLength:1,this.packedLength=p+o.Ellipsoid.packedLength+3}B.pack=function(t,r,i){var n;i=e.defaultValue(i,0);var s=t._positions,p=s.length;for(r[i++]=p,n=0;n<p;++n,i+=a.Cartesian3.packedLength)a.Cartesian3.pack(s[n],r,i);var d=t._colors;for(p=e.defined(d)?d.length:0,r[i++]=p,n=0;n<p;++n,i+=l.Color.packedLength)l.Color.pack(d[n],r,i);return o.Ellipsoid.pack(t._ellipsoid,r,i),i+=o.Ellipsoid.packedLength,r[i++]=t._colorsPerVertex?1:0,r[i++]=t._arcType,r[i]=t._granularity,r},B.unpack=function(t,r,i){var n;r=e.defaultValue(r,0);var s=t[r++],p=new Array(s);for(n=0;n<s;++n,r+=a.Cartesian3.packedLength)p[n]=a.Cartesian3.unpack(t,r);var d=(s=t[r++])>0?new Array(s):void 0;for(n=0;n<s;++n,r+=l.Color.packedLength)d[n]=l.Color.unpack(t,r);var c=o.Ellipsoid.unpack(t,r);r+=o.Ellipsoid.packedLength;var y=1===t[r++],u=t[r++],f=t[r];return e.defined(i)?(i._positions=p,i._colors=d,i._ellipsoid=c,i._colorsPerVertex=y,i._arcType=u,i._granularity=f,i):new B({positions:p,colors:d,ellipsoid:c,colorsPerVertex:y,arcType:u,granularity:f})};var E=new Array(2),A=new Array(2),k={positions:E,height:A,ellipsoid:void 0,minDistance:void 0,granularity:void 0};return B.createGeometry=function(o){var n,f,h,C,g,T=o._positions,m=o._colors,b=o._colorsPerVertex,v=o._arcType,P=o._granularity,B=o._ellipsoid,D=c.CesiumMath.chordLength(P,B.maximumRadius),G=e.defined(m)&&!b,w=T.length,L=0;if(v===t.ArcType.GEODESIC||v===t.ArcType.RHUMB){var V,x,R;v===t.ArcType.GEODESIC?(V=c.CesiumMath.chordLength(P,B.maximumRadius),x=y.PolylinePipeline.numberOfPoints,R=y.PolylinePipeline.generateArc):(V=P,x=y.PolylinePipeline.numberOfPointsRhumbLine,R=y.PolylinePipeline.generateRhumbArc);var S=y.PolylinePipeline.extractHeights(T,B),I=k;if(v===t.ArcType.GEODESIC?I.minDistance=D:I.granularity=P,I.ellipsoid=B,G){var O=0;for(n=0;n<w-1;n++)O+=x(T[n],T[n+1],V)+1;f=new Float64Array(3*O),C=new Uint8Array(4*O),I.positions=E,I.height=A;var M=0;for(n=0;n<w-1;++n){E[0]=T[n],E[1]=T[n+1],A[0]=S[n],A[1]=S[n+1];var U=R(I);if(e.defined(m)){var N=U.length/3;g=m[n];for(var F=0;F<N;++F)C[M++]=l.Color.floatToByte(g.red),C[M++]=l.Color.floatToByte(g.green),C[M++]=l.Color.floatToByte(g.blue),C[M++]=l.Color.floatToByte(g.alpha)}f.set(U,L),L+=U.length}}else if(I.positions=T,I.height=S,f=new Float64Array(R(I)),e.defined(m)){for(C=new Uint8Array(f.length/3*4),n=0;n<w-1;++n){L=_(T[n],T[n+1],m[n],m[n+1],D,C,L)}var H=m[w-1];C[L++]=l.Color.floatToByte(H.red),C[L++]=l.Color.floatToByte(H.green),C[L++]=l.Color.floatToByte(H.blue),C[L++]=l.Color.floatToByte(H.alpha)}}else{h=G?2*w-2:w,f=new Float64Array(3*h),C=e.defined(m)?new Uint8Array(4*h):void 0;var W=0,Y=0;for(n=0;n<w;++n){var q=T[n];if(G&&n>0&&(a.Cartesian3.pack(q,f,W),W+=3,g=m[n-1],C[Y++]=l.Color.floatToByte(g.red),C[Y++]=l.Color.floatToByte(g.green),C[Y++]=l.Color.floatToByte(g.blue),C[Y++]=l.Color.floatToByte(g.alpha)),G&&n===w-1)break;a.Cartesian3.pack(q,f,W),W+=3,e.defined(m)&&(g=m[n],C[Y++]=l.Color.floatToByte(g.red),C[Y++]=l.Color.floatToByte(g.green),C[Y++]=l.Color.floatToByte(g.blue),C[Y++]=l.Color.floatToByte(g.alpha))}}var z=new p.GeometryAttributes;z.position=new s.GeometryAttribute({componentDatatype:i.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:f}),e.defined(m)&&(z.color=new s.GeometryAttribute({componentDatatype:i.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:4,values:C,normalize:!0}));var J=2*((h=f.length/3)-1),j=d.IndexDatatype.createTypedArray(h,J),K=0;for(n=0;n<h-1;++n)j[K++]=n,j[K++]=n+1;return new s.Geometry({attributes:z,indices:j,primitiveType:u.PrimitiveType.LINES,boundingSphere:r.BoundingSphere.fromPoints(T)})},function(t,r){return e.defined(r)&&(t=B.unpack(t,r)),t._ellipsoid=o.Ellipsoid.clone(t._ellipsoid),B.createGeometry(t)}}));
