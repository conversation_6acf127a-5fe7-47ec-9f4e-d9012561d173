import axios from 'axios'
import { addOnanalysis } from '@/api/gis/onanalysis'
import { getCommentByTableName, queryByLinkedId } from '@/api/gis/postgisInterface'
/**
 * 返回 GeoJSON 对象
 * @param query
 * @returns {Promise<AxiosResponse<any>>}
 */
async function getGeoJson(query){
  const url = process.env.VUE_APP_EXTENSION_URL + 'getGeomeryGeoJson'
  const paramData = new URLSearchParams()
  paramData.append('tableName', query.tableName)
  paramData.append('recordId', query.id)

  const response = await axios.post(url,paramData,{
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
  const geometry = await response.data
  let feature = null
  if (response.status === 200){
    feature = {
      'type': 'FeatureCollection',
      'features': [
        {
          'type': 'Feature',
          'geometry': geometry
        }
      ]
    }
  }
  return feature
}

/**
 * 空间分析拓展服务接口
 * @param geometry：空间分析 GeoJSON 几何对象
 * @param overLayers：空间分析配置图层数组
 * @param level：地类统计级别，包括一级地类、二级地类
 * @param prjName：项目名
 * @param dkName：地块名
 * @param analyseType：分析类型
 * @param statisticLevel：空间分析地类统计级别
 * @returns {Promise<any>}
 */
async function getSpatialResult(analyseObj){
  console.log("几何对象：",analyseObj)
  const crs = { "type": "name","properties": { "name": "EPSG:4490" } }
  analyseObj.json.crs = crs

  const analyseParam = {
    json: JSON.stringify(analyseObj.json),
    overLayerStr: JSON.stringify(analyseObj.overLayerStr),
    level: analyseObj.level || 2,
    prjName: analyseObj.prjName,
    dkName: analyseObj.dkName,
    type: analyseObj.type,
    landPriceObj: JSON.stringify(analyseObj.landPriceObj)
  }
  const response = await addOnanalysis(analyseParam)
  if (response.code === 200){
    return response.data.classifyArr
  } else {
    console.error("空间分析出错！" + response)
  }
}
/**
 * 构造详情数据
 * @param tableSchema
 * @param detailData
 * @returns {[]}
 */
function getDetailData(tableSchema,detailData){
  const tableData = [];
  const schemas = Object.entries(tableSchema)
  // 每一行放置四个属性
  const loop = (i) => {
    if (i <= schemas.length - 1){
      const obj = {
        name2: '',
        value2: ''
      }
      const [key,value] = schemas[i]
      obj.name1 = value
      obj.value1 = detailData[key]
      if (schemas[i + 1]){
        const [key2,value2] = schemas[i + 1]
        obj.name2 = value2
        obj.value2 = detailData[key2]
      }
      tableData.push(obj)
      loop(i + 2)
    }
  }
  loop(0)
  return tableData;
}

/**
 * 获取GeoJSON数据
 * @param query
 * @returns {Promise<{features: *, type: string}>}
 */
async function getJSON(query){
  const result = await queryByLinkedId(query)
  console.log("查询数据：",result)
  if (!result.data.list){
    console.error("未查询到空间数据！")
  }
  const features = result.data.list
  // 构造 GeoJSON 数据
  const geoJSON = {
    type: "FeatureCollection",
    features
  }
  return geoJSON
}

/**
 * 获取表格字段数据
 * @spatialTableName：空间表名
 */
async function getCommentList(spatialTableName){
  // console.log("列信息：",spatialTableName)
  const commentList = []
  const result = await getCommentByTableName(spatialTableName)
  if (result.code !== 200){
    console.error("字段信息请求出错，请检查")
    return
  }
  result.data.forEach((item,index) => {
    if (item["column_comment"] === "" || item["column_name"] === 'smid' ||
      item["column_name"] === "import_oid"){
      return
    }
    const obj = {}
    obj.name = item["column_name"]
    obj.comment = item["column_comment"]
    commentList.push(obj)
  })
  return commentList
}

export {
  getSpatialResult,
  getDetailData,
  getJSON,
  getCommentList
}
