/**
 * @name: CesiumTool.js
 * @description: SuperMap3D 工具箱
 * @author: zyc
 * @time: 2024-03-11
 **/

/**
 * 飞到指定点
 * @param point：点坐标对象，包括经度、维度以及高度
 * @param viewer3d
 * 呈贡：{ lng: 102.381686,lat: 24.930287,height: 30000
 * 晋宁：{ lng: 102.616358,lat: 24.661059}
 * 磨憨：[101.709214,21.209912]
 */
export function flyToPoint(
  viewer3d,
  point = { lng: 101.715322, lat: 21.19827, height: 30000 }
) {
  viewer3d.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(
      point.lng,
      point.lat,
      point.height
    ),
    // destination: SuperMap3D.Rectangle.fromDegrees(
    //   102.631686,24.950287,
    //   102.733734,25.004434),
    orientation: {
      // 弧度单位
      heading: Cesium.Math.toRadians(0),
      // pitch: Cesium.Math.toRadians(-65),
      pitch: Cesium.Math.toRadians(-90),
      roll: 0.0
    }
  })
}

/**
 * 飞到s3m图层中心点，二次动画调整模型显示效果
 * @param layer：s3mLayer对象
 * @param height：高度
 * @param heading：方向角
 * @param pitch：俯仰角
 * @param roll：倾斜角
 */
export function flyToS3MLayerCenter(
  viewer,
  layer,
  height = 2000,
  heading = 0,
  pitch = -90,
  roll = 0
) {
  const center = Cesium.Rectangle.center(layer.layerBounds)

  center.height = height
  const targetCenter = Cesium.Cartographic.toCartesian(center)
  center.latitude = center.latitude - 0.000059999999974589
  const pt = Cesium.Cartographic.toCartesian(center)

  viewer.camera.setView({
    destination: targetCenter
  })
  viewer.camera.flyTo({
    destination: targetCenter,
    complete: () => {
      setTimeout(() => {
        viewer.camera.flyTo({
          destination: pt,
          orientation: {
            heading: Cesium.Math.toRadians(heading),
            pitch: Cesium.Math.toRadians(0),
            roll: roll
          }
          // duration: 2,
          // easingFunction: SuperMap3D.EasingFunction.BACK_IN
          // easingFunction: SuperMap3D.EasingFunction.CIRCULAR_IN
          // easingFunction: SuperMap3D.EasingFunction.ELASTIC_IN
          // easingFunction: SuperMap3D.EasingFunction.QUARTIC_IN
          // easingFunction: SuperMap3D.EasingFunction.SINUSOIDAL_IN
        })
      }, 1.5)
    }
  })
}

/**
 * 飞到s3m图层中心点
 * @param layer：s3mLayer对象
 * @param height：高度
 * @param heading：方向角
 * @param pitch：俯仰角
 * @param roll：倾斜角
 */
export function flyToS3MLayerBounds(
  viewer,
  layer,
  height = 2500,
  heading = 0,
  pitch = -90,
  roll = 0
) {
  const center = Cesium.Rectangle.center(layer.layerBounds)
  center.height = height
  const pt = Cesium.Cartographic.toCartesian(center)
  viewer.camera.flyTo({
    destination: pt,
    orientation: {
      heading: Cesium.Math.toRadians(heading),
      pitch: Cesium.Math.toRadians(pitch),
      roll: roll
    }
    // duration: 2.5,
    // easingFunction: SuperMap3D.EasingFunction.BACK_IN
    // easingFunction: SuperMap3D.EasingFunction.CIRCULAR_IN
    // easingFunction: SuperMap3D.EasingFunction.ELASTIC_IN
    // easingFunction: SuperMap3D.EasingFunction.QUARTIC_IN
    // easingFunction: SuperMap3D.EasingFunction.SINUSOIDAL_IN
  })
}

/**
 * 设置鼠标指针类型
 * @param cursor：鼠标指针类型
 * default(箭头)、help（箭头带问号）
 * wait（转圈）、crosshair（十字）
 * text（文本选择）、move（移动）
 * pointer（点击手势）、grab（抓取手势）
 */
export function setCursor(viewer3d, cursor) {
  const viewerEle = viewer3d.scene.canvas
  viewerEle.style.cursor = cursor
}

/**
 * 创建Viewer3d视图提示信息
 * @param parentEle：父元素，默认为【document.body】
 * @returns {Tooltip}
 */
export function createTooltip(parentEle) {
  const Tooltip = function (parentEle) {
    const div = document.createElement("DIV")
    div.className = "twipsy right"

    const arrow = document.createElement("DIV")
    arrow.className = "twipsy-arrow"
    div.appendChild(arrow)

    const title = document.createElement("DIV")
    title.className = "twipsy-inner"
    div.appendChild(title)

    this._div = div
    this._title = title
    this.message = ""

    // add to frame div and display coordinates
    parentEle.appendChild(div)
    div.onmousemove = (evt) => {
      this.showAt({ x: evt.clientX, y: evt.clientY }, this.message)
    }
  }

  Tooltip.prototype.setVisible = function (visible) {
    this._div.style.display = visible ? "block" : "none"
  }

  Tooltip.prototype.showAt = function (position, message) {
    if (position && message) {
      this.setVisible(true)
      this._title.innerHTML = message
      this._div.style.left = position.x + 10 + "px"
      this._div.style.top = position.y - this._div.clientHeight / 2 + "px"
      this.message = message
    }
  }

  return new Tooltip(parentEle)
}

/**
 * 开启或关闭场景图层选择，如点击事件
 * @param viewer3d：3d视图对象
 * @param isEnable：true启用图层选择，false禁用
 */
export function setLayerSelectStatus(viewer3d, isEnable) {
  // 获取场景图层列表
  const layers = viewer3d.scene.layers.layerQueue
  layers.forEach((layer) => {
    layer.selectEnabled = isEnable
  })
}

/**
 * 销毁场景事件
 * @param handlerEvt：事件处理器
 */
export function destroyEvtHandler(handlerEvt) {
  handlerEvt && handlerEvt.destroy()
}

/**
 * 设置场景效果
 * @param viewer3d：3d视图对象
 */
export function setSceneEffect(viewer3d) {
  const scene = viewer3d.scene
  // 设置二维场景
  // scene.mode = SuperMap3D.SceneMode.SCENE2D
  // 场景中的阴影贴图。启用时，模型、图元和地球可以投射和接收阴影。默认情况下，阴影贴图的光源是太阳。
  scene.shadowMap.darkness = 1.275
  // 开启大气层
  scene.skyAtmosphere.show = true
  // scene.skyAtmosphere.brightnessShift = 0.25; // 大气光的强度
  // scene.skyAtmosphere.saturationShift = 0.25; // 大气光的饱和度
  scene.skyAtmosphere.brightnessShift = 0.5 // 大气光的强度
  scene.skyAtmosphere.saturationShift = 0.5 // 大气光的饱和度
  // 显示地球
  scene.globe.show = true
  // 添加太阳光
  scene.sun = new Cesium.Sun()
  scene.sun.show = true
  // 默认值为 1.0，增加或减少以调整亮度
  scene.sun.intensity = 2.0
  // scene.sun.brightness = 0.5
  scene.sun.brightness = 1.5
  // 场景背景颜色
  scene.backgroundColor = new Cesium.Color(0.0, 0.0, 0.0, 0.0)
  // 开启高动态分辨率
  scene.hdrEnabled = true
  // 大气光色，环境光强度
  scene.lightSource.ambientLightColor = new Cesium.Color(0.98, 0.98, 0.99, 1)
  // 场景阴影颜色
  scene.shadowMap.color = Cesium.Color.AQUA
  // 设置环境光的强度
  scene.lightSource.ambientLightColor = new Cesium.Color(0.98, 0.98, 0.99, 1)

  // 开启颜色校正
  const correction = scene.colorCorrection
  correction.show = true

  // 以下场景设置暂时不能开启，因为开启之后雨雪场景失效。
  // 调整环境光强度，会形成雾蒙蒙的感觉
  // scene.globe.enableLighting = true; // 启用光照
  // scene.globe.lightingFadeOutDistance = 10; // 光照衰减距离
  // scene.globe.lightingFadeInDistance = 10; // 光照渐入距离
  // scene.globe.baseColor = Cesium.Color.WHITE; // 基础颜色

  // 启用后期处理
  // viewer3d.postProcessStages.fxaa.enabled = true; // 启用抗锯齿
  // viewer3d.postProcessStages.bloom.enabled = true; // 启用泛光效果

  // 调整亮度
  // viewer3d.postProcessStages.brightness = new Cesium.PostProcessStage({
  //   fragmentShader: `
  //       uniform sampler2D colorTexture;
  //       uniform float brightness;
  //       varying vec2 v_textureCoordinates;
  //       void main() {
  //           vec4 color = texture2D(colorTexture, v_textureCoordinates);
  //           gl_FragColor = vec4(color.rgb * brightness, color.a);
  //       }
  //   `,
  //   uniforms: {
  //     brightness: 1.5 // 调整亮度值
  //   }
  // });
}

/**
 * 销毁视图对象
 * @param viewer3d：3d视图对象
 */
export function destroyViewer(viewer3d) {
  const isDestroyed = viewer3d.isDestroyed()
  if (!isDestroyed) {
    viewer3d.destroy()
    viewer3d = null
  }
}

/**
 * 图层加载进度条：目前不适用
 * @param viewer3d
 */
export function showLayerLoadProgress(viewer3d) {
  let tileLoadedTotal = 0
  let tileTotal = 0
  viewer3d.scene.globe.tileLoadProgressEvent.addEventListener((tileCount) => {
    console.log("加载进度：", tileCount)
    tileTotal++
    if (tileCount == 0) {
      tileLoadedTotal++
    }
    const loadProgress = ((tileLoadedTotal / tileLoadedTotal) * 100).toFixed(2)
    console.log(`加载进度: ${loadProgress}%`)
    // 处理加载进度
    if (tileCount === 0 && tileLoadedTotal === tileTotal) {
      console.log("加载完成！！！：")
    }
  })
}

/**
 * 笛卡尔直角坐标转经纬度坐标
 * @param cartesian：笛卡尔直角坐标
 */
export function cartesian2Degrees(cartesian) {
  const cartographic = Cesium.Cartographic.fromCartesian(cartesian)
  const longitude = Cesium.Math.toDegrees(cartographic.longitude)
  const latitude = Cesium.Math.toDegrees(cartographic.latitude)
  const height = cartographic.height
  return { longitude, latitude, height }
}
