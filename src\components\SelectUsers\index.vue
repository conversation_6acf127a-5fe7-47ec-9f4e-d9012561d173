<template>
  <div>
    <el-button type="primary" @click="handleClick">
      {{ text }}
    </el-button>
    <el-dialog v-model="dialogVisible" :title="text" width="800">
      <div class="flx-justify-space-evenly">
        <!-- 左侧树形结构 -->
        <div class="Box">
          <el-tree
            ref="treeRef"
            :data="data"
            show-checkbox
            node-key="id"
            :default-expanded-keys="['100']"
            highlight-current
            :props="defaultProps"
            @check-change="handleChoose"
          />
        </div>

        <!-- 右侧选中叶子节点展示 -->
        <div class="Box">
          <el-tag
            v-for="node in selectedLeafNodes"
            :key="node.id"
            class="mx-1 mr5"
            closable
            @close="removeNode(node)"
          >
            {{ node.name }}
          </el-tag>
        </div>
      </div>

      <div class="mt15" style="width: 100%; text-align: right">
        <el-button type="info" @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="emitData">确定选中</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { nextTick, onMounted, ref } from "vue";
import { getDeptUserTree } from "@/api/system/user.js";
const props = defineProps({
  /* button文字 */
  text: {
    type: String,
  },
  userArr: {
    // 回传选择
    type: Array,
  },
});

const dialogVisible = ref(false);
const defaultProps = {
  children: "children",
  label: "name",
};

// 处理点击
const handleClick = () => {
  dialogVisible.value = true;
  //处理回显选择
  nextTick(() => {
    const tree = treeRef.value;
    if (props.userArr) {
      selectedLeafNodes.value = props.userArr;
      props.userArr.forEach((item) => {
        tree.setChecked(item.id, true);
      });
    }
  });
};

const data = ref([]);
//获取数据
const getDeptUserTreeData = () => {
  getDeptUserTree().then((res) => {
    data.value = res.data;
  });
};

// 存储已选择的叶子节点
const selectedLeafNodes = ref([]);

// 处理左侧树选中事件
const handleChoose = () => {
  const tree = treeRef.value;
  const selectedNodes = tree.getCheckedNodes();
  selectedLeafNodes.value = selectedNodes.filter(
    (n) => !n.children || n.children.length === 0
  );
};

// 移除右侧选中的叶子节点，并同步左侧树
const removeNode = (node) => {
  const tree = treeRef.value;
  tree.setChecked(node.id, false); // 取消左侧树节点选中
  selectedLeafNodes.value = selectedLeafNodes.value.filter(
    (n) => n.id !== node.id
  );
};

// emit选中数据
const emit = defineEmits(["saveData"]);
const emitData = () => {
  //   console.log("选中数据", selectedLeafNodes.value);
  emit("saveData", selectedLeafNodes.value);
  dialogVisible.value = false;
};

// 引用树组件
const treeRef = ref(null);

//
onMounted(() => {
  getDeptUserTreeData();
});
</script>

<style scoped lang="scss">
.Box {
  width: 270px;
  height: 400px;
  border: 1px solid #e7e4ed;
  overflow: auto;
  padding: 15px;
  border-radius: 6px;
}

.selected-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #e7e4ed;
  padding: 5px 15px;
  margin-bottom: 15px;
}
</style>