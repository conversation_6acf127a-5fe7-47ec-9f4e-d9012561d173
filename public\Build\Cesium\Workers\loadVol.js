define(["./createTaskProcessorWorker","./Cartographic-3309dd0d","./Cartesian4-3ca25aab","./pako_inflate-f73548c4","./Rectangle-dee65d21","./when-b60132fc","./Check-7b2a090c","./Math-119be1a3"],(function(e,t,a,l,n,r,i,h){"use strict";var o=Object.freeze({UNLOAD:0,LOADING:1,LOADED:2,VOLLOADED:3,PARSING:4,PARSED:5,TRANSFORMING:6,TRANSFORMED:7,LOST:8,LOADVOL:9,FAILED:10,VOLFAILED:11});function c(e){var a=new t.Cartesian3(1,255,65025);(a=t.Cartesian3.multiplyByScalar(a,e,a)).x=a.x-Math.floor(a.x),a.y=a.y-Math.floor(a.y),a.z=a.z-Math.floor(a.z);var l=new t.Cartesian3,n=new t.Cartesian3(1/255,1/255,0);return l.x=a.y*n.x,l.y=a.z*n.y,l.z=a.z*n.z,a=t.Cartesian3.subtract(a,l,a)}function d(e,t,l,n){for(var r=e._nLength*e._nLength*4,i=new Array(r),h=l-t,o=t,d=0;d<e.cellDepth;d++)for(var s=parseInt(d/e._nSideBlockCount)*e._nBlockLength,u=d%e._nSideBlockCount*e._nBlockLength,_=-1;_<e._nBlockLength-1;_++)for(var g=-1;g<e._nBlockLength-1;g++){var f=Math.min(g,e.validWidth-1),v=Math.min(_,e.validHeight-1);f=Math.max(0,f),v=Math.max(0,v);var y,L,p=0;if(e.ySpan>0&&(p=e.validHeight-e.cellHeight),f<e.cellWidth&&v>=p?(L=0,y=d*e.cellHeight*e.cellWidth+(e.validHeight-1-v)*e.cellWidth+f):f<e.cellWidth?(L=0==e.xSpan?1:2,y=d*e.cellHeight*e.cellWidth+(e.validHeight-e.cellHeight-1-v)*e.cellWidth+f):v>=p?(L=1,y=d*e.cellHeight*e.cellWidth+(e.validHeight-1-v)*e.cellWidth+f-e.cellWidth):(L=3,y=d*e.cellHeight*e.cellWidth+(e.validHeight-e.cellHeight-1-v)*e.cellWidth+f-e.cellWidth),!(y<0)){var x=n[L][y],w=new a.Cartesian4(0,0,0,0);if(-9999!=x){x<t||x>l?x=o:o=x;var B=c(x=(x-t)/h);w.x=B.x,w.y=B.y,w.z=B.z,w.w=1}var W=255*w.x,D=255*w.y,H=255*w.z,S=255*w.w;i[4*(y=(s+_)*e._nLength+u+g)]=parseInt(W),i[4*y+1]=parseInt(D),i[4*y+2]=parseInt(H),i[4*y+3]=parseInt(S)}}return new Uint8Array(i,0,r)}return e((function(e,t){for(var r=e.arrayWork,i=[],h=0;h<r.length;h++)if(r[h]._s3mLoadState===o.LOADED){var c=r[h]._arrayBuffer.slice(16,r[h]._arrayBuffer.byteLength),s=l.pako.inflate(c).buffer;r[h]._arrayBuffer=s,r[h]._s3mLoadState=o.TRANSFORMED,i.push({index:h,buffer:s})}var u=e.textureLevel,_=e._availableNode,g=e._time,f=e._layerBounds,v=e._minFiltration,y=e._maxFiltration;u.cellWidth=e._cellWidth,u.cellHeight=e._cellHeight,u.cellDepth=e._cellDepth;var L=function(e,t,l,r,i,h,o){for(var c=e.length,s=[],u=0;u<c;u++)s.push(e[u]._arrayBuffer.slice(36,e[u]._arrayBuffer.byteLength));var _=4*t.cellWidth*t.cellHeight*t.cellDepth,g=e[0],f=e[c-1],v=t.cellWidth,y=t.cellHeight;o.EndCol-1==f._x&&(v=o.Width-f._x*v),o.EndRow-1==f._y&&(y=o.Height-f._y*y),t.startCol=g._x,t.startRow=g._y,t.endCol=f._x,t.endRow=f._y,t.ySpan=f._y-g._y,t.xSpan=f._x-g._x,t.ySpan>0&&(y+=t.cellHeight),t.xSpan>0&&(v+=t.cellWidth),t._nSideBlockCount=Math.ceil(Math.sqrt(t.cellDepth)),t._nBlockLength=Math.max(y,v)+2,t._nLength=function(e){for(var t=1;t<e;)t<<=1;return t}(t._nBlockLength*t._nSideBlockCount-2),t.validWidth=v,t.validHeight=y,t.bounds=new n.Rectangle(g._dataBounds.west,f._dataBounds.south,f._dataBounds.east,g._dataBounds.north);var L=new n.Rectangle(r.west,r.south,r.east,r.north);t.multiResolutionTranslation=new a.Cartesian4((g._dataBounds.west-L.west)/L.width,(f._dataBounds.south-L.south)/L.height,L.width/t.bounds.width,L.height/t.bounds.height);for(var p=[],x=0;x<l;x++){var w=[];for(u=0;u<s.length;u++){var B=new Float32Array(s[u].slice(x*_,(x+1)*_));w.push(B)}p.push(d(t,i,h,w))}return p}(r,u,g,f,v,y,_);return{textureLevel:u,imageDatas:L,unzipTile:i}}))}));
