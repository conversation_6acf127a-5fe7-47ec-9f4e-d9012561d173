import request from "@/utils/request";

/**
 * 查询同步记录列表
 * @param query
 * @returns {*}
 */
export const listRecord = (query) => {
    return request({
        url: '/uav/sync_maintenance/list',
        method: 'get',
        params: query
    });
};

/**
 * 航飞照片数据同步初始化
 * @param query
 * @returns {*}
 */
export const syncAll = (query) => {
    return request({
        url: '/uav/sync_maintenance/sync/all',
        method: 'get',
        params: query
    });
};


/**
 * 处理需要人工运维数据同步问题
 * @param id
 */
export const putRecord = (id) => {
    return request({
        url: '/uav/sync_maintenance/handle/' + id,
        method: 'put'
    });
};


/**
 * 查询同步记录详细
 * @param id
 */
export const getRecord = (id) => {
    return request({
        url: '/uav/record/' + id,
        method: 'get'
    });
};

/**
 * 新增同步记录
 * @param data
 */
export const addRecord = (data) => {
    return request({
        url: '/uav/record',
        method: 'post',
        data: data
    });
};

/**
 * 修改同步记录
 * @param data
 */
export const updateRecord = (data) => {
    return request({
        url: '/uav/record',
        method: 'put',
        data: data
    });
};

/**
 * 删除同步记录
 * @param id
 */
export const delRecord = (id) => {
    return request({
        url: '/uav/record/' + id,
        method: 'delete'
    });
};
