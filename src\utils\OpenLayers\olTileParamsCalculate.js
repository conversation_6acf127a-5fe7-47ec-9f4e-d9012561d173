/**
 * @name：olTileParamsCalculate.js
 * @description：地图切片参数计算
 * @author: zyc
 * @time: 2024-07-02
 **/

/**==================================================一些参数===================================================**/

/**
 * dpi = 96px（每英寸点数为96）
 * 1inch = 2.54cm = 0.0254m = 96px
 * resolution = metersPerPixel * scaleDenominator，【scaleDenominator】为比例尺分母
 * scaleDenominator = resolution * dpi / 0.0254
 * scale = 1 / scaleDenominator
 * scale = 1 / scaleDenominator = metersPerPixel / resolution
 */
const earthRadius = 6378137
const dpi = 96
const metersPerPixel = 0.0254 / 96
const metersPerDegree = Math.PI * 2 * earthRadius / 360
/**=============================================================================================================**/

/**
 * 计算地图切片分辨率
 * @returns {[]}
 */
export function getResolutions(EPSGCode,bounds){
  const resolutions = []
  const scales = getScalesDenominator(EPSGCode,bounds)
  for (let i = 0;i < scales.length;i++){
    const resolution = metersPerPixel * scales[i]
    resolutions.push(resolution)
  }
  resolutions.sort((a,b) => b - a)
  return resolutions
}

/**
 * 计算地图比例尺分母
 * @bounds：{
 *   "left": 3.451476854618145E7,
 *   "right": 3.467060197551855E7
 * }
 */
export function getScalesDenominator(EPSGCode,bounds){
  const metersPerPixel = getMeters(EPSGCode)
  // 分辨率：单位米，这里两张切片，256pixel，0级比例尺
  const resolution0 = Math.abs(bounds.left - bounds.right) / 512 * metersPerPixel;
  let denominator = resolution0 * 0.0254 / 96
  const denominators = [];
  for (let i = 0;i < 22;i++){
    if (i == 0){
      denominators[i] = denominator;
      continue;
    }
    denominator = denominator * 2;
    denominators[i] = denominator;
  }
  denominators.sort((a,b) => b - a)
  return denominators;
}

export function getMeters(EPSGCode){
  let metersPerPixelOrDegree = ""
  if (EPSGCode === 4490){
    metersPerPixelOrDegree = metersPerDegree
  } else if (EPSGCode === 4522){
    metersPerPixelOrDegree = metersPerPixel
  }
  return metersPerPixelOrDegree
}
