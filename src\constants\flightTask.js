// statusUtils.js
/**
 * 任务类型映射列表
 */
export const TASK_TYPE_LIST = [
    {
        value: 'immediate',
        label: '立即任务',
    },
    {
        value: 'timed',
        label: '单次定时任务',
    },
    {
        value: 'recurring',
        label: '重复任务',
    },
    {
        value: 'continuous',
        label: '连续任务',
    },
];

/**
 * 任务状态映射列表
 */
export const STATUS_LIST = [
    {
        value: 'waiting',
        label: '待开始',
    },
    {
        value: 'starting_failure',
        label: '启动失败',
    },
    {
        value: 'executing',
        label: '执行中',
    },
    {
        value: 'paused',
        label: '暂停',
    },
    {
        value: 'terminated',
        label: '终止',
    },
    {
        value: 'success',
        label: '成功',
    },
    {
        value: 'suspended',
        label: '挂起',
    },
    {
        value: 'timeout',
        label: '超时',
    },
];

/**
 * 飞行状态映射列表
 */
export const FLIGHT_STATUS_LIST = [
    {
        value: 0,
        label: '待机',
    },
    {
        value: 1,
        label: '起飞准备',
    },
    {
        value: 2,
        label: '起飞准备完毕',
    },
    {
        value: 3,
        label: '手动飞行',
    },
    {
        value: 4,
        label: '自动起飞',
    },
    {
        value: 5,
        label: '航线飞行',
    },
    {
        value: 6,
        label: '全景拍照',
    },
    {
        value: 7,
        label: '智能跟随',
    },
    {
        value: 8,
        label: 'ADS-B躲避',
    },
    {
        value: 9,
        label: '自动返航',
    },
    {
        value: 10,
        label: '自动降落',
    },
    {
        value: 11,
        label: '强制降落',
    },
    {
        value: 12,
        label: '三桨叶降落',
    },
    {
        value: 13,
        label: '升级中',
    },
    {
        value: 14,
        label: '未连接',
    },
    {
        value: 15,
        label: 'APAS',
    },
    {
        value: 16,
        label: '虚拟摇杆状态',
    },
    {
        value: 17,
        label: '指令飞行',
    },
    {
        value: 18,
        label: '空中RTK收敛模式',
    },
    {
        value: 19,
        label: '机场选址中',
    },
];

/**
 * 状态转换工具类
 */
export const StatusUtils = {
    /**
     * 通用保留两位小数
     */
    toFixed2: (value) => {
        if (value === undefined || value === null || isNaN(value)) {
            return 'N/A';
        }
        return Number(value).toFixed(2);
    },

    /**
     * 通用保留三位小数
     */
    toFixed3: (value) => {
        if (value === undefined || value === null || isNaN(value)) {
            return 'N/A';
        }
        return Number(value).toFixed(3);
    },

    /**
     * 秒数转小时数（保留2位小数）
     * @param {number} seconds 秒数
     * @returns {string} 格式化后的小时字符串
     */
    secondsToHours: (seconds) => (seconds / 3600).toFixed(2),
    /**
     * 飞行器状态码转中文 (数字代码版本)
     * @param {number|string} code 状态码
     * @returns {string} 中文状态说明
     */
    flightStateCodeToChinese: (code) => ({
        '0': '待机',
        '1': '起飞准备',
        '2': '起飞准备完毕',
        '3': '手动飞行',
        '4': '自动起飞',
        '5': '航线飞行',
        '6': '全景拍照',
        '7': '智能跟随',
        '8': 'ADS-B 躲避',
        '9': '自动返航',
        '10': '自动降落',
        '11': '强制降落',
        '12': '三桨叶降落',
        '13': '升级中',
        '14': '未连接',
        '15': 'APAS',
        '16': '虚拟摇杆状态',
        '17': '指令飞行',
        '18': '空中 RTK 收敛模式',
        '19': '机场选址中',
    }[String(code)] || '未知状态'),

    /**
     * 航飞照片查询类型转中文
     * @param {number|string} code 查询类型
     * @returns {string} 中文说明
     */
    photoQueryTypeToChinese: (code) => ({
        '1': '航飞照片查询',
        // 可以继续扩展更多类型
    }[String(code)] || '未知类型'),

    /**
     * 时间戳转日期格式
     * @param {number} timestamp 秒级时间戳
     * @returns {string} YYYY-MM-DD格式日期
     */
    timestampToDate: (timestamp) => {
        const date = new Date(timestamp * 1000);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    },

    /**
     * 飞行任务状态码转中文
     * @param {string} code 状态码
     * @returns {string} 中文状态说明
     */
    flightTaskStep: (code) => ({
        operation_preparation: '作业准备中',
        in_flight_operation: '飞行作业中',
        post_operation_state_recovery: '作业后状态恢复',
        custom_flight_area_updating: '自定义飞行区更新中',
        terrain_obstacle_updating: '地形障碍物更新中',
        mission_idle: '任务空闲',
        aircraft_abnormality: '飞行器异常',
        unknown_state: '未知状态',
    }[code] || '未知状态'),

    /**
     * 设备状态码转中文
     * @param {string} code 状态码
     * @returns {string} 中文状态说明
     */
    /**
     * 飞行器状态码转中文
     * @param {string} code 状态码
     * @returns {string} 中文状态说明
     */
    flightStateToChinese: (code) => ({
        standby: '待机',
        takeoff_preparation: '起飞准备',
        takeoff_preparation_completed: '起飞准备完毕',
        manual_flight: '手动飞行',
        automatic_takeoff: '自动起飞',
        wayline_flight: '航线飞行',
        panoramic_photography: '全景拍照',
        intelligent_tracking: '智能跟随',
        adsb_avoidance: 'ADS-B 躲避',
        auto_returning_to_home: '自动返航',
        automatic_landing: '自动降落',
        forced_landing: '强制降落',
        three_blade_landing: '三桨叶降落',
        upgrading: '升级中',
        not_connected: '未连接',
        apas: 'APAS',
        virtual_stick_state: '虚拟摇杆状态',
        live_flight_controls: '指令飞行',
        airborne_rtk_fixing_mode: '空中 RTK 收敛模式',
        dock_address_selecting: '机场选址中',
    }[code] || '未知状态'),

    /**
     * 充电状态转中文
     * @param {string} code 状态码
     * @returns {string} 中文状态说明
     */
    chargeState: (code) => ({
        idle: '空闲',
        charging: '充电中',
    }[code] || '未知状态'),

    /**
     * 降雨量状态转中文
     * @param {string} code 状态码
     * @returns {string} 中文状态说明
     */
    rainfallStatus: (code) => ({
        no_rain: '无雨',
        light_rain: '小雨',
        moderate_rain: '中雨',
        heavy_rain: '大雨',
    }[code] || '未知状态'),

    /**
     * 无人机在仓状态转中文
     * @param {string} code 状态码
     * @returns {string} 中文状态说明
     */
    droneInDockStatus: (code) => ({
        outside: '在仓外',
        inside: '在仓内',
    }[code] || '未知状态'),

    /**
     * RTK状态转中文
     * @param {string} code 状态码
     * @returns {string} 中文状态说明
     */
    rtkStatus: (code) => ({
        not_started: '未开始',
        fixing: '收敛中',
        fixing_successful: '收敛成功',
        fixing_failed: '收敛失败',
    }[code] || '未知状态'),

    /**
     * KB转MB（保留2位小数）
     * @param {number} kb KB数值
     * @returns {string} MB数值字符串
     */
    kbToMb: (kb) => {
        if (kb === undefined || kb === null || isNaN(kb)) {
            return 'N/A';
        }
        return (kb / 1024).toFixed(2);
    },
};

/**
 * 任务状态类型映射列表
 */
export const STATUS_TYPE_LIST = [
    {
        status: '成功',
        type: 'success',
    },
    {
        status: '执行中',
        type: 'primary',
    },
    {
        status: '终止',
        type: 'danger',
    },
    {
        status: '超时',
        type: 'danger',
    },
    {
        status: '启动失败',
        type: 'danger',
    },
    {
        status: '挂起',
        type: 'warning',
    },
    {
        status: '暂停',
        type: 'warning',
    },
    {
        status: '待开始',
        type: 'info',
    },
];


/**
 * 设备状态映射列表
 */
export const DEVICE_STATUS_LIST = [
    {
        value: 'idle',
        label: '空闲中',
    },
    {
        value: 'on_site_debugging',
        label: '现场调试',
    },
    {
        value: 'remote_debugging',
        label: '远程调试',
    },
    {
        value: 'firmware_upgrade_in_progress',
        label: '固件升级中',
    },
    {
        value: 'in_operation',
        label: '作业中',
    },
    {
        value: 'to_be_calibrated',
        label: '待标定',
    },
];
