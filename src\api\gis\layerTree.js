import request from '@/utils/request'

export function getTreeData(query) {
  return request({
    url: '/map/tree/treeData',
    method: 'get',
    params: query
  })
}

export function regionTreeList() {
  return request({
    url: 'system/region/list',
    method: 'get'
  })
}

export function getTreeById(id) {
  return request({
    url: '/map/tree/' + id,
    method: 'get'
  })
}

export function getRegionList(query) {
  return request({
    url: '/system/region/regionList',
    method: 'get',
    params: query
  })
}

// 异步查询行政区数据
export function regionTreeAsync(query) {
  return request({
    url: '/patrol/region/regionTreeAsync',
    method: 'get',
    params: query
  })
}

// 查询行政区树
export function getRegionTreeList(query) {
  return request({
    url: '/patrol/region/tree',
    method: 'get',
    params: query
  })
}

/**
 * 地图树权限管理
 */
export function getTreeDataByRole(query){
  return request({
    url: '/qx/permission/selmapqxBypc',
    method: 'get',
    params: query
  })
}

/**
 * 数据资源目录树方式一：无用
 */
export function getDataResource(query){
  return request({
    url: "/land/data/dataList",
    method: 'get',
    params: query
  })
}

/**
 * 数据资源目录树：有用
 */
export function getDataList(query){
  return request({
    url: "/land/category/DataResourceDirectory",
    method: 'get',
    params: query
  })
}

