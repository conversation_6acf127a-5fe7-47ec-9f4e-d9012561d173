define(["exports","./Cartographic-3309dd0d","./Cartesian4-3ca25aab","./Check-7b2a090c","./when-b60132fc","./Math-119be1a3","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90"],(function(e,n,t,r,a,i,u,o){"use strict";function s(e,n,t,r,i,u,o,s,l){this[0]=a.defaultValue(e,0),this[1]=a.defaultValue(r,0),this[2]=a.defaultValue(o,0),this[3]=a.defaultValue(n,0),this[4]=a.defaultValue(i,0),this[5]=a.defaultValue(s,0),this[6]=a.defaultValue(t,0),this[7]=a.defaultValue(u,0),this[8]=a.defaultValue(l,0)}s.packedLength=9,s.pack=function(e,n,t){return t=a.defaultValue(t,0),n[t++]=e[0],n[t++]=e[1],n[t++]=e[2],n[t++]=e[3],n[t++]=e[4],n[t++]=e[5],n[t++]=e[6],n[t++]=e[7],n[t++]=e[8],n},s.unpack=function(e,n,t){return n=a.defaultValue(n,0),a.defined(t)||(t=new s),t[0]=e[n++],t[1]=e[n++],t[2]=e[n++],t[3]=e[n++],t[4]=e[n++],t[5]=e[n++],t[6]=e[n++],t[7]=e[n++],t[8]=e[n++],t},s.clone=function(e,n){if(a.defined(e))return a.defined(n)?(n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[3],n[4]=e[4],n[5]=e[5],n[6]=e[6],n[7]=e[7],n[8]=e[8],n):new s(e[0],e[3],e[6],e[1],e[4],e[7],e[2],e[5],e[8])},s.fromArray=function(e,n,t){return n=a.defaultValue(n,0),a.defined(t)||(t=new s),t[0]=e[n],t[1]=e[n+1],t[2]=e[n+2],t[3]=e[n+3],t[4]=e[n+4],t[5]=e[n+5],t[6]=e[n+6],t[7]=e[n+7],t[8]=e[n+8],t},s.fromColumnMajorArray=function(e,n){return s.clone(e,n)},s.fromRowMajorArray=function(e,n){return a.defined(n)?(n[0]=e[0],n[1]=e[3],n[2]=e[6],n[3]=e[1],n[4]=e[4],n[5]=e[7],n[6]=e[2],n[7]=e[5],n[8]=e[8],n):new s(e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8])},s.fromQuaternion=function(e,n){var t=e.x*e.x,r=e.x*e.y,i=e.x*e.z,u=e.x*e.w,o=e.y*e.y,l=e.y*e.z,f=e.y*e.w,c=e.z*e.z,d=e.z*e.w,m=e.w*e.w,h=t-o-c+m,p=2*(r-d),y=2*(i+f),v=2*(r+d),M=-t+o-c+m,b=2*(l-u),C=2*(i-f),g=2*(l+u),x=-t-o+c+m;return a.defined(n)?(n[0]=h,n[1]=v,n[2]=C,n[3]=p,n[4]=M,n[5]=g,n[6]=y,n[7]=b,n[8]=x,n):new s(h,p,y,v,M,b,C,g,x)},s.fromHeadingPitchRoll=function(e,n){var t=Math.cos(-e.pitch),r=Math.cos(-e.heading),i=Math.cos(e.roll),u=Math.sin(-e.pitch),o=Math.sin(-e.heading),l=Math.sin(e.roll),f=t*r,c=-i*o+l*u*r,d=l*o+i*u*r,m=t*o,h=i*r+l*u*o,p=-l*r+i*u*o,y=-u,v=l*t,M=i*t;return a.defined(n)?(n[0]=f,n[1]=m,n[2]=y,n[3]=c,n[4]=h,n[5]=v,n[6]=d,n[7]=p,n[8]=M,n):new s(f,c,d,m,h,p,y,v,M)},s.fromScale=function(e,n){return a.defined(n)?(n[0]=e.x,n[1]=0,n[2]=0,n[3]=0,n[4]=e.y,n[5]=0,n[6]=0,n[7]=0,n[8]=e.z,n):new s(e.x,0,0,0,e.y,0,0,0,e.z)},s.fromUniformScale=function(e,n){return a.defined(n)?(n[0]=e,n[1]=0,n[2]=0,n[3]=0,n[4]=e,n[5]=0,n[6]=0,n[7]=0,n[8]=e,n):new s(e,0,0,0,e,0,0,0,e)},s.fromCrossProduct=function(e,n){return a.defined(n)?(n[0]=0,n[1]=e.z,n[2]=-e.y,n[3]=-e.z,n[4]=0,n[5]=e.x,n[6]=e.y,n[7]=-e.x,n[8]=0,n):new s(0,-e.z,e.y,e.z,0,-e.x,-e.y,e.x,0)},s.fromRotationX=function(e,n){var t=Math.cos(e),r=Math.sin(e);return a.defined(n)?(n[0]=1,n[1]=0,n[2]=0,n[3]=0,n[4]=t,n[5]=r,n[6]=0,n[7]=-r,n[8]=t,n):new s(1,0,0,0,t,-r,0,r,t)},s.fromRotationY=function(e,n){var t=Math.cos(e),r=Math.sin(e);return a.defined(n)?(n[0]=t,n[1]=0,n[2]=-r,n[3]=0,n[4]=1,n[5]=0,n[6]=r,n[7]=0,n[8]=t,n):new s(t,0,r,0,1,0,-r,0,t)},s.fromRotationZ=function(e,n){var t=Math.cos(e),r=Math.sin(e);return a.defined(n)?(n[0]=t,n[1]=r,n[2]=0,n[3]=-r,n[4]=t,n[5]=0,n[6]=0,n[7]=0,n[8]=1,n):new s(t,-r,0,r,t,0,0,0,1)},s.toArray=function(e,n){return a.defined(n)?(n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[3],n[4]=e[4],n[5]=e[5],n[6]=e[6],n[7]=e[7],n[8]=e[8],n):[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8]]},s.getElementIndex=function(e,n){return 3*e+n},s.getColumn=function(e,n,t){var r=3*n,a=e[r],i=e[r+1],u=e[r+2];return t.x=a,t.y=i,t.z=u,t},s.setColumn=function(e,n,t,r){var a=3*n;return(r=s.clone(e,r))[a]=t.x,r[a+1]=t.y,r[a+2]=t.z,r},s.getRow=function(e,n,t){var r=e[n],a=e[n+3],i=e[n+6];return t.x=r,t.y=a,t.z=i,t},s.setRow=function(e,n,t,r){return(r=s.clone(e,r))[n]=t.x,r[n+3]=t.y,r[n+6]=t.z,r};var l=new n.Cartesian3;s.getScale=function(e,t){return t.x=n.Cartesian3.magnitude(n.Cartesian3.fromElements(e[0],e[1],e[2],l)),t.y=n.Cartesian3.magnitude(n.Cartesian3.fromElements(e[3],e[4],e[5],l)),t.z=n.Cartesian3.magnitude(n.Cartesian3.fromElements(e[6],e[7],e[8],l)),t};var f=new n.Cartesian3;s.getMaximumScale=function(e){return s.getScale(e,f),n.Cartesian3.maximumComponent(f)},s.multiply=function(e,n,t){var r=e[0]*n[0]+e[3]*n[1]+e[6]*n[2],a=e[1]*n[0]+e[4]*n[1]+e[7]*n[2],i=e[2]*n[0]+e[5]*n[1]+e[8]*n[2],u=e[0]*n[3]+e[3]*n[4]+e[6]*n[5],o=e[1]*n[3]+e[4]*n[4]+e[7]*n[5],s=e[2]*n[3]+e[5]*n[4]+e[8]*n[5],l=e[0]*n[6]+e[3]*n[7]+e[6]*n[8],f=e[1]*n[6]+e[4]*n[7]+e[7]*n[8],c=e[2]*n[6]+e[5]*n[7]+e[8]*n[8];return t[0]=r,t[1]=a,t[2]=i,t[3]=u,t[4]=o,t[5]=s,t[6]=l,t[7]=f,t[8]=c,t},s.add=function(e,n,t){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t[2]=e[2]+n[2],t[3]=e[3]+n[3],t[4]=e[4]+n[4],t[5]=e[5]+n[5],t[6]=e[6]+n[6],t[7]=e[7]+n[7],t[8]=e[8]+n[8],t},s.subtract=function(e,n,t){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t[2]=e[2]-n[2],t[3]=e[3]-n[3],t[4]=e[4]-n[4],t[5]=e[5]-n[5],t[6]=e[6]-n[6],t[7]=e[7]-n[7],t[8]=e[8]-n[8],t},s.multiplyByVector=function(e,n,t){var r=n.x,a=n.y,i=n.z,u=e[0]*r+e[3]*a+e[6]*i,o=e[1]*r+e[4]*a+e[7]*i,s=e[2]*r+e[5]*a+e[8]*i;return t.x=u,t.y=o,t.z=s,t},s.multiplyByScalar=function(e,n,t){return t[0]=e[0]*n,t[1]=e[1]*n,t[2]=e[2]*n,t[3]=e[3]*n,t[4]=e[4]*n,t[5]=e[5]*n,t[6]=e[6]*n,t[7]=e[7]*n,t[8]=e[8]*n,t},s.multiplyByScale=function(e,n,t){return t[0]=e[0]*n.x,t[1]=e[1]*n.x,t[2]=e[2]*n.x,t[3]=e[3]*n.y,t[4]=e[4]*n.y,t[5]=e[5]*n.y,t[6]=e[6]*n.z,t[7]=e[7]*n.z,t[8]=e[8]*n.z,t},s.negate=function(e,n){return n[0]=-e[0],n[1]=-e[1],n[2]=-e[2],n[3]=-e[3],n[4]=-e[4],n[5]=-e[5],n[6]=-e[6],n[7]=-e[7],n[8]=-e[8],n},s.transpose=function(e,n){var t=e[0],r=e[3],a=e[6],i=e[1],u=e[4],o=e[7],s=e[2],l=e[5],f=e[8];return n[0]=t,n[1]=r,n[2]=a,n[3]=i,n[4]=u,n[5]=o,n[6]=s,n[7]=l,n[8]=f,n};var c=new n.Cartesian3(1,1,1);s.getRotation=function(e,t){var r=n.Cartesian3.divideComponents(c,s.getScale(e,f),f);return t=s.multiplyByScale(e,r,t)};var d=[1,0,0],m=[2,2,1];function h(e){for(var n=0,t=0;t<3;++t){var r=e[s.getElementIndex(m[t],d[t])];n+=2*r*r}return Math.sqrt(n)}function p(e,n){for(var t=i.CesiumMath.EPSILON15,r=0,a=1,u=0;u<3;++u){var o=Math.abs(e[s.getElementIndex(m[u],d[u])]);o>r&&(a=u,r=o)}var l=1,f=0,c=d[a],h=m[a];if(Math.abs(e[s.getElementIndex(h,c)])>t){var p,y=(e[s.getElementIndex(h,h)]-e[s.getElementIndex(c,c)])/2/e[s.getElementIndex(h,c)];f=(p=y<0?-1/(-y+Math.sqrt(1+y*y)):1/(y+Math.sqrt(1+y*y)))*(l=1/Math.sqrt(1+p*p))}return(n=s.clone(s.IDENTITY,n))[s.getElementIndex(c,c)]=n[s.getElementIndex(h,h)]=l,n[s.getElementIndex(h,c)]=f,n[s.getElementIndex(c,h)]=-f,n}var y=new s,v=new s;function M(e,n,t,r,i,u,o,s,l,f,c,d,m,h,p,y){this[0]=a.defaultValue(e,0),this[1]=a.defaultValue(i,0),this[2]=a.defaultValue(l,0),this[3]=a.defaultValue(m,0),this[4]=a.defaultValue(n,0),this[5]=a.defaultValue(u,0),this[6]=a.defaultValue(f,0),this[7]=a.defaultValue(h,0),this[8]=a.defaultValue(t,0),this[9]=a.defaultValue(o,0),this[10]=a.defaultValue(c,0),this[11]=a.defaultValue(p,0),this[12]=a.defaultValue(r,0),this[13]=a.defaultValue(s,0),this[14]=a.defaultValue(d,0),this[15]=a.defaultValue(y,0)}s.computeEigenDecomposition=function(e,n){var t=i.CesiumMath.EPSILON20,r=0,u=0;a.defined(n)||(n={});for(var o=n.unitary=s.clone(s.IDENTITY,n.unitary),l=n.diagonal=s.clone(e,n.diagonal),f=t*function(e){for(var n=0,t=0;t<9;++t){var r=e[t];n+=r*r}return Math.sqrt(n)}(l);u<10&&h(l)>f;)p(l,y),s.transpose(y,v),s.multiply(l,y,l),s.multiply(v,l,l),s.multiply(o,y,o),++r>2&&(++u,r=0);return n},s.abs=function(e,n){return n[0]=Math.abs(e[0]),n[1]=Math.abs(e[1]),n[2]=Math.abs(e[2]),n[3]=Math.abs(e[3]),n[4]=Math.abs(e[4]),n[5]=Math.abs(e[5]),n[6]=Math.abs(e[6]),n[7]=Math.abs(e[7]),n[8]=Math.abs(e[8]),n},s.determinant=function(e){var n=e[0],t=e[3],r=e[6],a=e[1],i=e[4],u=e[7],o=e[2],s=e[5],l=e[8];return n*(i*l-s*u)+a*(s*r-t*l)+o*(t*u-i*r)},s.inverse=function(e,n){var t=e[0],r=e[1],a=e[2],i=e[3],u=e[4],o=e[5],l=e[6],f=e[7],c=e[8],d=s.determinant(e);n[0]=u*c-f*o,n[1]=f*a-r*c,n[2]=r*o-u*a,n[3]=l*o-i*c,n[4]=t*c-l*a,n[5]=i*a-t*o,n[6]=i*f-l*u,n[7]=l*r-t*f,n[8]=t*u-i*r;var m=1/d;return s.multiplyByScalar(n,m,n)},s.equals=function(e,n){return e===n||a.defined(e)&&a.defined(n)&&e[0]===n[0]&&e[1]===n[1]&&e[2]===n[2]&&e[3]===n[3]&&e[4]===n[4]&&e[5]===n[5]&&e[6]===n[6]&&e[7]===n[7]&&e[8]===n[8]},s.equalsEpsilon=function(e,n,t){return e===n||a.defined(e)&&a.defined(n)&&Math.abs(e[0]-n[0])<=t&&Math.abs(e[1]-n[1])<=t&&Math.abs(e[2]-n[2])<=t&&Math.abs(e[3]-n[3])<=t&&Math.abs(e[4]-n[4])<=t&&Math.abs(e[5]-n[5])<=t&&Math.abs(e[6]-n[6])<=t&&Math.abs(e[7]-n[7])<=t&&Math.abs(e[8]-n[8])<=t},s.IDENTITY=Object.freeze(new s(1,0,0,0,1,0,0,0,1)),s.ZERO=Object.freeze(new s(0,0,0,0,0,0,0,0,0)),s.COLUMN0ROW0=0,s.COLUMN0ROW1=1,s.COLUMN0ROW2=2,s.COLUMN1ROW0=3,s.COLUMN1ROW1=4,s.COLUMN1ROW2=5,s.COLUMN2ROW0=6,s.COLUMN2ROW1=7,s.COLUMN2ROW2=8,Object.defineProperties(s.prototype,{length:{get:function(){return s.packedLength}}}),s.prototype.clone=function(e){return s.clone(this,e)},s.prototype.equals=function(e){return s.equals(this,e)},s.equalsArray=function(e,n,t){return e[0]===n[t]&&e[1]===n[t+1]&&e[2]===n[t+2]&&e[3]===n[t+3]&&e[4]===n[t+4]&&e[5]===n[t+5]&&e[6]===n[t+6]&&e[7]===n[t+7]&&e[8]===n[t+8]},s.prototype.equalsEpsilon=function(e,n){return s.equalsEpsilon(this,e,n)},s.prototype.toString=function(){return"("+this[0]+", "+this[3]+", "+this[6]+")\n("+this[1]+", "+this[4]+", "+this[7]+")\n("+this[2]+", "+this[5]+", "+this[8]+")"},M.packedLength=16,M.pack=function(e,n,t){return t=a.defaultValue(t,0),n[t++]=e[0],n[t++]=e[1],n[t++]=e[2],n[t++]=e[3],n[t++]=e[4],n[t++]=e[5],n[t++]=e[6],n[t++]=e[7],n[t++]=e[8],n[t++]=e[9],n[t++]=e[10],n[t++]=e[11],n[t++]=e[12],n[t++]=e[13],n[t++]=e[14],n[t]=e[15],n},M.unpack=function(e,n,t){return n=a.defaultValue(n,0),a.defined(t)||(t=new M),t[0]=e[n++],t[1]=e[n++],t[2]=e[n++],t[3]=e[n++],t[4]=e[n++],t[5]=e[n++],t[6]=e[n++],t[7]=e[n++],t[8]=e[n++],t[9]=e[n++],t[10]=e[n++],t[11]=e[n++],t[12]=e[n++],t[13]=e[n++],t[14]=e[n++],t[15]=e[n],t},M.clone=function(e,n){if(a.defined(e))return a.defined(n)?(n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[3],n[4]=e[4],n[5]=e[5],n[6]=e[6],n[7]=e[7],n[8]=e[8],n[9]=e[9],n[10]=e[10],n[11]=e[11],n[12]=e[12],n[13]=e[13],n[14]=e[14],n[15]=e[15],n):new M(e[0],e[4],e[8],e[12],e[1],e[5],e[9],e[13],e[2],e[6],e[10],e[14],e[3],e[7],e[11],e[15])},M.fromArray=M.unpack,M.fromColumnMajorArray=function(e,n){return M.clone(e,n)},M.fromRowMajorArray=function(e,n){return a.defined(n)?(n[0]=e[0],n[1]=e[4],n[2]=e[8],n[3]=e[12],n[4]=e[1],n[5]=e[5],n[6]=e[9],n[7]=e[13],n[8]=e[2],n[9]=e[6],n[10]=e[10],n[11]=e[14],n[12]=e[3],n[13]=e[7],n[14]=e[11],n[15]=e[15],n):new M(e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8],e[9],e[10],e[11],e[12],e[13],e[14],e[15])},M.fromRotationTranslation=function(e,t,r){return t=a.defaultValue(t,n.Cartesian3.ZERO),a.defined(r)?(r[0]=e[0],r[1]=e[1],r[2]=e[2],r[3]=0,r[4]=e[3],r[5]=e[4],r[6]=e[5],r[7]=0,r[8]=e[6],r[9]=e[7],r[10]=e[8],r[11]=0,r[12]=t.x,r[13]=t.y,r[14]=t.z,r[15]=1,r):new M(e[0],e[3],e[6],t.x,e[1],e[4],e[7],t.y,e[2],e[5],e[8],t.z,0,0,0,1)},M.fromTranslationQuaternionRotationScale=function(e,n,t,r){a.defined(r)||(r=new M);var i=t.x,u=t.y,o=t.z,s=n.x*n.x,l=n.x*n.y,f=n.x*n.z,c=n.x*n.w,d=n.y*n.y,m=n.y*n.z,h=n.y*n.w,p=n.z*n.z,y=n.z*n.w,v=n.w*n.w,b=s-d-p+v,C=2*(l-y),g=2*(f+h),x=2*(l+y),w=-s+d-p+v,E=2*(m-c),O=2*(f-h),A=2*(m+c),R=-s-d+p+v;return r[0]=b*i,r[1]=x*i,r[2]=O*i,r[3]=0,r[4]=C*u,r[5]=w*u,r[6]=A*u,r[7]=0,r[8]=g*o,r[9]=E*o,r[10]=R*o,r[11]=0,r[12]=e.x,r[13]=e.y,r[14]=e.z,r[15]=1,r},M.fromTranslationRotationScale=function(e,n){return M.fromTranslationQuaternionRotationScale(e.translation,e.rotation,e.scale,n)},M.fromTranslation=function(e,n){return M.fromRotationTranslation(s.IDENTITY,e,n)},M.fromScale=function(e,n){return a.defined(n)?(n[0]=e.x,n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=e.y,n[6]=0,n[7]=0,n[8]=0,n[9]=0,n[10]=e.z,n[11]=0,n[12]=0,n[13]=0,n[14]=0,n[15]=1,n):new M(e.x,0,0,0,0,e.y,0,0,0,0,e.z,0,0,0,0,1)},M.fromUniformScale=function(e,n){return a.defined(n)?(n[0]=e,n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=e,n[6]=0,n[7]=0,n[8]=0,n[9]=0,n[10]=e,n[11]=0,n[12]=0,n[13]=0,n[14]=0,n[15]=1,n):new M(e,0,0,0,0,e,0,0,0,0,e,0,0,0,0,1)};var b=new n.Cartesian3,C=new n.Cartesian3,g=new n.Cartesian3;M.fromCamera=function(e,t){var r=e.position,i=e.direction,u=e.up;n.Cartesian3.normalize(i,b),n.Cartesian3.normalize(n.Cartesian3.cross(b,u,C),C),n.Cartesian3.normalize(n.Cartesian3.cross(C,b,g),g);var o=C.x,s=C.y,l=C.z,f=b.x,c=b.y,d=b.z,m=g.x,h=g.y,p=g.z,y=r.x,v=r.y,x=r.z,w=o*-y+s*-v+l*-x,E=m*-y+h*-v+p*-x,O=f*y+c*v+d*x;return a.defined(t)?(t[0]=o,t[1]=m,t[2]=-f,t[3]=0,t[4]=s,t[5]=h,t[6]=-c,t[7]=0,t[8]=l,t[9]=p,t[10]=-d,t[11]=0,t[12]=w,t[13]=E,t[14]=O,t[15]=1,t):new M(o,s,l,w,m,h,p,E,-f,-c,-d,O,0,0,0,1)},M.computePerspectiveFieldOfView=function(e,n,t,r,a){var i=1/Math.tan(.5*e),u=i/n,o=(r+t)/(t-r),s=2*r*t/(t-r);return a[0]=u,a[1]=0,a[2]=0,a[3]=0,a[4]=0,a[5]=i,a[6]=0,a[7]=0,a[8]=0,a[9]=0,a[10]=o,a[11]=-1,a[12]=0,a[13]=0,a[14]=s,a[15]=0,a},M.computeOrthographicOffCenter=function(e,n,t,r,a,i,u){var o=1/(n-e),s=1/(r-t),l=1/(i-a),f=-(n+e)*o,c=-(r+t)*s,d=-(i+a)*l;return o*=2,s*=2,l*=-2,u[0]=o,u[1]=0,u[2]=0,u[3]=0,u[4]=0,u[5]=s,u[6]=0,u[7]=0,u[8]=0,u[9]=0,u[10]=l,u[11]=0,u[12]=f,u[13]=c,u[14]=d,u[15]=1,u},M.computePerspectiveOffCenter=function(e,n,t,r,a,i,u){var o=2*a/(n-e),s=2*a/(r-t),l=(n+e)/(n-e),f=(r+t)/(r-t),c=-(i+a)/(i-a),d=-2*i*a/(i-a);return u[0]=o,u[1]=0,u[2]=0,u[3]=0,u[4]=0,u[5]=s,u[6]=0,u[7]=0,u[8]=l,u[9]=f,u[10]=c,u[11]=-1,u[12]=0,u[13]=0,u[14]=d,u[15]=0,u},M.computeInfinitePerspectiveOffCenter=function(e,n,t,r,a,i){var u=2*a/(n-e),o=2*a/(r-t),s=(n+e)/(n-e),l=(r+t)/(r-t),f=-2*a;return i[0]=u,i[1]=0,i[2]=0,i[3]=0,i[4]=0,i[5]=o,i[6]=0,i[7]=0,i[8]=s,i[9]=l,i[10]=-1,i[11]=-1,i[12]=0,i[13]=0,i[14]=f,i[15]=0,i},M.computeViewportTransformation=function(e,n,t,r){e=a.defaultValue(e,a.defaultValue.EMPTY_OBJECT);var i=a.defaultValue(e.x,0),u=a.defaultValue(e.y,0),o=a.defaultValue(e.width,0),s=a.defaultValue(e.height,0);n=a.defaultValue(n,0);var l=.5*o,f=.5*s,c=.5*((t=a.defaultValue(t,1))-n),d=l,m=f,h=c,p=i+l,y=u+f,v=n+c;return r[0]=d,r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[5]=m,r[6]=0,r[7]=0,r[8]=0,r[9]=0,r[10]=h,r[11]=0,r[12]=p,r[13]=y,r[14]=v,r[15]=1,r},M.computeView=function(e,t,r,a,i){return i[0]=a.x,i[1]=r.x,i[2]=-t.x,i[3]=0,i[4]=a.y,i[5]=r.y,i[6]=-t.y,i[7]=0,i[8]=a.z,i[9]=r.z,i[10]=-t.z,i[11]=0,i[12]=-n.Cartesian3.dot(a,e),i[13]=-n.Cartesian3.dot(r,e),i[14]=n.Cartesian3.dot(t,e),i[15]=1,i},M.toArray=function(e,n){return a.defined(n)?(n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[3],n[4]=e[4],n[5]=e[5],n[6]=e[6],n[7]=e[7],n[8]=e[8],n[9]=e[9],n[10]=e[10],n[11]=e[11],n[12]=e[12],n[13]=e[13],n[14]=e[14],n[15]=e[15],n):[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8],e[9],e[10],e[11],e[12],e[13],e[14],e[15]]},M.getElementIndex=function(e,n){return 4*e+n},M.getColumn=function(e,n,t){var r=4*n,a=e[r],i=e[r+1],u=e[r+2],o=e[r+3];return t.x=a,t.y=i,t.z=u,t.w=o,t},M.setColumn=function(e,n,t,r){var a=4*n;return(r=M.clone(e,r))[a]=t.x,r[a+1]=t.y,r[a+2]=t.z,r[a+3]=t.w,r},M.setTranslation=function(e,n,t){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t[6]=e[6],t[7]=e[7],t[8]=e[8],t[9]=e[9],t[10]=e[10],t[11]=e[11],t[12]=n.x,t[13]=n.y,t[14]=n.z,t[15]=e[15],t};var x=new n.Cartesian3;M.setScale=function(e,t,r){var a=M.getScale(e,x),i=n.Cartesian3.divideComponents(t,a,x);return M.multiplyByScale(e,i,r)},M.getRow=function(e,n,t){var r=e[n],a=e[n+4],i=e[n+8],u=e[n+12];return t.x=r,t.y=a,t.z=i,t.w=u,t},M.setRow=function(e,n,t,r){return(r=M.clone(e,r))[n]=t.x,r[n+4]=t.y,r[n+8]=t.z,r[n+12]=t.w,r};var w=new n.Cartesian3;M.getScale=function(e,t){return t.x=n.Cartesian3.magnitude(n.Cartesian3.fromElements(e[0],e[1],e[2],w)),t.y=n.Cartesian3.magnitude(n.Cartesian3.fromElements(e[4],e[5],e[6],w)),t.z=n.Cartesian3.magnitude(n.Cartesian3.fromElements(e[8],e[9],e[10],w)),t};var E=new n.Cartesian3;M.getMaximumScale=function(e){return M.getScale(e,E),n.Cartesian3.maximumComponent(E)},M.multiply=function(e,n,t){var r=e[0],a=e[1],i=e[2],u=e[3],o=e[4],s=e[5],l=e[6],f=e[7],c=e[8],d=e[9],m=e[10],h=e[11],p=e[12],y=e[13],v=e[14],M=e[15],b=n[0],C=n[1],g=n[2],x=n[3],w=n[4],E=n[5],O=n[6],A=n[7],R=n[8],z=n[9],I=n[10],N=n[11],L=n[12],S=n[13],V=n[14],T=n[15],W=r*b+o*C+c*g+p*x,F=a*b+s*C+d*g+y*x,P=i*b+l*C+m*g+v*x,U=u*b+f*C+h*g+M*x,q=r*w+o*E+c*O+p*A,B=a*w+s*E+d*O+y*A,G=i*w+l*E+m*O+v*A,k=u*w+f*E+h*O+M*A,j=r*R+o*z+c*I+p*N,_=a*R+s*z+d*I+y*N,D=i*R+l*z+m*I+v*N,Y=u*R+f*z+h*I+M*N,Q=r*L+o*S+c*V+p*T,J=a*L+s*S+d*V+y*T,Z=i*L+l*S+m*V+v*T,X=u*L+f*S+h*V+M*T;return t[0]=W,t[1]=F,t[2]=P,t[3]=U,t[4]=q,t[5]=B,t[6]=G,t[7]=k,t[8]=j,t[9]=_,t[10]=D,t[11]=Y,t[12]=Q,t[13]=J,t[14]=Z,t[15]=X,t},M.add=function(e,n,t){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t[2]=e[2]+n[2],t[3]=e[3]+n[3],t[4]=e[4]+n[4],t[5]=e[5]+n[5],t[6]=e[6]+n[6],t[7]=e[7]+n[7],t[8]=e[8]+n[8],t[9]=e[9]+n[9],t[10]=e[10]+n[10],t[11]=e[11]+n[11],t[12]=e[12]+n[12],t[13]=e[13]+n[13],t[14]=e[14]+n[14],t[15]=e[15]+n[15],t},M.subtract=function(e,n,t){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t[2]=e[2]-n[2],t[3]=e[3]-n[3],t[4]=e[4]-n[4],t[5]=e[5]-n[5],t[6]=e[6]-n[6],t[7]=e[7]-n[7],t[8]=e[8]-n[8],t[9]=e[9]-n[9],t[10]=e[10]-n[10],t[11]=e[11]-n[11],t[12]=e[12]-n[12],t[13]=e[13]-n[13],t[14]=e[14]-n[14],t[15]=e[15]-n[15],t},M.multiplyTransformation=function(e,n,t){var r=e[0],a=e[1],i=e[2],u=e[4],o=e[5],s=e[6],l=e[8],f=e[9],c=e[10],d=e[12],m=e[13],h=e[14],p=n[0],y=n[1],v=n[2],M=n[4],b=n[5],C=n[6],g=n[8],x=n[9],w=n[10],E=n[12],O=n[13],A=n[14],R=r*p+u*y+l*v,z=a*p+o*y+f*v,I=i*p+s*y+c*v,N=r*M+u*b+l*C,L=a*M+o*b+f*C,S=i*M+s*b+c*C,V=r*g+u*x+l*w,T=a*g+o*x+f*w,W=i*g+s*x+c*w,F=r*E+u*O+l*A+d,P=a*E+o*O+f*A+m,U=i*E+s*O+c*A+h;return t[0]=R,t[1]=z,t[2]=I,t[3]=0,t[4]=N,t[5]=L,t[6]=S,t[7]=0,t[8]=V,t[9]=T,t[10]=W,t[11]=0,t[12]=F,t[13]=P,t[14]=U,t[15]=1,t},M.multiplyByMatrix3=function(e,n,t){var r=e[0],a=e[1],i=e[2],u=e[4],o=e[5],s=e[6],l=e[8],f=e[9],c=e[10],d=n[0],m=n[1],h=n[2],p=n[3],y=n[4],v=n[5],M=n[6],b=n[7],C=n[8],g=r*d+u*m+l*h,x=a*d+o*m+f*h,w=i*d+s*m+c*h,E=r*p+u*y+l*v,O=a*p+o*y+f*v,A=i*p+s*y+c*v,R=r*M+u*b+l*C,z=a*M+o*b+f*C,I=i*M+s*b+c*C;return t[0]=g,t[1]=x,t[2]=w,t[3]=0,t[4]=E,t[5]=O,t[6]=A,t[7]=0,t[8]=R,t[9]=z,t[10]=I,t[11]=0,t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15],t},M.multiplyByTranslation=function(e,n,t){var r=n.x,a=n.y,i=n.z,u=r*e[0]+a*e[4]+i*e[8]+e[12],o=r*e[1]+a*e[5]+i*e[9]+e[13],s=r*e[2]+a*e[6]+i*e[10]+e[14];return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t[6]=e[6],t[7]=e[7],t[8]=e[8],t[9]=e[9],t[10]=e[10],t[11]=e[11],t[12]=u,t[13]=o,t[14]=s,t[15]=e[15],t};var O=new n.Cartesian3;M.multiplyByUniformScale=function(e,n,t){return O.x=n,O.y=n,O.z=n,M.multiplyByScale(e,O,t)},M.multiplyByScale=function(e,n,t){var r=n.x,a=n.y,i=n.z;return 1===r&&1===a&&1===i?M.clone(e,t):(t[0]=r*e[0],t[1]=r*e[1],t[2]=r*e[2],t[3]=0,t[4]=a*e[4],t[5]=a*e[5],t[6]=a*e[6],t[7]=0,t[8]=i*e[8],t[9]=i*e[9],t[10]=i*e[10],t[11]=0,t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=1,t)},M.multiplyByVector=function(e,n,t){var r=n.x,a=n.y,i=n.z,u=n.w,o=e[0]*r+e[4]*a+e[8]*i+e[12]*u,s=e[1]*r+e[5]*a+e[9]*i+e[13]*u,l=e[2]*r+e[6]*a+e[10]*i+e[14]*u,f=e[3]*r+e[7]*a+e[11]*i+e[15]*u;return t.x=o,t.y=s,t.z=l,t.w=f,t},M.multiplyByPointAsVector=function(e,n,t){var r=n.x,a=n.y,i=n.z,u=e[0]*r+e[4]*a+e[8]*i,o=e[1]*r+e[5]*a+e[9]*i,s=e[2]*r+e[6]*a+e[10]*i;return t.x=u,t.y=o,t.z=s,t},M.multiplyByPoint=function(e,n,t){var r=n.x,a=n.y,i=n.z,u=e[0]*r+e[4]*a+e[8]*i+e[12],o=e[1]*r+e[5]*a+e[9]*i+e[13],s=e[2]*r+e[6]*a+e[10]*i+e[14];return t.x=u,t.y=o,t.z=s,t},M.multiplyByScalar=function(e,n,t){return t[0]=e[0]*n,t[1]=e[1]*n,t[2]=e[2]*n,t[3]=e[3]*n,t[4]=e[4]*n,t[5]=e[5]*n,t[6]=e[6]*n,t[7]=e[7]*n,t[8]=e[8]*n,t[9]=e[9]*n,t[10]=e[10]*n,t[11]=e[11]*n,t[12]=e[12]*n,t[13]=e[13]*n,t[14]=e[14]*n,t[15]=e[15]*n,t},M.multiplyByPlane=function(e,r,a){var i=new M,u=new M;M.inverse(e,i),M.transpose(i,u);var o=new t.Cartesian4(r.normal.x,r.normal.y,r.normal.z,r.distance);M.multiplyByVector(u,o,o),a.normal.x=o.x,a.normal.y=o.y,a.normal.z=o.z;var s=n.Cartesian3.magnitude(a.normal);return n.Cartesian3.normalize(a.normal,a.normal),a.distance=o.w/s,a},M.negate=function(e,n){return n[0]=-e[0],n[1]=-e[1],n[2]=-e[2],n[3]=-e[3],n[4]=-e[4],n[5]=-e[5],n[6]=-e[6],n[7]=-e[7],n[8]=-e[8],n[9]=-e[9],n[10]=-e[10],n[11]=-e[11],n[12]=-e[12],n[13]=-e[13],n[14]=-e[14],n[15]=-e[15],n},M.transpose=function(e,n){var t=e[1],r=e[2],a=e[3],i=e[6],u=e[7],o=e[11];return n[0]=e[0],n[1]=e[4],n[2]=e[8],n[3]=e[12],n[4]=t,n[5]=e[5],n[6]=e[9],n[7]=e[13],n[8]=r,n[9]=i,n[10]=e[10],n[11]=e[14],n[12]=a,n[13]=u,n[14]=o,n[15]=e[15],n},M.abs=function(e,n){return n[0]=Math.abs(e[0]),n[1]=Math.abs(e[1]),n[2]=Math.abs(e[2]),n[3]=Math.abs(e[3]),n[4]=Math.abs(e[4]),n[5]=Math.abs(e[5]),n[6]=Math.abs(e[6]),n[7]=Math.abs(e[7]),n[8]=Math.abs(e[8]),n[9]=Math.abs(e[9]),n[10]=Math.abs(e[10]),n[11]=Math.abs(e[11]),n[12]=Math.abs(e[12]),n[13]=Math.abs(e[13]),n[14]=Math.abs(e[14]),n[15]=Math.abs(e[15]),n},M.equals=function(e,n){return e===n||a.defined(e)&&a.defined(n)&&e[12]===n[12]&&e[13]===n[13]&&e[14]===n[14]&&e[0]===n[0]&&e[1]===n[1]&&e[2]===n[2]&&e[4]===n[4]&&e[5]===n[5]&&e[6]===n[6]&&e[8]===n[8]&&e[9]===n[9]&&e[10]===n[10]&&e[3]===n[3]&&e[7]===n[7]&&e[11]===n[11]&&e[15]===n[15]},M.equalsEpsilon=function(e,n,t){return e===n||a.defined(e)&&a.defined(n)&&Math.abs(e[0]-n[0])<=t&&Math.abs(e[1]-n[1])<=t&&Math.abs(e[2]-n[2])<=t&&Math.abs(e[3]-n[3])<=t&&Math.abs(e[4]-n[4])<=t&&Math.abs(e[5]-n[5])<=t&&Math.abs(e[6]-n[6])<=t&&Math.abs(e[7]-n[7])<=t&&Math.abs(e[8]-n[8])<=t&&Math.abs(e[9]-n[9])<=t&&Math.abs(e[10]-n[10])<=t&&Math.abs(e[11]-n[11])<=t&&Math.abs(e[12]-n[12])<=t&&Math.abs(e[13]-n[13])<=t&&Math.abs(e[14]-n[14])<=t&&Math.abs(e[15]-n[15])<=t},M.getTranslation=function(e,n){return n.x=e[12],n.y=e[13],n.z=e[14],n},M.getMatrix3=function(e,n){return n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[4],n[4]=e[5],n[5]=e[6],n[6]=e[8],n[7]=e[9],n[8]=e[10],n},M.getRotation=function(e,n){return n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[4],n[4]=e[5],n[5]=e[6],n[6]=e[8],n[7]=e[9],n[8]=e[10],n};var A=new s,R=new s,z=new t.Cartesian4,I=new t.Cartesian4(0,0,0,1);M.inverse=function(e,n){var r=e[0],a=e[4],o=e[8],l=e[12],f=e[1],c=e[5],d=e[9],m=e[13],h=e[2],p=e[6],y=e[10],v=e[14],b=e[3],C=e[7],g=e[11],x=e[15],w=y*x,E=v*g,O=p*x,N=v*C,L=p*g,S=y*C,V=h*x,T=v*b,W=h*g,F=y*b,P=h*C,U=p*b,q=w*c+N*d+L*m-(E*c+O*d+S*m),B=E*f+V*d+F*m-(w*f+T*d+W*m),G=O*f+T*c+P*m-(N*f+V*c+U*m),k=S*f+W*c+U*d-(L*f+F*c+P*d),j=E*a+O*o+S*l-(w*a+N*o+L*l),_=w*r+T*o+W*l-(E*r+V*o+F*l),D=N*r+V*a+U*l-(O*r+T*a+P*l),Y=L*r+F*a+P*o-(S*r+W*a+U*o),Q=(w=o*m)*C+(N=l*c)*g+(L=a*d)*x-((E=l*d)*C+(O=a*m)*g+(S=o*c)*x),J=E*b+(V=r*m)*g+(F=o*f)*x-(w*b+(T=l*f)*g+(W=r*d)*x),Z=O*b+T*C+(P=r*c)*x-(N*b+V*C+(U=a*f)*x),X=S*b+W*C+U*g-(L*b+F*C+P*g),H=O*y+S*v+E*p-(L*v+w*p+N*y),K=W*v+w*h+T*y-(V*y+F*v+E*h),$=V*p+U*v+N*h-(P*v+O*h+T*p),ee=P*y+L*h+F*p-(W*p+U*y+S*h),ne=r*q+a*B+o*G+l*k;if(Math.abs(ne)<i.CesiumMath.EPSILON21){if(s.equalsEpsilon(M.getRotation(e,A),R,i.CesiumMath.EPSILON5)&&t.Cartesian4.equals(M.getRow(e,3,z),I))return n[0]=0,n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=0,n[6]=0,n[7]=0,n[8]=0,n[9]=0,n[10]=0,n[11]=0,n[12]=-e[12],n[13]=-e[13],n[14]=-e[14],n[15]=1,n;throw new u.RuntimeError("matrix is not invertible because its determinate is zero.")}return ne=1/ne,n[0]=q*ne,n[1]=B*ne,n[2]=G*ne,n[3]=k*ne,n[4]=j*ne,n[5]=_*ne,n[6]=D*ne,n[7]=Y*ne,n[8]=Q*ne,n[9]=J*ne,n[10]=Z*ne,n[11]=X*ne,n[12]=H*ne,n[13]=K*ne,n[14]=$*ne,n[15]=ee*ne,n},M.inverseTransformation=function(e,n){var t=e[0],r=e[1],a=e[2],i=e[4],u=e[5],o=e[6],s=e[8],l=e[9],f=e[10],c=e[12],d=e[13],m=e[14],h=-t*c-r*d-a*m,p=-i*c-u*d-o*m,y=-s*c-l*d-f*m;return n[0]=t,n[1]=i,n[2]=s,n[3]=0,n[4]=r,n[5]=u,n[6]=l,n[7]=0,n[8]=a,n[9]=o,n[10]=f,n[11]=0,n[12]=h,n[13]=p,n[14]=y,n[15]=1,n},M.IDENTITY=Object.freeze(new M(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1)),M.ZERO=Object.freeze(new M(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)),M.COLUMN0ROW0=0,M.COLUMN0ROW1=1,M.COLUMN0ROW2=2,M.COLUMN0ROW3=3,M.COLUMN1ROW0=4,M.COLUMN1ROW1=5,M.COLUMN1ROW2=6,M.COLUMN1ROW3=7,M.COLUMN2ROW0=8,M.COLUMN2ROW1=9,M.COLUMN2ROW2=10,M.COLUMN2ROW3=11,M.COLUMN3ROW0=12,M.COLUMN3ROW1=13,M.COLUMN3ROW2=14,M.COLUMN3ROW3=15,Object.defineProperties(M.prototype,{length:{get:function(){return M.packedLength}}}),M.prototype.clone=function(e){return M.clone(this,e)},M.prototype.equals=function(e){return M.equals(this,e)},M.equalsArray=function(e,n,t){return e[0]===n[t]&&e[1]===n[t+1]&&e[2]===n[t+2]&&e[3]===n[t+3]&&e[4]===n[t+4]&&e[5]===n[t+5]&&e[6]===n[t+6]&&e[7]===n[t+7]&&e[8]===n[t+8]&&e[9]===n[t+9]&&e[10]===n[t+10]&&e[11]===n[t+11]&&e[12]===n[t+12]&&e[13]===n[t+13]&&e[14]===n[t+14]&&e[15]===n[t+15]},M.prototype.equalsEpsilon=function(e,n){return M.equalsEpsilon(this,e,n)},M.prototype.toString=function(){return"("+this[0]+", "+this[4]+", "+this[8]+", "+this[12]+")\n("+this[1]+", "+this[5]+", "+this[9]+", "+this[13]+")\n("+this[2]+", "+this[6]+", "+this[10]+", "+this[14]+")\n("+this[3]+", "+this[7]+", "+this[11]+", "+this[15]+")"};var N,L,S,V,T,W,F,P,U,q,B,G,k,j,_,D,Y,Q,J,Z,X,H={POINTS:o.WebGLConstants.POINTS,LINES:o.WebGLConstants.LINES,LINE_LOOP:o.WebGLConstants.LINE_LOOP,LINE_STRIP:o.WebGLConstants.LINE_STRIP,TRIANGLES:o.WebGLConstants.TRIANGLES,TRIANGLE_STRIP:o.WebGLConstants.TRIANGLE_STRIP,TRIANGLE_FAN:o.WebGLConstants.TRIANGLE_FAN,validate:function(e){return e===H.POINTS||e===H.LINES||e===H.LINE_LOOP||e===H.LINE_STRIP||e===H.TRIANGLES||e===H.TRIANGLE_STRIP||e===H.TRIANGLE_FAN}},K=Object.freeze(H),$={requestFullscreen:void 0,exitFullscreen:void 0,fullscreenEnabled:void 0,fullscreenElement:void 0,fullscreenchange:void 0,fullscreenerror:void 0},ee={};function ne(e){for(var n=e.split("."),t=0,r=n.length;t<r;++t)n[t]=parseInt(n[t],10);return n}function te(){if(!a.defined(S)&&(S=!1,!ue())){var e=/ Chrome\/([\.0-9]+)/.exec(L.userAgent);null!==e&&(S=!0,V=ne(e[1]))}return S}function re(){if(!a.defined(T)&&(T=!1,!te()&&!ue()&&/ Safari\/[\.0-9]+/.test(L.userAgent))){var e=/ Version\/([\.0-9]+)/.exec(L.userAgent);null!==e&&(T=!0,W=ne(e[1]))}return T}function ae(){if(!a.defined(F)){F=!1;var e=/ AppleWebKit\/([\.0-9]+)(\+?)/.exec(L.userAgent);null!==e&&(F=!0,(P=ne(e[1])).isNightly=!!e[2])}return F}function ie(){var e;a.defined(U)||(U=!1,"Microsoft Internet Explorer"===L.appName?null!==(e=/MSIE ([0-9]{1,}[\.0-9]{0,})/.exec(L.userAgent))&&(U=!0,q=ne(e[1])):"Netscape"===L.appName&&null!==(e=/Trident\/.*rv:([0-9]{1,}[\.0-9]{0,})/.exec(L.userAgent))&&(U=!0,q=ne(e[1])));return U}function ue(){if(!a.defined(B)){B=!1;var e=/ Edge\/([\.0-9]+)/.exec(L.userAgent);null!==e&&(B=!0,G=ne(e[1]))}return B}function oe(){if(!a.defined(k)){k=!1;var e=/Firefox\/([\.0-9]+)/.exec(L.userAgent);null!==e&&(k=!0,j=ne(e[1]))}return k}function se(){if(!a.defined(J)){var e=document.createElement("canvas");e.setAttribute("style","image-rendering: -moz-crisp-edges;image-rendering: pixelated;");var n=e.style.imageRendering;(J=a.defined(n)&&""!==n)&&(Q=n)}return J}function le(){if(a.defined(X))return X.promise;X=a.when.defer(),ue()&&(Z=!1,X.resolve(Z));var e=new Image;return e.onload=function(){Z=e.width>0&&e.height>0,X.resolve(Z)},e.onerror=function(){Z=!1,X.resolve(Z)},e.src="data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA",X.promise}Object.defineProperties(ee,{element:{get:function(){if(ee.supportsFullscreen())return document[$.fullscreenElement]}},changeEventName:{get:function(){if(ee.supportsFullscreen())return $.fullscreenchange}},errorEventName:{get:function(){if(ee.supportsFullscreen())return $.fullscreenerror}},enabled:{get:function(){if(ee.supportsFullscreen())return document[$.fullscreenEnabled]}},fullscreen:{get:function(){if(ee.supportsFullscreen())return null!==ee.element}}}),ee.supportsFullscreen=function(){if(a.defined(N))return N;N=!1;var e=document.body;if("function"==typeof e.requestFullscreen)return $.requestFullscreen="requestFullscreen",$.exitFullscreen="exitFullscreen",$.fullscreenEnabled="fullscreenEnabled",$.fullscreenElement="fullscreenElement",$.fullscreenchange="fullscreenchange",$.fullscreenerror="fullscreenerror",N=!0;for(var n,t=["webkit","moz","o","ms","khtml"],r=0,i=t.length;r<i;++r){var u=t[r];("function"==typeof e[n=u+"RequestFullscreen"]||"function"==typeof e[n=u+"RequestFullScreen"])&&($.requestFullscreen=n,N=!0),n=u+"ExitFullscreen","function"==typeof document[n]?$.exitFullscreen=n:(n=u+"CancelFullScreen","function"==typeof document[n]&&($.exitFullscreen=n)),n=u+"FullscreenEnabled",void 0!==document[n]?$.fullscreenEnabled=n:(n=u+"FullScreenEnabled",void 0!==document[n]&&($.fullscreenEnabled=n)),n=u+"FullscreenElement",void 0!==document[n]?$.fullscreenElement=n:(n=u+"FullScreenElement",void 0!==document[n]&&($.fullscreenElement=n)),n=u+"fullscreenchange",void 0!==document["on"+n]&&("ms"===u&&(n="MSFullscreenChange"),$.fullscreenchange=n),n=u+"fullscreenerror",void 0!==document["on"+n]&&("ms"===u&&(n="MSFullscreenError"),$.fullscreenerror=n)}return N},ee.requestFullscreen=function(e,n){ee.supportsFullscreen()&&e[$.requestFullscreen]({vrDisplay:n})},ee.exitFullscreen=function(){ee.supportsFullscreen()&&document[$.exitFullscreen]()},ee._names=$,L="undefined"!=typeof navigator?navigator:{};var fe=[];"undefined"!=typeof ArrayBuffer&&(fe.push(Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array),"undefined"!=typeof Uint8ClampedArray&&fe.push(Uint8ClampedArray),"undefined"!=typeof CanvasPixelArray&&fe.push(CanvasPixelArray));var ce={isChrome:te,chromeVersion:function(){return te()&&V},isSafari:re,safariVersion:function(){return re()&&W},isWebkit:ae,webkitVersion:function(){return ae()&&P},isInternetExplorer:ie,internetExplorerVersion:function(){return ie()&&q},isEdge:ue,edgeVersion:function(){return ue()&&G},isFirefox:oe,firefoxVersion:function(){return oe()&&j},isWindows:function(){return a.defined(_)||(_=/Windows/i.test(L.appVersion)),_},isNodeJs:function(){return a.defined(D)||(D="object"==typeof process&&"[object process]"===Object.prototype.toString.call(process)),D},hardwareConcurrency:a.defaultValue(L.hardwareConcurrency,3),supportsPointerEvents:function(){return a.defined(Y)||(Y=!oe()&&"undefined"!=typeof PointerEvent&&(!a.defined(L.pointerEnabled)||L.pointerEnabled)),Y},supportsImageRenderingPixelated:se,supportsWebP:le,supportsWebPSync:function(){return a.defined(X)||le(),Z},imageRenderingValue:function(){return se()?Q:void 0},typedArrayTypes:fe,isPCBroswer:function(){var e=window.navigator.userAgent.toLowerCase(),n="ipad"==e.match(/ipad/i),t="iphone os"==e.match(/iphone os/i),r="midp"==e.match(/midp/i),a="rv:*******"==e.match(/rv:*******/i),i="ucweb"==e.match(/ucweb/i),u="android"==e.match(/android/i),o="windows ce"==e.match(/windows ce/i),s="windows mobile"==e.match(/windows mobile/i);return!(n||t||r||a||i||u||o||s)}};ce.supportsFullscreen=function(){return ee.supportsFullscreen()},ce.supportsTypedArrays=function(){return"undefined"!=typeof ArrayBuffer},ce.supportsWebWorkers=function(){return"undefined"!=typeof Worker},ce.supportsWebAssembly=function(){return"undefined"!=typeof WebAssembly&&!ce.isEdge()},ce.supportsOffscreenCanvas=function(){return"undefined"!=typeof OffscreenCanvas&&!ce.isEdge()},e.FeatureDetection=ce,e.Matrix3=s,e.Matrix4=M,e.PrimitiveType=K}));
