define(["require","./createTaskProcessorWorker","./Check-7b2a090c","./FeatureDetection-806b12f0","./when-b60132fc","./Cartesian2-db21342c","./Cartographic-3309dd0d","./Cartesian4-3ca25aab","./Color-2a095a27","./ComponentDatatype-c140a87d","./getStringFromTypedArray-c37342c0","./buildModuleUrl-9085faaa","./S3MCompressType-75aa9ff0","./IndexDatatype-8a5eead4","./RuntimeError-4a5c8994","./Rectangle-dee65d21","./BoundingRectangle-143a34da","./S3MPixelFormat-4f2b7689","./pako_inflate-f73548c4","./arrayFill-4513d7ad","./CompressedTextureBuffer-290a1ff4","./PixelFormat-9345f1c7","./Math-119be1a3","./WebGLConstants-4ae0db90","./Event-16a2dfbf"],(function(t,e,r,a,n,A,o,i,B,E,C,s,y,d,l,f,u,P,c,p,T,L,D,g,F){"use strict";function M(t,e,r){if("function"==typeof t.slice)return t.slice(e,r);for(var n=Array.prototype.slice.call(t,e,r),A=a.FeatureDetection.typedArrayTypes,o=A.length,i=0;i<o;++i)if(t instanceof A[i]){n=new A[i](n);break}return n}function m(){}var I;function v(t,e,r){var a,A=t.num_points(),o=r.num_components(),i=new I.AttributeQuantizationTransform;if(i.InitFromAttribute(r)){for(var B=new Array(o),C=0;C<o;++C)B[C]=i.min_value(C);a={quantizationBits:i.quantization_bits(),minValues:B,range:i.range(),octEncoded:!1}}I.destroy(i),(i=new I.AttributeOctahedronTransform).InitFromAttribute(r)&&(a={quantizationBits:i.quantization_bits(),octEncoded:!0}),I.destroy(i);var s,y=A*o;s=n.defined(a)?function(t,e,r,a,n){var A,o;a.quantizationBits<=8?(o=new I.DracoUInt8Array,A=new Uint8Array(n),e.GetAttributeUInt8ForAllPoints(t,r,o)):(o=new I.DracoUInt16Array,A=new Uint16Array(n),e.GetAttributeUInt16ForAllPoints(t,r,o));for(var i=0;i<n;++i)A[i]=o.GetValue(i);return I.destroy(o),A}(t,e,r,a,y):function(t,e,r,a){var n,A;switch(r.data_type()){case 1:case 11:A=new I.DracoInt8Array,n=new Int8Array(a),e.GetAttributeInt8ForAllPoints(t,r,A);break;case 2:A=new I.DracoUInt8Array,n=new Uint8Array(a),e.GetAttributeUInt8ForAllPoints(t,r,A);break;case 3:A=new I.DracoInt16Array,n=new Int16Array(a),e.GetAttributeInt16ForAllPoints(t,r,A);break;case 4:A=new I.DracoUInt16Array,n=new Uint16Array(a),e.GetAttributeUInt16ForAllPoints(t,r,A);break;case 5:case 7:A=new I.DracoInt32Array,n=new Int32Array(a),e.GetAttributeInt32ForAllPoints(t,r,A);break;case 6:case 8:A=new I.DracoUInt32Array,n=new Uint32Array(a),e.GetAttributeUInt32ForAllPoints(t,r,A);break;case 9:case 10:A=new I.DracoFloat32Array,n=new Float32Array(a),e.GetAttributeFloatForAllPoints(t,r,A)}for(var o=0;o<a;++o)n[o]=A.GetValue(o);return I.destroy(A),n}(t,e,r,y);var d=E.ComponentDatatype.fromTypedArray(s);return{array:s,data:{componentsPerAttribute:o,componentDatatype:d,byteOffset:r.byte_offset(),byteStride:E.ComponentDatatype.getSizeInBytes(d)*o,normalized:r.normalized(),quantization:a}}}var _=new o.Cartesian3(40680631590769,40680631590769,40408299984661.445),S=new o.Cartesian3,O=new o.Cartesian3;function N(t,e,r,a){var A=Math.cos(e);S.x=A*Math.cos(t),S.y=A*Math.sin(t),S.z=Math.sin(e),S=o.Cartesian3.normalize(S,S),o.Cartesian3.multiplyComponents(_,S,O);var i=Math.sqrt(o.Cartesian3.dot(S,O));return O=o.Cartesian3.divideByScalar(O,i,O),S=o.Cartesian3.multiplyByScalar(S,r,S),n.defined(a)||(a=new o.Cartesian3),o.Cartesian3.add(O,S,a)}var h=new a.Matrix4,R=new a.Matrix4,G=new o.Cartesian3,x=new o.Cartographic;function b(t,e,r,B,C,s,d,l){var u=void 0,P=void 0,c=void 0,p=void 0,T=r.vertexAttributes,L=r.attrLocation;if(r.nCompressOptions=0,n.defined(B.posUniqueID)&&B.posUniqueID>=0){n.defined(l)||(r.nCompressOptions|=y.VertexCompressOption.SVC_Vertex);var D=e.GetAttribute(t,B.posUniqueID),g=v(t,e,D),F=g.data.componentsPerAttribute;r.verticesCount=g.array.length/F,r.vertCompressConstant=g.data.quantization.range/(1<<g.data.quantization.quantizationBits);var M=g.data.quantization.minValues;r.minVerticesValue=new i.Cartesian4(M[0],M[1],M[2],1),F>3&&(r.minVerticesValue.w=M[3]);var m=r.verticesCount;if(s&&(u=new o.Cartographic,P=new o.Cartographic,c=new Float32Array(2*m),p=new Float64Array(2*m)),n.defined(l)){var I=g.array,_=3===F?o.Cartesian3.unpackArray(I):i.Cartesian4.unpackArray(I);for(let t=0,e=_.length;t<e;t++){let e=_[t];o.Cartesian3.multiplyByScalar(e,r.vertCompressConstant,e),o.Cartesian3.add(e,r.minVerticesValue,e)}var S=a.Matrix4.multiply(l.sphereMatrix,l.geoMatrix,h),O=a.Matrix4.multiply(l.ellipsoidMatrix,l.geoMatrix,R);a.Matrix4.inverse(O,O);var b=new f.Ellipsoid(6378137,6378137,6378137);for(let t=0,e=_.length;t<e;t++){let e=_[t];a.Matrix4.multiplyByPoint(S,e,G);let r=b.cartesianToCartographic(G,x);s&&(p[2*t]=r.longitude,p[2*t+1]=r.latitude,0===t?(u.longitude=r.longitude,u.latitude=r.latitude,P.longitude=r.longitude,P.latitude=r.latitude):(u.longitude=Math.max(r.longitude,u.longitude),u.latitude=Math.max(r.latitude,u.latitude),P.longitude=Math.min(r.longitude,P.longitude),P.latitude=Math.min(r.latitude,P.latitude)));let n=N(r.longitude,r.latitude,r.height,G);a.Matrix4.multiplyByPoint(O,n,e)}var U=new Array(3*_.length);3===F?o.Cartesian3.packArray(_,U):i.Cartesian4.packArray(_,U),g.array=new Float32Array(U),g.data.componentDatatype=E.ComponentDatatype.FLOAT,g.data.byteStride=4*F}if(L.aPosition=T.length,T.push({index:L.aPosition,typedArray:g.array,componentsPerAttribute:F,componentDatatype:g.data.componentDatatype,offsetInBytes:g.data.byteOffset,strideInBytes:g.data.byteStride,normalize:g.data.normalized}),!n.defined(l)&&s)for(var K=new o.Cartesian3,H=new o.Cartesian3,w=new o.Cartographic,V=0;V<m;V++)a.Matrix4.multiplyByPoint(C,o.Cartesian3.fromElements(g.array[3*V]*r.vertCompressConstant+M[0],g.array[3*V+1]*r.vertCompressConstant+M[1],g.array[3*V+2]*r.vertCompressConstant+M[2],K),H),w=o.Cartographic.fromCartesian(H),p[2*V]=w.longitude,p[2*V+1]=w.latitude,0===V?(u.longitude=w.longitude,u.latitude=w.latitude,P.longitude=w.longitude,P.latitude=w.latitude):(u.longitude=Math.max(w.longitude,u.longitude),u.latitude=Math.max(w.latitude,u.latitude),P.longitude=Math.min(w.longitude,P.longitude),P.latitude=Math.min(w.latitude,P.latitude));if(s){for(V=0;V<m;V++)c[2*V]=p[2*V]-P.longitude,c[2*V+1]=p[2*V+1]-P.latitude;L.img=T.length,T.push({index:L.img,typedArray:c,componentsPerAttribute:2,componentDatatype:E.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:2*Float32Array.BYTES_PER_ELEMENT,normalize:!1}),d.max=u,d.min=P}}if(n.defined(B.normalUniqueID)&&B.normalUniqueID>=0){r.nCompressOptions|=y.VertexCompressOption.SVC_Normal;var Y=e.GetAttribute(t,B.normalUniqueID),J=v(t,e,Y),k=J.data.quantization;r.normalRangeConstant=(1<<k.quantizationBits)-1,L.aNormal=T.length,T.push({index:L.aNormal,typedArray:J.array,componentsPerAttribute:J.data.componentsPerAttribute,componentDatatype:J.data.componentDatatype,offsetInBytes:J.data.byteOffset,strideInBytes:J.data.byteStride,normalize:J.data.normalized})}if(n.defined(B.colorUniqueID)&&B.colorUniqueID>=0){r.nCompressOptions|=y.VertexCompressOption.SVC_VertexColor;var W=e.GetAttribute(t,B.colorUniqueID),Z=v(t,e,W);L.aColor=T.length,T.push({index:L.aColor,typedArray:Z.array,componentsPerAttribute:Z.data.componentsPerAttribute,componentDatatype:Z.data.componentDatatype,offsetInBytes:Z.data.byteOffset,strideInBytes:Z.data.byteStride,normalize:Z.data.normalized})}for(V=0;V<B.texCoordUniqueIDs.length;V++){r.texCoordCompressConstant=[],r.minTexCoordValue=[];var X=B.texCoordUniqueIDs[V];if(!(X<0)){var Q=e.GetAttribute(t,X),z=v(t,e,Q);if(n.defined(z.data.quantization)){r.nCompressOptions|=y.VertexCompressOption.SVC_TexutreCoord,r.texCoordCompressConstant.push(z.data.quantization.range/(1<<z.data.quantization.quantizationBits));M=z.data.quantization.minValues;r.minTexCoordValue.push(new A.Cartesian2(M[0],M[1]))}var j="aTexCoord"+V;L[j]=T.length,T.push({index:L[j],typedArray:z.array,componentsPerAttribute:z.data.componentsPerAttribute,componentDatatype:z.data.componentDatatype,offsetInBytes:z.data.byteOffset,strideInBytes:z.data.byteStride,normalize:z.data.normalized}),r.textureCoordIsW=!0}}for(V=0;V<B.vertexAttrUniqueIDs.length;V++){var q=B.vertexAttrUniqueIDs[V];if(!(q<0)){var $=e.GetAttribute(t,q),tt=v(t,e,$);L.aVertexWeight=T.length,T.push({index:L.aVertexWeight,typedArray:tt.array,componentsPerAttribute:tt.data.componentsPerAttribute,componentDatatype:tt.data.componentDatatype,offsetInBytes:tt.data.byteOffset,strideInBytes:tt.data.byteStride,normalize:tt.data.normalized}),r.customVertexAttribute={VertexWeight:0}}}}m.dracoDecodePointCloud=function(t,e,r,a,n){for(var A=new(I=t).Decoder,o=["POSITION","NORMAL","COLOR"],i=0;i<o.length;++i)A.SkipAttributeTransform(I[o[i]]);var B=new I.DecoderBuffer;if(B.Init(e,r),A.GetEncodedGeometryType(B)!==I.POINT_CLOUD)throw new l.RuntimeError("Draco geometry type must be POINT_CLOUD.");var E=new I.PointCloud,C=A.DecodeBufferToPointCloud(B,E);if(!C.ok()||0===E.ptr)throw new l.RuntimeError("Error decoding draco point cloud: "+C.error_msg());I.destroy(B),b(E,A,a,n),I.destroy(E),I.destroy(A)},m.dracoDecodeMesh=function(t,e,r,n,A,o,i,B,E,C){for(var s=new(I=t).Decoder,y=["POSITION","NORMAL","COLOR","TEX_COORD"],f=0;f<y.length;++f)s.SkipAttributeTransform(I[y[f]]);var u=new I.DecoderBuffer;if(u.Init(e,r),s.GetEncodedGeometryType(u)!==I.TRIANGULAR_MESH)throw new l.RuntimeError("Unsupported draco mesh geometry type.");var P=new I.Mesh;if(!s.DecodeBufferToMesh(u,P).ok()||0===P.ptr)return!1;I.destroy(u),b(P,s,n,o,i,B,E,C);var c=function(t,e){for(var r=t.num_points(),a=t.num_faces(),n=new I.DracoInt32Array,A=3*a,o=d.IndexDatatype.createTypedArray(r,A),i=0,B=0;B<a;++B)e.GetFaceFromMesh(t,B,n),o[i+0]=n.GetValue(0),o[i+1]=n.GetValue(1),o[i+2]=n.GetValue(2),i+=3;var E=d.IndexDatatype.UNSIGNED_SHORT;return o instanceof Uint32Array&&(E=d.IndexDatatype.UNSIGNED_INT),I.destroy(n),{typedArray:o,numberOfIndices:A,indexDataType:E}}(P,s);A.indicesTypedArray=c.typedArray,A.indicesCount=c.numberOfIndices,A.indexType=c.indexDataType,A.primitiveType=a.PrimitiveType.TRIANGLES,I.destroy(P),I.destroy(s)};var U=Object.freeze({OSGBFile:0,OSGBCacheFile:1,ClampGroundPolygon:2,ClampObjectPolygon:3,ClampGroundLine:4,ClampObjectLine:5,IconPoint:6,Text:7,PointCloudFile:8,ExtendRegion3D:9,ExtendClampPolygonCache:10,PolylineEffect:11,RegionEffect:12,ClampGroundAndObjectLineCache:13,ClampGroundRealtimeRasterCache:14});function K(){}function H(t){var e=new s.BoundingSphere,r=t.instanceBounds;if(!n.defined(r))return e;var a=new o.Cartesian3(r[0],r[1],r[2]),A=new o.Cartesian3(r[3],r[4],r[5]),i=o.Cartesian3.lerp(a,A,.5,new o.Cartesian3),B=o.Cartesian3.distance(i,a);return e.center=i,e.radius=B,e}function w(t){var e,r,a=new s.BoundingSphere,A=new o.Cartesian3,i=t.vertexAttributes[0],B=i.componentsPerAttribute,E=n.defined(t.nCompressOptions)&&(t.nCompressOptions&y.VertexCompressOption.SVC_Vertex)===y.VertexCompressOption.SVC_Vertex,C=1;E?(C=t.vertCompressConstant,e=new o.Cartesian3(t.minVerticesValue.x,t.minVerticesValue.y,t.minVerticesValue.z),r=new Uint16Array(i.typedArray.buffer,i.typedArray.byteOffset,i.typedArray.byteLength/2)):r=new Float32Array(i.typedArray.buffer,i.typedArray.byteOffset,i.typedArray.byteLength/4);for(var d=[],l=0;l<t.verticesCount;l++)o.Cartesian3.fromArray(r,B*l,A),E&&(A=o.Cartesian3.multiplyByScalar(A,C,A),A=o.Cartesian3.add(A,e,A)),d.push(o.Cartesian3.clone(A));return s.BoundingSphere.fromPoints(d,a),d.length=0,a}function V(t){var e,r,a=new s.BoundingSphere,A=new o.Cartesian3,i=n.defined(t.nCompressOptions)&&(t.nCompressOptions&y.VertexCompressOption.SVC_Vertex)===y.VertexCompressOption.SVC_Vertex,B=t.vertexAttributes[0],E=B.componentsPerAttribute,C=1;i?(C=t.vertCompressConstant,r=new o.Cartesian3(t.minVerticesValue.x,t.minVerticesValue.y,t.minVerticesValue.z),e=new Uint16Array(B.typedArray.buffer,B.typedArray.byteOffset,B.typedArray.byteLength/2)):e=new Float32Array(B.typedArray.buffer,B.typedArray.byteOffset,B.typedArray.byteLength/4);for(var d=[],l=0;l<t.verticesCount;l++)o.Cartesian3.fromArray(e,E*l,A),i&&(A=o.Cartesian3.multiplyByScalar(A,C,A),A=o.Cartesian3.add(A,r,A)),d.push(o.Cartesian3.clone(A));return s.BoundingSphere.fromPoints(d,a),d.length=0,a}function Y(t){var e,r,a=n.defined(t.nCompressOptions)&&(t.nCompressOptions&y.VertexCompressOption.SVC_Vertex)===y.VertexCompressOption.SVC_Vertex,A=new s.BoundingSphere,B=new o.Cartesian3,E=new o.Cartesian3,C=t.vertexAttributes[0],d=C.componentsPerAttribute,l=t.attrLocation.aPosition,f=t.vertexAttributes[l],u=t.attrLocation.aTexCoord5,P=t.vertexAttributes[u],c=P.componentsPerAttribute;a?(d=3,c=3,e=k(t,f),r=function(t,e,r){for(var a,n,A,o=e.componentsPerAttribute,B=t.texCoordCompressConstant[r],E=new i.Cartesian4(t.minTexCoordValue[r].x,t.minTexCoordValue[r].y,t.minTexCoordValue[r].z,t.minTexCoordValue[r].w),C=new Uint16Array(e.typedArray.buffer,e.typedArray.byteOffset,e.typedArray.byteLength/2),s=new Float32Array(3*t.verticesCount),y=0;y<t.verticesCount;y++)a=C[o*y]*B+E.x,n=C[o*y+1]*B+E.y,A=C[o*y+2]*B+E.z,s[3*y]=a,s[3*y+1]=n,s[3*y+2]=A;return s}(t,P,5)):(e=new Float32Array(C.typedArray.buffer,C.typedArray.byteOffset,C.typedArray.byteLength/4),r=new Float32Array(P.typedArray.buffer,P.typedArray.byteOffset,P.typedArray.byteLength/4));for(var p=[],T=0;T<t.verticesCount;T++)o.Cartesian3.fromArray(e,d*T,B),o.Cartesian3.fromArray(r,c*T,E),o.Cartesian3.add(B,E,B),p.push(o.Cartesian3.clone(B));return s.BoundingSphere.fromPoints(p,A),p.length=0,A}function J(t){var e=a.PrimitiveType.TRIANGLES;switch(t){case 1:e=a.PrimitiveType.POINTS;break;case 2:e=a.PrimitiveType.LINES;break;case 3:e=a.PrimitiveType.LINE_STRIP;break;case 4:e=a.PrimitiveType.TRIANGLES}return e}function k(t,e){for(var r,a,n,A=e.componentsPerAttribute,i=t.vertCompressConstant,B=new o.Cartesian3(t.minVerticesValue.x,t.minVerticesValue.y,t.minVerticesValue.z),E=new Uint16Array(e.typedArray.buffer,e.typedArray.byteOffset,e.typedArray.byteLength/2),C=new Float32Array(3*t.verticesCount),s=0;s<t.verticesCount;s++)r=E[A*s]*i+B.x,a=E[A*s+1]*i+B.y,n=E[A*s+2]*i+B.z,C[3*s]=r,C[3*s+1]=a,C[3*s+2]=n;return C}K.calcBoundingSphereInWorker=function(t,e){return e.instanceIndex>-1?H(e):n.defined(e.clampRegionEdge)?Y(e):t>=U.ClampGroundPolygon&&t<=U.ClampObjectLine?V(e):t==U.ClampGroundAndObjectLineCache?Y(e):w(e)},K.calcBoundingSphere=function(t,e,r){var a,A=t._fileType;return a=e.instanceIndex>-1?H(e):n.defined(e.clampRegionEdge)?Y(e):A>=U.ClampGroundPolygon&&A<=U.ClampObjectLine?V(e):A==U.ClampGroundAndObjectLineCache?Y(e):w(e),s.BoundingSphere.transform(a,r,a),a},K.calcBoundingRectangle=function(t,e){var r;return t._fileType===U.ClampGroundPolygon&&(r=function(t){var e,r,a=n.defined(t.nCompressOptions)&&(t.nCompressOptions&y.VertexCompressOption.SVC_Vertex)===y.VertexCompressOption.SVC_Vertex,i=new u.BoundingRectangle,B=t.vertexAttributes[0],E=B.componentsPerAttribute,C=1;a?(C=t.vertCompressConstant,r=new o.Cartesian3(t.minVerticesValue.x,t.minVerticesValue.y,t.minVerticesValue.z),e=new Uint16Array(B.typedArray.buffer,B.typedArray.byteOffset,B.typedArray.byteLength/2)):e=new Float32Array(B.typedArray.buffer,B.typedArray.byteOffset,B.typedArray.byteLength/4);for(var s=[],d=0;d<t.verticesCount;d++){var l=e[E*d],f=e[E*d+1];a&&(l=C*l+r.x,f=C*f+r.y),s.push(new A.Cartesian2(l,f))}return u.BoundingRectangle.fromPoints(s,i),s.length=0,i}(e)),r},K.createEdge=function(t,e){if(!(e.length<1)){var r=function(t){for(var e=[],r=t.length,n=0;n<r;n++){var A=J(t[n].primitiveType);A!==a.PrimitiveType.LINES&&A!==a.PrimitiveType.LINE_STRIP||e.push(t[n])}return e}(e);if(0!=r.length){var A,i=function(t){for(var e=0,r=t.length,n=0;n<r;n++){var A=t[n],o=J(A.primitiveType);o==a.PrimitiveType.LINES?e+=A.indicesCount/2:o==a.PrimitiveType.LINE_STRIP&&e++}return e}(r),B=t.attrLocation.aPosition,C=t.vertexAttributes[B],s=n.defined(t.nCompressOptions)&&(t.nCompressOptions&y.VertexCompressOption.SVC_Vertex)===y.VertexCompressOption.SVC_Vertex,d=C.componentsPerAttribute;s?(d=3,A=k(t,C)):A=new Float32Array(C.typedArray.buffer,C.typedArray.byteOffset,C.typedArray.byteLength/4);for(var l=function(t){for(var e=0,r=t.length,a=0;a<r;a++)e+=t[a].indicesCount;return e}(r),f=function(t,e,r){for(var n,A=[],i=r.length,B=0;B<i;B++){var E,C=r[B];E=0===C.indexType?new Uint16Array(C.indicesTypedArray.buffer,C.indicesTypedArray.byteOffset,C.indicesTypedArray.byteLength/2):new Uint32Array(C.indicesTypedArray.buffer,C.indicesTypedArray.byteOffset,C.indicesTypedArray.byteLength/4);var s=J(C.primitiveType);if(s==a.PrimitiveType.LINES)for(n=0;n<C.indicesCount;n+=2){var y=[],d=new o.Cartesian3;d.x=t[E[n]*e],d.y=t[E[n]*e+1],d.z=t[E[n]*e+2],y.push(d);var l=new o.Cartesian3;l.x=t[E[n+1]*e],l.y=t[E[n+1]*e+1],l.z=t[E[n+1]*e+2],y.push(l),A.push(y)}else if(s==a.PrimitiveType.LINE_STRIP){for(y=[],n=0;n<C.indicesCount;n++){var f=new o.Cartesian3;f.x=t[E[n]*e],f.y=t[E[n]*e+1],f.z=t[E[n]*e+2],y.push(f)}A.push(y)}}return A}(A,d,r),u=4*l-4*i,P=new Float32Array(3*u),c=new Float32Array(3*u),p=new Float32Array(3*u),T=new Int8Array(2*u),L=0,D=0;D<i;D++){for(var g=f[D].length,F=0;F<g;F++){var M=4*L-4*D,m=3*M+12*F,I=f[D][F];0!=F&&(P[m-6]=I.x,P[m-5]=I.y,P[m-4]=I.z,P[m-3]=I.x,P[m-2]=I.y,P[m-1]=I.z),F!=g-1&&(P[m]=I.x,P[m+1]=I.y,P[m+2]=I.z,P[m+3]=I.x,P[m+4]=I.y,P[m+5]=I.z);var v=I;F+1<g&&(v=f[D][F+1]),0!=F&&(p[m-6]=v.x,p[m-5]=v.y,p[m-4]=v.z,p[m-3]=v.x,p[m-2]=v.y,p[m-1]=v.z),F!=g-1&&(p[m]=v.x,p[m+1]=v.y,p[m+2]=v.z,p[m+3]=v.x,p[m+4]=v.y,p[m+5]=v.z);var _=I;F>=1&&(_=f[D][F-1]),0!=F&&(c[m-6]=_.x,c[m-5]=_.y,c[m-4]=_.z,c[m-3]=_.x,c[m-2]=_.y,c[m-1]=_.z),F!=g-1&&(c[m]=_.x,c[m+1]=_.y,c[m+2]=_.z,c[m+3]=_.x,c[m+4]=_.y,c[m+5]=_.z),m=2*M+8*F,0!=F&&(T[m-4]=-1,T[m-3]=-1,T[m-2]=1,T[m-1]=-1),F!=g-1&&(T[m]=-1,T[m+1]=1,T[m+2]=1,T[m+3]=1)}L+=f[D].length}var S={vertexAttributes:[],attrLocation:{}},O=S.vertexAttributes,N=S.attrLocation;S.instanceCount=0,S.instanceMode=0,N.aPosition=0,O.push({index:N.aPosition,typedArray:P,componentsPerAttribute:3,componentDatatype:E.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:3*Float32Array.BYTES_PER_ELEMENT,normalize:!1}),N.aNormal=1,O.push({index:N.aNormal,typedArray:c,componentsPerAttribute:3,componentDatatype:E.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:3*Float32Array.BYTES_PER_ELEMENT,normalize:!1}),N.aTexCoord0=2,O.push({index:N.aTexCoord0,typedArray:p,componentsPerAttribute:3,componentDatatype:E.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:3*Float32Array.BYTES_PER_ELEMENT,normalize:!1}),N.aTexCoord1=3,O.push({index:N.aTexCoord1,typedArray:T,componentsPerAttribute:2,componentDatatype:E.ComponentDatatype.BYTE,offsetInBytes:0,strideInBytes:2*Int8Array.BYTES_PER_ELEMENT,normalize:!1});for(var h=[],R=0;R<f.length;R++)h.push(f[R].length);var G=function(t,e,r,n){var A,o={};o.indicesCount=6*(t-e),o.indexType=n>65535?1:0,o.primitiveType=a.PrimitiveType.TRIANGLES,A=0===o.indexType?new Uint16Array(o.indicesCount):new Uint32Array(o.indicesCount);for(var i=0,B=0;B<e;B++){for(var E=0;E<r[B]-1;E++)A[6*(i-B+E)]=4*(i-B+E),A[6*(i-B+E)+1]=4*(i-B+E)+2,A[6*(i-B+E)+2]=4*(i-B+E)+1,A[6*(i-B+E)+3]=4*(i-B+E)+1,A[6*(i-B+E)+4]=4*(i-B+E)+2,A[6*(i-B+E)+5]=4*(i-B+E)+3;i+=r[B]}return o.indicesTypedArray=A,o}(l,i,h,u);return{vertexPackage:S,indexPackage:G}}}};var W,Z,X=Object.freeze({S3M:49,S3M4:1}),Q=function(){var t=new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,3,2,0,0,5,3,1,0,1,12,1,0,10,22,2,12,0,65,0,65,0,65,0,252,10,0,0,11,7,0,65,0,253,15,26,11]),e=new Uint8Array([32,0,65,2,1,106,34,33,3,128,11,4,13,64,6,253,10,7,15,116,127,5,8,12,40,16,19,54,20,9,27,255,113,17,42,67,24,23,146,148,18,14,22,45,70,69,56,114,101,21,25,63,75,136,108,28,118,29,73,115]);if("object"!=typeof WebAssembly)return{supported:!1};var r,a="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";WebAssembly.validate(t)&&(a="B9h79tEBBBE5V9gBB9gVUUUUUEU9gIUUUB9gDUUB9gEUEUIMXBBEBEEDIDIDLLVE9wEEEVIEBEOWEUEC+Q/aEKR/LEdO9tw9t9vv95DBh9f9f939h79t9f9j9h229f9jT9vv7BB8a9tw79o9v9wT9f9kw9j9v9kw9WwvTw949C919m9mwvBDy9tw79o9v9wT9f9kw9j9v9kw69u9kw949C919m9mwvBLe9tw79o9v9wT9f9kw9j9v9kw69u9kw949Twg91w9u9jwBVl9tw79o9v9wT9f9kw9j9v9kws9p2Twv9P9jTBOk9tw79o9v9wT9f9kw9j9v9kws9p2Twv9R919hTBWl9tw79o9v9wT9f9kw9j9v9kws9p2Twvt949wBQL79iv9rBKQ/j6XLBZIK9+EVU8jJJJJBCZ9rHBCBHEINCBHDCBHIINABCWFADFAICJUAEAD4CEgGLe86BBAIALFHIADCEFGDCW9HMBKAEC+Q+YJJBFAI86BBAECITC+Q1JJBFAB8pIW83IBAECEFGECJD9HMBKK1HLSUD97EUO978jJJJJBCJ/KB9rGV8kJJJJBC9+HODNADCEFAL0MBCUHOAIrBBC+gE9HMBAVAIALFGRAD9rAD/8QBBCJ/ABAD9uC/wfBgGOCJDAOCJD6eHWAICEFHOCBHdDNINAdAE9PMEAWAEAd9rAdAWFAE6eHQDNDNADtMBAQCSFGLC9wgGKCI2HXAKCETHMALCL4CIFCD4HpCBHSINAOHZCBHhDNINDNARAZ9rAp9PMBCBHOXVKAVCJ/CBFAhAK2FHoAZApFHOCBHIDNAKC/AB6MBARAO9rC/gB6MBCBHLINAoALFHIDNDNDNDNDNAZALCO4FrBBGaCIgpLBEDIBKAICBPhPKLBXIKAIAOPBBLAOPBBBGcCLP+MEAcPMBZEhDoIaLcVxOqRlGcCDP+MEAcPMBZEhDoIaLcVxOqRlC+D+G+MkPhP9OGxCIPSP8jGcP5B9CJf/8/4/w/g/AB9+9Cu1+nGqCITC+Q1JJBFPBIBAqC+Q+YJJBFPBBBGlAlPMBBBBBBBBBBBBBBBBAcP5E9CJf/8/4/w/g/AB9+9Cu1+nGqCITC+Q1JJBFPBIBP9uPMBEDILVORZhoacxqlPpAxAcP9SPKLBAOCLFAlPqBFAqC+Q+YJJBFrBBFHOXDKAIAOPBBWAOPBBBGcCLP+MEAcPMBZEhDoIaLcVxOqRlC+P+e+8/4BPhP9OGxCSPSP8jGcP5B9CJf/8/4/w/g/AB9+9Cu1+nGqCITC+Q1JJBFPBIBAqC+Q+YJJBFPBBBGlAlPMBBBBBBBBBBBBBBBBAcP5E9CJf/8/4/w/g/AB9+9Cu1+nGqCITC+Q1JJBFPBIBP9uPMBEDILVORZhoacxqlPpAxAcP9SPKLBAOCWFAlPqBFAqC+Q+YJJBFrBBFHOXEKAIAOPBBBPKLBAOCZFHOKDNDNDNDNDNAaCD4CIgpLBEDIBKAICBPhPKLZXIKAIAOPBBLAOPBBBGcCLP+MEAcPMBZEhDoIaLcVxOqRlGcCDP+MEAcPMBZEhDoIaLcVxOqRlC+D+G+MkPhP9OGxCIPSP8jGcP5B9CJf/8/4/w/g/AB9+9Cu1+nGqCITC+Q1JJBFPBIBAqC+Q+YJJBFPBBBGlAlPMBBBBBBBBBBBBBBBBAcP5E9CJf/8/4/w/g/AB9+9Cu1+nGqCITC+Q1JJBFPBIBP9uPMBEDILVORZhoacxqlPpAxAcP9SPKLZAOCLFAlPqBFAqC+Q+YJJBFrBBFHOXDKAIAOPBBWAOPBBBGcCLP+MEAcPMBZEhDoIaLcVxOqRlC+P+e+8/4BPhP9OGxCSPSP8jGcP5B9CJf/8/4/w/g/AB9+9Cu1+nGqCITC+Q1JJBFPBIBAqC+Q+YJJBFPBBBGlAlPMBBBBBBBBBBBBBBBBAcP5E9CJf/8/4/w/g/AB9+9Cu1+nGqCITC+Q1JJBFPBIBP9uPMBEDILVORZhoacxqlPpAxAcP9SPKLZAOCWFAlPqBFAqC+Q+YJJBFrBBFHOXEKAIAOPBBBPKLZAOCZFHOKDNDNDNDNDNAaCL4CIgpLBEDIBKAICBPhPKLAXIKAIAOPBBLAOPBBBGcCLP+MEAcPMBZEhDoIaLcVxOqRlGcCDP+MEAcPMBZEhDoIaLcVxOqRlC+D+G+MkPhP9OGxCIPSP8jGcP5B9CJf/8/4/w/g/AB9+9Cu1+nGqCITC+Q1JJBFPBIBAqC+Q+YJJBFPBBBGlAlPMBBBBBBBBBBBBBBBBAcP5E9CJf/8/4/w/g/AB9+9Cu1+nGqCITC+Q1JJBFPBIBP9uPMBEDILVORZhoacxqlPpAxAcP9SPKLAAOCLFAlPqBFAqC+Q+YJJBFrBBFHOXDKAIAOPBBWAOPBBBGcCLP+MEAcPMBZEhDoIaLcVxOqRlC+P+e+8/4BPhP9OGxCSPSP8jGcP5B9CJf/8/4/w/g/AB9+9Cu1+nGqCITC+Q1JJBFPBIBAqC+Q+YJJBFPBBBGlAlPMBBBBBBBBBBBBBBBBAcP5E9CJf/8/4/w/g/AB9+9Cu1+nGqCITC+Q1JJBFPBIBP9uPMBEDILVORZhoacxqlPpAxAcP9SPKLAAOCWFAlPqBFAqC+Q+YJJBFrBBFHOXEKAIAOPBBBPKLAAOCZFHOKDNDNDNDNDNAaCO4pLBEDIBKAICBPhPKL8wXIKAIAOPBBLAOPBBBGcCLP+MEAcPMBZEhDoIaLcVxOqRlGcCDP+MEAcPMBZEhDoIaLcVxOqRlC+D+G+MkPhP9OGxCIPSP8jGcP5B9CJf/8/4/w/g/AB9+9Cu1+nGaCITC+Q1JJBFPBIBAaC+Q+YJJBFPBBBGlAlPMBBBBBBBBBBBBBBBBAcP5E9CJf/8/4/w/g/AB9+9Cu1+nGaCITC+Q1JJBFPBIBP9uPMBEDILVORZhoacxqlPpAxAcP9SPKL8wAOCLFAlPqBFAaC+Q+YJJBFrBBFHOXDKAIAOPBBWAOPBBBGcCLP+MEAcPMBZEhDoIaLcVxOqRlC+P+e+8/4BPhP9OGxCSPSP8jGcP5B9CJf/8/4/w/g/AB9+9Cu1+nGaCITC+Q1JJBFPBIBAaC+Q+YJJBFPBBBGlAlPMBBBBBBBBBBBBBBBBAcP5E9CJf/8/4/w/g/AB9+9Cu1+nGaCITC+Q1JJBFPBIBP9uPMBEDILVORZhoacxqlPpAxAcP9SPKL8wAOCWFAlPqBFAaC+Q+YJJBFrBBFHOXEKAIAOPBBBPKL8wAOCZFHOKALC/ABFHIALCJEFAK0MEAIHLARAO9rC/fB0MBKKDNAIAK9PMBAICI4HLINDNARAO9rCk9PMBCBHOXRKAoAIFHaDNDNDNDNDNAZAICO4FrBBALCOg4CIgpLBEDIBKAaCBPhPKLBXIKAaAOPBBLAOPBBBGcCLP+MEAcPMBZEhDoIaLcVxOqRlGcCDP+MEAcPMBZEhDoIaLcVxOqRlC+D+G+MkPhP9OGxCIPSP8jGcP5B9CJf/8/4/w/g/AB9+9Cu1+nGqCITC+Q1JJBFPBIBAqC+Q+YJJBFPBBBGlAlPMBBBBBBBBBBBBBBBBAcP5E9CJf/8/4/w/g/AB9+9Cu1+nGqCITC+Q1JJBFPBIBP9uPMBEDILVORZhoacxqlPpAxAcP9SPKLBAOCLFAlPqBFAqC+Q+YJJBFrBBFHOXDKAaAOPBBWAOPBBBGcCLP+MEAcPMBZEhDoIaLcVxOqRlC+P+e+8/4BPhP9OGxCSPSP8jGcP5B9CJf/8/4/w/g/AB9+9Cu1+nGqCITC+Q1JJBFPBIBAqC+Q+YJJBFPBBBGlAlPMBBBBBBBBBBBBBBBBAcP5E9CJf/8/4/w/g/AB9+9Cu1+nGqCITC+Q1JJBFPBIBP9uPMBEDILVORZhoacxqlPpAxAcP9SPKLBAOCWFAlPqBFAqC+Q+YJJBFrBBFHOXEKAaAOPBBBPKLBAOCZFHOKALCDFHLAICZFGIAK6MBKKDNAOtMBAOHZAhCEFGhCLsMDXEKKCBHOXIKDNAKtMBAVCJDFASFHIAVASFPBDBHlCBHaINAIAVCJ/CBFAaFGLPBLBGxCEP9tAxCEPSGcP9OP9hP9RGxALAKFPBLBGkCEP9tAkAcP9OP9hP9RGkPMBZEhDoIaLcVxOqRlGyALAMFPBLBG8aCEP9tA8aAcP9OP9hP9RG8aALAXFPBLBGeCEP9tAeAcP9OP9hP9RGePMBZEhDoIaLcVxOqRlG3PMBEZhDIoaLVcxORqlGcAcPMBEDIBEDIBEDIBEDIAlP9uGlPeBbDBAIADFGLAlAcAcPMLVORLVORLVORLVORP9uGlPeBbDBALADFGLAlAcAcPMWdQKWdQKWdQKWdQKP9uGlPeBbDBALADFGLAlAcAcPMXMpSXMpSXMpSXMpSP9uGlPeBbDBALADFGLAlAyA3PMWdkyQK8aeXM35pS8e8fGcAcPMBEDIBEDIBEDIBEDIP9uGlPeBbDBALADFGLAlAcAcPMLVORLVORLVORLVORP9uGlPeBbDBALADFGLAlAcAcPMWdQKWdQKWdQKWdQKP9uGlPeBbDBALADFGLAlAcAcPMXMpSXMpSXMpSXMpSP9uGlPeBbDBALADFGLAlAxAkPMWkdyQ8aKeX3M5p8eS8fGxA8aAePMWkdyQ8aKeX3M5p8eS8fGkPMBEZhDIoaLVcxORqlGcAcPMBEDIBEDIBEDIBEDIP9uGlPeBbDBALADFGLAlAcAcPMLVORLVORLVORLVORP9uGlPeBbDBALADFGLAlAcAcPMWdQKWdQKWdQKWdQKP9uGlPeBbDBALADFGLAlAcAcPMXMpSXMpSXMpSXMpSP9uGlPeBbDBALADFGLAlAxAkPMWdkyQK8aeXM35pS8e8fGcAcPMBEDIBEDIBEDIBEDIP9uGlPeBbDBALADFGLAlAcAcPMLVORLVORLVORLVORP9uGlPeBbDBALADFGLAlAcAcPMWdQKWdQKWdQKWdQKP9uGlPeBbDBALADFGLAlAcAcPMXMpSXMpSXMpSXMpSP9uGlPeBbDBALADFHIAaCZFGaAK6MBKKASCLFGSAD6MBKKABAdAD2FAVCJDFAQAD2/8QBBAVAVCJDFAQCUFAD2FAD/8QBBKAQCBAOeAdFHdAOMBKC9+HOXEKCBC99ARAO9rADCAADCA0eseHOKAVCJ/KBF8kJJJJBAOKWBZ+BJJJBK+KoEZU8jJJJJBC/AE9rGV8kJJJJBC9+HODNAECI9uGRChFAL0MBCUHOAIrBBGWC/wEgC/gE9HMBAWCSgGdCE0MBAVC/ABFCfECJE/8KBAVCuF9CU83IBAVC8wF9CU83IBAVCYF9CU83IBAVCAF9CU83IBAVCkF9CU83IBAVCZF9CU83IBAV9CU83IWAV9CU83IBAIALFC9wFHQAICEFGWARFHKDNAEtMBCMCSAdCEseHXABHICBHdCBHMCBHpCBHLCBHOINDNAKAQ9NMBC9+HOXIKDNDNAWrBBGRC/vE0MBAVC/ABFARCL4CU7AOFCSgCITFGSYDLHZASYDBHhDNARCSgGSAX9PMBAVARCU7ALFCSgCDTFYDBAdASeHRAStHSDNDNADCD9HMBABAh87EBABCLFAR87EBABCDFAZ87EBXEKAIAhbDBAICWFARbDBAICLFAZbDBKAdASFHdAVC/ABFAOCITFGoARbDBAoAZbDLAVALCDTFARbDBAVC/ABFAOCEFCSgGOCITFGZAhbDBAZARbDLALASFHLAOCEFHOXDKDNDNASCSsMBAMASFASC987FCEFHMXEKAK8sBBGSCfEgHRDNDNASCU9MMBAKCEFHKXEKAK8sBEGSCfBgCRTARCfBgvHRDNASCU9MMBAKCDFHKXEKAK8sBDGSCfBgCpTARvHRDNASCU9MMBAKCIFHKXEKAK8sBIGSCfBgCxTARvHRDNASCU9MMBAKCLFHKXEKAKrBLC3TARvHRAKCVFHKKARCE4CBARCEg9r7AMFHMKDNDNADCD9HMBABAh87EBABCLFAM87EBABCDFAZ87EBXEKAIAhbDBAICWFAMbDBAICLFAZbDBKAVC/ABFAOCITFGRAMbDBARAZbDLAVALCDTFAMbDBAVC/ABFAOCEFCSgGOCITFGRAhbDBARAMbDLALCEFHLAOCEFHOXEKDNARCPE0MBAVALAQARCSgFrBBGSCL4GZ9rCSgCDTFYDBAdCEFGhAZeHRAVALAS9rCSgCDTFYDBAhAZtGoFGhASCSgGZeHSAZtHZDNDNADCD9HMBABAd87EBABCLFAS87EBABCDFAR87EBXEKAIAdbDBAICWFASbDBAICLFARbDBKAVALCDTFAdbDBAVC/ABFAOCITFGaARbDBAaAdbDLAVALCEFGLCSgCDTFARbDBAVC/ABFAOCEFCSgCITFGaASbDBAaARbDLAVALAoFCSgGLCDTFASbDBAVC/ABFAOCDFCSgGOCITFGRAdbDBARASbDLAOCEFHOALAZFHLAhAZFHdXEKAdCBAKrBBGaeGZARC/+EsGcFHRAaCSgHhDNDNAaCL4GoMBARCEFHSXEKARHSAVALAo9rCSgCDTFYDBHRKDNDNAhMBASCEFHdXEKASHdAVALAa9rCSgCDTFYDBHSKDNDNActMBAKCEFHaXEKAK8sBEGaCfEgHZDNDNAaCU9MMBAKCDFHaXEKAK8sBDGaCfBgCRTAZCfBgvHZDNAaCU9MMBAKCIFHaXEKAK8sBIGaCfBgCpTAZvHZDNAaCU9MMBAKCLFHaXEKAK8sBLGaCfBgCxTAZvHZDNAaCU9MMBAKCVFHaXEKAKCOFHaAKrBVC3TAZvHZKAZCE4CBAZCEg9r7AMFGMHZKDNDNAoCSsMBAaHcXEKAa8sBBGKCfEgHRDNDNAKCU9MMBAaCEFHcXEKAa8sBEGKCfBgCRTARCfBgvHRDNAKCU9MMBAaCDFHcXEKAa8sBDGKCfBgCpTARvHRDNAKCU9MMBAaCIFHcXEKAa8sBIGKCfBgCxTARvHRDNAKCU9MMBAaCLFHcXEKAaCVFHcAarBLC3TARvHRKARCE4CBARCEg9r7AMFGMHRKDNDNAhCSsMBAcHKXEKAc8sBBGKCfEgHSDNDNAKCU9MMBAcCEFHKXEKAc8sBEGKCfBgCRTASCfBgvHSDNAKCU9MMBAcCDFHKXEKAc8sBDGKCfBgCpTASvHSDNAKCU9MMBAcCIFHKXEKAc8sBIGKCfBgCxTASvHSDNAKCU9MMBAcCLFHKXEKAcCVFHKAcrBLC3TASvHSKASCE4CBASCEg9r7AMFGMHSKDNDNADCD9HMBABAZ87EBABCLFAS87EBABCDFAR87EBXEKAIAZbDBAICWFASbDBAICLFARbDBKAVC/ABFAOCITFGaARbDBAaAZbDLAVALCDTFAZbDBAVC/ABFAOCEFCSgCITFGaASbDBAaARbDLAVALCEFGLCSgCDTFARbDBAVC/ABFAOCDFCSgCITFGRAZbDBARASbDLAVALAotAoCSsvFGLCSgCDTFASbDBALAhtAhCSsvFHLAOCIFHOKAWCEFHWABCOFHBAICXFHIAOCSgHOALCSgHLApCIFGpAE6MBKKCBC99AKAQseHOKAVC/AEF8kJJJJBAOK/tLEDU8jJJJJBCZ9rHVC9+HODNAECVFAL0MBCUHOAIrBBC/+EgC/QE9HMBAV9CB83IWAICEFHOAIALFC98FHIDNAEtMBDNADCDsMBINDNAOAI6MBC9+SKAO8sBBGDCfEgHLDNDNADCU9MMBAOCEFHOXEKAO8sBEGDCfBgCRTALCfBgvHLDNADCU9MMBAOCDFHOXEKAO8sBDGDCfBgCpTALvHLDNADCU9MMBAOCIFHOXEKAO8sBIGDCfBgCxTALvHLDNADCU9MMBAOCLFHOXEKAOrBLC3TALvHLAOCVFHOKAVCWFALCEgCDTvGDALCD4CBALCE4CEg9r7ADYDBFGLbDBABALbDBABCLFHBAECUFGEMBXDKKINDNAOAI6MBC9+SKAO8sBBGDCfEgHLDNDNADCU9MMBAOCEFHOXEKAO8sBEGDCfBgCRTALCfBgvHLDNADCU9MMBAOCDFHOXEKAO8sBDGDCfBgCpTALvHLDNADCU9MMBAOCIFHOXEKAO8sBIGDCfBgCxTALvHLDNADCU9MMBAOCLFHOXEKAOrBLC3TALvHLAOCVFHOKABALCD4CBALCE4CEg9r7AVCWFALCEgCDTvGLYDBFGD87EBALADbDBABCDFHBAECUFGEMBKKCBC99AOAIseHOKAOK/xVDIUO978jJJJJBCA9rGI8kJJJJBDNDNADCL9HMBDNAEC98gGLtMBABHDCBHVINADADPBBBGOCkP+rECkP+sEP/6EGRAOCWP+rECkP+sEP/6EARP/gEAOCZP+rECkP+sEP/6EGWP/gEP/kEP/lEGdCBPhP+2EGQARCJJJJ94PhGKP9OP9RP/kEGRjBB/+9CPaARARP/mEAdAdP/mEAWAQAWAKP9OP9RP/kEGRARP/mEP/kEP/kEP/jEP/nEGWP/mEjBBN0PaGQP/kECfEPhP9OAOCJJJ94PhP9OP9QARAWP/mEAQP/kECWP+rECJ/+IPhP9OP9QAdAWP/mEAQP/kECZP+rECJJ/8RPhP9OP9QPKBBADCZFHDAVCLFGVAL6MBKKALAE9PMEAIAECIgGVCDTGDvCBCZAD9r/8KBAIABALCDTFGLAD/8QBBDNAVtMBAIAIPBLBGOCkP+rECkP+sEP/6EGRAOCWP+rECkP+sEP/6EARP/gEAOCZP+rECkP+sEP/6EGWP/gEP/kEP/lEGdCBPhP+2EGQARCJJJJ94PhGKP9OP9RP/kEGRjBB/+9CPaARARP/mEAdAdP/mEAWAQAWAKP9OP9RP/kEGRARP/mEP/kEP/kEP/jEP/nEGWP/mEjBBN0PaGQP/kECfEPhP9OAOCJJJ94PhP9OP9QARAWP/mEAQP/kECWP+rECJ/+IPhP9OP9QAdAWP/mEAQP/kECZP+rECJJ/8RPhP9OP9QPKLBKALAIAD/8QBBXEKABAEC98gGDZ+HJJJBADAE9PMBAIAECIgGLCITGVFCBCAAV9r/8KBAIABADCITFGDAV/8QBBAIALZ+HJJJBADAIAV/8QBBKAICAF8kJJJJBK+yIDDUR97DNAEtMBCBHDINABCZFGIAIPBBBGLCBPhGVCJJ98P3ECJJ98P3IGOP9OABPBBBGRALPMLVORXMpScxql358e8fCffEPhP9OP/6EARALPMBEDIWdQKZhoaky8aeGLCZP+sEP/6EGWP/gEALCZP+rECZP+sEP/6EGdP/gEP/kEP/lEGLjB/+fsPaAdALAVP+2EGVAdCJJJJ94PhGQP9OP9RP/kEGdAdP/mEALALP/mEAWAVAWAQP9OP9RP/kEGLALP/mEP/kEP/kEP/jEP/nEGWP/mEjBBN0PaGVP/kECZP+rEAdAWP/mEAVP/kECffIPhP9OP9QGdALAWP/mEAVP/kECUPSCBPlDCBPlICBPlOCBPlRCBPlQCBPlKCBPlpCBPlSP9OGLPMWdkyQK8aeXM35pS8e8fP9QPKBBABARAOP9OAdALPMBEZhDIoaLVcxORqlP9QPKBBABCAFHBADCLFGDAE6MBKKK94EIU8jJJJJBCA9rGI8kJJJJBABAEC98gGLZ+JJJJBDNALAE9PMBAIAECIgGVCITGEFCBCAAE9r/8KBAIABALCITFGBAE/8QBBAIAVZ+JJJJBABAIAE/8QBBKAICAF8kJJJJBK/hILDUE97EUV978jJJJJBCZ9rHDDNAEtMBCBHIINADABPBBBGLABCZFGVPBBBGOPMLVORXMpScxql358e8fGRCZP+sEGWCLP+rEPKLBABjBBJzPaj/zL81zPaAWCIPhP9QP/6EP/nEGWALAOPMBEDIWdQKZhoaky8aeGLCZP+rECZP+sEP/6EP/mEGOAOP/mEAWALCZP+sEP/6EP/mEGdAdP/mEAWARCZP+rECZP+sEP/6EP/mEGRARP/mEP/kEP/kEP/lECBPhP+4EP/jEjB/+fsPaGWP/mEjBBN0PaGLP/kECffIPhGQP9OAdAWP/mEALP/kECZP+rEP9QGdARAWP/mEALP/kECZP+rEAOAWP/mEALP/kEAQP9OP9QGWPMBEZhDIoaLVcxORqlGLP5BADPBLBPeB+t+J83IBABCWFALP5EADPBLBPeE+t+J83IBAVAdAWPMWdkyQK8aeXM35pS8e8fGWP5BADPBLBPeD+t+J83IBABCkFAWP5EADPBLBPeI+t+J83IBABCAFHBAICLFGIAE6MBKKK/3EDIUE978jJJJJBC/AB9rHIDNADCD4AE2GLC98gGVtMBCBHDABHEINAEAEPBBBGOCWP+rECWP+sEP/6EAOCkP+sEClP+rECJJJ/8IPhP+uEP/mEPKBBAECZFHEADCLFGDAV6MBKKDNAVAL9PMBAIALCIgGDCDTGEvCBC/ABAE9r/8KBAIABAVCDTFGVAE/8QBBDNADtMBAIAIPBLBGOCWP+rECWP+sEP/6EAOCkP+sEClP+rECJJJ/8IPhP+uEP/mEPKLBKAVAIAE/8QBBKK9TEIUCBCBYDJ1JJBGEABCIFC98gFGBbDJ1JJBDNDNABzBCZTGD9NMBCUHIABAD9rCffIFCZ4NBCUsMEKAEHIKAIKKKEBCJWKLZ9tBB");var n=WebAssembly.instantiate(function(t){for(var r=new Uint8Array(t.length),a=0;a<t.length;++a){var n=t.charCodeAt(a);r[a]=n>96?n-71:n>64?n-65:n>47?n+4:n>46?63:62}var A=0;for(a=0;a<t.length;++a)r[A++]=r[a]<60?e[r[a]]:64*(r[a]-60)+r[++a];return r.buffer.slice(0,A)}(a),{}).then((function(t){(r=t.instance).exports.__wasm_call_ctors()}));function A(t,e,a,n,A,o){var i=r.exports.sbrk,B=a+3&-4,E=i(B*n),C=i(A.length),s=new Uint8Array(r.exports.memory.buffer);s.set(A,C);var y=t(E,a,n,C,A.length);if(0==y&&o&&o(E,B,n),e.set(s.subarray(E,E+a*n)),i(E-i(0)),0!=y)throw new Error("Malformed buffer data: "+y)}var o={0:"",1:"meshopt_decodeFilterOct",2:"meshopt_decodeFilterQuat",3:"meshopt_decodeFilterExp",NONE:"",OCTAHEDRAL:"meshopt_decodeFilterOct",QUATERNION:"meshopt_decodeFilterQuat",EXPONENTIAL:"meshopt_decodeFilterExp"},i={0:"meshopt_decodeVertexBuffer",1:"meshopt_decodeIndexBuffer",2:"meshopt_decodeIndexSequence",ATTRIBUTES:"meshopt_decodeVertexBuffer",TRIANGLES:"meshopt_decodeIndexBuffer",INDICES:"meshopt_decodeIndexSequence"};return{ready:n,supported:!0,decodeVertexBuffer:function(t,e,a,n,i){A(r.exports.meshopt_decodeVertexBuffer,t,e,a,n,r.exports[o[i]])},decodeIndexBuffer:function(t,e,a,n){A(r.exports.meshopt_decodeIndexBuffer,t,e,a,n)},decodeIndexSequence:function(t,e,a,n){A(r.exports.meshopt_decodeIndexSequence,t,e,a,n)},decodeGltfBuffer:function(t,e,a,n,B,E){A(r.exports[i[B]],t,e,a,n,r.exports[o[E]])}}}(),z=1,j=2,q={};q[0]=L.PixelFormat.RGB_DXT1,q[z]=L.PixelFormat.RGBA_DXT3,q[j]=L.PixelFormat.RGBA_DXT5;var $,tt=0,et=!1;function rt(t,e){var r=t.data,a=r.byteLength,A=new Uint8Array(r),o=$._malloc(a);!function(t,e,r,a){var n,A=r/4,o=a%4,i=new Uint32Array(t.buffer,0,(a-o)/4),B=new Uint32Array(e.buffer);for(n=0;n<i.length;n++)B[A+n]=i[n];for(n=a-o;n<a;n++)e[r+n]=t[n]}(A,$.HEAPU8,o,a);var i=$._crn_get_dxt_format(o,a),B=q[i];if(!n.defined(B))throw new l.RuntimeError("Unsupported compressed format.");var E,C=$._crn_get_levels(o,a),s=$._crn_get_width(o,a),y=$._crn_get_height(o,a),d=0;for(E=0;E<C;++E)d+=L.PixelFormat.compressedTextureSizeInBytes(B,s>>E,y>>E);if(tt<d&&(n.defined(W)&&$._free(W),W=$._malloc(d),Z=new Uint8Array($.HEAPU8.buffer,W,d),tt=d),$._crn_decompress(o,a,W,d,0,C),$._free(o),n.defaultValue(t.bMipMap,!1)){var f=Z.slice(0,d);return e.push(f.buffer),new T.CompressedTextureBuffer(B,s,y,f)}var u=L.PixelFormat.compressedTextureSizeInBytes(B,s,y),P=Z.subarray(0,u),c=new Uint8Array(u);return c.set(P,0),e.push(c.buffer),new T.CompressedTextureBuffer(B,s,y,c)}var at,nt=1,At=0,ot=1,it=2,Bt=3,Et=0,Ct=1,st=2;new B.Color;var yt,dt=!1,lt=null;function ft(t,e,r,a,n,A){this.left=t,this.bottom=e,this.right=r,this.top=a,this.minHeight=n,this.maxHeight=A,this.width=r-t,this.length=a-e,this.height=A-n}function ut(t,e,r){var a=r,n=t.getUint32(a,!0),A=a+=Uint32Array.BYTES_PER_ELEMENT,o=new Uint8Array(e,a,n);return{dataViewByteOffset:A,byteOffset:a+=n*Uint8Array.BYTES_PER_ELEMENT,buffer:o}}function Pt(t,e,r,a){var n=t.getUint32(a+e,!0);a+=Uint32Array.BYTES_PER_ELEMENT;var A=r.subarray(a,a+n);return{string:C.getStringFromTypedArray(A),bytesOffset:a+=n}}function ct(t,e,r,a,n,A){var o=r,i=t.getUint16(r+a,!0);o+=Uint16Array.BYTES_PER_ELEMENT,A||(o+=Uint16Array.BYTES_PER_ELEMENT);for(var B=0;B<i;B++){var C=t.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var s=t.getUint16(o+a,!0);if(o+=Uint16Array.BYTES_PER_ELEMENT,t.getUint16(o+a,!0),o+=Uint16Array.BYTES_PER_ELEMENT,20==s||35==s);else{var y=C*s*Float32Array.BYTES_PER_ELEMENT,d=e.subarray(o,o+y);o+=y;var l="aTexCoord"+B,f=n.vertexAttributes,u=n.attrLocation;u[l]=f.length,f.push({index:u[l],typedArray:d,componentsPerAttribute:s,componentDatatype:E.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:s*Float32Array.BYTES_PER_ELEMENT,normalize:!1})}}return{bytesOffset:o}}function pt(t,e,r,a,n){var A=r,o=t.getUint16(A+a,!0);A+=Uint16Array.BYTES_PER_ELEMENT,A+=Uint16Array.BYTES_PER_ELEMENT;for(var i=n.vertexAttributes,B=n.attrLocation,C=0;C<o;C++){var s=t.getUint32(A+a,!0);A+=Uint32Array.BYTES_PER_ELEMENT;var y=t.getUint16(A+a,!0);if(A+=Uint16Array.BYTES_PER_ELEMENT,16===y){A-=Uint16Array.BYTES_PER_ELEMENT;var d=s*(y*Float32Array.BYTES_PER_ELEMENT+Uint16Array.BYTES_PER_ELEMENT),l=e.subarray(A,A+d);A+=d;var f=new Uint8Array(Float32Array.BYTES_PER_ELEMENT*y*s);n.instanceCount=s,n.instanceMode=y,n.instanceBuffer=f,n.instanceIndex=1;for(var u=Float32Array.BYTES_PER_ELEMENT*y+Uint16Array.BYTES_PER_ELEMENT,P=0;P<s;P++){var c=P*u+Uint16Array.BYTES_PER_ELEMENT,p=l.subarray(c,c+u);f.set(p,P*(u-Uint16Array.BYTES_PER_ELEMENT))}T=16*Float32Array.BYTES_PER_ELEMENT,B.uv2=i.length,i.push({index:B.uv2,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:0,strideInBytes:T,instanceDivisor:1}),B.uv3=i.length,i.push({index:B.uv3,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:4*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.uv4=i.length,i.push({index:B.uv4,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:8*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.secondary_colour=i.length,i.push({index:B.secondary_colour,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:12*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1})}else{t.getUint16(A+a,!0),A+=Uint16Array.BYTES_PER_ELEMENT;d=s*y*Float32Array.BYTES_PER_ELEMENT;if(17===y||29===y){var T;f=e.subarray(A,A+d);n.instanceCount=s,n.instanceMode=y,n.instanceBuffer=f,n.instanceIndex=1,17===y?(T=17*Float32Array.BYTES_PER_ELEMENT,B.uv2=i.length,i.push({index:B.uv2,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:0,strideInBytes:T,instanceDivisor:1}),B.uv3=i.length,i.push({index:B.uv3,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:4*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.uv4=i.length,i.push({index:B.uv4,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:8*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.secondary_colour=i.length,i.push({index:B.secondary_colour,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:12*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.uv6=i.length,i.push({index:B.uv6,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.UNSIGNED_BYTE,normalize:!0,offsetInBytes:16*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1})):29===y&&(T=29*Float32Array.BYTES_PER_ELEMENT,B.uv1=i.length,i.push({index:B.uv1,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:0,strideInBytes:T,instanceDivisor:1,byteLength:d}),B.uv2=i.length,i.push({index:B.uv2,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:4*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.uv3=i.length,i.push({index:B.uv3,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:8*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.uv4=i.length,i.push({index:B.uv4,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:12*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.uv5=i.length,i.push({index:B.uv5,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:16*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.uv6=i.length,i.push({index:B.uv6,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:20*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.uv7=i.length,i.push({index:B.uv7,componentsPerAttribute:3,componentDatatype:E.ComponentDatatype.FLOAT,normalize:!1,offsetInBytes:24*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.secondary_colour=i.length,i.push({index:B.secondary_colour,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.UNSIGNED_BYTE,normalize:!0,offsetInBytes:27*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}),B.uv9=i.length,i.push({index:B.uv9,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.UNSIGNED_BYTE,normalize:!0,offsetInBytes:28*Float32Array.BYTES_PER_ELEMENT,strideInBytes:T,instanceDivisor:1}))}else{var L=s*y;n.instanceBounds=new Float32Array(L);for(var D=0;D<L;D++)n.instanceBounds[D]=t.getFloat32(A+a+D*Float32Array.BYTES_PER_ELEMENT,!0)}A+=d}}return{bytesOffset:A}}var Tt=new o.Cartesian3(40680631590769,40680631590769,40408299984661.445),Lt=new o.Cartesian3,Dt=new o.Cartesian3;function gt(t,e,r,a){var A=Math.cos(e);Lt.x=A*Math.cos(t),Lt.y=A*Math.sin(t),Lt.z=Math.sin(e),Lt=o.Cartesian3.normalize(Lt,Lt),o.Cartesian3.multiplyComponents(Tt,Lt,Dt);var i=Math.sqrt(o.Cartesian3.dot(Lt,Dt));return Dt=o.Cartesian3.divideByScalar(Dt,i,Dt),Lt=o.Cartesian3.multiplyByScalar(Lt,r,Lt),n.defined(a)||(a=new o.Cartesian3),o.Cartesian3.add(Dt,Lt,a)}var Ft=new o.Cartesian3,Mt=new o.Cartographic,mt=new a.Matrix4,It=new a.Matrix4;function vt(t,e,r,A,B,C,s,y,d){var l=A,u=e.getUint32(l+r,!0);if(B.verticesCount=u,l+=Uint32Array.BYTES_PER_ELEMENT,u<=0)return{bytesOffset:l};var P=e.getUint16(l+r,!0);l+=Uint16Array.BYTES_PER_ELEMENT;var c=e.getUint16(l+r,!0);c=P*Float32Array.BYTES_PER_ELEMENT,l+=Uint16Array.BYTES_PER_ELEMENT;var p=u*P*Float32Array.BYTES_PER_ELEMENT,T=t.subarray(l,l+p);l+=p;var L=B.vertexAttributes,D=B.attrLocation,g=void 0,F=void 0;if(s){g=new o.Cartographic,F=new o.Cartographic;var M=new Float32Array(2*u),m=new Float64Array(2*u);if(n.defined(y)){var I=new Float32Array(T.byteLength/4),v=new Float32Array(T.buffer,T.byteOffset,T.byteLength/4);k=3===P?o.Cartesian3.unpackArray(v):i.Cartesian4.unpackArray(v);var _=a.Matrix4.multiply(y.sphereMatrix,y.geoMatrix,mt),S=a.Matrix4.multiply(y.ellipsoidMatrix,y.geoMatrix,It);a.Matrix4.inverse(S,S);for(var O=new f.Ellipsoid(6378137,6378137,6378137),N=0,h=0,R=k.length;h<R;h++){var G=k[h];a.Matrix4.multiplyByPoint(_,G,Ft);var x=gt((W=O.cartesianToCartographic(Ft,Mt)).longitude,W.latitude,W.height,Ft);a.Matrix4.multiplyByPoint(S,x,G),3===P?(o.Cartesian3.pack(G,I,N),N+=3):(i.Cartesian4.pack(G,I,N),N+=4),m[2*h]=W.longitude,m[2*h+1]=W.latitude,0===h?(g.longitude=W.longitude,g.latitude=W.latitude,F.longitude=W.longitude,F.latitude=W.latitude):(g.longitude=Math.max(W.longitude,g.longitude),g.latitude=Math.max(W.latitude,g.latitude),F.longitude=Math.min(W.longitude,F.longitude),F.latitude=Math.min(W.latitude,F.latitude))}T=I}else{var b=new o.Cartesian3,U=new o.Cartesian3,K=new Float32Array(T.buffer,T.byteOffset,T.byteLength/4),H=new o.Cartographic;O=bt?new f.Ellipsoid(6378137,6378137,6356752.314245179):new f.Ellipsoid(6378137,6378137,6378137);for(var w=0;w<u;w++)a.Matrix4.multiplyByPoint(C,o.Cartesian3.fromElements(K[3*w],K[3*w+1],K[3*w+2],b),U),H=O.cartesianToCartographic(U,Mt),m[2*w]=H.longitude,m[2*w+1]=H.latitude,0===w?(g.longitude=H.longitude,g.latitude=H.latitude,F.longitude=H.longitude,F.latitude=H.latitude):(g.longitude=Math.max(H.longitude,g.longitude),g.latitude=Math.max(H.latitude,g.latitude),F.longitude=Math.min(H.longitude,F.longitude),F.latitude=Math.min(H.latitude,F.latitude))}for(w=0;w<u;w++)M[2*w]=m[2*w]-F.longitude,M[2*w+1]=m[2*w+1]-F.latitude;D.aPosition=L.length,L.push({index:D.aPosition,typedArray:T,componentsPerAttribute:P,componentDatatype:E.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:c,normalize:!1}),D.img=L.length,L.push({index:D.img,typedArray:M,componentsPerAttribute:2,componentDatatype:E.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:2*Float32Array.BYTES_PER_ELEMENT,normalize:!1})}else{if(3===P&&n.defined(C)){U=new o.Cartesian3,K=new Float32Array(T.buffer,T.byteOffset,T.byteLength/4);for(var V=new Float32Array(T.byteLength/4+u),Y=K.length,J=(w=0,0);w<Y;w+=3,J+=4)V[J]=K[w],V[J+1]=K[w+1],V[J+2]=K[w+2],a.Matrix4.multiplyByPoint(C,o.Cartesian3.fromElements(V[J],V[J+1],V[J+2],b),U),V[J+3]=o.Cartographic.fromCartesian(U).height;T=V,c=(P=4)*Float32Array.BYTES_PER_ELEMENT}if(n.defined(y)){var k;I=new Float32Array(T.byteLength/4),v=new Float32Array(T.buffer,T.byteOffset,T.byteLength/4);k=3===P?o.Cartesian3.unpackArray(v):i.Cartesian4.unpackArray(v);_=a.Matrix4.multiply(y.sphereMatrix,y.geoMatrix,mt),S=a.Matrix4.multiply(y.ellipsoidMatrix,y.geoMatrix,It);a.Matrix4.inverse(S,S);for(O=new f.Ellipsoid(6378137,6378137,6378137),N=0,h=0,R=k.length;h<R;h++){G=k[h];a.Matrix4.multiplyByPoint(_,G,Ft);var W;x=gt((W=O.cartesianToCartographic(Ft,Mt)).longitude,W.latitude,W.height,Ft);a.Matrix4.multiplyByPoint(S,x,G),3===P?(o.Cartesian3.pack(G,I,N),N+=3):(i.Cartesian4.pack(G,I,N),N+=4)}T=I}D.aPosition=L.length,L.push({index:D.aPosition,typedArray:T,componentsPerAttribute:P,componentDatatype:E.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:c,normalize:!1})}return{bytesOffset:l,cartographicBounds:{max:g,min:F}}}function _t(t,e,r,a,n){var A=a,o=e.getUint32(A+r,!0);if(A+=Uint32Array.BYTES_PER_ELEMENT,o<=0)return{bytesOffset:A};var i=e.getUint16(A+r,!0);A+=Uint16Array.BYTES_PER_ELEMENT;var B=e.getUint16(A+r,!0);A+=Uint16Array.BYTES_PER_ELEMENT;var C=o*i*Float32Array.BYTES_PER_ELEMENT,s=t.subarray(A,A+C);if(A+=C,!n.ignoreNormal){var y=n.vertexAttributes,d=n.attrLocation;d.aNormal=y.length,y.push({index:d.aNormal,typedArray:s,componentsPerAttribute:i,componentDatatype:E.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:B,normalize:!1})}return{bytesOffset:A}}var St={0:Uint32Array.BYTES_PER_ELEMENT,1:Float32Array.BYTES_PER_ELEMENT,2:Float64Array.BYTES_PER_ELEMENT};function Ot(t,e,r,a,n){var A,o=a,i=e.getUint32(o+r,!0);if(o+=Uint32Array.BYTES_PER_ELEMENT,n.verticesCount,i>0){e.getUint16(o+r,!0),o+=Uint16Array.BYTES_PER_ELEMENT,o+=2*Uint8Array.BYTES_PER_ELEMENT;var B=i*Uint8Array.BYTES_PER_ELEMENT*4;A=M(t,o,o+B),o+=B;var C=n.vertexAttributes,s=n.attrLocation;s.aColor=C.length,C.push({index:s.aColor,typedArray:A,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.UNSIGNED_BYTE,offsetInBytes:0,strideInBytes:4,normalize:!0})}return{bytesOffset:o}}function Nt(t,e,r,a,n){var A=a,o=e.getUint32(A+r,!0);return A+=Uint32Array.BYTES_PER_ELEMENT,o<=0?{bytesOffset:A}:(e.getUint16(A+r,!0),A+=Uint16Array.BYTES_PER_ELEMENT,A+=2*Uint8Array.BYTES_PER_ELEMENT,{bytesOffset:A+=o*Uint8Array.BYTES_PER_ELEMENT*4})}function ht(t,e,r,a,n){var A=n,o=[],i=r.getUint32(A+a,!0);A+=Uint32Array.BYTES_PER_ELEMENT;for(var B=0;B<i;B++){var E={};3===t&&(r.getUint32(A+a,!0),A+=Uint32Array.BYTES_PER_ELEMENT);var C=r.getUint32(A+a,!0);A+=Uint32Array.BYTES_PER_ELEMENT;var s=r.getUint8(A+a,!0);A+=Uint8Array.BYTES_PER_ELEMENT,r.getUint8(A+a,!0),A+=Uint8Array.BYTES_PER_ELEMENT;var y=r.getUint8(A+a,!0);if(A+=Uint8Array.BYTES_PER_ELEMENT,A+=Uint8Array.BYTES_PER_ELEMENT,C>0){var d=0,l=null;1===s||3===s?(d=C*Uint32Array.BYTES_PER_ELEMENT,l=e.subarray(A,A+d)):(d=C*Uint16Array.BYTES_PER_ELEMENT,l=e.subarray(A,A+d),C%2!=0&&(d+=2)),E.indicesTypedArray=l,A+=d}E.indicesCount=C,E.indexType=s,E.primitiveType=y;var f=[],u=r.getUint32(A+a,!0);A+=Uint32Array.BYTES_PER_ELEMENT;for(var P=0;P<u;P++){var c=Pt(r,a,e,A),p=c.string;A=c.bytesOffset,f.push(p),E.materialCode=p}if(0===u&&(E.materialCode="OSGBEmpty"),o.push(E),0!==A%4)A+=4-A%4}return{bytesOffset:A,arrIndexPackage:o}}function Rt(t,e,r,A,B,C,s,d,l,u){var P,c,p=A,T=e.getUint32(p+r,!0);return B.nCompressOptions=T,p+=Uint32Array.BYTES_PER_ELEMENT,(T&y.VertexCompressOption.SVC_Vertex)==y.VertexCompressOption.SVC_Vertex?(P=function(t,e,r,A,B,C,s){var y=A,d=e.getUint32(y+r,!0);if(B.verticesCount=d,y+=Uint32Array.BYTES_PER_ELEMENT,d<=0)return{bytesOffset:y};var l=e.getUint16(y+r,!0);y+=Uint16Array.BYTES_PER_ELEMENT;var u=e.getUint16(y+r,!0);u=l*Int16Array.BYTES_PER_ELEMENT,y+=Uint16Array.BYTES_PER_ELEMENT;var P=e.getFloat32(y+r,!0);y+=Float32Array.BYTES_PER_ELEMENT;var c=new i.Cartesian4;c.x=e.getFloat32(y+r,!0),y+=Float32Array.BYTES_PER_ELEMENT,c.y=e.getFloat32(y+r,!0),y+=Float32Array.BYTES_PER_ELEMENT,c.z=e.getFloat32(y+r,!0),y+=Float32Array.BYTES_PER_ELEMENT,c.w=e.getFloat32(y+r,!0),y+=Float32Array.BYTES_PER_ELEMENT,B.vertCompressConstant=P,B.minVerticesValue=c;var p=d*l*Int16Array.BYTES_PER_ELEMENT,T=t.subarray(y,y+p);if(y+=p,n.defined(C)){var L=new Uint16Array(T.byteLength/2),D=new Uint16Array(T.buffer,T.byteOffset,T.byteLength/2),g=i.Cartesian4.unpackArray(D);for(let t=0,e=g.length;t<e;t++){let e=g[t];o.Cartesian3.multiplyByScalar(e,P,e),o.Cartesian3.add(e,c,e)}var F=a.Matrix4.multiply(C.sphereMatrix,C.geoMatrix,mt),M=a.Matrix4.multiply(C.ellipsoidMatrix,C.geoMatrix,It);a.Matrix4.inverse(M,M);var m=new f.Ellipsoid(6378137,6378137,6378137),I=0;for(let t=0,e=g.length;t<e;t++){let e=g[t];a.Matrix4.multiplyByPoint(F,e,Ft);let r=m.cartesianToCartographic(Ft,Mt),n=gt(r.longitude,r.latitude,r.height,Ft);a.Matrix4.multiplyByPoint(M,n,e),o.Cartesian3.subtract(e,c,e),o.Cartesian3.divideByScalar(e,P,e),i.Cartesian4.pack(e,L,I),I+=4}T=L}var v=B.vertexAttributes,_=B.attrLocation;return _.aPosition=v.length,v.push({index:_.aPosition,typedArray:T,componentsPerAttribute:l,componentDatatype:E.ComponentDatatype.SHORT,offsetInBytes:0,strideInBytes:u,normalize:!1}),{bytesOffset:y}}(t,e,r,p,B,l),p=P.bytesOffset):(p=(P=vt(t,e,r,p,B,s,d,l)).bytesOffset,c=P.cartographicBounds),(T&y.VertexCompressOption.SVC_Normal)==y.VertexCompressOption.SVC_Normal?(P=function(t,e,r,a,n){var A=a,o=e.getUint32(A+r,!0);if(A+=Uint32Array.BYTES_PER_ELEMENT,o<=0)return{bytesOffset:A};e.getUint16(A+r,!0),A+=Uint16Array.BYTES_PER_ELEMENT;var i=e.getUint16(A+r,!0);A+=Uint16Array.BYTES_PER_ELEMENT;var B=2*o*Int16Array.BYTES_PER_ELEMENT,C=t.subarray(A,A+B);if(A+=B,!n.ignoreNormal){var s=n.vertexAttributes,y=n.attrLocation;y.aNormal=s.length,s.push({index:y.aNormal,typedArray:C,componentsPerAttribute:2,componentDatatype:E.ComponentDatatype.SHORT,offsetInBytes:0,strideInBytes:i,normalize:!1})}return{bytesOffset:A}}(t,e,r,p,B),p=P.bytesOffset):p=(P=_t(t,e,r,p,B)).bytesOffset,p=(P=Nt(0,e,r,p=(P=Ot(t,e,r,p,B)).bytesOffset)).bytesOffset,(T&y.VertexCompressOption.SVC_TexutreCoord)==y.VertexCompressOption.SVC_TexutreCoord?(P=function(t,e,r,a,n){n.texCoordCompressConstant=[],n.minTexCoordValue=[];var A=r,o=t.getUint16(r+a,!0);A+=Uint16Array.BYTES_PER_ELEMENT,A+=Uint16Array.BYTES_PER_ELEMENT;for(var B=0,C=0;C<o;C++){var s=t.getUint8(A+a,!0);A+=Uint8Array.BYTES_PER_ELEMENT,A+=3*Uint8Array.BYTES_PER_ELEMENT;var y=t.getUint32(A+a,!0);A+=Uint32Array.BYTES_PER_ELEMENT;var d=t.getUint16(A+a,!0);A+=Uint16Array.BYTES_PER_ELEMENT,t.getUint16(A+a,!0),A+=Uint16Array.BYTES_PER_ELEMENT;var l=t.getFloat32(A+a,!0);A+=Float32Array.BYTES_PER_ELEMENT,n.texCoordCompressConstant.push(l);var f=new i.Cartesian4;f.x=t.getFloat32(A+a,!0),A+=Float32Array.BYTES_PER_ELEMENT,f.y=t.getFloat32(A+a,!0),A+=Float32Array.BYTES_PER_ELEMENT,f.z=t.getFloat32(A+a,!0),A+=Float32Array.BYTES_PER_ELEMENT,f.w=t.getFloat32(A+a,!0),A+=Float32Array.BYTES_PER_ELEMENT,n.minTexCoordValue.push(f);var u=y*d*Int16Array.BYTES_PER_ELEMENT,P=e.subarray(A,A+u),c=(A+=u)%4;0!==c&&(A+=4-c);var p="aTexCoord"+B,T=n.vertexAttributes,L=n.attrLocation;if(L[p]=T.length,T.push({index:L[p],typedArray:P,componentsPerAttribute:d,componentDatatype:E.ComponentDatatype.SHORT,offsetInBytes:0,strideInBytes:d*Int16Array.BYTES_PER_ELEMENT,normalize:!1}),s){u=y*Float32Array.BYTES_PER_ELEMENT;var D=e.subarray(A,A+u);A+=u,n.texCoordZMatrix=!0,L[p="aTexCoordZ"+B]=T.length,T.push({index:L[p],typedArray:D,componentsPerAttribute:1,componentDatatype:E.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:Float32Array.BYTES_PER_ELEMENT,normalize:!1})}B++}return{bytesOffset:A}}(e,t,p,r,B),p=P.bytesOffset):p=(P=ct(e,t,p,r,B,C)).bytesOffset,(T&y.VertexCompressOption.SVC_TexutreCoordIsW)==y.VertexCompressOption.SVC_TexutreCoordIsW&&(B.textureCoordIsW=!0),{bytesOffset:p=(P=pt(e,t,p,r,B)).bytesOffset,cartographicBounds:c}}function Gt(t,e,r,a,A,o,i,B,C,s,y){3===t&&(r.getUint32(A,!0),A+=Uint32Array.BYTES_PER_ELEMENT,n.defined(C)&&C||(B=void 0));var d,l=A;l=(d=vt(e,r,a,l,o,B,C,s)).bytesOffset;var f=d.cartographicBounds;if(l=(d=Ot(e,r,a,l=(d=_t(e,r,a,l,o)).bytesOffset,o)).bytesOffset,3!==t&&(l=(d=Nt(0,r,a,l)).bytesOffset),l=(d=pt(r,e,l=(d=ct(r,e,l,a,o,i)).bytesOffset,a,o)).bytesOffset,3===t&&(d=function(t,e,r,a,n){var A=a,o=e.getUint32(A+r,!0);A+=Uint32Array.BYTES_PER_ELEMENT;for(var i=0;i<o;i++){var B=e.getUint32(A+r,!0);A+=Uint32Array.BYTES_PER_ELEMENT;var C=e.getUint16(A+r,!0);A+=Uint16Array.BYTES_PER_ELEMENT;var s=e.getUint16(A+r,!0);A+=Uint16Array.BYTES_PER_ELEMENT;var y=B*C*St[s],d=t.subarray(A,A+y);A+=y;var l=n.vertexAttributes,f=n.attrLocation,u="aCustom"+i;f[u]=l.length,l.push({index:f[u],typedArray:d,componentsPerAttribute:C,componentDatatype:E.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:0,normalize:!1})}return{bytesOffset:A}}(e,r,a,l,o),l=d.bytesOffset),3==t){var u=Pt(r,a,e,l);l=u.bytesOffset,o.customVertexAttribute=JSON.parse(u.string);var P="aCustom"+o.customVertexAttribute.TextureCoordMatrix,c="aCustom"+o.customVertexAttribute.VertexWeight,p="aCustom"+o.customVertexAttribute.VertexWeight_1;n.defined(o.attrLocation[P])&&(o.attrLocation.aTextureCoordMatrix=o.attrLocation[P],delete o.attrLocation[P]),n.defined(o.attrLocation[c])&&(o.attrLocation.aVertexWeight=o.attrLocation[c],delete o.attrLocation[c]),n.defined(o.attrLocation[p])&&(o.attrLocation.aVertexWeight_1=o.attrLocation[p],delete o.attrLocation[p]);for(var T=Object.keys(o.attrLocation),L=T.length,D=0;D<L;++D){var g=T[D];-1!==g.indexOf("aCustom")&&delete o.attrLocation[g]}var F=(l+a)%4;F&&(F=4-F),l+=F}return 3===t&&(d=function(t,e,r,a,n){var A=a,o=e.getUint32(A+r,!0);if(A+=Uint32Array.BYTES_PER_ELEMENT,o<=0)return{bytesOffset:A};var i=e.getUint16(A+r,!0);return A+=Uint16Array.BYTES_PER_ELEMENT,e.getUint16(A+r,!0),A+=Uint16Array.BYTES_PER_ELEMENT,{bytesOffset:A+=o*i*Float32Array.BYTES_PER_ELEMENT}}(0,r,a,l),l=d.bytesOffset),{bytesOffset:l,cartographicBounds:f}}function xt(t){return 0!==t.length&&"ClampGroundAndObjectLinePass"===t[0].materialCode}var bt,Ut=1,Kt=4,Ht=16,wt=32,Vt=64,Yt=128,Jt=512,kt=1024;function Wt(t,e,r,B,C,s,d,l,f,u,P,c,p){var T=t,L=0,D=e.getUint32(L+r,!0);L+=Uint32Array.BYTES_PER_ELEMENT,u=n.defaultValue(u,n.defaultValue.EMPTY_OBJECT);for(var g=void 0,F=0;F<D;F++){3===l&&(e.getUint32(L+r,!0),L+=Uint32Array.BYTES_PER_ELEMENT);var I=(Yt=Pt(e,r,T,L)).string;if(n.defined(f)){var v=n.defaultValue(u[I],a.Matrix4.IDENTITY);g=new a.Matrix4,a.Matrix4.multiply(f,v,g)}n.defined(p)&&(p.geoMatrix=n.defaultValue(u[I],a.Matrix4.IDENTITY));var _=(L=Yt.bytesOffset)%4;0!==_&&(L+=4-_);var S=At;if(S=e.getUint32(L+r,!0),L+=Int32Array.BYTES_PER_ELEMENT,(fe={vertexAttributes:[],attrLocation:{},instanceCount:0,instanceMode:0,instanceIndex:-1}).ignoreNormal=B.ignoreNormal,3===l)switch(S){case Et:S=ot;break;case Ct:S=Bt;break;case st:S=it}if(S===Bt){3===l&&(e.getInt32(L+r,!0),L+=Int32Array.BYTES_PER_ELEMENT),l>=2&&(e.getInt32(L+r,!0),L+=Int32Array.BYTES_PER_ELEMENT);var O,N={};N.posUniqueID=e.getInt32(L+r,!0),L+=Int32Array.BYTES_PER_ELEMENT,N.normalUniqueID=e.getInt32(L+r,!0),L+=Int32Array.BYTES_PER_ELEMENT,N.colorUniqueID=e.getInt32(L+r,!0),L+=Int32Array.BYTES_PER_ELEMENT,N.secondColorUniqueID=e.getInt32(L+r,!0),L+=Int32Array.BYTES_PER_ELEMENT,3===l?(O=e.getUint32(L+r,!0),L+=Int32Array.BYTES_PER_ELEMENT):(O=e.getUint16(L+r,!0),L+=Int16Array.BYTES_PER_ELEMENT);for(var h=[],R=0;R<O;R++){var G=e.getInt32(L+r,!0);h.push(G),L+=Int32Array.BYTES_PER_ELEMENT}N.texCoordUniqueIDs=h;var x=[];if(3===l){var b=e.getUint32(L+r,!0);L+=Int32Array.BYTES_PER_ELEMENT;for(var U=0;U<b;U++){var K=e.getInt32(L+r,!0);L+=Int32Array.BYTES_PER_ELEMENT,x.push(K)}}N.vertexAttrUniqueIDs=x;var H=e.getInt32(L+r,!0);L+=Int32Array.BYTES_PER_ELEMENT;var w=[],V={};if(H>0){var Y=(J=Pt(e,r,T,L)).string;L=J.bytesOffset,V.materialCode=Y,w.push(V)}3===l&&((zt=(L+r)%4)&&(zt=4-zt),L+=zt);var J,k=new Object,W=e.getUint32(L+r,!0),Z=M(T,L+=Int32Array.BYTES_PER_ELEMENT,L+W);if(H>0?m.dracoDecodeMesh(at,Z,W,fe,V,N,g,P,k,p):m.dracoDecodePointCloud(at,Z,W,fe,N),n.defined(k.min)&&n.defined(k.max)||(k=void 0),L+=W,3===l)(zt=(L+r)%4)&&(zt=4-zt),(zt=((L=(J=Pt(e,r,T,L+=zt)).bytesOffset)+r)%4)&&(zt=4-zt),L+=zt;B[I]={vertexPackage:fe,arrIndexPackage:w,cartographicBounds:k}}else if(S==it&&3==l){var X=e.getUint32(L+r,!0);L+=Uint32Array.BYTES_PER_ELEMENT;var z=e.getUint32(L+r,!0);L+=Uint32Array.BYTES_PER_ELEMENT,fe.minVerticesValue=new i.Cartesian4,fe.minTexCoordValue=[new A.Cartesian2,new A.Cartesian2],fe.texCoordCompressConstant=[new o.Cartesian3,new o.Cartesian3];w=[];for(var j=0;j<z;j++){var q=e.getUint32(L+r,!0);L+=Uint32Array.BYTES_PER_ELEMENT,fe.vertCompressConstant=e.getFloat32(L+r,!0),L+=Float32Array.BYTES_PER_ELEMENT,fe.minVerticesValue.x=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,fe.minVerticesValue.y=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,fe.minVerticesValue.z=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT;var $=e.getFloat64(L+r,!0);L+=Float64Array.BYTES_PER_ELEMENT;var tt=e.getFloat64(L+r,!0);L+=Float64Array.BYTES_PER_ELEMENT;var et=e.getFloat64(L+r,!0);L+=Float64Array.BYTES_PER_ELEMENT;var rt=e.getFloat64(L+r,!0);L+=Float64Array.BYTES_PER_ELEMENT;var nt=e.getFloat64(L+r,!0);L+=Float64Array.BYTES_PER_ELEMENT;var yt=e.getFloat64(L+r,!0);L+=Float64Array.BYTES_PER_ELEMENT;var dt=e.getFloat64(L+r,!0);L+=Float64Array.BYTES_PER_ELEMENT;var lt=e.getFloat64(L+r,!0);L+=Float64Array.BYTES_PER_ELEMENT,fe.minTexCoordValue[0].x=et,fe.minTexCoordValue[0].y=rt,fe.minTexCoordValue[1].x=dt,fe.minTexCoordValue[1].y=lt,fe.texCoordCompressConstant[0].x=$,fe.texCoordCompressConstant[0].y=tt,fe.texCoordCompressConstant[1].x=nt,fe.texCoordCompressConstant[1].y=yt;var ft=e.getInt32(L+r,!0);L+=Int32Array.BYTES_PER_ELEMENT;k=new Object;for(var ut=0;ut<ft;ut++){var ct=e.getInt32(L+r,!0);L+=Int32Array.BYTES_PER_ELEMENT;var pt=ct,Tt=0;pt!=Jt&&pt!=kt||(Tt=e.getInt32(L+r,!0),L+=Int32Array.BYTES_PER_ELEMENT);var Lt=e.getInt32(L+r,!0);L+=Int32Array.BYTES_PER_ELEMENT;var Dt=new Uint8Array(e.buffer,L+r,Lt);(zt=((L+=Uint8Array.BYTES_PER_ELEMENT*Lt)+r)%4)&&(zt=4-zt),L+=zt,Xt(q,pt,Tt,Dt,fe,X,p,P,k,g)}L=(Qt=Pt(e,r,T,L)).bytesOffset,fe.customVertexAttribute=JSON.parse(Qt.string);var gt="aCustom"+fe.customVertexAttribute.TextureCoordMatrix,Ft="aCustom"+fe.customVertexAttribute.VertexWeight,Mt="aCustom"+fe.customVertexAttribute.VertexWeight_1;n.defined(fe.attrLocation[gt])&&(fe.attrLocation.aTextureCoordMatrix=fe.attrLocation[gt],j===z-1&&delete fe.attrLocation[gt]),n.defined(fe.attrLocation[Ft])&&(fe.attrLocation.aVertexWeight=fe.attrLocation[Ft],j===z-1&&delete fe.attrLocation[Ft]),n.defined(fe.attrLocation[Mt])&&(fe.attrLocation.aVertexWeight_1=fe.attrLocation[Mt],j===z-1&&delete fe.attrLocation[Mt]);for(var mt=(jt=Object.keys(fe.attrLocation)).length,It=0;It<mt;++It){-1!==(qt=jt[It]).indexOf("aCustom")&&delete fe.attrLocation[qt]}(zt=(L+r)%4)&&(zt=4-zt),L+=zt;var vt=e.getInt32(L+r,!0);L+=Int32Array.BYTES_PER_ELEMENT;for(var _t=0;_t<vt;_t++){V={};var St=e.getInt32(L+r,!0);if(L+=Int32Array.BYTES_PER_ELEMENT,St>0){var Ot=e.getInt8(L+r,!0);L+=Int8Array.BYTES_PER_ELEMENT,e.getInt8(L+r,!0),L+=Int8Array.BYTES_PER_ELEMENT;var Nt=e.getInt8(L+r,!0);L+=Int8Array.BYTES_PER_ELEMENT,e.getInt8(L+r,!0),L+=Int8Array.BYTES_PER_ELEMENT;var bt,Ut,Kt=e.getInt32(L+r,!0);L+=Int32Array.BYTES_PER_ELEMENT,13!==Nt?(bt=new Uint8Array(e.buffer,L+r,Kt),L+=Uint8Array.BYTES_PER_ELEMENT*Kt):(bt=new Uint32Array(e.buffer,L+r,Kt),L+=Uint32Array.BYTES_PER_ELEMENT*Kt),(zt=(L+r)%4)&&(zt=4-zt),L+=zt,13!==Nt?(Ut=E.ComponentDatatype.createTypedArray(E.ComponentDatatype.UNSIGNED_BYTE,St*Uint32Array.BYTES_PER_ELEMENT),Q.decodeIndexBuffer(Ut,St,Uint32Array.BYTES_PER_ELEMENT,bt)):Ut=bt;var Ht,wt=e.getInt32(L+r,!0);L+=Int32Array.BYTES_PER_ELEMENT,V.indexType=Ot,0===Ot?Ht=new Uint16Array(St):1===Ot&&(Ht=new Uint32Array(St)),V.indicesCount=St;var Vt=new Uint32Array(Ut.buffer,Ut.byteOffset,Ut.byteLength/4);Ht.set(Vt,0),V.indicesTypedArray=Ht,V.primitiveType=Nt;for(ut=0;ut<wt;ut++){var Yt;Y=(Yt=Pt(e,r,T,L)).string;L=Yt.bytesOffset,V.materialCode=Y}if(w.length>0&&13!==Nt){var Wt=fe.preVertexCount;V.indicesTypedArray=Vt.map((function(t){return t+Wt})),V.indexType=1}w.push(V),(zt=(L+r)%4)&&(zt=4-zt),L+=zt}}}fe.nCompressOptions=X,2===w.length&&13===w[1].primitiveType&&w[1].indicesCount>=3&&(Zt=y.S3MEdgeProcessor.createEdgeDataByIndices(fe,w[1],s)),n.defined(k.min)&&n.defined(k.max)||(k=void 0),B[I]={vertexPackage:fe,arrIndexPackage:w,edgeGeometry:Zt,cartographicBounds:k}}else{var Zt;if(S===ot||S===At)L=(Yt=Gt(l,T,e,r,L,fe,C,g,P,p)).bytesOffset,k=Yt.cartographicBounds;else if(S===it&&(L=(Yt=Rt(T,e,r,L,fe,C,g,P,p)).bytesOffset,k=Yt.cartographicBounds,3==l)){var Qt;L=(Qt=Pt(e,r,T,L)).bytesOffset,fe.customVertexAttribute=JSON.parse(Qt.string);var zt;gt="aCustom"+fe.customVertexAttribute.TextureCoordMatrix,Ft="aCustom"+fe.customVertexAttribute.VertexWeight,Mt="aCustom"+fe.customVertexAttribute.VertexWeight_1;n.defined(fe.attrLocation[gt])&&(fe.attrLocation.aTextureCoordMatrix=fe.attrLocation[gt],delete fe.attrLocation[gt]),n.defined(fe.attrLocation[Ft])&&(fe.attrLocation.aVertexWeight=fe.attrLocation[Ft],delete fe.attrLocation[Ft]),n.defined(fe.attrLocation[Mt])&&(fe.attrLocation.aVertexWeight_1=fe.attrLocation[Mt],delete fe.attrLocation[Mt]);var jt;for(mt=(jt=Object.keys(fe.attrLocation)).length,It=0;It<mt;++It){var qt;-1!==(qt=jt[It]).indexOf("aCustom")&&delete fe.attrLocation[qt]}(zt=(L+r)%4)&&(zt=4-zt),L+=zt}xt(w=(Yt=ht(l,T,e,r,L)).arrIndexPackage)&&(fe.clampRegionEdge=!0),2===w.length&&13===w[1].primitiveType&&w[1].indicesCount>=3&&(Zt=y.S3MEdgeProcessor.createEdgeDataByIndices(fe,w[1],s)),L=Yt.bytesOffset,n.defined(k)&&n.defined(k.min)&&n.defined(k.max)||(k=void 0),B[I]={vertexPackage:fe,arrIndexPackage:w,edgeGeometry:Zt,cartographicBounds:k}}if(3!==l&&n.defined(d)&&d){var $t=e.getUint16(L+r,!0);if(L+=Uint16Array.BYTES_PER_ELEMENT,1===$t){var te=e.getUint32(L+r,!0);L+=Uint32Array.BYTES_PER_ELEMENT;var ee,re=e.getUint32(L+r,!0);L+=Uint32Array.BYTES_PER_ELEMENT,e.getFloat32(L+r,!0),L+=Float32Array.BYTES_PER_ELEMENT;var ae=new Array(te),ne=new Array(te),Ae=new Array(te),oe=new Array(te);for(ee=0;ee<te;ee++){var ie=e.getFloat32(L+r,!0);L+=Float32Array.BYTES_PER_ELEMENT,ae[ee]=ie;var Be=e.getUint16(L+r,!0);L+=Uint16Array.BYTES_PER_ELEMENT,ne[ee]=Be;var Ee=e.getUint16(L+r,!0);L+=Uint16Array.BYTES_PER_ELEMENT,Ae[ee]=Ee;for(var Ce=Ee*re,se=new Array(Ce),ye=0;ye<Ce;ye++){gt=e.getFloat32(L+r,!0);L+=Float32Array.BYTES_PER_ELEMENT,se[ye]=gt}oe[ee]=se}}var de=new o.Cartesian3,le=new o.Cartesian3;de.x=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,de.y=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,de.z=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,le.x=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,le.y=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,le.z=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,B[I].min=de,B[I].max=le;var fe=B[I].vertexPackage;n.defined(fe.instanceBuffer)&&2===l&&(fe.instanceBounds=new Float32Array(6),o.Cartesian3.pack(de,fe.instanceBounds,0),o.Cartesian3.pack(le,fe.instanceBounds,3))}if(3===l){var ue=new o.Cartesian3;ue.x=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,ue.y=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,ue.z=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT;var Pe=new o.Cartesian3;Pe.x=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,Pe.y=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,Pe.z=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT;var ce=new o.Cartesian3;ce.x=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,ce.y=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,ce.z=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT;var pe=new o.Cartesian3;pe.x=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,pe.y=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT,pe.z=e.getFloat64(L+r,!0),L+=Float64Array.BYTES_PER_ELEMENT}}}function Zt(t,e,r){var a=t.typedArray,n=new r(a.length+e.length);n.set(a,0),n.set(e,a.length),t.typedArray=n}function Xt(t,e,r,A,i,B,C,s,y,d){var l,u=0,P=i.vertexAttributes,c=i.attrLocation;switch(e){case Kt:case Ht:case wt:u=2*Uint16Array.BYTES_PER_ELEMENT,0!=(16&B)||e!==Ht&&e!==wt||(u=2*Float32Array.BYTES_PER_ELEMENT),l=E.ComponentDatatype.createTypedArray(E.ComponentDatatype.UNSIGNED_BYTE,t*u);break;case Vt:case Yt:u=4*Uint8Array.BYTES_PER_ELEMENT,l=E.ComponentDatatype.createTypedArray(E.ComponentDatatype.UNSIGNED_BYTE,4*t);break;case Jt:case kt:u=Float32Array.BYTES_PER_ELEMENT*r,l=E.ComponentDatatype.createTypedArray(E.ComponentDatatype.UNSIGNED_BYTE,t*r*4);break;default:u=4*Uint16Array.BYTES_PER_ELEMENT,l=E.ComponentDatatype.createTypedArray(E.ComponentDatatype.UNSIGNED_BYTE,t*u)}switch(Q.decodeVertexBuffer(l,t,u,A,A.length),e){case Ut:var p=new Uint16Array(l.buffer,0,l.length/2),T=E.ComponentDatatype.SHORT;if(n.defined(C)){var L=o.Cartesian3.unpackArray(p);for(let t=0,e=L.length;t<e;t++){let e=L[t];o.Cartesian3.multiplyByScalar(e,i.vertCompressConstant,e),o.Cartesian3.add(e,i.minVerticesValue,e)}var D=a.Matrix4.multiply(C.sphereMatrix,C.geoMatrix,mt),g=a.Matrix4.multiply(C.ellipsoidMatrix,C.geoMatrix,It);a.Matrix4.inverse(g,g);var F=new f.Ellipsoid(6378137,6378137,6378137);for(let t=0,e=L.length;t<e;t++){let e=L[t];a.Matrix4.multiplyByPoint(D,e,Ft);let r=F.cartesianToCartographic(Ft,Mt),n=gt(r.longitude,r.latitude,r.height,Ft);a.Matrix4.multiplyByPoint(g,n,e)}var M=new Array(3*L.length);o.Cartesian3.packArray(L,M),p=new Float32Array(M),T=E.ComponentDatatype.FLOAT}if(void 0!==(R=c.aPosition)?(Zt(P[R],p,Uint16Array),i.preVertexCount=i.verticesCount,i.verticesCount+=t):(c.aPosition=P.length,P.push({index:c.aPosition,typedArray:p,componentsPerAttribute:4,componentDatatype:T,offsetInBytes:0,strideInBytes:0,normalize:!1}),i.verticesCount=t),!n.defined(C)&&s){var m=new o.Cartographic,I=new o.Cartographic,v=new Float32Array(2*t),_=new Float64Array(2*t),S=new o.Cartesian3,O=new o.Cartesian3,N=new o.Cartographic;F=bt?new f.Ellipsoid(6378137,6378137,6356752.314245179):new f.Ellipsoid(6378137,6378137,6378137);for(var h=0;h<t;h++)a.Matrix4.multiplyByPoint(d,o.Cartesian3.fromElements(p[4*h]*i.vertCompressConstant+i.minVerticesValue.x,p[4*h+1]*i.vertCompressConstant+i.minVerticesValue.y,p[4*h+2]*i.vertCompressConstant+i.minVerticesValue.z,S),O),N=F.cartesianToCartographic(O,Mt),_[2*h]=N.longitude,_[2*h+1]=N.latitude,0===h?(m.longitude=N.longitude,m.latitude=N.latitude,I.longitude=N.longitude,I.latitude=N.latitude):(m.longitude=Math.max(N.longitude,m.longitude),m.latitude=Math.max(N.latitude,m.latitude),I.longitude=Math.min(N.longitude,I.longitude),I.latitude=Math.min(N.latitude,I.latitude));for(h=0;h<t;h++)v[2*h]=_[2*h]-I.longitude,v[2*h+1]=_[2*h+1]-I.latitude;c.img=P.length,P.push({index:c.img,typedArray:v,componentsPerAttribute:2,componentDatatype:E.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:2*Float32Array.BYTES_PER_ELEMENT,normalize:!1}),y.max=m,y.min=I}break;case Kt:var R=c.aNormal,G=new Int16Array(l.buffer,0,l.length/2);void 0!==R?Zt(P[R],G,Uint16Array):(c.aNormal=P.length,P.push({index:c.aNormal,typedArray:G,componentsPerAttribute:2,componentDatatype:E.ComponentDatatype.SHORT,offsetInBytes:0,strideInBytes:0,normalize:!1}));break;case Ht:var x=new Uint16Array(l.buffer,0,l.length/2),b=(R=c.aTexCoord0,T=E.ComponentDatatype.SHORT,Uint16Array);0==(16&B)&&(T=E.ComponentDatatype.FLOAT,b=Float32Array,x=new Float32Array(l.buffer,0,l.length/4)),void 0!==R?Zt(P[R],x,b):(c.aTexCoord0=P.length,P.push({index:c.aTexCoord0,typedArray:x,componentsPerAttribute:2,componentDatatype:T,offsetInBytes:0,strideInBytes:0,normalize:!1}));break;case wt:x=new Uint16Array(l.buffer,0,l.length/2),R=c.aTexCoord1,T=E.ComponentDatatype.SHORT,b=Uint16Array;0==(16&B)&&(T=E.ComponentDatatype.FLOAT,b=Float32Array,x=new Float32Array(l.buffer,0,l.length/4)),void 0!==R?Zt(P[R],x,b):(c.aTexCoord1=P.length,P.push({index:c.aTexCoord1,typedArray:x,componentsPerAttribute:2,componentDatatype:T,offsetInBytes:0,strideInBytes:0,normalize:!1}));break;case Vt:void 0!==(R=c.aColor)?Zt(P[R],l,Uint8Array):(c.aColor=P.length,P.push({index:c.aColor,typedArray:l,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.UNSIGNED_BYTE,offsetInBytes:0,strideInBytes:0,normalize:!0}));break;case Yt:void 0!==(R=c.aSecondColor)?Zt(P[R],l,Uint8Array):(c.aSecondColor=P.length,P.push({index:c.aSecondColor,typedArray:l,componentsPerAttribute:4,componentDatatype:E.ComponentDatatype.BYTE,offsetInBytes:0,strideInBytes:0,normalize:!1}));break;case Jt:x=new Float32Array(l.buffer,0,l.length/4);void 0!==(R=c.aCustom0||c.aVertexWeight)?Zt(P[R],x,Float32Array):(c.aCustom0=P.length,P.push({index:c.aCustom0,typedArray:x,componentsPerAttribute:r,componentDatatype:E.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:0,normalize:!1}));break;case kt:var U=new Float32Array(l.buffer,0,l.length/4);void 0!==(R=c.aCustom1||c.aTextureCoordMatrix)?Zt(P[R],U,Float32Array):(c.aCustom1=P.length,P.push({index:c.aCustom1,typedArray:U,componentsPerAttribute:r,componentDatatype:E.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:0,normalize:!1}))}}function Qt(t,e,r,A,o){var i={},B=[],E=new a.Matrix4,C=t;o=n.defaultValue(o,{});for(var s=0;s<16;s++)E[s]=e.getFloat64(r+A,!0),r+=Float64Array.BYTES_PER_ELEMENT;i.matrix=E,i.skeletonNames=B;var y=e.getUint32(r+A,!0);r+=Uint32Array.BYTES_PER_ELEMENT;for(var d=0;d<y;d++){var l=Pt(e,A,C,r),f=l.string;r=l.bytesOffset,B.push(f),o[f]=E}return{byteOffset:r,geode:i}}function zt(t){var e=t.indexOf("Geometry");if(-1===e)return t;var r=t.substring(e,t.length);return t.replace(r,"")}function jt(t,e,r,A,i,B,E,C){var y={},d=r.getFloat32(A+i,!0);A+=Float32Array.BYTES_PER_ELEMENT;var l=r.getUint16(A+i,!0);A+=Uint16Array.BYTES_PER_ELEMENT,y.rangeMode=l,y.rangeList=d;var u=new o.Cartesian3;u.x=r.getFloat64(A+i,!0),A+=Float64Array.BYTES_PER_ELEMENT,u.y=r.getFloat64(A+i,!0),A+=Float64Array.BYTES_PER_ELEMENT,u.z=r.getFloat64(A+i,!0),A+=Float64Array.BYTES_PER_ELEMENT;var P=r.getFloat64(A+i,!0);if(A+=Float64Array.BYTES_PER_ELEMENT,n.defined(E)){var c=a.Matrix4.clone(E.sphereMatrix,mt),p=a.Matrix4.clone(E.ellipsoidMatrix,It);a.Matrix4.inverse(p,p);var T=new f.Ellipsoid(6378137,6378137,6378137);a.Matrix4.multiplyByPoint(c,u,Ft);let t=T.cartesianToCartographic(Ft,Mt),e=gt(t.longitude,t.latitude,t.height,Ft);a.Matrix4.multiplyByPoint(p,e,u)}if(y.boundingSphere=new s.BoundingSphere(u,P),3===t){var L=new o.Cartesian3;L.x=r.getFloat64(A+i,!0),A+=Float64Array.BYTES_PER_ELEMENT,L.y=r.getFloat64(A+i,!0),A+=Float64Array.BYTES_PER_ELEMENT,L.z=r.getFloat64(A+i,!0),A+=Float64Array.BYTES_PER_ELEMENT;var D=new o.Cartesian3;D.x=r.getFloat64(A+i,!0),A+=Float64Array.BYTES_PER_ELEMENT,D.y=r.getFloat64(A+i,!0),A+=Float64Array.BYTES_PER_ELEMENT,D.z=r.getFloat64(A+i,!0),A+=Float64Array.BYTES_PER_ELEMENT;var g=new o.Cartesian3;g.x=r.getFloat64(A+i,!0),A+=Float64Array.BYTES_PER_ELEMENT,g.y=r.getFloat64(A+i,!0),A+=Float64Array.BYTES_PER_ELEMENT,g.z=r.getFloat64(A+i,!0),A+=Float64Array.BYTES_PER_ELEMENT;var F=new o.Cartesian3;F.x=r.getFloat64(A+i,!0),A+=Float64Array.BYTES_PER_ELEMENT,F.y=r.getFloat64(A+i,!0),A+=Float64Array.BYTES_PER_ELEMENT,F.z=r.getFloat64(A+i,!0),A+=Float64Array.BYTES_PER_ELEMENT,y._obb={xExtent:D,yExtent:g,zExtent:F,obbCenter:L}}var M=e,m=(_=Pt(r,i,M,A)).string;A=_.bytesOffset,m=zt(m=(m=m.replace(/(\.s3mblock)|(\.s3mbz)|(\.s3mb)/gi,"")).replace(/\\/gi,"/")),y.childTile=m,y.geodes=[];var I=r.getUint32(A+i,!0);A+=Uint32Array.BYTES_PER_ELEMENT;for(var v=0;v<I;v++){var _;A=(_=Qt(e,r,A,i,B)).byteOffset,y.geodes.push(_.geode)}return 3===t&&(A=(_=Pt(r,i,M,A)).bytesOffset),{pageLOD:y,bytesOffset:A}}function qt(t,e,r,a,n,A,o){var i=0,B={},E=[],C=r.getUint32(i+a,!0);i+=Uint32Array.BYTES_PER_ELEMENT;for(var s=0;s<C;s++){var y=jt(t,e,r,i,a,n,A);i=y.bytesOffset,E.push(y.pageLOD)}return B.pageLods=E,B}function $t(t,e,r){var a=t.vertexAttributes,n=t.attrLocation,A=a.length;n["aTextureBatchId"+r]=A,a.push({index:A,typedArray:e,componentsPerAttribute:1,componentDatatype:E.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:0})}function te(t,e,r,a){for(var A=r.length,o=0;o<A;o++)for(var i=r[o],B=i.subName.split("_")[0],E=i.subVertexOffsetArr,C=0;C<E.length;C++){var s=E[C],y=s.geoName,d=s.offset,l=s.count,f=s.texUnitIndex,u=e[y].vertexPackage.verticesCount,P=a[y];n.defined(P)||(P=a[y]={});var c=P[f];n.defined(c)||(c=P[f]=new Float32Array(u),p.arrayFill(c,-1));var T=n.defined(t)?t[B]:o;p.arrayFill(c,T,d,d+l)}}function ee(t,e,r){var a=t.vertexAttributes,n=t.attrLocation,A=a.length;n[1===r?"instanceId":"batchId"]=A,a.push({index:A,typedArray:e,componentsPerAttribute:1,componentDatatype:E.ComponentDatatype.FLOAT,offsetInBytes:0,strideInBytes:0,instanceDivisor:r})}function re(t,e,r,a,A){var o=0,i=t,B=e.getUint32(o+r,!0);o+=Uint32Array.BYTES_PER_ELEMENT;for(var E=0;E<B;E++){var C=Pt(e,r,i,o),s=C.string;o=C.bytesOffset;var d=e.getUint32(o+r,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var l={};a[s].pickInfo=l;var f=a[s].edgeGeometry;if(-1==a[s].vertexPackage.instanceIndex){for(var u=new Float32Array(a[s].vertexPackage.verticesCount),P=0;P<d;P++){var c=e.getUint32(o+r,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var T=e.getUint32(o+r,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var L=0,D=0;l[c]={batchId:P};for(var g=0;g<T;g++)D=e.getUint32(o+r,!0),o+=Uint32Array.BYTES_PER_ELEMENT,L=e.getUint32(o+r,!0),o+=Uint32Array.BYTES_PER_ELEMENT,p.arrayFill(u,P,D,D+L);l[c].vertexColorOffset=D,l[c].vertexCount=L}ee(a[s].vertexPackage,u,void 0)}else{var F=a[s].vertexPackage.instanceCount;a[s].vertexPackage.instanceBuffer,a[s].vertexPackage.instanceMode;var M=new Float32Array(F),m=0;for(P=0;P<d;P++){var I=e.getUint32(o+r,!0);o+=Uint32Array.BYTES_PER_ELEMENT;T=e.getUint32(o+r,!0);o+=Uint32Array.BYTES_PER_ELEMENT;for(g=0;g<T;g++){var v=e.getUint32(o+r,!0);if(o+=Uint32Array.BYTES_PER_ELEMENT,M[m]=m,void 0===l[I]&&(l[I]={vertexColorCount:1,instanceIds:[],vertexColorOffset:m}),l[I].instanceIds.push(v),m++,3===A){L=e.getUint32(o+r,!0);o+=Uint32Array.BYTES_PER_ELEMENT}}}ee(a[s].vertexPackage,M,1)}f=a[s].edgeGeometry;if(n.defined(f)){var _,S,O=f.regular.instancesData,N=y.S3MEdgeProcessor.RegularInstanceStride;if(n.defined(O))for(S=O.length,_=0;_<S;_+=N){var h=O[_+9];O[_+9]=u[h]}var R=f.silhouette.instancesData;if(N=y.S3MEdgeProcessor.SilhouetteInstanceStride,n.defined(R))for(S=R.length,_=0;_<S;_+=N){h=R[_+12];R[_+12]=u[h]}}}}function ae(t){return t<1e-10&&t>-1e-10}function ne(t,e,r,a,A,o,i,B){var E=new DataView(t),s=new Uint8Array(t),y=E.getUint32(r,!0);r+=Uint32Array.BYTES_PER_ELEMENT;var d=C.getStringFromTypedArray(s,r,y);d=d.replace(/(\.s3mblock)|(\.s3mbz)|(\.s3mb)/gi,""),r+=y;var l=E.getUint32(r,!0);r+=Uint32Array.BYTES_PER_ELEMENT;for(var f=0;f<l;f++){var u={},p=E.getFloat32(r,!0);r+=Float32Array.BYTES_PER_ELEMENT;var T=E.getUint16(r,!0);r+=Uint16Array.BYTES_PER_ELEMENT,u.rangeMode=T,u.rangeList=p;var L={};L.x=E.getFloat64(r,!0),r+=Float64Array.BYTES_PER_ELEMENT,L.y=E.getFloat64(r,!0),r+=Float64Array.BYTES_PER_ELEMENT,L.z=E.getFloat64(r,!0),r+=Float64Array.BYTES_PER_ELEMENT;var D=E.getFloat64(r,!0);r+=Float64Array.BYTES_PER_ELEMENT,u.boundingSphere={center:L,radius:D},y=E.getUint32(r,!0),r+=Uint32Array.BYTES_PER_ELEMENT;var g=C.getStringFromTypedArray(s,r,y);r+=y,g=zt(g=g.replace(/(\.s3mblock)|(\.s3mbz)|(\.s3mb)/gi,"")),u.childTile=g}var F={},M=E.getFloat32(r,!0);r+=Float32Array.BYTES_PER_ELEMENT;M>=3&&(E.getUint32(r,!0),r+=Uint32Array.BYTES_PER_ELEMENT),E.getUint32(r,!0),r+=Uint32Array.BYTES_PER_ELEMENT;var m=E.getUint32(r,!0);r+=Uint32Array.BYTES_PER_ELEMENT;var I=new Uint8Array(t,r,m),v=r+m,_=c.pako.inflate(I).buffer;B.push(_),E=new DataView(_);s=new Uint8Array(_);r=0;var S=E.getUint32(r,!0),O=ut(E,_,r+=Uint32Array.BYTES_PER_ELEMENT),N=O.buffer;r=O.byteOffset;var h=qt(M,N,E,O.dataViewByteOffset),R=r%4;0!==R&&(r+=4-R),Wt((O=ut(E,_,r)).buffer,E,O.dataViewByteOffset,F,!1,void 0,void 0,M),r=O.byteOffset,3!==M&&((O=ut(E,_,r)).buffer,r=O.byteOffset);var G={};!function(t,e,r,a,A,o,i,B,E,s){var y=B,d=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;for(var l={},f=0;f<d;f++){var u=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var c=C.getStringFromTypedArray(o,y-B,u),p=(y+=u)%4;0!==p&&(y+=4-p),i.getUint32(y,!0),y+=Uint32Array.BYTES_PER_ELEMENT;var T=i.getUint8(y,!0);y+=Uint8Array.BYTES_PER_ELEMENT;var L=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var D=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var g=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var F=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var M,m=i.getUint32(y,!0);if(y+=Uint32Array.BYTES_PER_ELEMENT,a&&T){var I=y-B;M=o.subarray(I,I+F),y+=F}var v=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;for(var _=[],S=0;S<v;S++){u=i.getUint32(y,!0),y+=Uint32Array.BYTES_PER_ELEMENT;var O=C.getStringFromTypedArray(o,y-B,u);y+=u,_.push(O),r[O]=c}var N=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var h=[];for(S=0;S<N;S++){u=i.getUint32(y,!0),y+=Uint32Array.BYTES_PER_ELEMENT;var R=C.getStringFromTypedArray(o,y-B,u);y+=u,h.push(R)}var G=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var x=[],b=void 0,U=c;if(a)b=e[c]={};else{var K=r[c];for(U=K;n.defined(K)&&K!==c;)U=K,K=r[K];n.defined(U)&&(b=e[U])}var H=0;for(S=0;S<G;S++){u=i.getUint32(y,!0),y+=Uint32Array.BYTES_PER_ELEMENT;var w=C.getStringFromTypedArray(o,y-B,u);if(y+=u,a){var V=w.split("_")[0];n.defined(b[V])?H++:b[V]=S-H}var Y=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var J=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var k=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var W=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var Z=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;for(var X=[],Q=0;Q<Z;Q++){u=i.getUint32(y,!0),y+=Uint32Array.BYTES_PER_ELEMENT;var z=C.getStringFromTypedArray(o,y-B,u);y+=u;var j=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var q=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT;var $=i.getUint32(y,!0);y+=Uint32Array.BYTES_PER_ELEMENT,X.push({geoName:z,offset:j,count:q,texUnitIndex:$})}x.push({subName:w,offsetX:Y,offsetY:J,width:k,height:W,subVertexOffsetArr:X})}te(b,t,x,l);var tt=!1;n.defined(M)&&g===P.S3MPixelFormat.CRN_DXT5&&et&&(M=rt({data:M},s).bufferView,tt=!0),E[c]={id:c,rootTextureName:U,width:L,height:D,compressType:g,size:F,format:m,textureData:M,subTexInfos:x,requestNames:h,isDXT:tt}}for(var z in l)if(l.hasOwnProperty(z)){var at=t[z].vertexPackage,nt=l[z];for(var $ in nt)nt.hasOwnProperty($)&&$t(at,nt[$],$)}}(F,a,A,o,0,(O=ut(E,_,r)).buffer,E,O.dataViewByteOffset,G,B),r=O.byteOffset;var x=E.getUint32(r,!0);r+=Uint32Array.BYTES_PER_ELEMENT;var b=s.subarray(r,r+x),U=C.getStringFromTypedArray(b);r+=x;var H=JSON.parse(U);(3===M&&(S=E.getUint32(r,!0),r+=Uint32Array.BYTES_PER_ELEMENT),(S&nt)==nt)&&(re((O=ut(E,_,r)).buffer,E,O.dataViewByteOffset,F,M),r=O.byteOffset);if(1==M){var w=h.pageLods,V=!0;for(f=0;f<w.length;f++){var Y=w[f];V=""===Y.childTile;for(var J=Y.geodes,k=0;k<J.length;k++)for(var W=J[k].skeletonNames,Z=0;Z<W.length;Z++){var Q=W[Z];if(V){var z=F[Q].vertexPackage;z.boundingSphere=K.calcBoundingSphereInWorker(1,z)}}}}i[d]={result:!0,groupNode:h,geoPackage:F,matrials:H,texturePackage:G,version:X.S3M4,dataVersion:M,rootBatchIdMap:a,ancestorMap:A},v<e&&ne(t,e,v,a,A,!1,i,B)}function Ae(e,r){var a=e.buffer;if(bt=e.ellipsoid,e.isOSGB){if(n.defined(yt)||new Promise((function(e,r){t(["./OSGBToS3M-caf1aa52"],e,r)})).then((function(t){(yt=t.default).onRuntimeInitialized=function(){dt=!0},lt=yt.cwrap("OSGBToS3MB","number",["number","number","number","number"])})),!dt)return null;var A;switch(e.suffix){case"dae":case"DAE":A=4;break;case"x":case"X":A=2;break;default:A=0}a=function(t,e,r){var a=yt._malloc(20*e),n=yt._malloc(Uint8Array.BYTES_PER_ELEMENT*e);yt.HEAPU8.set(t,n/Uint8Array.BYTES_PER_ELEMENT);var A=lt(n,e,a,r),o=new Uint8Array(yt.HEAPU8.buffer,a,A);return t=null,t=new Uint8Array(o).buffer,yt._free(a),yt._free(n),t}(new Uint8Array(a),a.byteLength,A)}var o=e.isS3MZ,i=e.fileType,B=e.supportCompressType,E=e.bVolume,s=e.isS3MBlock,d=e.modelMatrix,l=e.materialType,f=e.isCoverImageryLayer,u=e.transformPar,p=null,T=null,L=null;if(E&&e.volbuffer.byteLength<8&&(E=!1),E){var D=e.volbuffer,g=new Uint8Array(D,8),F=c.pako.inflate(g).buffer,M=new Float64Array(F,0,1),m=new Uint32Array(F,48,1);if(0===M[0]||3200===m[0]||3201===m[0]){var I=0;0==M[0]&&(I=8),r.push(F);var v=new Float64Array(F,I,6),_=v[0],S=v[1],O=v[2],N=v[3],h=v[4]<v[5]?v[4]:v[5],R=v[4]>v[5]?v[4]:v[5];T={left:_,top:S,right:O,bottom:N,minHeight:h,maxHeight:R,width:(p=new ft(_,N,O,S,h,R)).width,length:p.length,height:p.height};var G=new Uint32Array(F,48+I,7),x=G[0],b=G[1],U=G[2],H=G[3];L={nFormat:x,nSideBlockCount:b,nBlockLength:U,nLength:H,nWidth:G[4],nHeight:G[5],nDepth:G[6],imageArray:new Uint8Array(F,76+I,H*H*4)}}}var w=0,V={};V.ignoreNormal=e.ignoreNormal;var Y=e.rootBatchIdMap||{},J=e.ancestorMap||{},k={},W=new DataView(a),Z=W.getFloat32(w,!0);if(w+=Float32Array.BYTES_PER_ELEMENT,s)return W.getUint32(w,!0),w+=Uint32Array.BYTES_PER_ELEMENT,ne(a,a.byteLength,w,Y,J,e.isRoot,k,r),k;var Q=!1;if(Z>=3&&(W.getUint32(w,!0),w+=Uint32Array.BYTES_PER_ELEMENT),Z>=2&&(W.getUint32(w,!0),w+=Uint32Array.BYTES_PER_ELEMENT),ae(Z-1)||ae(Z-2)||ae(Z-3)||Z>2.09&&Z<2.11){var z=W.getUint32(w,!0);w+=Uint32Array.BYTES_PER_ELEMENT;var j=new Uint8Array(a,w,z);a=c.pako.inflate(j).buffer,r.push(a),W=new DataView(a),w=0}else if(Z>1.199&&Z<1.201){z=W.getUint32(w,!0);w+=Uint32Array.BYTES_PER_ELEMENT,r.push(a)}else{Q=!0,w=0;z=W.getInt32(w,!0);if(w+=Int32Array.BYTES_PER_ELEMENT,w+=Uint8Array.BYTES_PER_ELEMENT*z,o){W.getUint32(w,!0),w+=Uint32Array.BYTES_PER_ELEMENT;g=new Uint8Array(a,w);a=c.pako.inflate(g).buffer,r.push(a),W=new DataView(a),w=0}}var q=W.getUint32(w,!0),$=ut(W,a,w+=Uint32Array.BYTES_PER_ELEMENT),tt=$.buffer;w=$.byteOffset;var et={},rt=qt(Z,tt,W,$.dataViewByteOffset,et,u),at=w%4;0!==at&&(w+=4-at);var At=Z>2.09&&3!==Z;if(Wt(($=ut(W,a,w)).buffer,W,$.dataViewByteOffset,V,Q,r,At,Z,d,et,f,e.fileType,u),w=$.byteOffset,At)for(var ot=0;ot<rt.pageLods.length;ot++)for(var it=rt.pageLods[ot],Bt=it.geodes,Et=0;Et<Bt.length;Et++)for(var Ct=Bt[Et].skeletonNames,st=0;st<Ct.length;st++){var ct=Ct[st];n.defined(V[ct].max)&&(n.defined(it.max)?(it.max.x=Math.max(V[ct].max.x,it.max.x),it.max.y=Math.max(V[ct].max.y,it.max.y),it.max.z=Math.max(V[ct].max.z,it.max.z),it.min.x=Math.min(V[ct].min.x,it.min.x),it.min.y=Math.min(V[ct].min.y,it.min.y),it.min.z=Math.min(V[ct].min.z,it.min.z)):(it.max=V[ct].max,it.min=V[ct].min))}3!==Z&&(($=ut(W,a,w)).buffer,w=$.byteOffset);var pt={};!function(t,e,r,a,n,A){var o=0,i=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;for(var B=0;B<i;B++){var E=Pt(r,a,e,o),C=E.string,s=(o=E.bytesOffset)%4;0!==s&&(o+=4-s);var d=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var l=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var f=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var u=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var c=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var p=r.getUint32(o+a,!0);o+=Uint32Array.BYTES_PER_ELEMENT;var T=e.subarray(o,o+c);o+=c;var L=null,D=u;u===y.S3MCompressType.enrS3TCDXTN&&1!=t?(y.DXTTextureDecode.decode(L,l,f,T,p),p>P.S3MPixelFormat.BGR||p===P.S3MPixelFormat.LUMINANCE_ALPHA?(L=new Uint8Array(l*f*4),T=new Uint8Array(e.buffer,T.byteOffset,l*f)):(L=new Uint16Array(l*f),T=new Uint16Array(e.buffer,T.byteOffset,c/2)),y.DXTTextureDecode.decode(L,l,f,T,p),A.push(L.buffer),u=0):L=T,n[C]={id:C,width:l,height:f,compressType:u,oriCompressType:D,nFormat:p,imageBuffer:L,mipmapLevel:d}}}(B,($=ut(W,a,w)).buffer,W,$.dataViewByteOffset,pt,r),w=$.byteOffset;var Tt=W.getUint32(w,!0);w+=Uint32Array.BYTES_PER_ELEMENT;var Lt=new Uint8Array(a).subarray(w,w+Tt),Dt=C.getStringFromTypedArray(Lt);w+=Tt,Dt=Dt.replace(/\n\0/,"");var gt=JSON.parse(Dt);(3===Z&&(q=W.getUint32(w,!0),w+=Uint32Array.BYTES_PER_ELEMENT),(q&nt)==nt)&&re(($=ut(W,a,w)).buffer,W,$.dataViewByteOffset,V,Z);if(1==Z){var Ft=rt.pageLods,Mt=!0;for(ot=0;ot<Ft.length;ot++){var mt=Ft[ot];Mt=""===mt.childTile;for(var It=mt.geodes,vt=0;vt<It.length;vt++){Ct=It[vt].skeletonNames;for(var _t=0;_t<Ct.length;_t++){var St=Ct[_t];if(Mt){var Ot=V[St].vertexPackage;Ot.boundingSphere=K.calcBoundingSphereInWorker(i,Ot)}}}}}return"BatchPBR"===l&&function(t,e,r){for(var a in delete t.ignoreNormal,t)if(t.hasOwnProperty(a)){var A=t[a],o=A.arrIndexPackage;if(o.length<1)continue;if(1===o.length||2===o.length&&13===o[1].primitiveType){var i=A.vertexPackage.attrLocation.aTextureCoordMatrix;if(void 0!==i){if((B=(O=A.vertexPackage.vertexAttributes[i]).typedArray)[0]<0)continue}else if(void 0!==(i=A.vertexPackage.attrLocation.aTextureCoordMatrix||A.vertexPackage.attrLocation.aTexCoord0)){O=A.vertexPackage.vertexAttributes[i];var B=new Float32Array(O.typedArray.buffer,O.typedArray.byteOffset,O.typedArray.byteLength/4);if(3===O.componentsPerAttribute&&B[2]<0)continue}}var E,C,s=0,d={},l=void 0;for(E=0,C=o.length;E<C;E++)13!==(N=o[E]).primitiveType?s+=0===N.indexType?N.indicesTypedArray.byteLength/2:N.indicesTypedArray.byteLength/4:l=o[E],0===E&&(d.indicesCount=0,d.indexType=o[E].indexType,d.primitiveType=o[E].primitiveType,d.materialCode=o[E].materialCode);d.indicesCount=s;var f=A.vertexPackage.verticesCount>65535?new Uint32Array(s):new Uint16Array(s),u=0;for(E=0,C=o.length;E<C;E++)if(13!==(N=o[E]).primitiveType){var P=0===N.indexType?Uint16Array:Uint32Array,c=0===N.indexType?N.indicesTypedArray.byteLength/2:N.indicesTypedArray.byteLength/4,p=new P(N.indicesTypedArray.buffer,N.indicesTypedArray.byteOffset,c);f.set(p,u),u+=p.length}d.indicesTypedArray=f,A.arrIndexPackage=[d],n.defined(l)&&(A.arrIndexPackage.push(l),A.edgeGeometry=y.S3MEdgeProcessor.createEdgeDataByIndices(A.vertexPackage,l));var T=2*o.length*4,L=new Float32Array(T),D={};for(E=0,C=r.material.length;E<C;E++)D[(h=r.material[E].material).id]=h;for(E=0,C=o.length;E<C;E++)if(h=D[(N=o[E]).materialCode]){var g=h.pbrMetallicRoughness;if(g){L[8*E]=g.metallicFactor,L[8*E+1]=g.roughnessFactor,L[8*E+2]=h.alphaCutoff;var F=""===h.alphaMode?0:1,M="none"===h.cullMode?0:1;L[8*E+3]=M|F<<16,L[8*E+4]=g.emissiveFactor.x,L[8*E+5]=g.emissiveFactor.y,L[8*E+6]=g.emissiveFactor.z,L[8*E+7]=0,h.pbrIndex=E}}var m="PBRMaterialParam_"+a;for(E=0,C=r.material.length;E<C;E++)if((h=r.material[E].material).id===d.materialCode){h.textureunitstates.push({textureunitstate:{addressmode:{u:0,v:0,w:0},filteringoption:0,filtermax:2,filtermin:2,id:m,texmodmatrix:[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1],url:""}});break}var I,v,_=A.vertexPackage,S=_.attrLocation.aTexCoord1;if(void 0!==S){var O=_.vertexAttributes[S];I=new Float32Array(2*_.verticesCount),O.typedArray=I}else I=new Float32Array(2*_.verticesCount),S=_.vertexAttributes.length,_.attrLocation.aTexCoord1=S,_.vertexAttributes.push({index:S,typedArray:I,componentsPerAttribute:2,componentDatatype:5126,offsetInBytes:0,strideInBytes:8,normalize:!1});for(void 0!==(S=_.attrLocation.aColor)&&(v=(O=_.vertexAttributes[S]).typedArray),E=0,C=o.length;E<C;E++){var N,h;if((h=D[(N=o[E]).materialCode])&&h.pbrMetallicRoughness)for(var R=h.pbrMetallicRoughness.baseColor,G=void 0!==v,x=h.pbrIndex,b=(f=N.indicesTypedArray,0),U=(f=0===N.indexType?new Uint16Array(f.buffer,f.byteOffset,f.byteLength/2):new Uint32Array(f.buffer,f.byteOffset,f.byteLength/4)).length;b<U;b++){var K=f[b];I[2*K]=x,G&&(v[4*K]=255*R.x,v[4*K+1]=255*R.y,v[4*K+2]=255*R.z,v[4*K+3]=255*R.w)}}e[m]={id:m,width:2*o.length,height:1,compressType:0,nFormat:25,imageBuffer:L,mipmapLevel:0}}}(V,pt,gt),{result:!0,groupNode:rt,geoPackage:V,matrials:gt,texturePackage:pt,version:X.S3M4,dataVersion:Z,volImageBuffer:L,volBounds:T}}function oe(){n.defined($)&&n.defined(at)&&($.onRuntimeInitialized=function(){et=!0},self.onmessage=e(Ae),self.postMessage(!0))}return function(r){if("undefined"==typeof WebAssembly)return self.onmessage=e(Ae),void self.postMessage(!0);var A=r.data.webAssemblyConfig;return n.defined(A)?a.FeatureDetection.isInternetExplorer()?t([s.buildModuleUrl("ThirdParty/Workers/ie-webworker-promise-polyfill.js")],(function(e){return self.Promise=e,-1!==A.modulePath.indexOf("crunch")?t([A.modulePath],(function(t){n.defined(A.wasmBinaryFile)?(n.defined(t)||(t=self.Module),$=t,oe()):($=t,oe())})):t([A.modulePath],(function(t){n.defined(A.wasmBinaryFile)?(n.defined(t)||(t=self.DracoDecoderModule),t(A).then((function(t){at=t,oe()}))):(at=t(),oe())}))})):-1!==A.modulePath.indexOf("crunch")?t([A.modulePath],(function(t){n.defined(A.wasmBinaryFile)?(n.defined(t)||(t=self.Module),$=t,oe()):($=t,oe())})):t([A.modulePath],(function(t){n.defined(A.wasmBinaryFile)?(n.defined(t)||(t=self.DracoDecoderModule),t(A).then((function(t){at=t,oe()}))):(at=t(),oe())})):void 0}}));
