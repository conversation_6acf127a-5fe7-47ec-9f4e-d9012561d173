define(["./createTaskProcessorWorker","./when-b60132fc","./earcut-2.2.1-20c8012f"],(function(t,e,r){"use strict";var n=function(t,e,r,n,i){var a,o,s=8*i-n-1,u=(1<<s)-1,l=u>>1,p=-7,c=r?i-1:0,f=r?-1:1,h=t[e+c];for(c+=f,a=h&(1<<-p)-1,h>>=-p,p+=s;p>0;a=256*a+t[e+c],c+=f,p-=8);for(o=a&(1<<-p)-1,a>>=-p,p+=n;p>0;o=256*o+t[e+c],c+=f,p-=8);if(0===a)a=1-l;else{if(a===u)return o?NaN:1/0*(h?-1:1);o+=Math.pow(2,n),a-=l}return(h?-1:1)*o*Math.pow(2,a-n)},i=function(t,e,r,n,i,a){var o,s,u,l=8*a-i-1,p=(1<<l)-1,c=p>>1,f=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,h=n?0:a-1,d=n?1:-1,y=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(s=isNaN(e)?1:0,o=p):(o=Math.floor(Math.log(e)/Math.LN2),e*(u=Math.pow(2,-o))<1&&(o--,u*=2),(e+=o+c>=1?f/u:f*Math.pow(2,1-c))*u>=2&&(o++,u/=2),o+c>=p?(s=0,o=p):o+c>=1?(s=(e*u-1)*Math.pow(2,i),o+=c):(s=e*Math.pow(2,c-1)*Math.pow(2,i),o=0));i>=8;t[r+h]=255&s,h+=d,s/=256,i-=8);for(o=o<<i|s,l+=i;l>0;t[r+h]=255&o,h+=d,o/=256,l-=8);t[r+h-d]|=128*y},a={read:n,write:i};function o(t){this.buf=ArrayBuffer.isView&&ArrayBuffer.isView(t)?t:new Uint8Array(t||0),this.pos=0,this.type=0,this.length=this.buf.length}o.Varint=0,o.Fixed64=1,o.Bytes=2,o.Fixed32=5;var s=4294967296,u=1/s;function l(t,e,r){var n,i,a=r.buf;if(n=(112&(i=a[r.pos++]))>>4,i<128)return c(t,n,e);if(n|=(127&(i=a[r.pos++]))<<3,i<128)return c(t,n,e);if(n|=(127&(i=a[r.pos++]))<<10,i<128)return c(t,n,e);if(n|=(127&(i=a[r.pos++]))<<17,i<128)return c(t,n,e);if(n|=(127&(i=a[r.pos++]))<<24,i<128)return c(t,n,e);if(n|=(1&(i=a[r.pos++]))<<31,i<128)return c(t,n,e);throw new Error("Expected varint not more than 10 bytes")}function p(t){return t.type===o.Bytes?t.readVarint()+t.pos:t.pos+1}function c(t,e,r){return r?4294967296*e+(t>>>0):4294967296*(e>>>0)+(t>>>0)}function f(t,e){var r,n;if(t>=0?(r=t%4294967296|0,n=t/4294967296|0):(n=~(-t/4294967296),4294967295^(r=~(-t%4294967296))?r=r+1|0:(r=0,n=n+1|0)),t>=0x10000000000000000||t<-0x10000000000000000)throw new Error("Given varint doesn't fit into 10 bytes");e.realloc(10),h(r,n,e),d(n,e)}function h(t,e,r){r.buf[r.pos++]=127&t|128,t>>>=7,r.buf[r.pos++]=127&t|128,t>>>=7,r.buf[r.pos++]=127&t|128,t>>>=7,r.buf[r.pos++]=127&t|128,t>>>=7,r.buf[r.pos]=127&t}function d(t,e){var r=(7&t)<<4;e.buf[e.pos++]|=r|((t>>>=3)?128:0),t&&(e.buf[e.pos++]=127&t|((t>>>=7)?128:0),t&&(e.buf[e.pos++]=127&t|((t>>>=7)?128:0),t&&(e.buf[e.pos++]=127&t|((t>>>=7)?128:0),t&&(e.buf[e.pos++]=127&t|((t>>>=7)?128:0),t&&(e.buf[e.pos++]=127&t)))))}function y(t,e,r){var n=e<=16383?1:e<=2097151?2:e<=268435455?3:Math.ceil(Math.log(e)/(7*Math.LN2));r.realloc(n);for(var i=r.pos-1;i>=t;i--)r.buf[i+n]=r.buf[i]}function m(t,e){for(var r=0;r<t.length;r++)e.writeVarint(t[r])}function v(t,e){for(var r=0;r<t.length;r++)e.writeSVarint(t[r])}function g(t,e){for(var r=0;r<t.length;r++)e.writeFloat(t[r])}function x(t,e){for(var r=0;r<t.length;r++)e.writeDouble(t[r])}function b(t,e){for(var r=0;r<t.length;r++)e.writeBoolean(t[r])}function _(t,e){for(var r=0;r<t.length;r++)e.writeFixed32(t[r])}function w(t,e){for(var r=0;r<t.length;r++)e.writeSFixed32(t[r])}function E(t,e){for(var r=0;r<t.length;r++)e.writeFixed64(t[r])}function T(t,e){for(var r=0;r<t.length;r++)e.writeSFixed64(t[r])}function A(t,e){return(t[e]|t[e+1]<<8|t[e+2]<<16)+16777216*t[e+3]}function S(t,e,r){t[r]=e,t[r+1]=e>>>8,t[r+2]=e>>>16,t[r+3]=e>>>24}function I(t,e){return(t[e]|t[e+1]<<8|t[e+2]<<16)+(t[e+3]<<24)}function k(t,e,r){for(var n="",i=e;i<r;){var a,o,s,u=t[i],l=null,p=u>239?4:u>223?3:u>191?2:1;if(i+p>r)break;1===p?u<128&&(l=u):2===p?128==(192&(a=t[i+1]))&&(l=(31&u)<<6|63&a)<=127&&(l=null):3===p?(a=t[i+1],o=t[i+2],128==(192&a)&&128==(192&o)&&((l=(15&u)<<12|(63&a)<<6|63&o)<=2047||l>=55296&&l<=57343)&&(l=null)):4===p&&(a=t[i+1],o=t[i+2],s=t[i+3],128==(192&a)&&128==(192&o)&&128==(192&s)&&((l=(15&u)<<18|(63&a)<<12|(63&o)<<6|63&s)<=65535||l>=1114112)&&(l=null)),null===l?(l=65533,p=1):l>65535&&(l-=65536,n+=String.fromCharCode(l>>>10&1023|55296),l=56320|1023&l),n+=String.fromCharCode(l),i+=p}return n}function M(t,e,r){for(var n,i,a=0;a<e.length;a++){if((n=e.charCodeAt(a))>55295&&n<57344){if(!i){n>56319||a+1===e.length?(t[r++]=239,t[r++]=191,t[r++]=189):i=n;continue}if(n<56320){t[r++]=239,t[r++]=191,t[r++]=189,i=n;continue}n=i-55296<<10|n-56320|65536,i=null}else i&&(t[r++]=239,t[r++]=191,t[r++]=189,i=null);n<128?t[r++]=n:(n<2048?t[r++]=n>>6|192:(n<65536?t[r++]=n>>12|224:(t[r++]=n>>18|240,t[r++]=n>>12&63|128),t[r++]=n>>6&63|128),t[r++]=63&n|128)}return r}function F(t){this._stringToNumber={},this._numberToString=[];for(var e=0;e<t.length;e++){var r=t[e];this._stringToNumber[r]=e,this._numberToString[e]=r}}o.prototype={destroy:function(){this.buf=null},readFields:function(t,e,r){for(r=r||this.length;this.pos<r;){var n=this.readVarint(),i=n>>3,a=this.pos;this.type=7&n,t(i,e,this),this.pos===a&&this.skip(n)}return e},readMessage:function(t,e){return this.readFields(t,e,this.readVarint()+this.pos)},readFixed32:function(){var t=A(this.buf,this.pos);return this.pos+=4,t},readSFixed32:function(){var t=I(this.buf,this.pos);return this.pos+=4,t},readFixed64:function(){var t=A(this.buf,this.pos)+A(this.buf,this.pos+4)*s;return this.pos+=8,t},readSFixed64:function(){var t=A(this.buf,this.pos)+I(this.buf,this.pos+4)*s;return this.pos+=8,t},readFloat:function(){var t=a.read(this.buf,this.pos,!0,23,4);return this.pos+=4,t},readDouble:function(){var t=a.read(this.buf,this.pos,!0,52,8);return this.pos+=8,t},readVarint:function(t){var e,r,n=this.buf;return e=127&(r=n[this.pos++]),r<128?e:(e|=(127&(r=n[this.pos++]))<<7,r<128?e:(e|=(127&(r=n[this.pos++]))<<14,r<128?e:(e|=(127&(r=n[this.pos++]))<<21,r<128?e:l(e|=(15&(r=n[this.pos]))<<28,t,this))))},readVarint64:function(){return this.readVarint(!0)},readSVarint:function(){var t=this.readVarint();return t%2==1?(t+1)/-2:t/2},readBoolean:function(){return Boolean(this.readVarint())},readString:function(){var t=this.readVarint()+this.pos,e=k(this.buf,this.pos,t);return this.pos=t,e},readBytes:function(){var t=this.readVarint()+this.pos,e=this.buf.subarray(this.pos,t);return this.pos=t,e},readPackedVarint:function(t,e){var r=p(this);for(t=t||[];this.pos<r;)t.push(this.readVarint(e));return t},readPackedSVarint:function(t){var e=p(this);for(t=t||[];this.pos<e;)t.push(this.readSVarint());return t},readPackedBoolean:function(t){var e=p(this);for(t=t||[];this.pos<e;)t.push(this.readBoolean());return t},readPackedFloat:function(t){var e=p(this);for(t=t||[];this.pos<e;)t.push(this.readFloat());return t},readPackedDouble:function(t){var e=p(this);for(t=t||[];this.pos<e;)t.push(this.readDouble());return t},readPackedFixed32:function(t){var e=p(this);for(t=t||[];this.pos<e;)t.push(this.readFixed32());return t},readPackedSFixed32:function(t){var e=p(this);for(t=t||[];this.pos<e;)t.push(this.readSFixed32());return t},readPackedFixed64:function(t){var e=p(this);for(t=t||[];this.pos<e;)t.push(this.readFixed64());return t},readPackedSFixed64:function(t){var e=p(this);for(t=t||[];this.pos<e;)t.push(this.readSFixed64());return t},skip:function(t){var e=7&t;if(e===o.Varint)for(;this.buf[this.pos++]>127;);else if(e===o.Bytes)this.pos=this.readVarint()+this.pos;else if(e===o.Fixed32)this.pos+=4;else{if(e!==o.Fixed64)throw new Error("Unimplemented type: "+e);this.pos+=8}},writeTag:function(t,e){this.writeVarint(t<<3|e)},realloc:function(t){for(var e=this.length||16;e<this.pos+t;)e*=2;if(e!==this.length){var r=new Uint8Array(e);r.set(this.buf),this.buf=r,this.length=e}},finish:function(){return this.length=this.pos,this.pos=0,this.buf.subarray(0,this.length)},writeFixed32:function(t){this.realloc(4),S(this.buf,t,this.pos),this.pos+=4},writeSFixed32:function(t){this.realloc(4),S(this.buf,t,this.pos),this.pos+=4},writeFixed64:function(t){this.realloc(8),S(this.buf,-1&t,this.pos),S(this.buf,Math.floor(t*u),this.pos+4),this.pos+=8},writeSFixed64:function(t){this.realloc(8),S(this.buf,-1&t,this.pos),S(this.buf,Math.floor(t*u),this.pos+4),this.pos+=8},writeVarint:function(t){(t=+t||0)>268435455||t<0?f(t,this):(this.realloc(4),this.buf[this.pos++]=127&t|(t>127?128:0),t<=127||(this.buf[this.pos++]=127&(t>>>=7)|(t>127?128:0),t<=127||(this.buf[this.pos++]=127&(t>>>=7)|(t>127?128:0),t<=127||(this.buf[this.pos++]=t>>>7&127))))},writeSVarint:function(t){this.writeVarint(t<0?2*-t-1:2*t)},writeBoolean:function(t){this.writeVarint(Boolean(t))},writeString:function(t){t=String(t),this.realloc(4*t.length),this.pos++;var e=this.pos;this.pos=M(this.buf,t,this.pos);var r=this.pos-e;r>=128&&y(e,r,this),this.pos=e-1,this.writeVarint(r),this.pos+=r},writeFloat:function(t){this.realloc(4),a.write(this.buf,t,this.pos,!0,23,4),this.pos+=4},writeDouble:function(t){this.realloc(8),a.write(this.buf,t,this.pos,!0,52,8),this.pos+=8},writeBytes:function(t){var e=t.length;this.writeVarint(e),this.realloc(e);for(var r=0;r<e;r++)this.buf[this.pos++]=t[r]},writeRawMessage:function(t,e){this.pos++;var r=this.pos;t(e,this);var n=this.pos-r;n>=128&&y(r,n,this),this.pos=r-1,this.writeVarint(n),this.pos+=n},writeMessage:function(t,e,r){this.writeTag(t,o.Bytes),this.writeRawMessage(e,r)},writePackedVarint:function(t,e){this.writeMessage(t,m,e)},writePackedSVarint:function(t,e){this.writeMessage(t,v,e)},writePackedBoolean:function(t,e){this.writeMessage(t,b,e)},writePackedFloat:function(t,e){this.writeMessage(t,g,e)},writePackedDouble:function(t,e){this.writeMessage(t,x,e)},writePackedFixed32:function(t,e){this.writeMessage(t,_,e)},writePackedSFixed32:function(t,e){this.writeMessage(t,w,e)},writePackedFixed64:function(t,e){this.writeMessage(t,E,e)},writePackedSFixed64:function(t,e){this.writeMessage(t,T,e)},writeBytesField:function(t,e){this.writeTag(t,o.Bytes),this.writeBytes(e)},writeFixed32Field:function(t,e){this.writeTag(t,o.Fixed32),this.writeFixed32(e)},writeSFixed32Field:function(t,e){this.writeTag(t,o.Fixed32),this.writeSFixed32(e)},writeFixed64Field:function(t,e){this.writeTag(t,o.Fixed64),this.writeFixed64(e)},writeSFixed64Field:function(t,e){this.writeTag(t,o.Fixed64),this.writeSFixed64(e)},writeVarintField:function(t,e){this.writeTag(t,o.Varint),this.writeVarint(e)},writeSVarintField:function(t,e){this.writeTag(t,o.Varint),this.writeSVarint(e)},writeStringField:function(t,e){this.writeTag(t,o.Bytes),this.writeString(e)},writeFloatField:function(t,e){this.writeTag(t,o.Fixed32),this.writeFloat(e)},writeDoubleField:function(t,e){this.writeTag(t,o.Fixed64),this.writeDouble(e)},writeBooleanField:function(t,e){this.writeVarintField(t,Boolean(e))}},F.prototype.encode=function(t){return this._stringToNumber[t]},F.prototype.decode=function(t){return this._numberToString[t]};var O={Int8:Int8Array,Uint8:Uint8Array,Int16:Int16Array,Uint16:Uint16Array,Int32:Int32Array,Uint32:Uint32Array,Float32:Float32Array};function P(t,e){void 0===e&&(e=1);var r=0,n=0;return{members:t.map((function(t){var i=z(t.type),a=r=R(r,Math.max(e,i)),o=t.components||1;return n=Math.max(n,i),r+=i*o,{name:t.name,type:t.type,components:o,offset:a}})),size:R(r,Math.max(n,e)),alignment:e}}function z(t){return O[t].BYTES_PER_ELEMENT}function R(t,e){return Math.ceil(t/e)*e}var C=1,B=function(t,e){var r=e.pixelRatio,n=e.version,i=e.stretchX,a=e.stretchY,o=e.content;this.paddedRect=t,this.pixelRatio=r,this.stretchX=i,this.stretchY=a,this.content=o,this.version=n},D={tl:{configurable:!0},br:{configurable:!0},tlbr:{configurable:!0},displaySize:{configurable:!0}};D.tl.get=function(){return[this.paddedRect.x+C,this.paddedRect.y+C]},D.br.get=function(){return[this.paddedRect.x+this.paddedRect.w-C,this.paddedRect.y+this.paddedRect.h-C]},D.tlbr.get=function(){return this.tl.concat(this.br)},D.displaySize.get=function(){return[(this.paddedRect.w-2*C)/this.pixelRatio,(this.paddedRect.h-2*C)/this.pixelRatio]},Object.defineProperties(B.prototype,D);var V=function(t,e){U(this,t,4,e)};function U(t,e,r,n){var i=e.width,a=e.height;if(n){if(n instanceof Uint8ClampedArray)n=new Uint8Array(n.buffer);else if(n.length!==i*a*r)throw new RangeError("mismatched image size")}else n=new Uint8Array(i*a*r);return t.width=i,t.height=a,t.data=n,t}function N(t,e,r){var n=e.width,i=e.height;if(n!==t.width||i!==t.height){var a=U({},{width:n,height:i},r);L(t,a,{x:0,y:0},{x:0,y:0},{width:Math.min(t.width,n),height:Math.min(t.height,i)},r),t.width=n,t.height=i,t.data=a.data}}function L(t,e,r,n,i,a){if(0===i.width||0===i.height)return e;if(i.width>t.width||i.height>t.height||r.x>t.width-i.width||r.y>t.height-i.height)throw new RangeError("out of range source coordinates for image copy");if(i.width>e.width||i.height>e.height||n.x>e.width-i.width||n.y>e.height-i.height)throw new RangeError("out of range destination coordinates for image copy");for(var o=t.data,s=e.data,u=0;u<i.height;u++)for(var l=((r.y+u)*t.width+r.x)*a,p=((n.y+u)*e.width+n.x)*a,c=0;c<i.width*a;c++)s[p+c]=o[l+c];return e}function X(t){for(var e=0,r=0,n=0,i=t;n<i.length;n+=1){var a=i[n];e+=a.w*a.h,r=Math.max(r,a.w)}t.sort((function(t,e){return e.h-t.h}));for(var o=[{x:0,y:0,w:Math.max(Math.ceil(Math.sqrt(e/.95)),r),h:1/0}],s=0,u=0,l=0,p=t;l<p.length;l+=1)for(var c=p[l],f=o.length-1;f>=0;f--){var h=o[f];if(!(c.w>h.w||c.h>h.h)){if(c.x=h.x,c.y=h.y,u=Math.max(u,c.y+c.h),s=Math.max(s,c.x+c.w),c.w===h.w&&c.h===h.h){var d=o.pop();f<o.length&&(o[f]=d)}else c.h===h.h?(h.x+=c.w,h.w-=c.w):c.w===h.w?(h.y+=c.h,h.h-=c.h):(o.push({x:h.x+c.w,y:h.y,w:h.w-c.w,h:c.h}),h.y+=c.h,h.h-=c.h);break}}return{w:s,h:u,fill:e/(s*u)||0}}V.prototype.resize=function(t){N(this,t,4)},V.prototype.replace=function(t,e){e?this.data.set(t):t instanceof Uint8ClampedArray?this.data=new Uint8Array(t.buffer):this.data=t},V.prototype.clone=function(){return new V({width:this.width,height:this.height},new Uint8Array(this.data))},V.copy=function(t,e,r,n,i){L(t,e,r,n,i,4)};var q=1,j=function(t,e){var r={},n={};this.haveRenderCallbacks=[];var i=[];this.addImages(t,r,i),this.addImages(e,n,i);var a=X(i),o=a.w,s=a.h,u=new V({width:o||1,height:s||1});for(var l in t){var p=t[l],c=r[l].paddedRect;V.copy(p.data,u,{x:0,y:0},{x:c.x+q,y:c.y+q},p.data)}for(var f in e){var h=e[f],d=n[f].paddedRect,y=d.x+q,m=d.y+q,v=h.data.width,g=h.data.height;V.copy(h.data,u,{x:0,y:0},{x:y,y:m},h.data),V.copy(h.data,u,{x:0,y:g-1},{x:y,y:m-1},{width:v,height:1}),V.copy(h.data,u,{x:0,y:0},{x:y,y:m+g},{width:v,height:1}),V.copy(h.data,u,{x:v-1,y:0},{x:y-1,y:m},{width:1,height:g}),V.copy(h.data,u,{x:0,y:0},{x:y+v,y:m},{width:1,height:g})}this.image=u,this.iconPositions=r,this.patternPositions=n};j.prototype.addImages=function(t,e,r){for(var n in t){var i=t[n],a={x:0,y:0,w:i.data.width+2*q,h:i.data.height+2*q};r.push(a),e[n]=new B(a,i),i.hasRenderCallback&&this.haveRenderCallbacks.push(n)}},j.prototype.patchUpdatedImages=function(t,e){for(var r in t.dispatchRenderCallbacks(this.haveRenderCallbacks),t.updatedImages)this.patchUpdatedImage(this.iconPositions[r],t.getImage(r),e),this.patchUpdatedImage(this.patternPositions[r],t.getImage(r),e)},j.prototype.patchUpdatedImage=function(t,e,r){if(t&&e&&t.version!==e.version){t.version=e.version;var n=t.tl,i=n[0],a=n[1];r.update(e.data,void 0,{x:i,y:a})}};var H={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],rebeccapurple:[102,51,153,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function Y(t){return(t=Math.round(t))<0?0:t>255?255:t}function Q(t){return t<0?0:t>1?1:t}function K(t){return"%"===t[t.length-1]?Y(parseFloat(t)/100*255):Y(parseInt(t))}function G(t){return"%"===t[t.length-1]?Q(parseFloat(t)/100):Q(parseFloat(t))}function W(t,e,r){return r<0?r+=1:r>1&&(r-=1),6*r<1?t+(e-t)*r*6:2*r<1?e:3*r<2?t+(e-t)*(2/3-r)*6:t}function J(t){var e,r=t.replace(/ /g,"").toLowerCase();if(r in H)return H[r].slice();if("#"===r[0])return 4===r.length?(e=parseInt(r.substr(1),16))>=0&&e<=4095?[(3840&e)>>4|(3840&e)>>8,240&e|(240&e)>>4,15&e|(15&e)<<4,1]:null:7===r.length&&(e=parseInt(r.substr(1),16))>=0&&e<=16777215?[(16711680&e)>>16,(65280&e)>>8,255&e,1]:null;var n=r.indexOf("("),i=r.indexOf(")");if(-1!==n&&i+1===r.length){var a=r.substr(0,n),o=r.substr(n+1,i-(n+1)).split(","),s=1;switch(a){case"rgba":if(4!==o.length)return null;s=G(o.pop());case"rgb":return 3!==o.length?null:[K(o[0]),K(o[1]),K(o[2]),s];case"hsla":if(4!==o.length)return null;s=G(o.pop());case"hsl":if(3!==o.length)return null;var u=(parseFloat(o[0])%360+360)%360/360,l=G(o[1]),p=G(o[2]),c=p<=.5?p*(l+1):p+l-p*l,f=2*p-c;return[Y(255*W(f,c,u+1/3)),Y(255*W(f,c,u)),Y(255*W(f,c,u-1/3)),s];default:return null}}return null}var Z=function(t,e,r,n){void 0===n&&(n=1),this.r=t,this.g=e,this.b=r,this.a=n};Z.parse=function(t){if(t){if(t instanceof Z)return t;if("string"==typeof t){var e=J(t);if(e)return new Z(e[0]/255*e[3],e[1]/255*e[3],e[2]/255*e[3],e[3])}}},Z.prototype.toString=function(){var t=this.toArray(),e=t[0],r=t[1],n=t[2],i=t[3];return"rgba("+Math.round(e)+","+Math.round(r)+","+Math.round(n)+","+i+")"},Z.prototype.toArray=function(){var t=this,e=t.r,r=t.g,n=t.b,i=t.a;return 0===i?[0,0,0,0]:[255*e/i,255*r/i,255*n/i,i]},Z.black=new Z(0,0,0,1),Z.white=new Z(1,1,1,1),Z.transparent=new Z(0,0,0,0),Z.red=new Z(1,0,0,1);var $={kind:"null"},tt={kind:"number"},et={kind:"string"},rt={kind:"boolean"},nt={kind:"color"},it={kind:"object"},at={kind:"value"},ot={kind:"formatted"},st={kind:"resolvedImage"};function ut(t,e){return{kind:"array",itemType:t,N:e}}function lt(t){if("array"===t.kind){var e=lt(t.itemType);return"number"==typeof t.N?"array<"+e+", "+t.N+">":"value"===t.itemType.kind?"array":"array<"+e+">"}return t.kind}var pt=[$,tt,et,rt,nt,ot,it,ut(at),st];function ct(t,e){if("error"===e.kind)return null;if("array"===t.kind){if("array"===e.kind&&(0===e.N&&"value"===e.itemType.kind||!ct(t.itemType,e.itemType))&&("number"!=typeof t.N||t.N===e.N))return null}else{if(t.kind===e.kind)return null;if("value"===t.kind)for(var r=0,n=pt;r<n.length;r+=1){if(!ct(n[r],e))return null}}return"Expected "+lt(t)+" but found "+lt(e)+" instead."}var ft=function(t,e,r){this.sensitivity=t?e?"variant":"case":e?"accent":"base",this.locale=r,this.collator=new Intl.Collator(this.locale?this.locale:[],{sensitivity:this.sensitivity,usage:"search"})};ft.prototype.compare=function(t,e){return this.collator.compare(t,e)},ft.prototype.resolvedLocale=function(){return new Intl.Collator(this.locale?this.locale:[]).resolvedOptions().locale};var ht=function(t,e,r,n,i){this.text=t,this.image=e,this.scale=r,this.fontStack=n,this.textColor=i},dt=function(t){this.sections=t};dt.fromString=function(t){return new dt([new ht(t,null,null,null,null)])},dt.prototype.isEmpty=function(){return 0===this.sections.length||!this.sections.some((function(t){return 0!==t.text.length||t.image&&0!==t.image.name.length}))},dt.factory=function(t){return t instanceof dt?t:dt.fromString(t)},dt.prototype.toString=function(){return 0===this.sections.length?"":this.sections.map((function(t){return t.text})).join("")},dt.prototype.serialize=function(){for(var t=["format"],e=0,r=this.sections;e<r.length;e+=1){var n=r[e];if(n.image)t.push(["image",n.image.name]);else{t.push(n.text);var i={};n.fontStack&&(i["text-font"]=["literal",n.fontStack.split(",")]),n.scale&&(i["font-scale"]=n.scale),n.textColor&&(i["text-color"]=["rgba"].concat(n.textColor.toArray())),t.push(i)}}return t};var yt=function(t){this.name=t.name,this.available=t.available};yt.prototype.toString=function(){return this.name},yt.fromString=function(t){return new yt({name:t,available:!1})},yt.prototype.serialize=function(){return["image",this.name]};var mt={kind:"null"},vt={kind:"number"},gt={kind:"string"},xt={kind:"boolean"},bt={kind:"color"},_t={kind:"object"},wt={kind:"value"},Et={kind:"collator"},Tt={kind:"formatted"},At={kind:"resolvedImage"};function St(t,e){return{kind:"array",itemType:t,N:e}}function It(){}It.validateRGBA=function(t,e,r,n){return"number"==typeof t&&t>=0&&t<=255&&"number"==typeof e&&e>=0&&e<=255&&"number"==typeof r&&r>=0&&r<=255?void 0===n||"number"==typeof n&&n>=0&&n<=1?null:"Invalid rgba value ["+[t,e,r,n].join(", ")+"]: 'a' must be between 0 and 1.":"Invalid rgba value ["+("number"==typeof n?[t,e,r,n]:[t,e,r]).join(", ")+"]: 'r', 'g', and 'b' must be between 0 and 255."},It.isValue=function(t){if(null===t)return!0;if("string"==typeof t)return!0;if("boolean"==typeof t)return!0;if("number"==typeof t)return!0;if(t instanceof Z)return!0;if(t instanceof ft)return!0;if(t instanceof dt)return!0;if(t instanceof yt)return!0;if(Array.isArray(t)){for(var e=0,r=t;e<r.length;e+=1){var n=r[e];if(!It.isValue(n))return!1}return!0}if("object"==typeof t){for(var i in t)if(!It.isValue(t[i]))return!1;return!0}return!1},It.typeOf=function(t){if(null===t)return mt;if("string"==typeof t)return gt;if("boolean"==typeof t)return xt;if("number"==typeof t)return vt;if(t instanceof Z)return bt;if(t instanceof ft)return Et;if(t instanceof dt)return Tt;if(t instanceof yt)return At;if(Array.isArray(t)){for(var e,r=t.length,n=0,i=t;n<i.length;n+=1){var a=i[n],o=It.typeOf(a);if(e){if(e===o)continue;e=wt;break}e=o}return St(e||wt,r)}return _t},It.toString$1=function(t){var e=typeof t;return null===t?"":"string"===e||"number"===e||"boolean"===e?String(t):t instanceof Z||t instanceof dt||t instanceof yt?t.toString():JSON.stringify(t)};var kt={kind:"number"},Mt={kind:"string"},Ft={kind:"boolean"},Ot={kind:"object"},Pt={kind:"value"};function zt(t,e){return{kind:"array",itemType:t,N:e}}var Rt={string:Mt,number:kt,boolean:Ft,object:Ot},Ct=function(t,e){this.type=t,this.args=e};Ct.parse=function(t,e){if(t.length<2)return e.error("Expected at least one argument.");var r,n=1,i=t[0];if("array"===i){var a,o;if(t.length>2){var s=t[1];if("string"!=typeof s||!(s in Rt)||"object"===s)return e.error('The item type argument of "array" must be one of string, number, boolean',1);a=Rt[s],n++}else a=Pt;if(t.length>3){if(null!==t[2]&&("number"!=typeof t[2]||t[2]<0||t[2]!==Math.floor(t[2])))return e.error('The length argument to "array" must be a positive integer literal',2);o=t[2],n++}r=zt(a,o)}else r=Rt[i];for(var u=[];n<t.length;n++){var l=e.parse(t[n],n,Pt);if(!l)return null;u.push(l)}return new Ct(r,u)},Ct.prototype.evaluate=function(t){for(var e=0;e<this.args.length;e++){var r=this.args[e].evaluate(t);if(!ct(this.type,It.typeOf(r)))return r;if(e===this.args.length-1)throw new RuntimeError("Expected value to be of type "+toString(this.type)+", but found "+toString(It.typeOf(r))+" instead.")}return null},Ct.prototype.eachChild=function(t){this.args.forEach(t)},Ct.prototype.possibleOutputs=function(){var t;return(t=[]).concat.apply(t,this.args.map((function(t){return t.possibleOutputs()})))},Ct.prototype.serialize=function(){var t=this.type,e=[t.kind];if("array"===t.kind){var r=t.itemType;if("string"===r.kind||"number"===r.kind||"boolean"===r.kind){e.push(r.kind);var n=t.N;("number"==typeof n||this.args.length>1)&&e.push(n)}}return e.concat(this.args.map((function(t){return t.serialize()})))};var Bt={kind:"number"},Dt={kind:"value"};function Vt(t,e){return{kind:"array",itemType:t,N:e}}var Ut=function(t,e,r){this.type=t,this.index=e,this.input=r};Ut.parse=function(t,e){if(3!==t.length)return e.error("Expected 2 arguments, but found "+(t.length-1)+" instead.");var r=e.parse(t[1],1,Bt),n=e.parse(t[2],2,Vt(e.expectedType||Dt));if(!r||!n)return null;var i=n.type;return new Ut(i.itemType,r,n)},Ut.prototype.evaluate=function(t){var e=this.index.evaluate(t),r=this.input.evaluate(t);if(e<0)throw new RuntimeError("Array index out of bounds: "+e+" < 0.");if(e>=r.length)throw new RuntimeError("Array index out of bounds: "+e+" > "+(r.length-1)+".");if(e!==Math.floor(e))throw new RuntimeError("Array index must be an integer, but found "+e+" instead.");return r[e]},Ut.prototype.eachChild=function(t){t(this.index),t(this.input)},Ut.prototype.possibleOutputs=function(){return[void 0]},Ut.prototype.serialize=function(){return["at",this.index.serialize(),this.input.serialize()]};var Nt={kind:"boolean"},Lt=function(t,e,r){this.type=t,this.branches=e,this.otherwise=r};Lt.parse=function(t,e){if(t.length<4)return e.error("Expected at least 3 arguments, but found only "+(t.length-1)+".");if(t.length%2!=0)return e.error("Expected an odd number of arguments.");var r;e.expectedType&&"value"!==e.expectedType.kind&&(r=e.expectedType);for(var n=[],i=1;i<t.length-1;i+=2){var a=e.parse(t[i],i,Nt);if(!a)return null;var o=e.parse(t[i+1],i+1,r);if(!o)return null;n.push([a,o]),r=r||o.type}var s=e.parse(t[t.length-1],t.length-1,r);return s?new Lt(r,n,s):null},Lt.prototype.evaluate=function(t){for(var e=0,r=this.branches;e<r.length;e+=1){var n=r[e],i=n[0],a=n[1];if(i.evaluate(t))return a.evaluate(t)}return this.otherwise.evaluate(t)},Lt.prototype.eachChild=function(t){for(var e=0,r=this.branches;e<r.length;e+=1){var n=r[e],i=n[0],a=n[1];t(i),t(a)}t(this.otherwise)},Lt.prototype.possibleOutputs=function(){var t;return(t=[]).concat.apply(t,this.branches.map((function(t){return t[0],t[1].possibleOutputs()}))).concat(this.otherwise.possibleOutputs())},Lt.prototype.serialize=function(){var t=["case"];return this.eachChild((function(e){t.push(e.serialize())})),t};var Xt={kind:"value"},qt=function(t,e){this.type=t,this.args=e};qt.parse=function(t,e){if(t.length<2)return e.error("Expectected at least one argument.");var r=null,n=e.expectedType;n&&"value"!==n.kind&&(r=n);for(var i=[],a=0,o=t.slice(1);a<o.length;a+=1){var s=o[a],u=e.parse(s,1+i.length,r,void 0,{typeAnnotation:"omit"});if(!u)return null;r=r||u.type,i.push(u)}var l=n&&i.some((function(t){return ct(n,t.type)}));return new qt(l?Xt:r,i)},qt.prototype.evaluate=function(t){for(var e,r=null,n=0,i=0,a=this.args;i<a.length;i+=1){if(n++,(r=a[i].evaluate(t))&&r instanceof yt&&!r.available&&(e||(e=r.name),r=null,n===this.args.length&&(r=e)),null!==r)break}return r},qt.prototype.eachChild=function(t){this.args.forEach(t)},qt.prototype.possibleOutputs=function(){var t;return(t=[]).concat.apply(t,this.args.map((function(t){return t.possibleOutputs()})))},qt.prototype.serialize=function(){var t=["coalesce"];return this.eachChild((function(e){t.push(e.serialize())})),t};var jt={kind:"number"},Ht={kind:"string"},Yt={kind:"boolean"},Qt={kind:"color"},Kt={kind:"value"},Gt={"to-boolean":Yt,"to-color":Qt,"to-number":jt,"to-string":Ht},Wt=function(t,e){this.type=t,this.args=e};Wt.parse=function(t,e){if(t.length<2)return e.error("Expected at least one argument.");var r=t[0];if(("to-boolean"===r||"to-string"===r)&&2!==t.length)return e.error("Expected one argument.");for(var n=Gt[r],i=[],a=1;a<t.length;a++){var o=e.parse(t[a],a,Kt);if(!o)return null;i.push(o)}return new Wt(n,i)},Wt.prototype.evaluate=function(t){if("boolean"===this.type.kind)return Boolean(this.args[0].evaluate(t));if("color"===this.type.kind){for(var e,r,n=0,i=this.args;n<i.length;n+=1){if(r=null,(e=i[n].evaluate(t))instanceof Z)return e;if("string"==typeof e){var a=t.parseColor(e);if(a)return a}else if(Array.isArray(e)&&!(r=e.length<3||e.length>4?"Invalid rbga value "+JSON.stringify(e)+": expected an array containing either three or four numeric values.":validateRGBA(e[0],e[1],e[2],e[3])))return new Z(e[0]/255,e[1]/255,e[2]/255,e[3])}throw new RuntimeError(r||"Could not parse color from value '"+("string"==typeof e?e:String(JSON.stringify(e)))+"'")}if("number"===this.type.kind){for(var o=null,s=0,u=this.args;s<u.length;s+=1){if(null===(o=u[s].evaluate(t)))return 0;var l=Number(o);if(!isNaN(l))return l}throw new RuntimeError("Could not convert "+JSON.stringify(o)+" to number.")}return"formatted"===this.type.kind?Formatted.fromString(It.toString$1(this.args[0].evaluate(t))):"resolvedImage"===this.type.kind?yt.fromString(It.toString$1(this.args[0].evaluate(t))):It.toString$1(this.args[0].evaluate(t))},Wt.prototype.eachChild=function(t){this.args.forEach(t)},Wt.prototype.possibleOutputs=function(){var t;return(t=[]).concat.apply(t,this.args.map((function(t){return t.possibleOutputs()})))},Wt.prototype.serialize=function(){if("formatted"===this.type.kind)return new FormatExpression([{content:this.args[0],scale:null,font:null,textColor:null}]).serialize();if("resolvedImage"===this.type.kind)return new ImageExpression(this.args[0]).serialize();var t=["to-"+this.type.kind];return this.eachChild((function(e){t.push(e.serialize())})),t};var Jt={kind:"string"},Zt={kind:"boolean"},$t={kind:"collator"},te=function(t,e,r){this.type=$t,this.locale=r,this.caseSensitive=t,this.diacriticSensitive=e};te.parse=function(t,e){if(2!==t.length)return e.error("Expected one argument.");var r=t[1];if("object"!=typeof r||Array.isArray(r))return e.error("Collator options argument must be an object.");var n=e.parse(void 0!==r["case-sensitive"]&&r["case-sensitive"],1,Zt);if(!n)return null;var i=e.parse(void 0!==r["diacritic-sensitive"]&&r["diacritic-sensitive"],1,Zt);if(!i)return null;var a=null;return r.locale&&!(a=e.parse(r.locale,1,Jt))?null:new te(n,i,a)},te.prototype.evaluate=function(t){return new ft(this.caseSensitive.evaluate(t),this.diacriticSensitive.evaluate(t),this.locale?this.locale.evaluate(t):null)},te.prototype.eachChild=function(t){t(this.caseSensitive),t(this.diacriticSensitive),this.locale&&t(this.locale)},te.prototype.possibleOutputs=function(){return[void 0]},te.prototype.serialize=function(){var t={};return t["case-sensitive"]=this.caseSensitive.serialize(),t["diacritic-sensitive"]=this.diacriticSensitive.serialize(),this.locale&&(t.locale=this.locale.serialize()),["collator",t]};var ee={kind:"boolean"},re={kind:"value"},ne={kind:"collator"};function ie(t,e){return"=="===t||"!="===t?"boolean"===e.kind||"string"===e.kind||"number"===e.kind||"null"===e.kind||"value"===e.kind:"string"===e.kind||"number"===e.kind||"value"===e.kind}function ae(t,e,r){return e===r}function oe(t,e,r){return e!==r}function se(t,e,r){return e<r}function ue(t,e,r){return e>r}function le(t,e,r){return e<=r}function pe(t,e,r){return e>=r}function ce(t,e,r,n){return 0===n.compare(e,r)}function fe(t,e,r,n){return!ce(0,e,r,n)}function he(t,e,r,n){return n.compare(e,r)<0}function de(t,e,r,n){return n.compare(e,r)>0}function ye(t,e,r,n){return n.compare(e,r)<=0}function me(t,e,r,n){return n.compare(e,r)>=0}function ve(t,e,r){var n="=="!==t&&"!="!==t;return function(){function i(t,e,r){this.type=ee,this.lhs=t,this.rhs=e,this.collator=r,this.hasUntypedArgument="value"===t.type.kind||"value"===e.type.kind}return i.parse=function(t,e){if(3!==t.length&&4!==t.length)return e.error("Expected two or three arguments.");var r=t[0],a=e.parse(t[1],1,re);if(!a)return null;if(!ie(r,a.type))return e.concat(1).error('"'+r+"\" comparisons are not supported for type '"+toString(a.type)+"'.");var o=e.parse(t[2],2,re);if(!o)return null;if(!ie(r,o.type))return e.concat(2).error('"'+r+"\" comparisons are not supported for type '"+toString(o.type)+"'.");if(a.type.kind!==o.type.kind&&"value"!==a.type.kind&&"value"!==o.type.kind)return e.error("Cannot compare types '"+toString(a.type)+"' and '"+toString(o.type)+"'.");n&&("value"===a.type.kind&&"value"!==o.type.kind?a=new Ct(o.type,[a]):"value"!==a.type.kind&&"value"===o.type.kind&&(o=new Ct(a.type,[o])));var s=null;if(4===t.length){if("string"!==a.type.kind&&"string"!==o.type.kind&&"value"!==a.type.kind&&"value"!==o.type.kind)return e.error("Cannot use collator to compare non-string types.");if(!(s=e.parse(t[3],3,ne)))return null}return new i(a,o,s)},i.prototype.evaluate=function(i){var a=this.lhs.evaluate(i),o=this.rhs.evaluate(i);if(n&&this.hasUntypedArgument){var s=It.typeOf(a),u=It.typeOf(o);if(s.kind!==u.kind||"string"!==s.kind&&"number"!==s.kind)throw new RuntimeError('Expected arguments for "'+t+'" to be (string, string) or (number, number), but found ('+s.kind+", "+u.kind+") instead.")}if(this.collator&&!n&&this.hasUntypedArgument){var l=It.typeOf(a),p=It.typeOf(o);if("string"!==l.kind||"string"!==p.kind)return e(i,a,o)}return this.collator?r(i,a,o,this.collator.evaluate(i)):e(i,a,o)},i.prototype.eachChild=function(t){t(this.lhs),t(this.rhs),this.collator&&t(this.collator)},i.prototype.possibleOutputs=function(){return[!0,!1]},i.prototype.serialize=function(){var e=[t];return this.eachChild((function(t){e.push(t.serialize())})),e},i}()}var ge={};ge.Equals=ve("==",ae,ce),ge.NotEquals=ve("!=",oe,fe),ge.LessThan=ve("<",se,he),ge.GreaterThan=ve(">",ue,de),ge.LessThanOrEqual=ve("<=",le,ye),ge.GreaterThanOrEqual=ve(">=",pe,me);var xe={kind:"number"},be={kind:"string"},_e={kind:"color"},we={kind:"value"},Ee={kind:"formatted"},Te={kind:"resolvedImage"};function Ae(t,e){return{kind:"array",itemType:t,N:e}}var Se=function(t){this.type=Ee,this.sections=t};Se.parse=function(t,e){if(t.length<2)return e.error("Expected at least one argument.");var r=t[1];if(!Array.isArray(r)&&"object"==typeof r)return e.error("First argument must be an image or text section.");for(var n=[],i=!1,a=1;a<=t.length-1;++a){var o=t[a];if(i&&"object"==typeof o&&!Array.isArray(o)){i=!1;var s=null;if(o["font-scale"]&&!(s=e.parse(o["font-scale"],1,xe)))return null;var u=null;if(o["text-font"]&&!(u=e.parse(o["text-font"],1,Ae(be))))return null;var l=null;if(o["text-color"]&&!(l=e.parse(o["text-color"],1,_e)))return null;var p=n[n.length-1];p.scale=s,p.font=u,p.textColor=l}else{var c=e.parse(t[a],1,we);if(!c)return null;var f=c.type.kind;if("string"!==f&&"value"!==f&&"null"!==f&&"resolvedImage"!==f)return e.error("Formatted text type must be 'string', 'value', 'image' or 'null'.");i=!0,n.push({content:c,scale:null,font:null,textColor:null})}}return new Se(n)},Se.prototype.evaluate=function(t){return new dt(this.sections.map((function(e){var r=e.content.evaluate(t);return It.typeOf(r)===Te?new ht("",r,null,null,null):new ht(It.toString$1(r),null,e.scale?e.scale.evaluate(t):null,e.font?e.font.evaluate(t).join(","):null,e.textColor?e.textColor.evaluate(t):null)})))},Se.prototype.eachChild=function(t){for(var e=0,r=this.sections;e<r.length;e+=1){var n=r[e];t(n.content),n.scale&&t(n.scale),n.font&&t(n.font),n.textColor&&t(n.textColor)}},Se.prototype.possibleOutputs=function(){return[void 0]},Se.prototype.serialize=function(){for(var t=["format"],e=0,r=this.sections;e<r.length;e+=1){var n=r[e];t.push(n.content.serialize());var i={};n.scale&&(i["font-scale"]=n.scale.serialize()),n.font&&(i["text-font"]=n.font.serialize()),n.textColor&&(i["text-color"]=n.textColor.serialize()),t.push(i)}return t};var Ie={kind:"string"},ke={kind:"resolvedImage"},Me=function(t){this.type=ke,this.input=t};Me.parse=function(t,e){if(2!==t.length)return e.error("Expected two arguments.");var r=e.parse(t[1],1,Ie);return r?new Me(r):e.error("No image name provided.")},Me.prototype.evaluate=function(t){var e=this.input.evaluate(t),r=!1;return t.availableImages&&t.availableImages.indexOf(e)>-1&&(r=!0),new yt({name:e,available:r})},Me.prototype.eachChild=function(t){t(this.input)},Me.prototype.possibleOutputs=function(){return[void 0]},Me.prototype.serialize=function(){return["image",this.input.serialize()]};var Fe=function(t,e,r,n,i){this.type=t,this.operator=e,this.interpolation=r,this.input=n,this.labels=[],this.outputs=[];for(var a=0,o=i;a<o.length;a+=1){var s=o[a],u=s[0],l=s[1];this.labels.push(u),this.outputs.push(l)}},Oe={kind:"number"},Pe={kind:"color"};function ze(t,e,r,n){var i=n-r,a=t-r;return 0===i?0:1===e?a/i:(Math.pow(e,a)-1)/(Math.pow(e,i)-1)}Fe.interpolationFactor=function(t,e,r,n){var i=0;if("exponential"===t.name)i=ze(e,t.base,r,n);else if("linear"===t.name)i=ze(e,1,r,n);else if("cubic-bezier"===t.name){var a=t.controlPoints;i=new unitbezier(a[0],a[1],a[2],a[3]).solve(ze(e,1,r,n))}return i},Fe.parse=function(t,e){var r=t[0],n=t[1],i=t[2],a=t.slice(3);if(!Array.isArray(n)||0===n.length)return e.error("Expected an interpolation type expression.",1);if("linear"===n[0])n={name:"linear"};else if("exponential"===n[0]){var o=n[1];if("number"!=typeof o)return e.error("Exponential interpolation requires a numeric base.",1,1);n={name:"exponential",base:o}}else{if("cubic-bezier"!==n[0])return e.error("Unknown interpolation type "+String(n[0]),1,0);var s=n.slice(1);if(4!==s.length||s.some((function(t){return"number"!=typeof t||t<0||t>1})))return e.error("Cubic bezier interpolation requires four numeric arguments with values between 0 and 1.",1);n={name:"cubic-bezier",controlPoints:s}}if(t.length-1<4)return e.error("Expected at least 4 arguments, but found only "+(t.length-1)+".");if((t.length-1)%2!=0)return e.error("Expected an even number of arguments.");if(!(i=e.parse(i,2,Oe)))return null;var u=[],l=null;"interpolate-hcl"===r||"interpolate-lab"===r?l=Pe:e.expectedType&&"value"!==e.expectedType.kind&&(l=e.expectedType);for(var p=0;p<a.length;p+=2){var c=a[p],f=a[p+1],h=p+3,d=p+4;if("number"!=typeof c)return e.error('Input/output pairs for "interpolate" expressions must be defined using literal numeric values (not computed expressions) for the input values.',h);if(u.length&&u[u.length-1][0]>=c)return e.error('Input/output pairs for "interpolate" expressions must be arranged with input values in strictly ascending order.',h);var y=e.parse(f,d,l);if(!y)return null;l=l||y.type,u.push([c,y])}return"number"===l.kind||"color"===l.kind||"array"===l.kind&&"number"===l.itemType.kind&&"number"==typeof l.N?new Fe(l,r,n,i,u):e.error("Type "+toString(l)+" is not interpolatable.")},Fe.prototype.evaluate=function(t){var e=this.labels,r=this.outputs;if(1===e.length)return r[0].evaluate(t);var n=this.input.evaluate(t);if(n<=e[0])return r[0].evaluate(t);var i=e.length;if(n>=e[i-1])return r[i-1].evaluate(t);var a=findStopLessThanOrEqualTo(e,n),o=e[a],s=e[a+1],u=Fe.interpolationFactor(this.interpolation,n,o,s),l=r[a].evaluate(t),p=r[a+1].evaluate(t);return"interpolate"===this.operator?interpolate[this.type.kind.toLowerCase()](l,p,u):"interpolate-hcl"===this.operator?hcl.reverse(hcl.interpolate(hcl.forward(l),hcl.forward(p),u)):lab.reverse(lab.interpolate(lab.forward(l),lab.forward(p),u))},Fe.prototype.eachChild=function(t){t(this.input);for(var e=0,r=this.outputs;e<r.length;e+=1){t(r[e])}},Fe.prototype.possibleOutputs=function(){var t;return(t=[]).concat.apply(t,this.outputs.map((function(t){return t.possibleOutputs()})))},Fe.prototype.serialize=function(){var t;t="linear"===this.interpolation.name?["linear"]:"exponential"===this.interpolation.name?1===this.interpolation.base?["linear"]:["exponential",this.interpolation.base]:["cubic-bezier"].concat(this.interpolation.controlPoints);for(var e=[this.operator,t,this.input.serialize()],r=0;r<this.labels.length;r++)e.push(this.labels[r],this.outputs[r].serialize());return e};var Re={kind:"boolean"},Ce={kind:"value"};function Be(t){return"boolean"===t.kind||"string"===t.kind||"number"===t.kind||"null"===t.kind||"value"===t.kind}function De(t){return"boolean"==typeof t||"string"==typeof t||"number"==typeof t}function Ve(t){return Array.isArray(t)||"string"==typeof t}var Ue=function(t,e){this.type=Re,this.needle=t,this.haystack=e};Ue.parse=function(t,e){if(3!==t.length)return e.error("Expected 2 arguments, but found "+(t.length-1)+" instead.");var r=e.parse(t[1],1,Ce),n=e.parse(t[2],2,Ce);return r&&n?Be(r.type)?new Ue(r,n):e.error("Expected first argument to be of type boolean, string, number or null, but found "+toString(r.type)+" instead"):null},Ue.prototype.evaluate=function(t){var e=this.needle.evaluate(t),r=this.haystack.evaluate(t);if(!e||!r)return!1;if(!De(e))throw new RuntimeError("Expected first argument to be of type boolean, string or number, but found "+toString(typeOf(e))+" instead.");if(!Ve(r))throw new RuntimeError("Expected second argument to be of type array or string, but found "+toString(typeOf(r))+" instead.");return r.indexOf(e)>=0},Ue.prototype.eachChild=function(t){t(this.needle),t(this.haystack)},Ue.prototype.possibleOutputs=function(){return[!0,!1]},Ue.prototype.serialize=function(){return["in",this.needle.serialize(),this.haystack.serialize()]};var Ne=function(t,e){this.type=e.type,this.bindings=[].concat(t),this.result=e};Ne.prototype.evaluate=function(t){return this.result.evaluate(t)},Ne.prototype.eachChild=function(t){for(var e=0,r=this.bindings;e<r.length;e+=1){t(r[e][1])}t(this.result)},Ne.parse=function(t,e){if(t.length<4)return e.error("Expected at least 3 arguments, but found "+(t.length-1)+" instead.");for(var r=[],n=1;n<t.length-1;n+=2){var i=t[n];if("string"!=typeof i)return e.error("Expected string, but found "+typeof i+" instead.",n);if(/[^a-zA-Z0-9_]/.test(i))return e.error("Variable names must contain only alphanumeric characters or '_'.",n);var a=e.parse(t[n+1],n+1);if(!a)return null;r.push([i,a])}var o=e.parse(t[t.length-1],t.length-1,e.expectedType,r);return o?new Ne(r,o):null},Ne.prototype.possibleOutputs=function(){return this.result.possibleOutputs()},Ne.prototype.serialize=function(){for(var t=["let"],e=0,r=this.bindings;e<r.length;e+=1){var n=r[e],i=n[0],a=n[1];t.push(i,a.serialize())}return t.push(this.result.serialize()),t};var Le={kind:"number"},Xe=function(t){this.type=Le,this.input=t};Xe.parse=function(t,e){if(2!==t.length)return e.error("Expected 1 argument, but found "+(t.length-1)+" instead.");var r=e.parse(t[1],1);return r?"array"!==r.type.kind&&"string"!==r.type.kind&&"value"!==r.type.kind?e.error("Expected argument of type string or array, but found "+toString(r.type)+" instead."):new Xe(r):null},Xe.prototype.evaluate=function(t){var e=this.input.evaluate(t);if("string"==typeof e)return e.length;if(Array.isArray(e))return e.length;throw new RuntimeError("Expected value to be of type string or array, but found "+toString(typeOf(e))+" instead.")},Xe.prototype.eachChild=function(t){t(this.input)},Xe.prototype.possibleOutputs=function(){return[void 0]},Xe.prototype.serialize=function(){var t=["length"];return this.eachChild((function(e){t.push(e.serialize())})),t};var qe=function(t,e){this.type=t,this.value=e};qe.parse=function(t,e){if(2!==t.length)return e.error("'literal' expression requires exactly one argument, but found "+(t.length-1)+" instead.");if(!It.isValue(t[1]))return e.error("invalid value");var r=t[1],n=It.typeOf(r),i=e.expectedType;return"array"!==n.kind||0!==n.N||!i||"array"!==i.kind||"number"==typeof i.N&&0!==i.N||(n=i),new qe(n,r)},qe.prototype.evaluate=function(){return this.value},qe.prototype.eachChild=function(){},qe.prototype.possibleOutputs=function(){return[this.value]},qe.prototype.serialize=function(){return"array"===this.type.kind||"object"===this.type.kind?["literal",this.value]:this.value instanceof Color?["rgba"].concat(this.value.toArray()):this.value instanceof dt?this.value.serialize():this.value};var je={kind:"value"},He=function(t,e,r,n,i,a){this.inputType=t,this.type=e,this.input=r,this.cases=n,this.outputs=i,this.otherwise=a};He.parse=function(t,e){if(t.length<5)return e.error("Expected at least 4 arguments, but found only "+(t.length-1)+".");if(t.length%2!=1)return e.error("Expected an even number of arguments.");var r,n;e.expectedType&&"value"!==e.expectedType.kind&&(n=e.expectedType);for(var i={},a=[],o=2;o<t.length-1;o+=2){var s=t[o],u=t[o+1];Array.isArray(s)||(s=[s]);var l=e.concat(o);if(0===s.length)return l.error("Expected at least one branch label.");for(var p=0,c=s;p<c.length;p+=1){var f=c[p];if("number"!=typeof f&&"string"!=typeof f)return l.error("Branch labels must be numbers or strings.");if("number"==typeof f&&Math.abs(f)>Number.MAX_SAFE_INTEGER)return l.error("Branch labels must be integers no larger than "+Number.MAX_SAFE_INTEGER+".");if("number"==typeof f&&Math.floor(f)!==f)return l.error("Numeric branch labels must be integer values.");if(r){if(l.checkSubtype(r,It.typeOf(f)))return null}else r=It.typeOf(f);if(void 0!==i[String(f)])return l.error("Branch labels must be unique.");i[String(f)]=a.length}var h=e.parse(u,o,n);if(!h)return null;n=n||h.type,a.push(h)}var d=e.parse(t[1],1,je);if(!d)return null;var y=e.parse(t[t.length-1],t.length-1,n);return y?"value"!==d.type.kind&&e.concat(1).checkSubtype(r,d.type)?null:new He(r,n,d,i,a,y):null},He.prototype.evaluate=function(t){var e=this.input.evaluate(t);return(It.typeOf(e)===this.inputType&&this.outputs[this.cases[e]]||this.otherwise).evaluate(t)},He.prototype.eachChild=function(t){t(this.input),this.outputs.forEach(t),t(this.otherwise)},He.prototype.possibleOutputs=function(){var t;return(t=[]).concat.apply(t,this.outputs.map((function(t){return t.possibleOutputs()}))).concat(this.otherwise.possibleOutputs())},He.prototype.serialize=function(){for(var t=this,e=["match",this.input.serialize()],r=[],n={},i=0,a=Object.keys(this.cases).sort();i<a.length;i+=1){var o=a[i];void 0===(c=n[this.cases[o]])?(n[this.cases[o]]=r.length,r.push([this.cases[o],[o]])):r[c][1].push(o)}for(var s=function(e){return"number"===t.inputType.kind?Number(e):e},u=0,l=r;u<l.length;u+=1){var p=l[u],c=p[0],f=p[1];1===f.length?e.push(s(f[0])):e.push(f.map(s)),e.push(this.outputs[outputIndex$1].serialize())}return e.push(this.otherwise.serialize()),e};var Ye={kind:"number"},Qe={kind:"string"},Ke=function(t,e,r,n,i){this.type=Qe,this.number=t,this.locale=e,this.currency=r,this.minFractionDigits=n,this.maxFractionDigits=i};function Ge(t,e){for(var r,n,i=t.length-1,a=0,o=i,s=0;a<=o;)if(r=t[s=Math.floor((a+o)/2)],n=t[s+1],r<=e){if(s===i||e<n)return s;a=s+1}else{if(!(r>e))throw new RuntimeError("Input is not a number.");o=s-1}return 0}Ke.parse=function(t,e){if(3!==t.length)return e.error("Expected two arguments.");var r=e.parse(t[1],1,Ye);if(!r)return null;var n=t[2];if("object"!=typeof n||Array.isArray(n))return e.error("NumberFormat options argument must be an object.");var i=null;if(n.locale&&!(i=e.parse(n.locale,1,Qe)))return null;var a=null;if(n.currency&&!(a=e.parse(n.currency,1,Qe)))return null;var o=null;if(n["min-fraction-digits"]&&!(o=e.parse(n["min-fraction-digits"],1,Ye)))return null;var s=null;return n["max-fraction-digits"]&&!(s=e.parse(n["max-fraction-digits"],1,Ye))?null:new Ke(r,i,a,o,s)},Ke.prototype.evaluate=function(t){return new Intl.NumberFormat(this.locale?this.locale.evaluate(t):[],{style:this.currency?"currency":"decimal",currency:this.currency?this.currency.evaluate(t):void 0,minimumFractionDigits:this.minFractionDigits?this.minFractionDigits.evaluate(t):void 0,maximumFractionDigits:this.maxFractionDigits?this.maxFractionDigits.evaluate(t):void 0}).format(this.number.evaluate(t))},Ke.prototype.eachChild=function(t){t(this.number),this.locale&&t(this.locale),this.currency&&t(this.currency),this.minFractionDigits&&t(this.minFractionDigits),this.maxFractionDigits&&t(this.maxFractionDigits)},Ke.prototype.possibleOutputs=function(){return[void 0]},Ke.prototype.serialize=function(){var t={};return this.locale&&(t.locale=this.locale.serialize()),this.currency&&(t.currency=this.currency.serialize()),this.minFractionDigits&&(t["min-fraction-digits"]=this.minFractionDigits.serialize()),this.maxFractionDigits&&(t["max-fraction-digits"]=this.maxFractionDigits.serialize()),["number-format",this.number.serialize(),t]};var We={kind:"number"},Je=function(t,e,r){this.type=t,this.input=e,this.labels=[],this.outputs=[];for(var n=0,i=r;n<i.length;n+=1){var a=i[n],o=a[0],s=a[1];this.labels.push(o),this.outputs.push(s)}};Je.parse=function(t,e){if(t.length-1<4)return e.error("Expected at least 4 arguments, but found only "+(t.length-1)+".");if((t.length-1)%2!=0)return e.error("Expected an even number of arguments.");var r=e.parse(t[1],1,We);if(!r)return null;var n=[],i=null;e.expectedType&&"value"!==e.expectedType.kind&&(i=e.expectedType);for(var a=1;a<t.length;a+=2){var o=1===a?-1/0:t[a],s=t[a+1],u=a,l=a+1;if("number"!=typeof o)return e.error('Input/output pairs for "step" expressions must be defined using literal numeric values (not computed expressions) for the input values.',u);if(n.length&&n[n.length-1][0]>=o)return e.error('Input/output pairs for "step" expressions must be arranged with input values in strictly ascending order.',u);var p=e.parse(s,l,i);if(!p)return null;i=i||p.type,n.push([o,p])}return new Je(i,r,n)},Je.prototype.evaluate=function(t){var e=this.labels,r=this.outputs;if(1===e.length)return r[0].evaluate(t);var n=this.input.evaluate(t);if(n<=e[0])return r[0].evaluate(t);var i=e.length;return n>=e[i-1]?r[i-1].evaluate(t):r[Ge(e,n)].evaluate(t)},Je.prototype.eachChild=function(t){t(this.input);for(var e=0,r=this.outputs;e<r.length;e+=1){t(r[e])}},Je.prototype.possibleOutputs=function(){var t;return(t=[]).concat.apply(t,this.outputs.map((function(t){return t.possibleOutputs()})))},Je.prototype.serialize=function(){for(var t=["step",this.input.serialize()],e=0;e<this.labels.length;e++)e>0&&t.push(this.labels[e]),t.push(this.outputs[e].serialize());return t};var Ze=function(t,e){this.type=e.type,this.name=t,this.boundExpression=e};Ze.parse=function(t,e){if(2!==t.length||"string"!=typeof t[1])return e.error("'var' expression requires exactly one string literal argument.");var r=t[1];return e.scope.has(r)?new Ze(r,e.scope.get(r)):e.error('Unknown variable "'+r+'". Make sure "'+r+'" has been bound in an enclosing "let" expression before using it.',1)},Ze.prototype.evaluate=function(t){return this.boundExpression.evaluate(t)},Ze.prototype.eachChild=function(){},Ze.prototype.possibleOutputs=function(){return[void 0]},Ze.prototype.serialize=function(){return["var",this.name]};var $e={"==":ge.Equals,"!=":ge.NotEquals,">":ge.GreaterThan,"<":ge.LessThan,">=":ge.GreaterThanOrEqual,"<=":ge.LessThanOrEqual,array:Ct,at:Ut,boolean:Ct,case:Lt,coalesce:qt,collator:te,format:Se,image:Me,in:Ue,interpolate:Fe,"interpolate-hcl":Fe,"interpolate-lab":Fe,length:Xe,let:Ne,literal:qe,match:He,number:Ct,"number-format":Ke,object:Ct,step:Je,string:Ct,"to-boolean":Wt,"to-color":Wt,"to-number":Wt,"to-string":Wt,var:Ze};function tr(){}var er={};for(var rr in tr.register=function(t,e,r){void 0===r&&(r={}),Object.defineProperty(e,"_classRegistryKey",{value:t,writeable:!1}),er[t]={klass:e,omit:r.omit||[],shallow:r.shallow||[]}},tr.register("Object",Object),tr.register("Color",Z),tr.register("ResolvedImage",yt),tr.register("ImageAtlas",j),tr.register("ImagePosition",B),tr.register("RGBAImage",V),tr.register("Formatted",dt),tr.register("FormattedSection",ht),$e)$e[rr]._classRegistryKey||tr.register("Expression_"+rr,$e[rr]);function nr(t){return t&&"undefined"!=typeof ArrayBuffer&&(t instanceof ArrayBuffer||t.constructor&&"ArrayBuffer"===t.constructor.name)}tr.serialize=function(t,e){if(null==t||"boolean"==typeof t||"number"==typeof t||"string"==typeof t||t instanceof Boolean||t instanceof Number||t instanceof String||t instanceof Date||t instanceof RegExp)return t;if(nr(t))return e&&e.push(t),t;if(ArrayBuffer.isView(t)){var r=t;return e&&e.push(r.buffer),r}if(t instanceof ImageData)return e&&e.push(t.data.buffer),t;if(Array.isArray(t)){for(var n=[],i=0,a=t;i<a.length;i+=1){var o=a[i];n.push(tr.serialize(o,e))}return n}if("object"==typeof t){var s=t.constructor,u=s._classRegistryKey;if(!u)throw new Error("can't serialize object of unregistered class");var l=s.serialize?s.serialize(t,e):{};if(!s.serialize){for(var p in t)if(t.hasOwnProperty(p)&&!(er[u].omit.indexOf(p)>=0)){var c=t[p];"function"!=typeof c&&(l[p]=er[u].shallow.indexOf(p)>=0?c:tr.serialize(c,e))}t instanceof Error&&(l.message=t.message)}if(l.$name)throw new Error("$name property is reserved for worker serialization logic.");return"Object"!==u&&(l.$name=u),l}throw new Error("can't serialize object of type "+typeof t)},tr.deserialize=function(t){if(null==t||"boolean"==typeof t||"number"==typeof t||"string"==typeof t||t instanceof Boolean||t instanceof Number||t instanceof String||t instanceof Date||t instanceof RegExp||nr(t)||ArrayBuffer.isView(t)||t instanceof ImageData)return t;if(Array.isArray(t))return t.map(tr.deserialize);if("object"==typeof t){var e=t.$name||"Object",r=er[e].klass;if(!r)throw new Error("can't deserialize unregistered class "+e);if(r.deserialize)return r.deserialize(t);for(var n=Object.create(r.prototype),i=0,a=Object.keys(t);i<a.length;i+=1){var o=a[i];if("$name"!==o){var s=t[o];n[o]=er[e].shallow.indexOf(o)>=0?s:tr.deserialize(s)}}return n}throw new Error("can't deserialize object of type "+typeof t)};var ir=function(){this.first=!0};ir.prototype.update=function(t,e){var r=Math.floor(t);return this.first?(this.first=!1,this.lastIntegerZoom=r,this.lastIntegerZoomTime=0,this.lastZoom=t,this.lastFloorZoom=r,!0):(this.lastFloorZoom>r?(this.lastIntegerZoom=r+1,this.lastIntegerZoomTime=e):this.lastFloorZoom<r&&(this.lastIntegerZoom=r,this.lastIntegerZoomTime=e),t!==this.lastZoom&&(this.lastZoom=t,this.lastFloorZoom=r,!0))},tr.register("ZoomHistory",ir);var ar=function(t,e){this.zoom=t,e?(this.now=e.now,this.fadeDuration=e.fadeDuration,this.zoomHistory=e.zoomHistory,this.transition=e.transition):(this.now=0,this.fadeDuration=0,this.zoomHistory=new ir,this.transition={})};ar.prototype.isSupportedScript=function(t){return!1},ar.prototype.crossFadingFactor=function(){return 0===this.fadeDuration?1:Math.min((this.now-this.zoomHistory.lastIntegerZoomTime)/this.fadeDuration,1)},ar.prototype.getCrossfadeParameters=function(){var t=this.zoom,e=t-Math.floor(t),r=this.crossFadingFactor();return t>this.zoomHistory.lastIntegerZoom?{fromScale:2,toScale:1,t:e+(1-e)*r}:{fromScale:.5,toScale:1,t:1-(1-r)*e}},tr.register("EvaluationParameters",ar);var or=8192;function sr(t,e,r){return Math.min(r,Math.max(e,t))}function ur(t){return{min:-1*Math.pow(2,t-1),max:Math.pow(2,t-1)-1}}var lr=ur(15);function pr(t){for(var e=or/t.extent,r=t.loadGeometry(),n=0;n<r.length;n++)for(var i=r[n],a=0;a<i.length;a++){var o=i[a];o.x=Math.round(o.x*e),o.y=or-Math.round(o.y*e),(o.x<lr.min||o.x>lr.max||o.y<lr.min||o.y>lr.max)&&(o.x=sr(o.x,lr.min,lr.max),o.y=sr(o.y,lr.min,lr.max))}return r}var cr=function(t){void 0===t&&(t=[]),this.segments=t};cr.prototype.prepareSegment=function(t,e,r,n){var i=this.segments[this.segments.length-1];return(!i||i.vertexLength+t>cr.MAX_VERTEX_ARRAY_LENGTH||i.sortKey!==n)&&(i={vertexOffset:e.length,primitiveOffset:r.length,vertexLength:0,primitiveLength:0},void 0!==n&&(i.sortKey=n),this.segments.push(i)),i},cr.prototype.get=function(){return this.segments},cr.prototype.destroy=function(){for(var t=0,e=this.segments;t<e.length;t+=1){var r=e[t];for(var n in r.vaos)r.vaos[n].destroy()}},cr.simpleSegment=function(t,e,r,n){return new cr([{vertexOffset:t,primitiveOffset:e,vertexLength:r,primitiveLength:n,vaos:{},sortKey:0}])},cr.MAX_VERTEX_ARRAY_LENGTH=Math.pow(2,16)-1,tr.register("SegmentVector",cr);var fr=128,hr=5,dr=function(){this.isTransferred=!1,this.capacity=-1,this.resize(0)};dr.serialize=function(t,e){return t.isTransferred&&console.log("StructArray array.isTransferred."),t._trim(),e&&(t.isTransferred=!0,e.push(t.arrayBuffer)),{length:t.length,arrayBuffer:t.arrayBuffer}},dr.deserialize=function(t){var e=Object.create(this.prototype);return e.arrayBuffer=t.arrayBuffer,e.length=t.length,e.capacity=t.arrayBuffer.byteLength/e.bytesPerElement,e._refreshViews(),e},dr.prototype._trim=function(){this.length!==this.capacity&&(this.capacity=this.length,this.arrayBuffer=this.arrayBuffer.slice(0,this.length*this.bytesPerElement),this._refreshViews())},dr.prototype.clear=function(){this.length=0},dr.prototype.resize=function(t){this.reserve(t),this.length=t},dr.prototype.reserve=function(t){if(t>this.capacity){this.capacity=Math.max(t,Math.floor(this.capacity*hr),fr),this.arrayBuffer=new ArrayBuffer(this.capacity*this.bytesPerElement);var e=this.uint8;this._refreshViews(),e&&this.uint8.set(e)}},dr.prototype._refreshViews=function(){throw new Error("_refreshViews() must be implemented by each concrete StructArray layout")};var yr=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype._refreshViews=function(){this.uint8=new Uint8Array(this.arrayBuffer),this.int16=new Int16Array(this.arrayBuffer)},e.prototype.emplaceBack=function(t,e,r,n,i,a){var o=this.length;return this.resize(o+1),this.emplace(o,t,e,r,n,i,a)},e.prototype.emplace=function(t,e,r,n,i,a,o){var s=4*t,u=8*t;return this.int16[s+0]=e,this.int16[s+1]=r,this.uint8[u+4]=n,this.uint8[u+5]=i,this.uint8[u+6]=a,this.uint8[u+7]=o,t},e}(dr);yr.prototype.bytesPerElement=8,tr.register("StructArrayLayout2i4ub8",yr);var mr=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype._refreshViews=function(){this.uint8=new Uint8Array(this.arrayBuffer),this.uint16=new Uint16Array(this.arrayBuffer)},e.prototype.emplaceBack=function(t,e,r){var n=this.length;return this.resize(n+1),this.emplace(n,t,e,r)},e.prototype.emplace=function(t,e,r,n){var i=3*t;return this.uint16[i+0]=e,this.uint16[i+1]=r,this.uint16[i+2]=n,t},e}(dr);mr.prototype.bytesPerElement=6,tr.register("StructArrayLayout3ui6",mr);var vr=function(){this.ids=[],this.positions=[],this.indexed=!1};function gr(t,e,r,n){if(!(r>=n)){for(var i=t[r+n>>1],a=r-1,o=n+1;;){do{a++}while(t[a]<i);do{o--}while(t[o]>i);if(a>=o)break;xr(t,a,o),xr(e,3*a,3*o),xr(e,3*a+1,3*o+1),xr(e,3*a+2,3*o+2)}gr(t,e,r,o),gr(t,e,o+1,n)}}function xr(t,e,r){var n=t[e];t[e]=t[r],t[r]=n}vr.prototype.add=function(t,e,r,n){this.ids.push(t),this.positions.push(e,r,n)},vr.prototype.getPositions=function(t){for(var e=0,r=this.ids.length-1;e<r;){var n=e+r>>1;this.ids[n]>=t?r=n:e=n+1}for(var i=[];this.ids[e]===t;){var a=this.positions[3*e],o=this.positions[3*e+1],s=this.positions[3*e+2];i.push({index:a,start:o,end:s}),e++}return i},vr.serialize=function(t,e){var r=new Float64Array(t.ids),n=new Uint32Array(t.positions);return gr(r,n,0,r.length-1),e&&e.push(r.buffer,n.buffer),{ids:r,positions:n}},vr.deserialize=function(t){var e=new vr;return e.ids=t.ids,e.positions=t.positions,e.indexed=!0,e},tr.register("FeaturePositionMap",vr);var br=function(t,e,r){this.property=t,this.value=e,this.parameters=r};function _r(){}br.prototype.isConstant=function(){return"constant"===this.value.kind},br.prototype.constantOr=function(t){return"constant"===this.value.kind?this.value.value:t},br.prototype.evaluate=function(t,e,r){return this.property.evaluate(this.value,this.parameters,t,e,r)},_r.supportsPropertyExpression=function(t){return"data-driven"===t["property-type"]||"cross-faded-data-driven"===t["property-type"]},_r.supportsZoomExpression=function(t){return!!t.expression&&t.expression.parameters.indexOf("zoom")>-1},_r.supportsInterpolation=function(t){return!!t.expression&&t.expression.interpolated};var wr={},Er=function(t,e){this.gl=t.gl,this.location=e};wr.Uniform1i=function(t){function e(e,r){t.call(this,e,r),this.current=0}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.set=function(t){this.current!==t&&(this.current=t,this.gl.uniform1i(this.location,t))},e}(Er),wr.Uniform1f=function(t){function e(e,r){t.call(this,e,r),this.current=0}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.set=function(t){this.current!==t&&(this.current=t,this.gl.uniform1f(this.location,t))},e}(Er),wr.Uniform2f=function(t){function e(e,r){t.call(this,e,r),this.current=[0,0]}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.set=function(t){t[0]===this.current[0]&&t[1]===this.current[1]||(this.current=t,this.gl.uniform2f(this.location,t[0],t[1]))},e}(Er),wr.Uniform3f=function(t){function e(e,r){t.call(this,e,r),this.current=[0,0,0]}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.set=function(t){t[0]===this.current[0]&&t[1]===this.current[1]&&t[2]===this.current[2]||(this.current=t,this.gl.uniform3f(this.location,t[0],t[1],t[2]))},e}(Er),wr.Uniform4f=function(t){function e(e,r){t.call(this,e,r),this.current=[0,0,0,0]}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.set=function(t){t[0]===this.current[0]&&t[1]===this.current[1]&&t[2]===this.current[2]&&t[3]===this.current[3]||(this.current=t,this.gl.uniform4f(this.location,t[0],t[1],t[2],t[3]))},e}(Er),wr.UniformColor=function(t){function e(e,r){t.call(this,e,r),this.current=Z.transparent}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.set=function(t){t.r===this.current.r&&t.g===this.current.g&&t.b===this.current.b&&t.a===this.current.a||(this.current=t,this.gl.uniform4f(this.location,t.r,t.g,t.b,t.a))},e}(Er);var Tr=new Float32Array(16);wr.UniformMatrix4f=function(t){function e(e,r){t.call(this,e,r),this.current=Tr}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.set=function(t){this.gl.uniformMatrix4fv(this.location,!1,t)},e}(Er);var Ar=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype._refreshViews=function(){this.uint8=new Uint8Array(this.arrayBuffer),this.uint16=new Uint16Array(this.arrayBuffer)},e.prototype.emplaceBack=function(t,e,r,n,i,a,o,s){var u=this.length;return this.resize(u+1),this.emplace(u,t,e,r,n,i,a,o,s)},e.prototype.emplace=function(t,e,r,n,i,a,o,s,u){var l=8*t;return this.uint16[l+0]=e,this.uint16[l+1]=r,this.uint16[l+2]=n,this.uint16[l+3]=i,this.uint16[l+4]=a,this.uint16[l+5]=o,this.uint16[l+6]=s,this.uint16[l+7]=u,t},e}(dr);Ar.prototype.bytesPerElement=16,tr.register("StructArrayLayout8ui16",Ar);var Sr=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype._refreshViews=function(){this.uint8=new Uint8Array(this.arrayBuffer),this.float32=new Float32Array(this.arrayBuffer)},e.prototype.emplaceBack=function(t,e){var r=this.length;return this.resize(r+1),this.emplace(r,t,e)},e.prototype.emplace=function(t,e,r){var n=2*t;return this.float32[n+0]=e,this.float32[n+1]=r,t},e}(dr);Sr.prototype.bytesPerElement=8,tr.register("StructArrayLayout2f8",Sr);var Ir=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype._refreshViews=function(){this.uint8=new Uint8Array(this.arrayBuffer),this.float32=new Float32Array(this.arrayBuffer)},e.prototype.emplaceBack=function(t,e,r,n){var i=this.length;return this.resize(i+1),this.emplace(i,t,e,r,n)},e.prototype.emplace=function(t,e,r,n,i){var a=4*t;return this.float32[a+0]=e,this.float32[a+1]=r,this.float32[a+2]=n,this.float32[a+3]=i,t},e}(dr);Ir.prototype.bytesPerElement=16,tr.register("StructArrayLayout4f16",Ir);var kr=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype._refreshViews=function(){this.uint8=new Uint8Array(this.arrayBuffer),this.float32=new Float32Array(this.arrayBuffer)},e.prototype.emplaceBack=function(t){var e=this.length;return this.resize(e+1),this.emplace(e,t)},e.prototype.emplace=function(t,e){var r=1*t;return this.float32[r+0]=e,t},e}(dr);function Mr(t,e,r){return Math.min(r,Math.max(e,t))}function Fr(t,e){return 256*(t=Mr(Math.floor(t),0,255))+(e=Mr(Math.floor(e),0,255))}function Or(t){return[Fr(255*t.r,255*t.g),Fr(255*t.b,255*t.a)]}kr.prototype.bytesPerElement=4,tr.register("StructArrayLayout1f4",kr);var Pr=function(t,e,r){this.value=t,this.names=e,this.uniformNames=this.names.map((function(t){return"u_"+t})),this.type=r,this.maxValue=-1/0};Pr.prototype.defines=function(){return this.names.map((function(t){return"#define HAS_UNIFORM_u_"+t}))},Pr.prototype.setConstantPatternPositions=function(){},Pr.prototype.populatePaintArray=function(){},Pr.prototype.updatePaintArray=function(){},Pr.prototype.upload=function(){},Pr.prototype.destroy=function(){},Pr.prototype.setUniforms=function(t,e,r,n){e.set(n.constantOr(this.value))},Pr.prototype.getBinding=function(t,e){return"color"===this.type?new wr.UniformColor(t,e):new wr.Uniform1f(t,e)},Pr.serialize=function(t){var e=t.value,r=t.names,n=t.type;return{value:tr.serialize(e),names:r,type:n}},Pr.deserialize=function(t){var e=t.value,r=t.names,n=t.type;return new Pr(tr.deserialize(e),r,n)};var zr=function(t,e,r){this.value=t,this.names=e,this.uniformNames=this.names.map((function(t){return"u_"+t})),this.type=r,this.maxValue=-1/0,this.patternPositions={patternTo:null,patternFrom:null}};zr.prototype.defines=function(){return this.names.map((function(t){return"#define HAS_UNIFORM_u_"+t}))},zr.prototype.populatePaintArray=function(){},zr.prototype.updatePaintArray=function(){},zr.prototype.upload=function(){},zr.prototype.destroy=function(){},zr.prototype.setConstantPatternPositions=function(t,e){this.patternPositions.patternTo=t.tlbr,this.patternPositions.patternFrom=e.tlbr},zr.prototype.setUniforms=function(t,e,r,n,i){var a=this.patternPositions;"u_pattern_to"===i&&a.patternTo&&e.set(a.patternTo),"u_pattern_from"===i&&a.patternFrom&&e.set(a.patternFrom)},zr.prototype.getBinding=function(t,e){return new wr.Uniform4f(t,e)};var Rr=function(t,e,r,n){this.expression=t,this.names=e,this.type=r,this.uniformNames=this.names.map((function(t){return"a_"+t})),this.maxValue=-1/0,this.paintVertexAttributes=e.map((function(t){return{name:"a_"+t,type:"Float32",components:"color"===r?2:1,offset:0}})),this.paintVertexArray=new n};Rr.prototype.defines=function(){return[]},Rr.prototype.setConstantPatternPositions=function(){},Rr.prototype.populatePaintArray=function(t,e,r,n){var i=this.paintVertexArray,a=i.length;i.reserve(t);var o=this.expression.evaluate(new ar(0),e,{},[],n);if("color"===this.type)for(var s=Or(o),u=a;u<t;u++)i.emplaceBack(s[0],s[1]);else{for(var l=a;l<t;l++)i.emplaceBack(o);this.maxValue=Math.max(this.maxValue,o)}},Rr.prototype.updatePaintArray=function(t,e,r,n){var i=this.paintVertexArray,a=this.expression.evaluate({zoom:0},r,n);if("color"===this.type)for(var o=Or(a),s=t;s<e;s++)i.emplace(s,o[0],o[1]);else{for(var u=t;u<e;u++)i.emplace(u,a);this.maxValue=Math.max(this.maxValue,a)}},Rr.prototype.upload=function(t){this.paintVertexArray&&this.paintVertexArray.arrayBuffer&&(this.paintVertexBuffer&&this.paintVertexBuffer.buffer?this.paintVertexBuffer.updateData(this.paintVertexArray):this.paintVertexBuffer=t.createVertexBuffer(this.paintVertexArray,this.paintVertexAttributes,this.expression.isStateDependent))},Rr.prototype.destroy=function(){this.paintVertexBuffer&&this.paintVertexBuffer.destroy()},Rr.prototype.setUniforms=function(t,e){e.set(0)},Rr.prototype.getBinding=function(t,e){return new wr.Uniform1f(t,e)};var Cr=function(t,e,r,n,i,a){this.expression=t,this.names=e,this.uniformNames=this.names.map((function(t){return"u_"+t+"_t"})),this.type=r,this.useIntegerZoom=n,this.zoom=i,this.maxValue=-1/0;var o=a;this.paintVertexAttributes=e.map((function(t){return{name:"a_"+t,type:"Float32",components:"color"===r?4:2,offset:0}})),this.paintVertexArray=new o};Cr.prototype.defines=function(){return[]},Cr.prototype.setConstantPatternPositions=function(){},Cr.prototype.populatePaintArray=function(t,e,r,n){var i=this.paintVertexArray,a=i.length;i.reserve(t);var o=this.expression.evaluate(new ar(this.zoom),e,{},[],n),s=this.expression.evaluate(new ar(this.zoom+1),e,{},[],n);if("color"===this.type)for(var u=Or(o),l=Or(s),p=a;p<t;p++)i.emplaceBack(u[0],u[1],l[0],l[1]);else{for(var c=a;c<t;c++)i.emplaceBack(o,s);this.maxValue=Math.max(this.maxValue,o,s)}},Cr.prototype.updatePaintArray=function(t,e,r,n){var i=this.paintVertexArray,a=this.expression.evaluate({zoom:this.zoom},r,n),o=this.expression.evaluate({zoom:this.zoom+1},r,n);if("color"===this.type)for(var s=Or(a),u=Or(o),l=t;l<e;l++)i.emplace(l,s[0],s[1],u[0],u[1]);else{for(var p=t;p<e;p++)i.emplace(p,a,o);this.maxValue=Math.max(this.maxValue,a,o)}},Cr.prototype.upload=function(t){this.paintVertexArray&&this.paintVertexArray.arrayBuffer&&(this.paintVertexBuffer&&this.paintVertexBuffer.buffer?this.paintVertexBuffer.updateData(this.paintVertexArray):this.paintVertexBuffer=t.createVertexBuffer(this.paintVertexArray,this.paintVertexAttributes,this.expression.isStateDependent))},Cr.prototype.destroy=function(){this.paintVertexBuffer&&this.paintVertexBuffer.destroy()},Cr.prototype.interpolationFactor=function(t){return this.useIntegerZoom&&(t=Math.floor(t)),Mr(this.expression.interpolationFactor(t,this.zoom,this.zoom+1),0,1)},Cr.prototype.setUniforms=function(t,e,r){e.set(this.interpolationFactor(r.zoom))},Cr.prototype.getBinding=function(t,e){return new wr.Uniform1f(t,e)};var Br=function(t,e,r,n,i,a,o){this.expression=t,this.names=e,this.type=r,this.uniformNames=this.names.map((function(t){return"u_"+t+"_t"})),this.useIntegerZoom=n,this.zoom=i,this.maxValue=-1/0,this.layerId=o,this.paintVertexAttributes=e.map((function(t){return{name:"a_"+t,type:"Uint16",components:4,offset:0}})),this.zoomInPaintVertexArray=new a,this.zoomOutPaintVertexArray=new a};Br.prototype.defines=function(){return[]},Br.prototype.setConstantPatternPositions=function(){},Br.prototype.populatePaintArray=function(t,e,r){var n=this.zoomInPaintVertexArray,i=this.zoomOutPaintVertexArray,a=this.layerId,o=n.length;if(n.reserve(t),i.reserve(t),r&&e.patterns&&e.patterns[a]){var s=e.patterns[a],u=s.min,l=s.mid,p=s.max,c=r[u],f=r[l],h=r[p];if(!c||!f||!h)return;for(var d=o;d<t;d++)n.emplaceBack(f.tl[0],f.tl[1],f.br[0],f.br[1],c.tl[0],c.tl[1],c.br[0],c.br[1]),i.emplaceBack(f.tl[0],f.tl[1],f.br[0],f.br[1],h.tl[0],h.tl[1],h.br[0],h.br[1])}},Br.prototype.updatePaintArray=function(t,e,r,n,i){var a=this.zoomInPaintVertexArray,o=this.zoomOutPaintVertexArray,s=this.layerId;if(i&&r.patterns&&r.patterns[s]){var u=r.patterns[s],l=u.min,p=u.mid,c=u.max,f=i[l],h=i[p],d=i[c];if(!f||!h||!d)return;for(var y=t;y<e;y++)a.emplace(y,h.tl[0],h.tl[1],h.br[0],h.br[1],f.tl[0],f.tl[1],f.br[0],f.br[1]),o.emplace(y,h.tl[0],h.tl[1],h.br[0],h.br[1],d.tl[0],d.tl[1],d.br[0],d.br[1])}},Br.prototype.upload=function(t){this.zoomInPaintVertexArray&&this.zoomInPaintVertexArray.arrayBuffer&&this.zoomOutPaintVertexArray&&this.zoomOutPaintVertexArray.arrayBuffer&&(this.zoomInPaintVertexBuffer=t.createVertexBuffer(this.zoomInPaintVertexArray,this.paintVertexAttributes,this.expression.isStateDependent),this.zoomOutPaintVertexBuffer=t.createVertexBuffer(this.zoomOutPaintVertexArray,this.paintVertexAttributes,this.expression.isStateDependent))},Br.prototype.destroy=function(){this.zoomOutPaintVertexBuffer&&this.zoomOutPaintVertexBuffer.destroy(),this.zoomInPaintVertexBuffer&&this.zoomInPaintVertexBuffer.destroy()},Br.prototype.setUniforms=function(t,e){e.set(0)},Br.prototype.getBinding=function(t,e){return new Uniform1f(t,e)};var Dr=function(){this.binders={},this.cacheKey="",this._buffers=[]};function Vr(t,e){return{"text-opacity":["opacity"],"icon-opacity":["opacity"],"text-color":["fill_color"],"icon-color":["fill_color"],"text-halo-color":["halo_color"],"icon-halo-color":["halo_color"],"text-halo-blur":["halo_blur"],"icon-halo-blur":["halo_blur"],"text-halo-width":["halo_width"],"text-show-background":["show-background"],"icon-halo-width":["halo_width"],"line-gap-width":["gapwidth"],"line-pattern":["pattern_to","pattern_from"],"fill-pattern":["pattern_to","pattern_from"],"fill-extrusion-pattern":["pattern_to","pattern_from"]}[t]||[t.replace(e+"-","").replace(/-/g,"_")]}function Ur(t){return{"line-pattern":{source:Ar,composite:Ar},"fill-pattern":{source:Ar,composite:Ar},"fill-extrusion-pattern":{source:Ar,composite:Ar}}[t]}function Nr(t,e,r){var n={color:{source:Sr,composite:Ir},number:{source:kr,composite:Sr}},i=Ur(t);return i&&i[r]||n[e][r]}Dr.createDynamic=function(t,e,r){var n=new Dr,i=[];for(var a in t.paint._values)if(r(a)){var o=t.paint.get(a);if(o instanceof br&&_r.supportsPropertyExpression(o.property.specification)){var s=Vr(a,t.type),u=o.property.specification.type,l=o.property.useIntegerZoom;if("cross-faded"===o.property.specification["property-type"]||"cross-faded-data-driven"===o.property.specification["property-type"])if("constant"===o.value.kind)n.binders[a]=new zr(o.value.value,s,u),i.push("/u_"+a);else{var p=Nr(a,u,"source");n.binders[a]=new Br(o.value,s,u,l,e,p,t.id),i.push("/a_"+a)}else if("constant"===o.value.kind)n.binders[a]=new Pr(o.value.value,s,u),i.push("/u_"+a);else if("source"===o.value.kind){var c=Nr(a,u,"source");n.binders[a]=new Rr(o.value,s,u,c),i.push("/a_"+a)}else{var f=Nr(a,u,"composite");n.binders[a]=new Cr(o.value,s,u,l,e,f),i.push("/z_"+a)}}}return n.cacheKey=i.sort().join(""),n},Dr.prototype.populatePaintArrays=function(t,e,r,n,i){for(var a in this.binders){this.binders[a].populatePaintArray(t,e,n,i)}},Dr.prototype.setConstantPatternPositions=function(t,e){for(var r in this.binders){this.binders[r].setConstantPatternPositions(t,e)}},Dr.prototype.updatePaintArrays=function(t,e,r,n,i){var a=!1;for(var o in t)for(var s=0,u=e.getPositions(+o);s<u.length;s+=1){var l=u[s],p=r.feature(l.index);for(var c in this.binders){var f=this.binders[c];if(!(f instanceof Pr||f instanceof zr)&&!0===f.expression.isStateDependent){var h=n.paint.get(c);f.expression=h.value,f.updatePaintArray(l.start,l.end,p,t[o],i),a=!0}}}return a},Dr.prototype.defines=function(){var t=[];for(var e in this.binders)t.push.apply(t,this.binders[e].defines());return t},Dr.prototype.getPaintVertexBuffers=function(){return this._buffers},Dr.prototype.getUniforms=function(t,e){var r=[];for(var n in this.binders)for(var i=this.binders[n],a=0,o=i.uniformNames;a<o.length;a+=1){var s=o[a];if(e[s]){var u=i.getBinding(t,e[s]);r.push({name:s,property:n,binding:u})}}return r},Dr.prototype.setUniforms=function(t,e,r,n){for(var i=0,a=e;i<a.length;i+=1){var o=a[i],s=o.name,u=o.property,l=o.binding;this.binders[u].setUniforms(t,l,n,r.get(u),s)}},Dr.prototype.updatePatternPaintBuffers=function(t){var e=[];for(var r in this.binders){var n=this.binders[r];if(n instanceof Br){var i=2===t.fromScale?n.zoomInPaintVertexBuffer:n.zoomOutPaintVertexBuffer;i&&e.push(i)}else(n instanceof Rr||n instanceof Cr)&&n.paintVertexBuffer&&e.push(n.paintVertexBuffer)}this._buffers=e},Dr.prototype.upload=function(t){for(var e in this.binders)this.binders[e].upload(t);var r=[];for(var n in this.binders){var i=this.binders[n];(i instanceof Rr||i instanceof Cr)&&i.paintVertexBuffer&&r.push(i.paintVertexBuffer)}this._buffers=r},Dr.prototype.destroy=function(){for(var t in this.binders)this.binders[t].destroy()},tr.register("ConstantBinder",Pr),tr.register("CrossFadedConstantBinder",zr),tr.register("SourceExpressionBinder",Rr),tr.register("CrossFadedCompositeBinder",Br),tr.register("CompositeExpressionBinder",Cr),tr.register("ProgramConfiguration",Dr,{omit:["_buffers"]});var Lr=function(t,e,r,n){void 0===n&&(n=function(){return!0}),this.programConfigurations={};for(var i=0,a=e;i<a.length;i+=1){var o=a[i];this.programConfigurations[o.id]=Dr.createDynamic(o,r,n),this.programConfigurations[o.id].layoutAttributes=t}this.needsUpload=!1,this._featureMap=new vr,this._bufferOffset=0};function Xr(t,e,r,n,i){for(var a=i.patternDependencies,o=0,s=e;o<s.length;o+=1){var u=s[o],l=u.paint.get(t+"-pattern").value;if("constant"!==l.kind){var p=l.evaluate({zoom:n-1},r,{},i.availableImages),c=l.evaluate({zoom:n},r,{},i.availableImages),f=l.evaluate({zoom:n+1},r,{},i.availableImages);p=p&&p.name?p.name:p,c=c&&c.name?c.name:c,f=f&&f.name?f.name:f,a[p]=!0,a[c]=!0,a[f]=!0,r.patterns[u.id]={min:p,mid:c,max:f}}}return r}Lr.prototype.populatePaintArrays=function(t,e,r,n,i){for(var a in this.programConfigurations)this.programConfigurations[a].populatePaintArrays(t,e,r,n,i);void 0!==e.id&&this._featureMap.add(+e.id,r,this._bufferOffset,t),this._bufferOffset=t,this.needsUpload=!0},Lr.prototype.updatePaintArrays=function(t,e,r,n){for(var i=0,a=r;i<a.length;i+=1){var o=a[i];this.needsUpload=this.programConfigurations[o.id].updatePaintArrays(t,this._featureMap,e,o,n)||this.needsUpload}},Lr.prototype.get=function(t){return this.programConfigurations[t]},Lr.prototype.upload=function(t){if(this.needsUpload){for(var e in this.programConfigurations)this.programConfigurations[e].upload(t);this.needsUpload=!1}},Lr.prototype.destroy=function(){for(var t in this.programConfigurations)this.programConfigurations[t].destroy()},tr.register("ProgramConfigurationSet",Lr);var qr=["Unknown","Point","LineString","Polygon"],jr=P([{name:"a_pos_normal",components:2,type:"Int16"},{name:"a_data",components:4,type:"Uint8"}],4),Hr=jr.members;jr.size,jr.alignment;var Yr=63,Qr=Math.cos(Math.PI/180*37.5),Kr=15,Gr=20,Wr=15,Jr=.5,Zr=Math.pow(2,Wr-1)/Jr,$r=function(t){this.zoom=t.zoom,this.overscaling=t.overscaling,this.layers=t.layers,this.layerIds=this.layers.map((function(t){return t.id})),this.index=t.index,this.hasPattern=!1,this.patternFeatures=[],this.layoutVertexArray=new yr,this.indexArray=new mr,this.programConfigurations=new Lr(Hr,t.layers,t.zoom),this.segments=new cr,this.stateDependentLayerIds=this.layers.filter((function(t){return t.isStateDependent()})).map((function(t){return t.id}))};function tn(t,e,r,n,i){en(t,e,r||0,n||t.length-1,i||nn)}function en(t,e,r,n,i){for(;n>r;){if(n-r>600){var a=n-r+1,o=e-r+1,s=Math.log(a),u=.5*Math.exp(2*s/3),l=.5*Math.sqrt(s*u*(a-u)/a)*(o-a/2<0?-1:1);en(t,e,Math.max(r,Math.floor(e-o*u/a+l)),Math.min(n,Math.floor(e+(a-o)*u/a+l)),i)}var p=t[e],c=r,f=n;for(rn(t,r,e),i(t[n],p)>0&&rn(t,r,n);c<f;){for(rn(t,c,f),c++,f--;i(t[c],p)<0;)c++;for(;i(t[f],p)>0;)f--}0===i(t[r],p)?rn(t,r,f):rn(t,++f,n),f<=e&&(r=f+1),e<=f&&(n=f-1)}}function rn(t,e,r){var n=t[e];t[e]=t[r],t[r]=n}function nn(t,e){return t<e?-1:t>e?1:0}function an(t){for(var e=0,r=0,n=t.length,i=n-1,a=void 0,o=void 0;r<n;i=r++)a=t[r],e+=((o=t[i]).x-a.x)*(a.y+o.y);return e}function on(t,e){var r=t.length;if(r<=1)return[t];for(var n,i,a=[],o=0;o<r;o++){var s=an(t[o]);0!==s&&(t[o].area=Math.abs(s),void 0===i&&(i=s<0),i===s<0?(n&&a.push(n),n=[t[o]]):n.push(t[o]))}if(n&&a.push(n),e>1)for(var u=0;u<a.length;u++)a[u].length<=e||(tn(a[u],e,1,a[u].length-1,sn),a[u]=a[u].slice(0,e));return a}function sn(t,e){return e.area-t.area}$r.prototype.populate=function(t,e){this.hasPattern=!1;for(var r=this.layers[0].layout.get("line-sort-key"),n=[],i=0,a=t;i<a.length;i+=1){var o=a[i],s=o.feature,u=o.index,l=o.sourceLayerIndex;if(this.layers[0]._featureFilter(new ar(0),s)){var p=pr(s),c=r?r.evaluate(s,{}):void 0,f={id:s.id,properties:s.properties,type:s.type,sourceLayerIndex:l,index:u,geometry:p,patterns:{},sortKey:c};n.push(f)}}r&&n.sort((function(t,e){return t.sortKey-e.sortKey}));for(var h=0,d=n;h<d.length;h+=1){var y=d[h],m=y,v=m.geometry,g=m.index,x=m.sourceLayerIndex;if(this.hasPattern){var b=Xr("line",this.layers,y,this.zoom,e);this.patternFeatures.push(b)}else this.addFeature(y,v,g,{});var _=t[g].feature;e.featureIndex.insert(_,v,g,x,this.index)}},$r.prototype.update=function(t,e,r){this.stateDependentLayers.length&&this.programConfigurations.updatePaintArrays(t,e,this.stateDependentLayers,r)},$r.prototype.addFeatures=function(t,e){for(var r=0,n=this.patternFeatures;r<n.length;r+=1){var i=n[r];this.addFeature(i,i.geometry,i.index,e)}},$r.prototype.isEmpty=function(){return 0===this.layoutVertexArray.length},$r.prototype.uploadPending=function(){return!this.uploaded||this.programConfigurations.needsUpload},$r.prototype.upload=function(t){if(!this.uploaded){if(null==this.layoutVertexArray)return;this.layoutVertexBuffer=t.createVertexBuffer(this.layoutVertexArray,Hr),this.indexBuffer=t.createIndexBuffer(this.indexArray)}this.programConfigurations.upload(t),this.uploaded=!0},$r.prototype.destroy=function(){this.layoutVertexBuffer&&(this.layoutVertexBuffer.destroy(),this.indexBuffer.destroy(),this.programConfigurations.destroy(),this.segments.destroy())},$r.prototype.clear=function(){e.defined(this.layoutVertexArray)&&(this.layoutVertexArray=null),e.defined(this.indexArray)&&(this.indexArray=null)},$r.prototype.addFeature=function(t,e,r,n){for(var i=this.layers[0].layout,a=i.get("line-join").evaluate(t,{}),o=i.get("line-cap"),s=i.get("line-miter-limit"),u=i.get("line-round-limit"),l=0,p=e;l<p.length;l+=1){var c=p[l];this.addLine(c,t,a,o,s,u,r,n)}},$r.prototype.addLine=function(t,e,r,n,i,a,o,s){if(this.distance=0,this.scaledDistance=0,this.totalDistance=0,e.properties&&e.properties.hasOwnProperty("mapbox_clip_start")&&e.properties.hasOwnProperty("mapbox_clip_end")){this.clipStart=+e.properties.mapbox_clip_start,this.clipEnd=+e.properties.mapbox_clip_end;for(var u=0;u<t.length-1;u++)this.totalDistance+=t[u].dist(t[u+1])}for(var l="Polygon"===qr[e.type],p=t.length;p>=2&&t[p-1].equals(t[p-2]);)p--;for(var c=0;c<p-1&&t[c].equals(t[c+1]);)c++;if(!(p<(l?3:2))){"bevel"===r&&(i=1.05);var f,h=this.overscaling<=16?Kr*or/(512*this.overscaling):0,d=this.segments.prepareSegment(10*p,this.layoutVertexArray,this.indexArray),y=void 0,m=void 0,v=void 0,g=void 0;this.e1=this.e2=-1,l&&(f=t[p-2],g=t[c].sub(f)._unit()._perp());for(var x=c;x<p;x++)if(!(m=l&&x===p-1?t[c+1]:t[x+1])||!t[x].equals(m)){g&&(v=g),f&&(y=f),f=t[x],g=m?m.sub(f)._unit()._perp():v;var b=(v=v||g).add(g);0===b.x&&0===b.y||b._unit();var _=v.x*g.x+v.y*g.y,w=b.x*g.x+b.y*g.y,E=0!==w?1/w:1/0,T=2*Math.sqrt(2-2*w),A=w<Qr&&y&&m,S=v.x*g.y-v.y*g.x>0;if(A&&x>c){var I=f.dist(y);if(I>2*h){var k=f.sub(f.sub(y)._mult(h/I)._round());this.updateDistance(y,k),this.addCurrentVertex(k,v,0,0,d),y=k}}var M=y&&m,F=M?r:l?"butt":n;if(M&&"round"===F&&(E<a?F="miter":E<=2&&(F="fakeround")),"miter"===F&&E>i&&(F="bevel"),"bevel"===F&&(E>2&&(F="flipbevel"),E<i&&(F="miter")),y&&this.updateDistance(y,f),"miter"===F)b._mult(E),this.addCurrentVertex(f,b,0,0,d);else if("flipbevel"===F){if(E>100)b=g.mult(-1);else{var O=E*v.add(g).mag()/v.sub(g).mag();b._perp()._mult(O*(S?-1:1))}this.addCurrentVertex(f,b,0,0,d),this.addCurrentVertex(f,b.mult(-1),0,0,d)}else if("bevel"===F||"fakeround"===F){var P=-Math.sqrt(E*E-1),z=S?P:0,R=S?0:P;if(y&&this.addCurrentVertex(f,v,z,R,d),"fakeround"===F)for(var C=Math.round(180*T/Math.PI/Gr),B=1;B<C;B++){var D=B/C;if(.5!==D){var V=D-.5;D+=D*V*(D-1)*((1.0904+_*(_*(3.55645-1.43519*_)-3.2452))*V*V+(.848013+_*(.215638*_-1.06021)))}var U=g.sub(v)._mult(D)._add(v)._unit()._mult(S?-1:1);this.addHalfVertex(f,U.x,U.y,!1,S,0,d)}m&&this.addCurrentVertex(f,g,-z,-R,d)}else if("butt"===F)this.addCurrentVertex(f,b,0,0,d);else if("square"===F){var N=y?1:-1;this.addCurrentVertex(f,b,N,N,d)}else"round"===F&&(y&&(this.addCurrentVertex(f,v,0,0,d),this.addCurrentVertex(f,v,1,1,d,!0)),m&&(this.addCurrentVertex(f,g,-1,-1,d,!0),this.addCurrentVertex(f,g,0,0,d)));if(A&&x<p-1){var L=f.dist(m);if(L>2*h){var X=f.add(m.sub(f)._mult(h/L)._round());this.updateDistance(f,X),this.addCurrentVertex(X,g,0,0,d),f=X}}}this.programConfigurations.populatePaintArrays(this.layoutVertexArray.length,e,o,s)}},$r.prototype.addCurrentVertex=function(t,e,r,n,i,a){void 0===a&&(a=!1);var o=e.x+e.y*r,s=e.y-e.x*r,u=-e.x+e.y*n,l=-e.y-e.x*n;this.addHalfVertex(t,o,s,a,!1,r,i),this.addHalfVertex(t,u,l,a,!0,-n,i),this.distance>Zr/2&&0===this.totalDistance&&(this.distance=0,this.addCurrentVertex(t,e,r,n,i,a))},$r.prototype.addHalfVertex=function(t,e,r,n,i,a,o){var s=t.x,u=t.y,l=this.scaledDistance*Jr;this.layoutVertexArray.emplaceBack((s<<1)+(n?1:0),(u<<1)+(i?1:0),Math.round(Yr*e)+128,Math.round(Yr*r)+128,1+(0===a?0:a<0?-1:1)|(63&l)<<2,l>>6);var p=o.vertexLength++;this.e1>=0&&this.e2>=0&&(this.indexArray.emplaceBack(this.e1,this.e2,p),o.primitiveLength++),i?this.e2=p:this.e1=p},$r.prototype.updateDistance=function(t,e){this.distance+=t.dist(e),this.scaledDistance=this.totalDistance>0?(this.clipStart+(this.clipEnd-this.clipStart)*this.distance/this.totalDistance)*(Zr-1):this.distance},tr.register("LineBucket",$r,{omit:["layers","patternFeatures"]});var un=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype._refreshViews=function(){this.uint8=new Uint8Array(this.arrayBuffer),this.int16=new Int16Array(this.arrayBuffer)},e.prototype.emplaceBack=function(t,e){var r=this.length;return this.resize(r+1),this.emplace(r,t,e)},e.prototype.emplace=function(t,e,r){var n=2*t;return this.int16[n+0]=e,this.int16[n+1]=r,t},e}(dr);un.prototype.bytesPerElement=4,tr.register("StructArrayLayout2i4",un);var ln,pn=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype._refreshViews=function(){this.uint8=new Uint8Array(this.arrayBuffer),this.uint16=new Uint16Array(this.arrayBuffer)},e.prototype.emplaceBack=function(t,e){var r=this.length;return this.resize(r+1),this.emplace(r,t,e)},e.prototype.emplace=function(t,e,r){var n=2*t;return this.uint16[n+0]=e,this.uint16[n+1]=r,t},e}(dr);function cn(t,e,r){for(var n=r.patternDependencies,i=!1,a=0,o=e;a<o.length;a+=1){var s=o[a].paint.get(t+"-pattern");s.isConstant()||(i=!0);var u=s.constantOr(null);u&&(i=!0,n[u.to]=!0,n[u.from]=!0)}return i}if(pn.prototype.bytesPerElement=4,tr.register("StructArrayLayout2ui4",pn),"undefined"!=typeof WebAssembly){var fn,hn=void 0!==hn?hn:{},dn={};for(fn in hn)hn.hasOwnProperty(fn)&&(dn[fn]=hn[fn]);hn.arguments=[],hn.thisProgram="./this.program",hn.quit=function(t,e){throw e},hn.preRun=[],hn.postRun=[];var yn=!1,mn=!1,vn=!1,gn=!1;if(yn="object"==typeof window,mn="function"==typeof importScripts,vn="object"==typeof process&&"function"==typeof require&&!yn&&!mn,gn=!yn&&!vn&&!mn,hn.ENVIRONMENT)throw new Error("Module.ENVIRONMENT has been deprecated. To force the environment, use the ENVIRONMENT compile-time option (for example, -s ENVIRONMENT=web or -s ENVIRONMENT=node)");var xn,bn,_n="";if(vn)_n=__dirname+"/",hn.read=function(t,e){var r;return xn||(xn=require("fs")),bn||(bn=require("path")),t=bn.normalize(t),r=xn.readFileSync(t),e?r:r.toString()},hn.readBinary=function(t){var e=hn.read(t,!0);return e.buffer||(e=new Uint8Array(e)),_l(e.buffer),e},process.argv.length>1&&(hn.thisProgram=process.argv[1].replace(/\\/g,"/")),hn.arguments=process.argv.slice(2),"undefined"!=typeof module&&(module.exports=hn),process.on("uncaughtException",(function(t){if(!(t instanceof bp))throw t})),process.on("unhandledRejection",wp),hn.quit=function(t){process.exit(t)},hn.inspect=function(){return"[Emscripten Module object]"};else if(gn)"undefined"!=typeof read&&(hn.read=function(t){return read(t)}),hn.readBinary=function(t){var e;return"function"==typeof readbuffer?new Uint8Array(readbuffer(t)):(_l("object"==typeof(e=read(t,"binary"))),e)},"undefined"!=typeof scriptArgs?hn.arguments=scriptArgs:void 0!==arguments&&(hn.arguments=arguments),"function"==typeof quit&&(hn.quit=function(t){quit(t)});else{if(!yn&&!mn)throw new Error("environment detection error");mn?_n=self.location.href:document.currentScript&&(_n=document.currentScript.src),_n=0!==_n.indexOf("blob:")?_n.substr(0,_n.lastIndexOf("/")+1):"",hn.read=function(t){var e=new XMLHttpRequest;return e.open("GET",t,!1),e.send(null),e.responseText},mn&&(hn.readBinary=function(t){var e=new XMLHttpRequest;return e.open("GET",t,!1),e.responseType="arraybuffer",e.send(null),new Uint8Array(e.response)}),hn.readAsync=function(t,e,r){var n=new XMLHttpRequest;n.open("GET",t,!0),n.responseType="arraybuffer",n.onload=function(){200==n.status||0==n.status&&n.response?e(n.response):r()},n.onerror=r,n.send(null)},hn.setWindowTitle=function(t){document.title=t}}var wn=hn.print||("undefined"!=typeof console?console.log.bind(console):"undefined"!=typeof print?print:null),En=hn.printErr||("undefined"!=typeof printErr?printErr:"undefined"!=typeof console&&console.warn.bind(console)||wn);for(fn in dn)dn.hasOwnProperty(fn)&&(hn[fn]=dn[fn]);function xl(t){xl.shown||(xl.shown={}),xl.shown[t]||(xl.shown[t]=1)}dn=void 0,_l(void 0===hn.memoryInitializerPrefixURL,"Module.memoryInitializerPrefixURL option was removed, use Module.locateFile instead"),_l(void 0===hn.pthreadMainPrefixURL,"Module.pthreadMainPrefixURL option was removed, use Module.locateFile instead"),_l(void 0===hn.cdInitializerPrefixURL,"Module.cdInitializerPrefixURL option was removed, use Module.locateFile instead"),_l(void 0===hn.filePackagePrefixURL,"Module.filePackagePrefixURL option was removed, use Module.locateFile instead"),Pi=Oi=Fi=function(){wp("cannot use the stack before compiled code is ready to run, and has provided stack access")};var Tn={"f64-rem":function(t,e){return t%e},debugger:function(){}};new Array(0);var An,Sn=0,In=function(t){Sn=t},kn=function(){return Sn};function bl(t,e,r){switch("*"===(e=e||"i8").charAt(e.length-1)&&(e="i32"),e){case"i1":case"i8":return Pn[t>>0];case"i16":return Rn[t>>1];case"i32":case"i64":return Cn[t>>2];case"float":return Dn[t>>2];case"double":return Vn[t>>3];default:wp("invalid type for getValue: "+e)}return null}"object"!=typeof WebAssembly&&wp("No WebAssembly support found. Build with -s WASM=0 to target JavaScript instead.");var Mn=!1;function _l(t,e){t||wp("Assertion failed: "+e)}function wl(t){var e=hn["_"+t];return _l(e,"Cannot call unknown function "+t+", make sure it is exported"),e}function El(t,e,r,n,i){var a={string:function(t){var e=0;if(null!=t&&0!==t){var r=1+(t.length<<2);kl(t,e=Fi(r),r)}return e},array:function(t){var e=Fi(t.length);return Ml(t,e),e}};var o=wl(t),s=[],u=0;if(_l("array"!==e,'Return type should not be "array".'),n)for(var l=0;l<n.length;l++){var p=a[r[l]];p?(0===u&&(u=Pi()),s[l]=p(n[l])):s[l]=n[l]}var c=o.apply(null,s);return c=function(t){return"string"===e?Sl(t):"boolean"===e?Boolean(t):t}(c),0!==u&&Oi(u),c}function Tl(t,e,r,n){return function(){return El(t,e,r,arguments)}}var Fn="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function Al(t,e,r){for(var n=e+r,i=e;t[i]&&!(i>=n);)++i;if(i-e>16&&t.subarray&&Fn)return Fn.decode(t.subarray(e,i));for(var a="";e<i;){var o=t[e++];if(128&o){var s=63&t[e++];if(192!=(224&o)){var u=63&t[e++];if(224==(240&o)?o=(15&o)<<12|s<<6|u:(240!=(248&o)&&xl("Invalid UTF-8 leading byte 0x"+o.toString(16)+" encountered when deserializing a UTF-8 string on the asm.js/wasm heap to a JS string!"),o=(7&o)<<18|s<<12|u<<6|63&t[e++]),o<65536)a+=String.fromCharCode(o);else{var l=o-65536;a+=String.fromCharCode(55296|l>>10,56320|1023&l)}}else a+=String.fromCharCode((31&o)<<6|s)}else a+=String.fromCharCode(o)}return a}function Sl(t,e){return t?Al(zn,t,e):""}function Il(t,e,r,n){if(!(n>0))return 0;for(var i=r,a=r+n-1,o=0;o<t.length;++o){var s=t.charCodeAt(o);if(s>=55296&&s<=57343)s=65536+((1023&s)<<10)|1023&t.charCodeAt(++o);if(s<=127){if(r>=a)break;e[r++]=s}else if(s<=2047){if(r+1>=a)break;e[r++]=192|s>>6,e[r++]=128|63&s}else if(s<=65535){if(r+2>=a)break;e[r++]=224|s>>12,e[r++]=128|s>>6&63,e[r++]=128|63&s}else{if(r+3>=a)break;s>=2097152&&xl("Invalid Unicode code point 0x"+s.toString(16)+" encountered when serializing a JS string to an UTF-8 string on the asm.js/wasm heap! (Valid unicode code points should be in range 0-0x1FFFFF)."),e[r++]=240|s>>18,e[r++]=128|s>>12&63,e[r++]=128|s>>6&63,e[r++]=128|63&s}}return e[r]=0,r-i}function kl(t,e,r){return _l("number"==typeof r,"stringToUTF8(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!"),Il(t,zn,e,r)}function Ml(t,e){_l(t.length>=0,"writeArrayToMemory array must have a length (should be an array or typed array)"),Pn.set(t,e)}function Fl(){var t=new Error;if(!t.stack){try{throw new Error(0)}catch(e){t=e}if(!t.stack)return"(no stack trace available)"}return t.stack.toString()}"undefined"!=typeof TextDecoder&&new TextDecoder("utf-16le");var On,Pn,zn,Rn,Cn,Bn,Dn,Vn,Un=65536;function Ol(t,e){return t%e>0&&(t+=e-t%e),t}function Pl(){hn.HEAP8=Pn=new Int8Array(On),hn.HEAP16=Rn=new Int16Array(On),hn.HEAP32=Cn=new Int32Array(On),hn.HEAPU8=zn=new Uint8Array(On),hn.HEAPU16=new Uint16Array(On),hn.HEAPU32=Bn=new Uint32Array(On),hn.HEAPF32=Dn=new Float32Array(On),hn.HEAPF64=Vn=new Float64Array(On)}var Nn=5872,Ln=5248752,Xn=5248752,qn=5840;_l(Nn%16==0,"stack must start aligned"),_l(Xn%16==0,"heap must start aligned");var jn=5242880;hn.TOTAL_STACK&&_l(jn===hn.TOTAL_STACK,"the stack size can no longer be determined at runtime");var Hn=hn.TOTAL_MEMORY||16777216;function zl(){34821223==Bn[(Ln>>2)-1]&&2310721022==Bn[(Ln>>2)-2]||wp("Stack overflow! Stack cookie has been overwritten, expected hex dwords 0x89BACDFE and 0x02135467, but received 0x"+Bn[(Ln>>2)-2].toString(16)+" "+Bn[(Ln>>2)-1].toString(16)),1668509029!==Cn[0]&&wp("Runtime error: The application has corrupted its heap memory area (address zero)!")}function Rl(t){wp("Stack overflow! Attempted to allocate "+t+" bytes on the stack, but stack has only "+(Ln-Pi()+t)+" bytes available!")}if(Hn<jn&&En("TOTAL_MEMORY should be larger than TOTAL_STACK, was "+Hn+"! (TOTAL_STACK="+jn+")"),_l("undefined"!=typeof Int32Array&&"undefined"!=typeof Float64Array&&void 0!==Int32Array.prototype.subarray&&void 0!==Int32Array.prototype.set,"JS engine does not provide full typed array support"),hn.buffer?_l((On=hn.buffer).byteLength===Hn,"provided buffer should be "+Hn+" bytes, but it is "+On.byteLength):("object"==typeof WebAssembly&&"function"==typeof WebAssembly.Memory?(_l(Hn%Un==0),An=new WebAssembly.Memory({initial:Hn/Un}),On=An.buffer):On=new ArrayBuffer(Hn),_l(On.byteLength===Hn)),Pl(),Cn[qn>>2]=Xn,Cn[0]=1668509029,Rn[1]=25459,115!==zn[2]||99!==zn[3])throw"Runtime error: expected the system to be little-endian!";function Cl(t){for(;t.length>0;){var e=t.shift();if("function"!=typeof e){var r=e.func;"number"==typeof r?void 0===e.arg?hn.dynCall_v(r):hn.dynCall_vi(r,e.arg):r(void 0===e.arg?null:e.arg)}else e()}}var Yn=[],Qn=[],Kn=[],Gn=[],Wn=!1,Jn=!1;function Bl(){if(hn.preRun)for("function"==typeof hn.preRun&&(hn.preRun=[hn.preRun]);hn.preRun.length;)t=hn.preRun.shift(),Yn.unshift(t);var t;Cl(Yn)}function Dl(){if(zl(),hn.postRun)for("function"==typeof hn.postRun&&(hn.postRun=[hn.postRun]);hn.postRun.length;)t=hn.postRun.shift(),Gn.unshift(t);var t;Cl(Gn)}_l(Math.imul,"This browser does not support Math.imul(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),_l(Math.fround,"This browser does not support Math.fround(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),_l(Math.clz32,"This browser does not support Math.clz32(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),_l(Math.trunc,"This browser does not support Math.trunc(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill");var Zn=0,$n=null,ti=null,ei={};function Vl(t){Zn++,hn.monitorRunDependencies&&hn.monitorRunDependencies(Zn),t&&(_l(!ei[t]),ei[t]=1,null===$n&&"undefined"!=typeof setInterval&&($n=setInterval((function(){if(Mn)return clearInterval($n),void($n=null)}),1e4)))}function Ul(t){if(Zn--,hn.monitorRunDependencies&&hn.monitorRunDependencies(Zn),t&&(_l(ei[t]),delete ei[t]),0==Zn&&(null!==$n&&(clearInterval($n),$n=null),ti)){var e=ti;ti=null,e()}}hn.preloadedImages={},hn.preloadedAudios={};var ri={error:function(){wp("Filesystem support (FS) was not included. The problem is that you are using files from JS, but files were not used from C/C++, so filesystem support was not auto-included. You can force-include filesystem support with  -s FORCE_FILESYSTEM=1")},init:function(){ri.error()},createDataFile:function(){ri.error()},createPreloadedFile:function(){ri.error()},createLazyFile:function(){ri.error()},open:function(){ri.error()},mkdev:function(){ri.error()},registerDevice:function(){ri.error()},analyzePath:function(){ri.error()},loadFilesFromDB:function(){ri.error()},ErrnoError:function(){ri.error()}};hn.FS_createDataFile=ri.createDataFile,hn.FS_createPreloadedFile=ri.createPreloadedFile;var ni,ii="data:application/octet-stream;base64,";function Nl(t){return String.prototype.startsWith?t.startsWith(ii):0===t.indexOf(ii)}var ai="undefined"==typeof window?self:window;function Ll(){try{if(hn.wasmBinary)return new Uint8Array(hn.wasmBinary);if(hn.readBinary)return hn.readBinary(ni);throw"both async and sync fetching of the wasm failed"}catch(t){wp(t)}}function Xl(t){var e={env:t,global:{NaN:NaN,Infinity:1/0},"global.Math":Math,asm2wasm:Tn};function r(t,e){var r=t.exports;hn.asm=r,Ul("wasm-instantiate")}Vl("wasm-instantiate");var n=hn;function i(t){_l(hn===n,"the Module object should not be replaced during async compilation - perhaps the order of HTML elements is wrong?"),n=null,r(t.instance)}function a(t){return(hn.wasmBinary||!yn&&!mn||"function"!=typeof fetch?new Promise((function(t,e){t(Ll())})):fetch(ni,{credentials:"same-origin"}).then((function(t){if(!t.ok)throw"failed to load wasm binary file at '"+ni+"'";return t.arrayBuffer()})).catch((function(){return Ll()}))).then((function(t){return WebAssembly.instantiate(t,e)})).then(t,(function(t){}))}if(hn.instantiateWasm)try{return hn.instantiateWasm(e,r)}catch(t){return!1}return function(){if(hn.wasmBinary||"function"!=typeof WebAssembly.instantiateStreaming||Nl(ni)||"function"!=typeof fetch)return a(i);fetch(ni,{credentials:"same-origin"}).then((function(t){return WebAssembly.instantiateStreaming(t,e).then(i,(function(t){a(i)}))}))}(),{}}Nl(ni=ai.location.href.endsWith(".openrealspace")?"../../static/Build/Cesium/ThirdParty/earcut.wasm":"ThirdParty/earcut.wasm")||(ln=ni,ni=hn.locateFile?hn.locateFile(ln,_n):_n+ln),hn.asm=function(t,e,r){e.memory=An,e.table=new WebAssembly.Table({initial:260,maximum:260,element:"anyfunc"}),e.__memory_base=1024,e.__table_base=0;var n=Xl(e);return _l(n,"binaryen setup failed (no wasm support?)"),n};var oi=5856;function ql(t){return Mi(t)}_l(oi%8==0);var si={},ui=[];function jl(t){t&&si[t].refcount++}function Hl(t){if(!t||si[t])return t;for(var e in si)for(var r=+e,n=si[r].adjusted,i=n.length,a=0;a<i;a++)if(n[a]===t)return r;return t}function Yl(t){var e=si[t];return e&&!e.caught&&(e.caught=!0,ki.uncaught_exception--),e&&(e.rethrown=!1),ui.push(t),jl(Hl(t)),t}function Ql(t,e,r){throw si[t]={ptr:t,adjusted:[t],type:e,destructor:r,refcount:0,caught:!1,rethrown:!1},"uncaught_exception"in ki?ki.uncaught_exception++:ki.uncaught_exception=1,t+" - Exception catching is disabled, this exception cannot be caught. Compile with -s DISABLE_EXCEPTION_CATCHING=0 or DISABLE_EXCEPTION_CATCHING=2 to catch."}function Kl(){return!!ki.uncaught_exception}function Gl(){}function Wl(){}var li={buffers:[null,[],[]],printChar:function(t,e){var r=li.buffers[t];_l(r),0===e||10===e?((1===t?wn:En)(Al(r,0)),r.length=0):r.push(e)},varargs:0,get:function(t){return li.varargs+=4,Cn[li.varargs-4>>2]},getStr:function(){return Sl(li.get())},get64:function(){var t=li.get(),e=li.get();return _l(t>=0?0===e:-1===e),t},getZero:function(){_l(0===li.get())}};function Jl(t,e){li.varargs=e;try{li.getStreamFromFD(),li.get(),li.get(),li.get(),li.get();return wp("it should not be possible to operate on streams when !SYSCALLS_REQUIRE_FILESYSTEM"),0}catch(t){return void 0!==ri&&t instanceof ri.ErrnoError||wp(t),-t.errno}}function Zl(){var t=hn._fflush;t&&t(0);var e=li.buffers;e[1].length&&li.printChar(1,10),e[2].length&&li.printChar(2,10)}function $l(t,e){li.varargs=e;try{for(var r=li.get(),n=li.get(),i=li.get(),a=0,o=0;o<i;o++){for(var s=Cn[n+8*o>>2],u=Cn[n+(8*o+4)>>2],l=0;l<u;l++)li.printChar(r,zn[s+l]);a+=u}return a}catch(t){return void 0!==ri&&t instanceof ri.ErrnoError||wp(t),-t.errno}}function tp(t,e){li.varargs=e;try{return 0}catch(t){return void 0!==ri&&t instanceof ri.ErrnoError||wp(t),-t.errno}}function ep(t,e){li.varargs=e;try{li.getStreamFromFD();return wp("it should not be possible to operate on streams when !SYSCALLS_REQUIRE_FILESYSTEM"),0}catch(t){return void 0!==ri&&t instanceof ri.ErrnoError||wp(t),-t.errno}}function rp(){}function np(){hn.abort()}function ip(){return Pn.length}function ap(t,e,r){zn.set(zn.subarray(e,e+r),t)}function op(t){if(!hn.___errno_location)return t;Cn[hn.___errno_location()>>2]=t}function sp(t){wp("Cannot enlarge memory arrays to size "+t+" bytes (OOM). Either (1) compile with  -s TOTAL_MEMORY=X  with X higher than the current value "+Pn.length+", (2) compile with  -s ALLOW_MEMORY_GROWTH=1  which allows increasing the size at runtime, or (3) if you want malloc to return NULL (0) instead of this abort, compile with  -s ABORTING_MALLOC=0 ")}function up(t){t=Ol(t,65536);var e=On.byteLength;try{return-1!==An.grow((t-e)/65536)&&(On=An.buffer,!0)}catch(r){return console.error("emscripten_realloc_buffer: Attempted to grow from "+e+" bytes to "+t+" bytes, but got error: "+r),!1}}function lp(t){var e=ip();_l(t>e);var r=65536,n=2147418112;if(t>n)return!1;for(var i=Math.max(e,16777216);i<t;)(i=i<=536870912?Ol(2*i,r):Math.min(Ol((3*i+2147483648)/4,r),n))===e&&xl("Cannot ask for more memory since we reached the practical limit in browsers (which is just below 2GB), so the request would have failed. Requesting only "+Pn.length);return!!up(i)&&(Pl(),!0)}function pp(t){En("Invalid function pointer called with signature 'ii'. Perhaps this is an invalid value (e.g. caused by calling a virtual method on a NULL pointer)? Or calling a function with an incorrect type, which will fail? (it is worth building your source files with -Werror (warnings are errors), as warnings can indicate undefined behavior which can cause this)"),En("Build with ASSERTIONS=2 for more info."),wp(t)}function cp(t){En("Invalid function pointer called with signature 'iidiiii'. Perhaps this is an invalid value (e.g. caused by calling a virtual method on a NULL pointer)? Or calling a function with an incorrect type, which will fail? (it is worth building your source files with -Werror (warnings are errors), as warnings can indicate undefined behavior which can cause this)"),En("Build with ASSERTIONS=2 for more info."),wp(t)}function fp(t){En("Invalid function pointer called with signature 'iiii'. Perhaps this is an invalid value (e.g. caused by calling a virtual method on a NULL pointer)? Or calling a function with an incorrect type, which will fail? (it is worth building your source files with -Werror (warnings are errors), as warnings can indicate undefined behavior which can cause this)"),En("Build with ASSERTIONS=2 for more info."),wp(t)}function hp(t){En("Invalid function pointer called with signature 'jiji'. Perhaps this is an invalid value (e.g. caused by calling a virtual method on a NULL pointer)? Or calling a function with an incorrect type, which will fail? (it is worth building your source files with -Werror (warnings are errors), as warnings can indicate undefined behavior which can cause this)"),En("Build with ASSERTIONS=2 for more info."),wp(t)}function dp(t){En("Invalid function pointer called with signature 'v'. Perhaps this is an invalid value (e.g. caused by calling a virtual method on a NULL pointer)? Or calling a function with an incorrect type, which will fail? (it is worth building your source files with -Werror (warnings are errors), as warnings can indicate undefined behavior which can cause this)"),En("Build with ASSERTIONS=2 for more info."),wp(t)}function yp(t){En("Invalid function pointer called with signature 'vi'. Perhaps this is an invalid value (e.g. caused by calling a virtual method on a NULL pointer)? Or calling a function with an incorrect type, which will fail? (it is worth building your source files with -Werror (warnings are errors), as warnings can indicate undefined behavior which can cause this)"),En("Build with ASSERTIONS=2 for more info."),wp(t)}function mp(t){En("Invalid function pointer called with signature 'vii'. Perhaps this is an invalid value (e.g. caused by calling a virtual method on a NULL pointer)? Or calling a function with an incorrect type, which will fail? (it is worth building your source files with -Werror (warnings are errors), as warnings can indicate undefined behavior which can cause this)"),En("Build with ASSERTIONS=2 for more info."),wp(t)}function vp(t){En("Invalid function pointer called with signature 'viiii'. Perhaps this is an invalid value (e.g. caused by calling a virtual method on a NULL pointer)? Or calling a function with an incorrect type, which will fail? (it is worth building your source files with -Werror (warnings are errors), as warnings can indicate undefined behavior which can cause this)"),En("Build with ASSERTIONS=2 for more info."),wp(t)}function gp(t){En("Invalid function pointer called with signature 'viiiii'. Perhaps this is an invalid value (e.g. caused by calling a virtual method on a NULL pointer)? Or calling a function with an incorrect type, which will fail? (it is worth building your source files with -Werror (warnings are errors), as warnings can indicate undefined behavior which can cause this)"),En("Build with ASSERTIONS=2 for more info."),wp(t)}function xp(t){En("Invalid function pointer called with signature 'viiiiii'. Perhaps this is an invalid value (e.g. caused by calling a virtual method on a NULL pointer)? Or calling a function with an incorrect type, which will fail? (it is worth building your source files with -Werror (warnings are errors), as warnings can indicate undefined behavior which can cause this)"),En("Build with ASSERTIONS=2 for more info."),wp(t)}var pi={},ci={abort:wp,setTempRet0:In,getTempRet0:kn,abortStackOverflow:Rl,nullFunc_ii:pp,nullFunc_iidiiii:cp,nullFunc_iiii:fp,nullFunc_jiji:hp,nullFunc_v:dp,nullFunc_vi:yp,nullFunc_vii:mp,nullFunc_viiii:vp,nullFunc_viiiii:gp,nullFunc_viiiiii:xp,___cxa_allocate_exception:ql,___cxa_begin_catch:Yl,___cxa_throw:Ql,___cxa_uncaught_exception:Kl,___exception_addRef:jl,___exception_deAdjust:Hl,___gxx_personality_v0:Gl,___lock:Wl,___setErrNo:op,___syscall140:Jl,___syscall146:$l,___syscall54:tp,___syscall6:ep,___unlock:rp,_abort:np,_emscripten_get_heap_size:ip,_emscripten_memcpy_big:ap,_emscripten_resize_heap:lp,abortOnCannotGrowMemory:sp,emscripten_realloc_buffer:up,flush_NO_FILESYSTEM:Zl,tempDoublePtr:oi,DYNAMICTOP_PTR:qn},fi=hn.asm(pi,ci,On),hi=fi.__ZSt18uncaught_exceptionv;fi.__ZSt18uncaught_exceptionv=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),hi.apply(null,arguments)};var di=fi.___cxa_can_catch;fi.___cxa_can_catch=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),di.apply(null,arguments)};var yi=fi.___cxa_is_pointer_type;fi.___cxa_is_pointer_type=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),yi.apply(null,arguments)};var mi=fi.___errno_location;fi.___errno_location=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),mi.apply(null,arguments)};var vi=fi._earcut;fi._earcut=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),vi.apply(null,arguments)};var gi=fi._fflush;fi._fflush=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),gi.apply(null,arguments)};var xi=fi._free;fi._free=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),xi.apply(null,arguments)};var bi=fi._llvm_maxnum_f64;fi._llvm_maxnum_f64=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),bi.apply(null,arguments)};var _i=fi._llvm_minnum_f64;fi._llvm_minnum_f64=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),_i.apply(null,arguments)};var wi=fi._malloc;fi._malloc=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),wi.apply(null,arguments)};var Ei=fi._sbrk;fi._sbrk=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),Ei.apply(null,arguments)};var Ti=fi.establishStackSpace;fi.establishStackSpace=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),Ti.apply(null,arguments)};var Ai=fi.stackAlloc;fi.stackAlloc=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),Ai.apply(null,arguments)};var Si=fi.stackRestore;fi.stackRestore=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),Si.apply(null,arguments)};var Ii=fi.stackSave;fi.stackSave=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),Ii.apply(null,arguments)},hn.asm=fi;var ki=hn.__ZSt18uncaught_exceptionv=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),hn.asm.__ZSt18uncaught_exceptionv.apply(null,arguments)};hn.___cxa_can_catch=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),hn.asm.___cxa_can_catch.apply(null,arguments)},hn.___cxa_is_pointer_type=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),hn.asm.___cxa_is_pointer_type.apply(null,arguments)},hn.___errno_location=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),hn.asm.___errno_location.apply(null,arguments)},hn._earcut=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),hn.asm._earcut.apply(null,arguments)},hn._emscripten_replace_memory=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),hn.asm._emscripten_replace_memory.apply(null,arguments)},hn._fflush=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),hn.asm._fflush.apply(null,arguments)},hn._free=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),hn.asm._free.apply(null,arguments)},hn._llvm_maxnum_f64=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),hn.asm._llvm_maxnum_f64.apply(null,arguments)},hn._llvm_minnum_f64=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),hn.asm._llvm_minnum_f64.apply(null,arguments)};var Mi=hn._malloc=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),hn.asm._malloc.apply(null,arguments)};hn._memcpy=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),hn.asm._memcpy.apply(null,arguments)},hn._memset=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),hn.asm._memset.apply(null,arguments)},hn._sbrk=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),hn.asm._sbrk.apply(null,arguments)},hn.establishStackSpace=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),hn.asm.establishStackSpace.apply(null,arguments)};var Fi=hn.stackAlloc=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),hn.asm.stackAlloc.apply(null,arguments)},Oi=hn.stackRestore=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),hn.asm.stackRestore.apply(null,arguments)},Pi=hn.stackSave=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),hn.asm.stackSave.apply(null,arguments)};function bp(t){this.name="ExitStatus",this.message="Program terminated with exit("+t+")",this.status=t}function _p(t){function e(){hn.calledRun||(hn.calledRun=!0,Mn||(zl(),Wn||(Wn=!0,Cl(Qn)),zl(),Cl(Kn),hn.onRuntimeInitialized&&hn.onRuntimeInitialized(),_l(!hn._main,'compiled without a main, but one is present. if you added it from JS, use Module["onRuntimeInitialized"]'),Dl()))}t=t||hn.arguments,Zn>0||(_l(0==(3&Ln)),Bn[(Ln>>2)-1]=34821223,Bn[(Ln>>2)-2]=2310721022,Bl(),Zn>0||hn.calledRun||(hn.setStatus?(hn.setStatus("Running..."),setTimeout((function(){setTimeout((function(){hn.setStatus("")}),1),e()}),1)):e(),zl()))}hn.dynCall_ii=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),hn.asm.dynCall_ii.apply(null,arguments)},hn.dynCall_iidiiii=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),hn.asm.dynCall_iidiiii.apply(null,arguments)},hn.dynCall_iiii=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),hn.asm.dynCall_iiii.apply(null,arguments)},hn.dynCall_jiji=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),hn.asm.dynCall_jiji.apply(null,arguments)},hn.dynCall_v=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),hn.asm.dynCall_v.apply(null,arguments)},hn.dynCall_vi=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),hn.asm.dynCall_vi.apply(null,arguments)},hn.dynCall_vii=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),hn.asm.dynCall_vii.apply(null,arguments)},hn.dynCall_viiii=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),hn.asm.dynCall_viiii.apply(null,arguments)},hn.dynCall_viiiii=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),hn.asm.dynCall_viiiii.apply(null,arguments)},hn.dynCall_viiiiii=function(){return _l(Wn,"you need to wait for the runtime to be ready (e.g. wait for main() to be called)"),_l(!Jn,"the runtime was exited (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),hn.asm.dynCall_viiiiii.apply(null,arguments)},hn.asm=fi,hn.intArrayFromString||(hn.intArrayFromString=function(){wp("'intArrayFromString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.intArrayToString||(hn.intArrayToString=function(){wp("'intArrayToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.ccall=El,hn.cwrap=Tl,hn.setValue||(hn.setValue=function(){wp("'setValue' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.getValue=bl,hn.allocate||(hn.allocate=function(){wp("'allocate' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.getMemory||(hn.getMemory=function(){wp("'getMemory' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),hn.AsciiToString||(hn.AsciiToString=function(){wp("'AsciiToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.stringToAscii||(hn.stringToAscii=function(){wp("'stringToAscii' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.UTF8ArrayToString||(hn.UTF8ArrayToString=function(){wp("'UTF8ArrayToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.UTF8ToString||(hn.UTF8ToString=function(){wp("'UTF8ToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.stringToUTF8Array||(hn.stringToUTF8Array=function(){wp("'stringToUTF8Array' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.stringToUTF8||(hn.stringToUTF8=function(){wp("'stringToUTF8' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.lengthBytesUTF8||(hn.lengthBytesUTF8=function(){wp("'lengthBytesUTF8' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.UTF16ToString||(hn.UTF16ToString=function(){wp("'UTF16ToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.stringToUTF16||(hn.stringToUTF16=function(){wp("'stringToUTF16' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.lengthBytesUTF16||(hn.lengthBytesUTF16=function(){wp("'lengthBytesUTF16' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.UTF32ToString||(hn.UTF32ToString=function(){wp("'UTF32ToString' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.stringToUTF32||(hn.stringToUTF32=function(){wp("'stringToUTF32' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.lengthBytesUTF32||(hn.lengthBytesUTF32=function(){wp("'lengthBytesUTF32' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.allocateUTF8||(hn.allocateUTF8=function(){wp("'allocateUTF8' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.stackTrace||(hn.stackTrace=function(){wp("'stackTrace' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.addOnPreRun||(hn.addOnPreRun=function(){wp("'addOnPreRun' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.addOnInit||(hn.addOnInit=function(){wp("'addOnInit' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.addOnPreMain||(hn.addOnPreMain=function(){wp("'addOnPreMain' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.addOnExit||(hn.addOnExit=function(){wp("'addOnExit' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.addOnPostRun||(hn.addOnPostRun=function(){wp("'addOnPostRun' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.writeStringToMemory||(hn.writeStringToMemory=function(){wp("'writeStringToMemory' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.writeArrayToMemory||(hn.writeArrayToMemory=function(){wp("'writeArrayToMemory' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.writeAsciiToMemory||(hn.writeAsciiToMemory=function(){wp("'writeAsciiToMemory' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.addRunDependency||(hn.addRunDependency=function(){wp("'addRunDependency' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),hn.removeRunDependency||(hn.removeRunDependency=function(){wp("'removeRunDependency' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),hn.ENV||(hn.ENV=function(){wp("'ENV' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.FS||(hn.FS=function(){wp("'FS' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.FS_createFolder||(hn.FS_createFolder=function(){wp("'FS_createFolder' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),hn.FS_createPath||(hn.FS_createPath=function(){wp("'FS_createPath' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),hn.FS_createDataFile||(hn.FS_createDataFile=function(){wp("'FS_createDataFile' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),hn.FS_createPreloadedFile||(hn.FS_createPreloadedFile=function(){wp("'FS_createPreloadedFile' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),hn.FS_createLazyFile||(hn.FS_createLazyFile=function(){wp("'FS_createLazyFile' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),hn.FS_createLink||(hn.FS_createLink=function(){wp("'FS_createLink' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),hn.FS_createDevice||(hn.FS_createDevice=function(){wp("'FS_createDevice' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),hn.FS_unlink||(hn.FS_unlink=function(){wp("'FS_unlink' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),hn.GL||(hn.GL=function(){wp("'GL' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.dynamicAlloc||(hn.dynamicAlloc=function(){wp("'dynamicAlloc' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.warnOnce||(hn.warnOnce=function(){wp("'warnOnce' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.loadDynamicLibrary||(hn.loadDynamicLibrary=function(){wp("'loadDynamicLibrary' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.loadWebAssemblyModule||(hn.loadWebAssemblyModule=function(){wp("'loadWebAssemblyModule' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.getLEB||(hn.getLEB=function(){wp("'getLEB' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.getFunctionTables||(hn.getFunctionTables=function(){wp("'getFunctionTables' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.alignFunctionTables||(hn.alignFunctionTables=function(){wp("'alignFunctionTables' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.registerFunctions||(hn.registerFunctions=function(){wp("'registerFunctions' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.addFunction||(hn.addFunction=function(){wp("'addFunction' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.removeFunction||(hn.removeFunction=function(){wp("'removeFunction' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.getFuncWrapper||(hn.getFuncWrapper=function(){wp("'getFuncWrapper' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.prettyPrint||(hn.prettyPrint=function(){wp("'prettyPrint' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.makeBigInt||(hn.makeBigInt=function(){wp("'makeBigInt' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.dynCall||(hn.dynCall=function(){wp("'dynCall' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.getCompilerSetting||(hn.getCompilerSetting=function(){wp("'getCompilerSetting' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.stackSave||(hn.stackSave=function(){wp("'stackSave' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.stackRestore||(hn.stackRestore=function(){wp("'stackRestore' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.stackAlloc||(hn.stackAlloc=function(){wp("'stackAlloc' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.establishStackSpace||(hn.establishStackSpace=function(){wp("'establishStackSpace' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.print||(hn.print=function(){wp("'print' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.printErr||(hn.printErr=function(){wp("'printErr' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.getTempRet0||(hn.getTempRet0=function(){wp("'getTempRet0' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.setTempRet0||(hn.setTempRet0=function(){wp("'setTempRet0' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.Pointer_stringify||(hn.Pointer_stringify=function(){wp("'Pointer_stringify' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}),hn.ALLOC_NORMAL||Object.defineProperty(hn,"ALLOC_NORMAL",{get:function(){wp("'ALLOC_NORMAL' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}}),hn.ALLOC_STACK||Object.defineProperty(hn,"ALLOC_STACK",{get:function(){wp("'ALLOC_STACK' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}}),hn.ALLOC_DYNAMIC||Object.defineProperty(hn,"ALLOC_DYNAMIC",{get:function(){wp("'ALLOC_DYNAMIC' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}}),hn.ALLOC_NONE||Object.defineProperty(hn,"ALLOC_NONE",{get:function(){wp("'ALLOC_NONE' was not exported. add it to EXTRA_EXPORTED_RUNTIME_METHODS (see the FAQ)")}}),bp.prototype=new Error,bp.prototype.constructor=bp,ti=function t(){hn.calledRun||_p(),hn.calledRun||(ti=t)},hn.run=_p;var zi=[];function wp(t){hn.onAbort&&hn.onAbort(t),Mn=!0;var e,r="abort("+(t=void 0!==t?'"'+t+'"':"")+") at "+(e=Fl(),hn.extraStackTrace&&(e+="\n"+hn.extraStackTrace()),e.replace(/__Z[\w\d_]+/g,(function(t){return t==t?t:t+" ["+t+"]"})));throw zi&&zi.forEach((function(e){r=e(r,t)})),r}if(hn.abort=wp,hn.preInit)for("function"==typeof hn.preInit&&(hn.preInit=[hn.preInit]);hn.preInit.length>0;)hn.preInit.pop()();hn.noExitRuntime=!0,_p()}else hn=null;var Ri=hn,Ci=!1;if(e.defined(Ri)){Ri.onRuntimeInitialized=function(){Ci=!0};var Bi=Ri.cwrap("earcut","number",["number","number","number","number","number","number"])}var Di=P([{name:"a_pos",components:2,type:"Int16"}],4),Vi=Di.members,Ui=500,Ni=function(t){this.zoom=t.zoom,this.overscaling=t.overscaling,this.layers=t.layers,this.layerIds=this.layers.map((function(t){return t.id})),this.index=t.index,this.hasPattern=!1,this.patternFeatures=[],this.layoutVertexArray=new un,this.indexArray=new mr,this.indexArray2=new pn,this.programConfigurations=new Lr(Vi,t.layers,t.zoom),this.segments=new cr,this.segments2=new cr,this.stateDependentLayerIds=this.layers.filter((function(t){return t.isStateDependent()})).map((function(t){return t.id}))};Ni.prototype.populate=function(t,e){this.hasPattern=cn("fill",this.layers,e);for(var r=[],n=0,i=t;n<i.length;n+=1){var a=i[n],o=a.feature,s=a.index,u=a.sourceLayerIndex;if(this.layers[0]._featureFilter(new ar(0),o)){var l=pr(o),p={id:o.id,properties:o.properties,type:o.type,sourceLayerIndex:u,index:s,geometry:l,patterns:{},sortKey:undefined};r.push(p)}}for(var c=0,f=r;c<f.length;c+=1){var h=f[c],d=h,y=d.geometry,m=d.index,v=d.sourceLayerIndex;if(this.hasPattern){var g=Xr("fill",this.layers,h,this.zoom,e);this.patternFeatures.push(g)}else this.addFeature(h,y,m,{},e.indexData);var x=t[m].feature;e.featureIndex.insert(x,y,m,v,this.index)}},Ni.prototype.update=function(t,e,r){this.stateDependentLayers.length&&this.programConfigurations.updatePaintArrays(t,e,this.stateDependentLayers,r)},Ni.prototype.addFeatures=function(t,e){for(var r=0,n=this.patternFeatures;r<n.length;r+=1){var i=n[r];this.addFeature(i,i.geometry,i.index,e)}},Ni.prototype.isEmpty=function(){return 0===this.layoutVertexArray.length},Ni.prototype.uploadPending=function(){return!this.uploaded||this.programConfigurations.needsUpload},Ni.prototype.upload=function(t){if(!this.uploaded){if(null==this.layoutVertexArray)return;this.layoutVertexBuffer=t.createVertexBuffer(this.layoutVertexArray,Vi),this.indexBuffer=t.createIndexBuffer(this.indexArray),this.indexBuffer2=t.createIndexBuffer(this.indexArray2)}this.programConfigurations.upload(t),this.uploaded=!0},Ni.prototype.destroy=function(){this.layoutVertexBuffer&&(this.layoutVertexBuffer.destroy(),this.indexBuffer.destroy(),this.indexBuffer2.destroy(),this.programConfigurations.destroy(),this.segments.destroy(),this.segments2.destroy())},Ni.prototype.clear=function(){e.defined(this.layoutVertexArray)&&(this.layoutVertexArray=null),e.defined(this.indexArray)&&(this.indexArray=null),e.defined(this.indexArray2)&&(this.indexArray2=null)},Ni.prototype.addFeature=function(t,n,i,a,o){for(var s=0,u=on(n,Ui);s<u.length;s+=1){for(var l=u[s],p=0,c=0,f=l;c<f.length;c+=1){p+=f[c].length}for(var h,d=this.segments.prepareSegment(p,this.layoutVertexArray,this.indexArray),y=d.vertexLength,m=[],v=[],g=0,x=l;g<x.length;g+=1){var b=x[g];if(0!==b.length){b!==l[0]&&v.push(m.length/2);var _=this.segments2.prepareSegment(b.length,this.layoutVertexArray,this.indexArray2),w=_.vertexLength;this.layoutVertexArray.emplaceBack(b[0].x,b[0].y),this.indexArray2.emplaceBack(w+b.length-1,w),m.push(b[0].x),m.push(b[0].y);for(var E=1;E<b.length;E++)this.layoutVertexArray.emplaceBack(b[E].x,b[E].y),this.indexArray2.emplaceBack(w+E-1,w+E),m.push(b[E].x),m.push(b[E].y);_.vertexLength+=b.length,_.primitiveLength+=b.length}}if(e.defined(o)&&e.defined(o[t.id]))h=o[t.id];else if(!0===Ci){var T=new Int32Array(m),A=T.length,S=Ri._malloc(Int32Array.BYTES_PER_ELEMENT*A);Ri.HEAP32.set(T,S/Int32Array.BYTES_PER_ELEMENT);var I=new Int32Array(v),k=I.length,M=Ri._malloc(Int32Array.BYTES_PER_ELEMENT*k);Ri.HEAP32.set(I,M/Int32Array.BYTES_PER_ELEMENT);var F=new Int32Array(10*A),O=Ri._malloc(Int32Array.BYTES_PER_ELEMENT*A*10);Ri.HEAP32.set(F,O/Int32Array.BYTES_PER_ELEMENT);var P=Bi(S,A,M,k,2,O),z=new Int32Array(Ri.HEAP32.buffer,O,P);h=new Int32Array(z),Ri._free(S),Ri._free(M),Ri._free(O)}else h=r.earcut(m,v);for(var R=0;R<h.length;R+=3)this.indexArray.emplaceBack(y+h[R],y+h[R+1],y+h[R+2]);d.vertexLength+=p,d.primitiveLength+=h.length/3}this.programConfigurations.populatePaintArrays(this.layoutVertexArray.length,t,i,a)},tr.register("FillBucket",Ni,{omit:["layers","patternFeatures"]});var Li=function(t,e){this._structArray=t,this._pos1=e*this.size,this._pos2=this._pos1/2,this._pos4=this._pos1/4,this._pos8=this._pos1/8},Xi=128,qi=5,ji=function(){this.isTransferred=!1,this.capacity=-1,this.resize(0)};ji.serialize=function(t,e){return t.isTransferred&&console.log("StructArray array.isTransferred."),t._trim(),e&&(t.isTransferred=!0,e.push(t.arrayBuffer)),{length:t.length,arrayBuffer:t.arrayBuffer}},ji.deserialize=function(t){var e=Object.create(this.prototype);return e.arrayBuffer=t.arrayBuffer,e.length=t.length,e.capacity=t.arrayBuffer.byteLength/e.bytesPerElement,e._refreshViews(),e},ji.prototype._trim=function(){this.length!==this.capacity&&(this.capacity=this.length,this.arrayBuffer=this.arrayBuffer.slice(0,this.length*this.bytesPerElement),this._refreshViews())},ji.prototype.clear=function(){this.length=0},ji.prototype.resize=function(t){this.reserve(t),this.length=t},ji.prototype.reserve=function(t){if(t>this.capacity){this.capacity=Math.max(t,Math.floor(this.capacity*qi),Xi),this.arrayBuffer=new ArrayBuffer(this.capacity*this.bytesPerElement);var e=this.uint8;this._refreshViews(),e&&this.uint8.set(e)}},ji.prototype._refreshViews=function(){throw new Error("_refreshViews() must be implemented by each concrete StructArray layout")};var Hi=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype._refreshViews=function(){this.uint8=new Uint8Array(this.arrayBuffer),this.uint32=new Uint32Array(this.arrayBuffer),this.uint16=new Uint16Array(this.arrayBuffer)},e.prototype.emplaceBack=function(t,e,r){var n=this.length;return this.resize(n+1),this.emplace(n,t,e,r)},e.prototype.emplace=function(t,e,r,n){var i=2*t,a=4*t;return this.uint32[i+0]=e,this.uint16[a+2]=r,this.uint16[a+3]=n,t},e}(ji);Hi.prototype.bytesPerElement=8;var Yi=function(t){function e(){t.apply(this,arguments)}t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e;var r={featureIndex:{configurable:!0},sourceLayerIndex:{configurable:!0},bucketIndex:{configurable:!0}};return r.featureIndex.get=function(){return this._structArray.uint32[this._pos4+0]},r.featureIndex.set=function(t){this._structArray.uint32[this._pos4+0]=t},r.sourceLayerIndex.get=function(){return this._structArray.uint16[this._pos2+2]},r.sourceLayerIndex.set=function(t){this._structArray.uint16[this._pos2+2]=t},r.bucketIndex.get=function(){return this._structArray.uint16[this._pos2+3]},r.bucketIndex.set=function(t){this._structArray.uint16[this._pos2+3]=t},Object.defineProperties(e.prototype,r),e}(Li);Yi.prototype.size=8;var Qi=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.get=function(t){return new Yi(this,t)},e}(Hi);tr.register("FeatureIndexArray",Qi,{omit:["layers","patternFeatures"]});var Ki={FeatureIndexArray:Qi};function Gi(t,e){this.x=t,this.y=e}function Wi(t,e,r,n,i){this.properties={},this.extent=r,this.type=0,this._pbf=t,this._geometry=-1,this._keys=n,this._values=i,t.readFields(Ji,this,e)}function Ji(t,e,r){1==t?e.id=r.readVarint():2==t?Zi(r,e):3==t?e.type=r.readVarint():4==t&&(e._geometry=r.pos)}function Zi(t,e){for(var r=t.readVarint()+t.pos;t.pos<r;){var n=e._keys[t.readVarint()],i=e._values[t.readVarint()];e.properties[n]=i}}function $i(t){var e=t.length;if(e<=1)return[t];for(var r,n,i=[],a=0;a<e;a++){var o=ta(t[a]);0!==o&&(void 0===n&&(n=o<0),n===o<0?(r&&i.push(r),r=[t[a]]):r.push(t[a]))}return r&&i.push(r),i}function ta(t){for(var e,r,n=0,i=0,a=t.length,o=a-1;i<a;o=i++)e=t[i],n+=((r=t[o]).x-e.x)*(e.y+r.y);return n}function ea(t,e){this.version=1,this.name=null,this.extent=4096,this.length=0,this._pbf=t,this._keys=[],this._values=[],this._features=[],t.readFields(ra,this,e),this.length=this._features.length}function ra(t,e,r){15===t?e.version=r.readVarint():1===t?e.name=r.readString():5===t?e.extent=r.readVarint():2===t?e._features.push(r.pos):3===t?e._keys.push(r.readString()):4===t&&e._values.push(na(r))}function na(t){for(var e=null,r=t.readVarint()+t.pos;t.pos<r;){var n=t.readVarint()>>3;e=1===n?t.readString():2===n?t.readFloat():3===n?t.readDouble():4===n?t.readVarint64():5===n?t.readVarint():6===n?t.readSVarint():7===n?t.readBoolean():null}return e}function ia(t,e){this.layers=t.readFields(aa,{},e)}function aa(t,e,r){if(3===t){var n=new ea(r,r.readVarint()+r.pos);n.length&&(e[n.name]=n)}}Gi.prototype={clone:function(){return new Gi(this.x,this.y)},add:function(t){return this.clone()._add(t)},sub:function(t){return this.clone()._sub(t)},multByPoint:function(t){return this.clone()._multByPoint(t)},divByPoint:function(t){return this.clone()._divByPoint(t)},mult:function(t){return this.clone()._mult(t)},div:function(t){return this.clone()._div(t)},rotate:function(t){return this.clone()._rotate(t)},rotateAround:function(t,e){return this.clone()._rotateAround(t,e)},matMult:function(t){return this.clone()._matMult(t)},unit:function(){return this.clone()._unit()},perp:function(){return this.clone()._perp()},round:function(){return this.clone()._round()},mag:function(){return Math.sqrt(this.x*this.x+this.y*this.y)},equals:function(t){return this.x===t.x&&this.y===t.y},dist:function(t){return Math.sqrt(this.distSqr(t))},distSqr:function(t){var e=t.x-this.x,r=t.y-this.y;return e*e+r*r},angle:function(){return Math.atan2(this.y,this.x)},angleTo:function(t){return Math.atan2(this.y-t.y,this.x-t.x)},angleWith:function(t){return this.angleWithSep(t.x,t.y)},angleWithSep:function(t,e){return Math.atan2(this.x*e-this.y*t,this.x*t+this.y*e)},_matMult:function(t){var e=t[0]*this.x+t[1]*this.y,r=t[2]*this.x+t[3]*this.y;return this.x=e,this.y=r,this},_add:function(t){return this.x+=t.x,this.y+=t.y,this},_sub:function(t){return this.x-=t.x,this.y-=t.y,this},_mult:function(t){return this.x*=t,this.y*=t,this},_div:function(t){return this.x/=t,this.y/=t,this},_multByPoint:function(t){return this.x*=t.x,this.y*=t.y,this},_divByPoint:function(t){return this.x/=t.x,this.y/=t.y,this},_unit:function(){return this._div(this.mag()),this},_perp:function(){var t=this.y;return this.y=this.x,this.x=-t,this},_rotate:function(t){var e=Math.cos(t),r=Math.sin(t),n=e*this.x-r*this.y,i=r*this.x+e*this.y;return this.x=n,this.y=i,this},_rotateAround:function(t,e){var r=Math.cos(t),n=Math.sin(t),i=e.x+r*(this.x-e.x)-n*(this.y-e.y),a=e.y+n*(this.x-e.x)+r*(this.y-e.y);return this.x=i,this.y=a,this},_round:function(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this}},Gi.convert=function(t){return t instanceof Gi?t:Array.isArray(t)?new Gi(t[0],t[1]):t},Wi.types=["Unknown","Point","LineString","Polygon"],Wi.prototype.loadGeometry=function(){var t=this._pbf;t.pos=this._geometry;for(var e,r=t.readVarint()+t.pos,n=1,i=0,a=0,o=0,s=[];t.pos<r;){if(i<=0){var u=t.readVarint();n=7&u,i=u>>3}i--,1===n||2===n?(a+=t.readSVarint(),o+=t.readSVarint(),1===n&&(e&&s.push(e),e=[]),e.push(new Gi(a,o))):7===n?e&&e.push(e[0].clone()):console.log("VectorTileFeature loadGeometry unknown command "+n)}return e&&s.push(e),s},Wi.prototype.bbox=function(){var t=this._pbf;t.pos=this._geometry;for(var e=t.readVarint()+t.pos,r=1,n=0,i=0,a=0,o=1/0,s=-1/0,u=1/0,l=-1/0;t.pos<e;){if(n<=0){var p=t.readVarint();r=7&p,n=p>>3}if(n--,1===r||2===r)(i+=t.readSVarint())<o&&(o=i),i>s&&(s=i),(a+=t.readSVarint())<u&&(u=a),a>l&&(l=a);else if(7!==r)throw new Error("unknown command "+r)}return[o,u,s,l]},Wi.prototype.toGeoJSON=function(t,e,r){var n,i,a=this.extent*Math.pow(2,r),o=this.extent*t,s=this.extent*e,u=this.loadGeometry(),l=Wi.types[this.type];function p(t){for(var e=0;e<t.length;e++){var r=t[e],n=180-360*(r.y+s)/a;t[e]=[360*(r.x+o)/a-180,360/Math.PI*Math.atan(Math.exp(n*Math.PI/180))-90]}}switch(this.type){case 1:var c=[];for(n=0;n<u.length;n++)c[n]=u[n][0];p(u=c);break;case 2:for(n=0;n<u.length;n++)p(u[n]);break;case 3:for(u=$i(u),n=0;n<u.length;n++)for(i=0;i<u[n].length;i++)p(u[n][i])}1===u.length?u=u[0]:l="Multi"+l;var f={type:"Feature",geometry:{type:l,coordinates:u},properties:this.properties};return"id"in this&&(f.id=this.id),f},ea.prototype.feature=function(t){if(t<0||t>=this._features.length)throw new Error("feature index out of bounds");this._pbf.pos=this._features[t];var e=this._pbf.readVarint()+this._pbf.pos;return new Wi(this._pbf,e,this.extent,this._keys,this._values)};var oa=3;function sa(t,e,r){var n=this.cells=[];if(t instanceof ArrayBuffer){this.arrayBuffer=t;var i=new Int32Array(this.arrayBuffer);t=i[0],e=i[1],r=i[2],this.d=e+2*r;for(var a=0;a<this.d*this.d;a++){var o=i[oa+a],s=i[oa+a+1];n.push(o===s?null:i.subarray(o,s))}var u=i[oa+n.length],l=i[oa+n.length+1];this.keys=i.subarray(u,l),this.bboxes=i.subarray(l),this.insert=this._insertReadonly}else{this.d=e+2*r;for(var p=0;p<this.d*this.d;p++)n.push([]);this.keys=[],this.bboxes=[]}this.n=e,this.extent=t,this.padding=r,this.scale=e/t,this.uid=0;var c=r/e*t;this.min=-c,this.max=t+c}function ua(){}sa.prototype.insert=function(t,e,r,n,i){this._forEachCell(e,r,n,i,this._insertCell,this.uid++),this.keys.push(t),this.bboxes.push(e),this.bboxes.push(r),this.bboxes.push(n),this.bboxes.push(i)},sa.prototype._insertReadonly=function(){throw"Cannot insert into a GridIndex created from an ArrayBuffer."},sa.prototype._insertCell=function(t,e,r,n,i,a){this.cells[i].push(a)},sa.prototype.query=function(t,e,r,n,i){var a=this.min,o=this.max;if(t<=a&&e<=a&&o<=r&&o<=n&&!i)return Array.prototype.slice.call(this.keys);var s=[];return this._forEachCell(t,e,r,n,this._queryCell,s,{},i),s},sa.prototype._queryCell=function(t,e,r,n,i,a,o,s){var u=this.cells[i];if(null!==u)for(var l=this.keys,p=this.bboxes,c=0;c<u.length;c++){var f=u[c];if(void 0===o[f]){var h=4*f;(s?s(p[h+0],p[h+1],p[h+2],p[h+3]):t<=p[h+2]&&e<=p[h+3]&&r>=p[h+0]&&n>=p[h+1])?(o[f]=!0,a.push(l[f])):o[f]=!1}}},sa.prototype._forEachCell=function(t,e,r,n,i,a,o,s){for(var u=this._convertToCellCoord(t),l=this._convertToCellCoord(e),p=this._convertToCellCoord(r),c=this._convertToCellCoord(n),f=u;f<=p;f++)for(var h=l;h<=c;h++){var d=this.d*h+f;if((!s||s(this._convertFromCellCoord(f),this._convertFromCellCoord(h),this._convertFromCellCoord(f+1),this._convertFromCellCoord(h+1)))&&i.call(this,t,e,r,n,d,a,o,s))return}},sa.prototype._convertFromCellCoord=function(t){return(t-this.padding)/this.scale},sa.prototype._convertToCellCoord=function(t){return Math.max(0,Math.min(this.d-1,Math.floor(t*this.scale)+this.padding))},sa.prototype.toArrayBuffer=function(){if(this.arrayBuffer)return this.arrayBuffer;for(var t=this.cells,e=oa+this.cells.length+1+1,r=0,n=0;n<this.cells.length;n++)r+=this.cells[n].length;var i=new Int32Array(e+r+this.keys.length+this.bboxes.length);i[0]=this.extent,i[1]=this.n,i[2]=this.padding;for(var a=e,o=0;o<t.length;o++){var s=t[o];i[oa+o]=a,i.set(s,a),a+=s.length}return i[oa+t.length]=a,i.set(this.keys,a),a+=this.keys.length,i[oa+t.length+1]=a,i.set(this.bboxes,a),a+=this.bboxes.length,i.buffer},tr.register("GridIndex",sa,{omit:["layers","patternFeatures"]}),ua.easeCubicInOut=function(t){if(t<=0)return 0;if(t>=1)return 1;var e=t*t,r=e*t;return 4*(t<.5?r:3*(t-e)+r-.75)},ua.asyncAll=function(t,e,r){if(!t.length)return r(null,[]);var n=t.length,i=new Array(t.length),a=null;t.forEach((function(t,o){e(t,(function(t,e){t&&(a=t),i[o]=e,0==--n&&r(a,i)}))}))},ua.extend=function(t){for(var e=[],r=arguments.length-1;r-- >0;)e[r]=arguments[r+1];for(var n=0,i=e;n<i.length;n+=1){var a=i[n];for(var o in a)t[o]=a[o]}return t};var la=1;ua.uniqueId=function(){return la++},ua.uuid=function(){return function t(e){return e?(e^16*Math.random()>>e/4).toString(16):([1e7]+-[1e3]+-4e3+-8e3+-1e11).replace(/[018]/g,t)}()},ua.validateUuid=function(t){return!!t&&/^[0-9a-f]{8}-[0-9a-f]{4}-[4][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(t)},ua.bindAll=function(t,e){t.forEach((function(t){e[t]&&(e[t]=e[t].bind(e))}))},ua.endsWith=function(t,e){return-1!==t.indexOf(e,t.length-e.length)},ua.mapObject=function(t,e,r){var n={};for(var i in t)n[i]=e.call(r||this,t[i],i,t);return n},ua.filterObject=function(t,e,r){var n={};for(var i in t)e.call(r||this,t[i],i,t)&&(n[i]=t[i]);return n},ua.clone=function(t){return Array.isArray(t)?t.map(ua.clone):"object"==typeof t&&t?ua.mapObject(t,ua.clone):t},ua.deepEqual=function(t,e){if(Array.isArray(t)){if(!Array.isArray(e)||t.length!==e.length)return!1;for(var r=0;r<t.length;r++)if(!ua.deepEqual(t[r],e[r]))return!1;return!0}if("object"==typeof t&&null!==t&&null!==e){if("object"!=typeof e)return!1;if(Object.keys(t).length!==Object.keys(e).length)return!1;for(var n in t)if(!ua.deepEqual(t[n],e[n]))return!1;return!0}return t===e},ua.arraysIntersect=function(t,e){for(var r=0;r<t.length;r++)if(e.indexOf(t[r])>=0)return!0;return!1};var pa={};ua.warnOnce=function(t){pa[t]||("undefined"!=typeof console&&console.warn(t),pa[t]=!0)},ua.isCounterClockwise=function(t,e,r){return(r.y-t.y)*(e.x-t.x)>(e.y-t.y)*(r.x-t.x)},ua.isWorker=function(){return"undefined"!=typeof WorkerGlobalScope&&"undefined"!=typeof self&&self instanceof WorkerGlobalScope},ua.parseCacheControl=function(t){var e={};if(t.replace(/(?:^|(?:\s*\,\s*))([^\x00-\x20\(\)<>@\,;\:\\"\/\[\]\?\=\{\}\x7F]+)(?:\=(?:([^\x00-\x20\(\)<>@\,;\:\\"\/\[\]\?\=\{\}\x7F]+)|(?:\"((?:[^"\\]|\\.)*)\")))?/g,(function(t,r,n,i){var a=n||i;return e[r]=!a||a.toLowerCase(),""})),e["max-age"]){var r=parseInt(e["max-age"],10);isNaN(r)?delete e["max-age"]:e["max-age"]=r}return e};var ca=null;ua.isSafari=function(t){if(null==ca){var e=t.navigator?t.navigator.userAgent:null;ca=!!t.safari||!(!e||!(/\b(iPad|iPhone|iPod)\b/.test(e)||e.match("Safari")&&!e.match("Chrome")))}return ca},ua.resolveTokens=function(t,e){return e.replace(/{([^{}]+)}/g,(function(e,r){return r in t?String(t[r]):""}))};var fa=function(t,e){void 0===e&&(e=[]),this.parent=t,this.bindings={};for(var r=0,n=e;r<n.length;r+=1){var i=n[r],a=i[0],o=i[1];this.bindings[a]=o}};fa.prototype.concat=function(t){return new fa(this,t)},fa.prototype.get=function(t){if(this.bindings[t])return this.bindings[t];if(this.parent)return this.parent.get(t);throw new Error(t+" not found in scope.")},fa.prototype.has=function(t){return!!this.bindings[t]||!!this.parent&&this.parent.has(t)};var ha=function(t){function e(e,r){t.call(this,r),this.message=r,this.key=e}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e}(Error),da=["Unknown","Point","LineString","Polygon"],ya=function(){this.globals=null,this.feature=null,this.featureState=null,this.formattedSection=null,this._parseColorCache={},this.availableImages=null};function ma(){}ya.prototype.id=function(){return this.feature&&"id"in this.feature?this.feature.id:null},ya.prototype.geometryType=function(){return this.feature?"number"==typeof this.feature.type?da[this.feature.type]:this.feature.type:null},ya.prototype.properties=function(){return this.feature&&this.feature.properties||{}},ya.prototype.parseColor=function(t){var e=this._parseColorCache[t];return e||(e=this._parseColorCache[t]=Z.parse(t)),e},tr.register("EvaluationContext",ya),ma.isFeatureConstant=function(t){if(t instanceof va.CompoundExpression){if("get"===t.name&&1===t.args.length)return!1;if("feature-state"===t.name)return!1;if("has"===t.name&&1===t.args.length)return!1;if("properties"===t.name||"geometry-type"===t.name||"id"===t.name)return!1;if(/^filter-/.test(t.name))return!1}var e=!0;return t.eachChild((function(t){e&&!ma.isFeatureConstant(t)&&(e=!1)})),e},ma.isStateConstant=function(t){if(t instanceof va.CompoundExpression&&"feature-state"===t.name)return!1;var e=!0;return t.eachChild((function(t){e&&!ma.isStateConstant(t)&&(e=!1)})),e},ma.isGlobalPropertyConstant=function(t,e){if(t instanceof va.CompoundExpression&&e.indexOf(t.name)>=0)return!1;var r=!0;return t.eachChild((function(t){r&&!ma.isGlobalPropertyConstant(t,e)&&(r=!1)})),r};var va=function(t,e,r,n,i){void 0===e&&(e=[]),void 0===n&&(n=new fa),void 0===i&&(i=[]),this.registry=t,this.path=e,this.key=e.map((function(t){return"["+t+"]"})).join(""),this.scope=n,this.errors=i,this.expectedType=r};function ga(t,e){const r=e[t];return void 0===r?null:r}va.prototype.parse=function(t,e,r,n,i){return void 0===i&&(i={}),e?this.concat(e,r,n)._parse(t,i):this._parse(t,i)},va.prototype._parse=function(t,e){function r(t,e,r){return"assert"===r?new Ct(e,[t]):"coerce"===r?new Wt(e,[t]):t}if(null!==t&&"string"!=typeof t&&"boolean"!=typeof t&&"number"!=typeof t||(t=["literal",t]),Array.isArray(t)){if(0===t.length)return this.error('Expected an array with at least one element. If you wanted a literal array, use ["literal", []].');var n=t[0];if("string"!=typeof n)return this.error("Expression name must be a string, but found "+typeof n+' instead. If you wanted a literal array, use ["literal", [...]].',0),null;var i=this.registry[n];if(i){var a=i.parse(t,this);if(!a)return null;if(this.expectedType){var o=this.expectedType,s=a.type;if("string"!==o.kind&&"number"!==o.kind&&"boolean"!==o.kind&&"object"!==o.kind&&"array"!==o.kind||"value"!==s.kind)if("color"!==o.kind&&"formatted"!==o.kind&&"resolvedImage"!==o.kind||"value"!==s.kind&&"string"!==s.kind){if(this.checkSubtype(o,s))return null}else a=r(a,o,e.typeAnnotation||"coerce");else a=r(a,o,e.typeAnnotation||"assert")}return!(a instanceof qe)&&a.type.kind,a}return this.error('Unknown expression "'+n+'". If you wanted a literal array, use ["literal", [...]].',0)}return void 0===t?this.error("'undefined' value invalid. Use null instead."):"object"==typeof t?this.error('Bare objects invalid. Use ["literal", {...}] instead.'):this.error("Expected an array, but found "+typeof t+" instead.")},va.prototype.concat=function(t,e,r){var n="number"==typeof t?this.path.concat(t):this.path,i=r?this.scope.concat(r):this.scope;return new va(this.registry,n,e||null,i,this.errors)},va.prototype.error=function(t){for(var e=[],r=arguments.length-1;r-- >0;)e[r]=arguments[r+1];var n=""+this.key+e.map((function(t){return"["+t+"]"})).join("");this.errors.push(new ha(n,t))},va.prototype.checkSubtype=function(t,e){var r=ct(t,e);return r&&this.error(r),r};var xa=function(t,e,r,n){this.name=t,this.type=e,this._evaluate=r,this.args=n};function ba(t){return Array.isArray(t)?"("+t.map(toString).join(", ")+")":"("+toString(t.type)+"...)"}xa.prototype.evaluate=function(t,e){return this._evaluate(t,this.args,e)},xa.prototype.eachChild=function(t){this.args.forEach(t)},xa.prototype.possibleOutputs=function(){return[void 0]},xa.prototype.serialize=function(){return[this.name].concat(this.args.map((function(t){return t.serialize()})))},xa.parse=function(t,e){var r,n=t[0],i=xa.definitions[n];if(!i)return e.error('Unknown expression "'+n+'". If you wanted a literal array, use ["literal", [...]].',0);for(var a=Array.isArray(i)?i[0]:i.type,o=Array.isArray(i)?[[i[1],i[2]]]:i.overloads,s=o.filter((function(e){var r=e[0];return!Array.isArray(r)||r.length===t.length-1})),u=null,l=0,p=s;l<p.length;l+=1){var c=p[l],f=c[0],h=c[1];u=new va(e.registry,e.path,null,e.scope);for(var d=[],y=!1,m=1;m<t.length;m++){var v=t[m],g=Array.isArray(f)?f[m-1]:f.type,x=u.parse(v,1+d.length,g);if(!x){y=!0;break}d.push(x)}if(!y)if(Array.isArray(f)&&f.length!==d.length)u.error("Expected "+f.length+" arguments, but found "+d.length+" instead.");else{for(var b=0;b<d.length;b++){var _=Array.isArray(f)?f[b]:f.type,w=d[b];u.concat(b+1).checkSubtype(_,w.type)}if(0===u.errors.length)return new xa(n,a,h,d)}}if(1===s.length)(r=e.errors).push.apply(r,u.errors);else{for(var E=(s.length?s:o).map((function(t){return ba(t[0])})).join(" | "),T=[],A=1;A<t.length;A++){var S=e.parse(t[A],1+T.length);if(!S)return null;T.push(toString(S.type))}e.error("Expected arguments of type "+E+", but found ("+T.join(", ")+") instead.")}return null},xa.register=function(t,e){for(var r in xa.definitions=e,e)t[r]=xa},tr.register("CompoundExpression",xa);var _a={kind:"number"},wa={kind:"string"},Ea={kind:"boolean"},Ta={kind:"color"},Aa={kind:"object"},Sa={kind:"value"},Ia={kind:"error"},ka={kind:"collator"};function Ma(t,e){return{kind:"array",itemType:t,N:e}}function Fa(t){return{type:t}}function Oa(t,e){var r=e[0],n=e[1],i=e[2],a=e[3];r=r.evaluate(t),n=n.evaluate(t),i=i.evaluate(t);var o=a?a.evaluate(t):1,s=Values.validateRGBA(r,n,i,o);if(s)throw new RuntimeError(s);return new Color(r/255*o,n/255*o,i/255*o,o)}xa.register($e,{error:[Ia,[wa],function(t,e){var r=e[0];throw new RuntimeError(r.evaluate(t))}],typeof:[wa,[Sa],function(t,e){var r=e[0];return toString(Values.typeOf(r.evaluate(t)))}],"to-rgba":[Ma(_a,4),[Ta],function(t,e){return e[0].evaluate(t).toArray()}],rgb:[Ta,[_a,_a,_a],Oa],rgba:[Ta,[_a,_a,_a,_a],Oa],has:{type:Ea,overloads:[[[wa],function(t,e){var r=e[0];return has(r.evaluate(t),t.properties())}],[[wa,Aa],function(t,e){var r=e[0],n=e[1];return has(r.evaluate(t),n.evaluate(t))}]]},get:{type:Sa,overloads:[[[wa],function(t,e){return ga(e[0].evaluate(t),t.properties())}],[[wa,Aa],function(t,e){var r=e[0],n=e[1];return ga(r.evaluate(t),n.evaluate(t))}]]},"feature-state":[Sa,[wa],function(t,e){return ga(e[0].evaluate(t),t.featureState||{})}],properties:[Aa,[],function(t){return t.properties()}],"geometry-type":[wa,[],function(t){return t.geometryType()}],id:[Sa,[],function(t){return t.id()}],zoom:[_a,[],function(t){return t.globals.zoom}],"heatmap-density":[_a,[],function(t){return t.globals.heatmapDensity||0}],"line-progress":[_a,[],function(t){return t.globals.lineProgress||0}],accumulated:[Sa,[],function(t){return void 0===t.globals.accumulated?null:t.globals.accumulated}],"+":[_a,Fa(_a),function(t,e){for(var r=0,n=0,i=e;n<i.length;n+=1){r+=i[n].evaluate(t)}return r}],"*":[_a,Fa(_a),function(t,e){for(var r=1,n=0,i=e;n<i.length;n+=1){r*=i[n].evaluate(t)}return r}],"-":{type:_a,overloads:[[[_a,_a],function(t,e){var r=e[0],n=e[1];return r.evaluate(t)-n.evaluate(t)}],[[_a],function(t,e){return-e[0].evaluate(t)}]]},"/":[_a,[_a,_a],function(t,e){var r=e[0],n=e[1];return r.evaluate(t)/n.evaluate(t)}],"%":[_a,[_a,_a],function(t,e){var r=e[0],n=e[1];return r.evaluate(t)%n.evaluate(t)}],ln2:[_a,[],function(){return Math.LN2}],pi:[_a,[],function(){return Math.PI}],e:[_a,[],function(){return Math.E}],"^":[_a,[_a,_a],function(t,e){var r=e[0],n=e[1];return Math.pow(r.evaluate(t),n.evaluate(t))}],sqrt:[_a,[_a],function(t,e){var r=e[0];return Math.sqrt(r.evaluate(t))}],log10:[_a,[_a],function(t,e){var r=e[0];return Math.log(r.evaluate(t))/Math.LN10}],ln:[_a,[_a],function(t,e){var r=e[0];return Math.log(r.evaluate(t))}],log2:[_a,[_a],function(t,e){var r=e[0];return Math.log(r.evaluate(t))/Math.LN2}],sin:[_a,[_a],function(t,e){var r=e[0];return Math.sin(r.evaluate(t))}],cos:[_a,[_a],function(t,e){var r=e[0];return Math.cos(r.evaluate(t))}],tan:[_a,[_a],function(t,e){var r=e[0];return Math.tan(r.evaluate(t))}],asin:[_a,[_a],function(t,e){var r=e[0];return Math.asin(r.evaluate(t))}],acos:[_a,[_a],function(t,e){var r=e[0];return Math.acos(r.evaluate(t))}],atan:[_a,[_a],function(t,e){var r=e[0];return Math.atan(r.evaluate(t))}],min:[_a,Fa(_a),function(t,e){return Math.min.apply(Math,e.map((function(e){return e.evaluate(t)})))}],max:[_a,Fa(_a),function(t,e){return Math.max.apply(Math,e.map((function(e){return e.evaluate(t)})))}],abs:[_a,[_a],function(t,e){var r=e[0];return Math.abs(r.evaluate(t))}],round:[_a,[_a],function(t,e){var r=e[0].evaluate(t);return r<0?-Math.round(-r):Math.round(r)}],floor:[_a,[_a],function(t,e){var r=e[0];return Math.floor(r.evaluate(t))}],ceil:[_a,[_a],function(t,e){var r=e[0];return Math.ceil(r.evaluate(t))}],"filter-==":[Ea,[wa,Sa],function(t,e,r){var n=e[0],i=e[1];if(r){var a,o,s=n.value,u=i.value;if(/(\S*)\s*([+-])\s*(\S*)/.test(s)){var l=s.match(/(\S*)\s*([+-])\s*(\S*)/),p=t.properties()[l[1]],c=l[2],f=t.properties()[l[3]];switch(c){case"+":a=p+f;break;case"-":a=p-f}return a===(o=i.value)}if(/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/.test(s)){var h=s.match(/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/),d=h[1],y=(p=t.properties()[h[2]],h[3]);if(a="left"==d?p.substring(0,y):p.substring(p.length-y),/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/.test(u)){var m=u.match(/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/),v=m[1],g=(f=t.properties()[m[2]],m[3]);o="left"==v?f.substring(0,g):f.substring(f.length-g)}else o=i.value;return a===o}return t.properties()[n.value]===t.properties()[i.value]}return t.properties()[n.value]===i.value}],"filter-id-==":[Ea,[Sa],function(t,e){var r=e[0];return t.id()===r.value}],"filter-like":[Ea,[wa,wa],function(t,e){var r=e[0].value,n=e[1].value,i=t.properties();return r in i&&(/^%.*[^%]$/.test(n)?(n=n.replace("%",""),i[r].endsWith(n)):/^(?!%).+%$/.test(n)?(n=n.replace("%",""),i[r].startsWith(n)):(n=n.replace(/%/g,""),i[r].indexOf(n)>-1))}],"filter-type-==":[Ea,[wa],function(t,e){var r=e[0];return t.geometryType()===r.value}],"filter-<":[Ea,[wa,Sa],function(t,e,r){var n,i,a=e[0],o=e[1],s=a.value,u=o.value;if(/(\S*)\s*([+-])\s*(\S*)/.test(s)){var l=s.match(/(\S*)\s*([+-])\s*(\S*)/),p=t.properties()[l[1]],c=l[2],f=t.properties()[l[3]];switch(c){case"+":n=p+f;break;case"-":n=p-f}i=o.value}else if(/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/.test(s)){var h=s.match(/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/),d=h[1],y=(p=t.properties()[h[2]],h[3]);if(n="left"==d?p.substring(0,y):p.substring(p.length-y),/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/.test(u)){var m=u.match(/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/),v=m[1],g=(f=t.properties()[m[2]],m[3]);i="left"==v?f.substring(0,g):f.substring(f.length-g)}else i=o.value}else n=t.properties()[a.value],i=o.value,r&&(i=t.properties()[i]);return"number"!=typeof i||isNaN(Number(n))||(n=Number(n)),typeof n==typeof i&&n<i}],"filter-id-<":[Ea,[Sa],function(t,e){var r=e[0],n=t.id(),i=r.value;return typeof n==typeof i&&n<i}],"filter->":[Ea,[wa,Sa],function(t,e,r){var n,i,a=e[0],o=e[1],s=a.value,u=o.value;if(/(\S*)\s*([+-])\s*(\S*)/.test(s)){var l=s.match(/(\S*)\s*([+-])\s*(\S*)/),p=t.properties()[l[1]],c=l[2],f=t.properties()[l[3]];switch(c){case"+":n=p+f;break;case"-":n=p-f}i=o.value}else if(/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/.test(s)){var h=s.match(/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/),d=h[1],y=(p=t.properties()[h[2]],h[3]);if(n="left"==d?p.substring(0,y):p.substring(p.length-y),/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/.test(u)){var m=u.match(/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/),v=m[1],g=(f=t.properties()[m[2]],m[3]);i="left"==v?f.substring(0,g):f.substring(f.length-g)}else i=o.value}else n=t.properties()[a.value],i=o.value,r&&(i=t.properties()[i]);return"number"!=typeof i||isNaN(Number(n))||(n=Number(n)),typeof n==typeof i&&n>i}],"filter-id->":[Ea,[Sa],function(t,e){var r=e[0],n=t.id(),i=r.value;return typeof n==typeof i&&n>i}],"filter-<=":[Ea,[wa,Sa],function(t,e,r){var n,i,a=e[0],o=e[1],s=a.value,u=o.value;if(/(\S*)\s*([+-])\s*(\S*)/.test(s)){var l=s.match(/(\S*)\s*([+-])\s*(\S*)/),p=t.properties()[l[1]],c=l[2],f=t.properties()[l[3]];switch(c){case"+":n=p+f;break;case"-":n=p-f}i=o.value}else if(/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/.test(s)){var h=s.match(/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/),d=h[1],y=(p=t.properties()[h[2]],h[3]);if(n="left"==d?p.substring(0,y):p.substring(p.length-y),/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/.test(u)){var m=u.match(/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/),v=m[1],g=(f=t.properties()[m[2]],m[3]);i="left"==v?f.substring(0,g):f.substring(f.length-g)}else i=o.value}else n=t.properties()[a.value],i=o.value,r&&(i=t.properties()[i]);return"number"!=typeof i||isNaN(Number(n))||(n=Number(n)),typeof n==typeof i&&n<=i}],"filter-id-<=":[Ea,[Sa],function(t,e){var r=e[0],n=t.id(),i=r.value;return typeof n==typeof i&&n<=i}],"filter->=":[Ea,[wa,Sa],function(t,e,r){var n,i,a=e[0],o=e[1],s=a.value,u=o.value;if(/(\S*)\s*([+-])\s*(\S*)/.test(s)){var l=s.match(/(\S*)\s*([+-])\s*(\S*)/),p=t.properties()[l[1]],c=l[2],f=t.properties()[l[3]];switch(c){case"+":n=p+f;break;case"-":n=p-f}i=o.value}else if(/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/.test(s)){var h=s.match(/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/),d=h[1],y=(p=t.properties()[h[2]],h[3]);if(n="left"==d?p.substring(0,y):p.substring(p.length-y),/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/.test(u)){var m=u.match(/^(left|right)\s*\((.+)\s*,\s*(\d+)\)/),v=m[1],g=(f=t.properties()[m[2]],m[3]);i="left"==v?f.substring(0,g):f.substring(f.length-g)}else i=o.value}else n=t.properties()[a.value],i=o.value,r&&(i=t.properties()[i]);return"number"!=typeof i||isNaN(Number(n))||(n=Number(n)),typeof n==typeof i&&n>=i}],"filter-id->=":[Ea,[Sa],function(t,e){var r=e[0],n=t.id(),i=r.value;return typeof n==typeof i&&n>=i}],"filter-has":[Ea,[Sa],function(t,e){return e[0].value in t.properties()}],"filter-has-id":[Ea,[],function(t){return null!==t.id()}],"filter-type-in":[Ea,[Ma(wa)],function(t,e){return e[0].value.indexOf(t.geometryType())>=0}],"filter-id-in":[Ea,[Ma(Sa)],function(t,e){return e[0].value.indexOf(t.id())>=0}],"filter-in-small":[Ea,[wa,Ma(Sa)],function(t,e){var r=e[0];return e[1].value.indexOf(t.properties()[r.value])>=0}],"filter-in-large":[Ea,[wa,Ma(Sa)],function(t,e){var r=e[0],n=e[1];return binarySearch(t.properties()[r.value],n.value,0,n.value.length-1)}],all:{type:Ea,overloads:[[[Ea,Ea],function(t,e){var r=e[0],n=e[1];return r.evaluate(t)&&n.evaluate(t)}],[Fa(Ea),function(t,e){for(var r=0,n=e;r<n.length;r+=1){if(!n[r].evaluate(t))return!1}return!0}]]},crossFields:{type:Ea,overloads:[[[Ea,Ea],function(t,e){var r=e[0],n=e[1];return r.evaluate(t,!0)&&n.evaluate(t,!0)}],[Fa(Ea),function(t,e){for(var r=0,n=e;r<n.length;r+=1){if(!n[r].evaluate(t,!0))return!1}return!0}]]},any:{type:Ea,overloads:[[[Ea,Ea],function(t,e){var r=e[0],n=e[1];return r.evaluate(t)||n.evaluate(t)}],[Fa(Ea),function(t,e){for(var r=0,n=e;r<n.length;r+=1){if(n[r].evaluate(t))return!0}return!1}]]},"!":[Ea,[Ea],function(t,e,r){return!e[0].evaluate(t,r)}],"is-supported-script":[Ea,[wa],function(t,e){var r=e[0],n=t.globals&&t.globals.isSupportedScript;return!n||n(r.evaluate(t))}],upcase:[wa,[wa],function(t,e){return e[0].evaluate(t).toUpperCase()}],downcase:[wa,[wa],function(t,e){return e[0].evaluate(t).toLowerCase()}],concat:[wa,Fa(Sa),function(t,e){return e.map((function(e){return Values.toString$1(e.evaluate(t))})).join("")}],"resolved-locale":[wa,[ka],function(t,e){return e[0].evaluate(t).resolvedLocale()}]}),va.CompoundExpression=xa;var Pa=function(t,e){this.expression=t,this._warningHistory={},this._evaluator=new ya,this._defaultValue=e?Ra(e):null,this._enumValues=e&&"enum"===e.type?e.values:null};function za(t){return"object"==typeof t&&null!==t&&!Array.isArray(t)}function Ra(t){return"color"===t.type&&za(t.default)?new Z(0,0,0,0):"color"===t.type?Z.parse(t.default)||null:void 0===t.default?null:t.default}Pa.prototype.evaluateWithoutErrorHandling=function(t,e,r,n,i){return this._evaluator.globals=t,this._evaluator.feature=e,this._evaluator.featureState=r,this._evaluator.availableImages=n||null,this._evaluator.formattedSection=i,this.expression.evaluate(this._evaluator)},Pa.prototype.evaluate=function(t,e,r,n,i){this._evaluator.globals=t,this._evaluator.feature=e||null,this._evaluator.featureState=r||null,this._evaluator.availableImages=n||null,this._evaluator.formattedSection=i||null;try{var a=this.expression.evaluate(this._evaluator);if(null==a||"number"==typeof a&&a!=a)return this._defaultValue;if(this._enumValues&&!(a in this._enumValues))throw new RuntimeError("Expected value to be one of "+Object.keys(this._enumValues).map((function(t){return JSON.stringify(t)})).join(", ")+", but found "+JSON.stringify(a)+" instead.");return a}catch(t){return this._warningHistory[t.message]||(this._warningHistory[t.message]=!0,"undefined"!=typeof console&&console.warn(t.message)),this._defaultValue}},tr.register("StyleExpression",Pa);var Ca=.95047,Ba=1,Da=1.08883,Va=4/29,Ua=6/29,Na=3*Ua*Ua,La=Ua*Ua*Ua,Xa=Math.PI/180,qa=180/Math.PI;function ja(t){return t>La?Math.pow(t,1/3):t/Na+Va}function Ha(t){return t>Ua?t*t*t:Na*(t-Va)}function Ya(t){return 255*(t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055)}function Qa(t){return(t/=255)<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4)}function Ka(t){var e=Qa(t.r),r=Qa(t.g),n=Qa(t.b),i=ja((.4124564*e+.3575761*r+.1804375*n)/Ca),a=ja((.2126729*e+.7151522*r+.072175*n)/Ba);return{l:116*a-16,a:500*(i-a),b:200*(a-ja((.0193339*e+.119192*r+.9503041*n)/Da)),alpha:t.a}}function Ga(t){var e=(t.l+16)/116,r=isNaN(t.a)?e:e+t.a/500,n=isNaN(t.b)?e:e-t.b/200;return e=Ba*Ha(e),r=Ca*Ha(r),n=Da*Ha(n),new Color(Ya(3.2404542*r-1.5371385*e-.4985314*n),Ya(-.969266*r+1.8760108*e+.041556*n),Ya(.0556434*r-.2040259*e+1.0572252*n),t.alpha)}function Wa(t,e,r){return{l:number(t.l,e.l,r),a:number(t.a,e.a,r),b:number(t.b,e.b,r),alpha:number(t.alpha,e.alpha,r)}}function Ja(t){var e=Ka(t),r=e.l,n=e.a,i=e.b,a=Math.atan2(i,n)*qa;return{h:a<0?a+360:a,c:Math.sqrt(n*n+i*i),l:r,alpha:t.a}}function Za(t){var e=t.h*Xa,r=t.c;return Ga({l:t.l,a:Math.cos(e)*r,b:Math.sin(e)*r,alpha:t.alpha})}function $a(t,e,r){var n=e-t;return t+r*(n>180||n<-180?n-360*Math.round(n/360):n)}function to(t,e,r){return{h:$a(t.h,e.h,r),c:number(t.c,e.c,r),l:number(t.l,e.l,r),alpha:number(t.alpha,e.alpha,r)}}var eo={forward:Ka,reverse:Ga,interpolate:Wa},ro={forward:Ja,reverse:Za,interpolate:to},no=Object.freeze({__proto__:null,lab:eo,hcl:ro});function io(){}function ao(t){return{result:"success",value:t}}function oo(t){return{result:"error",value:t}}io.isExpression=function(t){return Array.isArray(t)&&t.length>0&&"string"==typeof t[0]&&t[0]in $e};var so={kind:"number"},uo={kind:"string"},lo={kind:"boolean"},po={kind:"color"},co={kind:"value"},fo={kind:"formatted"},ho={kind:"resolvedImage"};function yo(t,e){return{kind:"array",itemType:t,N:e}}function mo(t){var e=null;if(t instanceof Ne)e=mo(t.result);else if(t instanceof qt){for(var r of t.args)if(e=mo(r))break}else(t instanceof Je||t instanceof Fe)&&t.input instanceof va.CompoundExpression&&"zoom"===t.input.name&&(e=t);return e instanceof ha||t.eachChild((t=>{var r=mo(t);r instanceof ha?e=r:!e&&r?e=new ha("",'"zoom" expression may only be used as input to a top-level "step" or "interpolate" expression.'):e&&r&&e!==r&&(e=new ha("",'Only one zoom-based "step" or "interpolate" subexpression may be used in an expression.'))})),e}function vo(t){var e={color:po,string:uo,number:so,enum:uo,boolean:lo,formatted:fo,resolvedImage:ho};return"array"===t.type?yo(e[t.value]||co,t.length):e[t.type]}function go(t){return"object"==typeof t&&null!==t&&!Array.isArray(t)}function xo(t){return t}function bo(t){for(var e=[],r=arguments.length-1;r-- >0;)e[r]=arguments[r+1];for(var n=0,i=e;n<i.length;n+=1){var a=i[n];for(var o in a)t[o]=a[o]}return t}function _o(t,e,r){var n=void 0!==t.base?t.base:1;if("number"!==Oo(r))return ko(t.default,e.default);var i=t.stops.length;if(1===i)return t.stops[0][1];if(r<=t.stops[0][0])return t.stops[0][1];if(r>=t.stops[i-1][0])return t.stops[i-1][1];var a=Ge(t.stops.map((t=>t[0])),r),o=Mo(r,n,t.stops[a][0],t.stops[a+1][0]),s=t.stops[a][1],u=t.stops[a+1][1],l=interpolate[e.type]||xo;if(t.colorSpace&&"rgb"!==t.colorSpace){var p=no[t.colorSpace];l=(t,e)=>p.reverse(p.interpolate(p.forward(t),p.forward(e),o))}return"function"==typeof s.evaluate?{evaluate(...t){var e=s.evaluate.apply(void 0,t),r=u.evaluate.apply(void 0,t);if(void 0!==e&&void 0!==r)return l(e,r,o)}}:l(s,u,o)}function wo(t,e,r){return"color"===e.type?r=Z.parse(r):"formatted"===e.type?r=Formatted.fromString(r.toString()):"resolvedImage"===e.type?r=ResolvedImage.fromString(r.toString()):Oo(r)===e.type||"enum"===e.type&&e.values[r]||(r=void 0),ko(r,t.default,e.default)}function Eo(t,e){var r=new va($e,[],e?vo(e):void 0),n=r.parse(t,void 0,void 0,void 0,e&&"string"===e.type?{typeAnnotation:"coerce"}:void 0);return n?ao(new Pa(n,e)):(assert(r.errors.length>0),oo(r.errors))}function To(t,e){this.kind=t,this._styleExpression=e,this.isStateDependent="constant"!==t&&!ma.isStateConstant(e.expression)}function Ao(t,e,r,n){this.kind=t,this.zoomStops=r,this._styleExpression=e,this.isStateDependent="camera"!==t&&!ma.isStateConstant(e.expression),this.interpolationType=n}function So(t,e){if("error"===(t=Eo(t,e)).result)return t;var r=t.value.expression,n=ma.isFeatureConstant(r);if(!n&&!_r.supportsPropertyExpression(e))return oo([new ha("","data expressions not supported")]);var i=ma.isGlobalPropertyConstant(r,["zoom"]);if(!i&&!_r.supportsZoomExpression(e))return oo([new ha("","zoom expressions not supported")]);var a=mo(r);if(!a&&!i)return oo([new ha("",'"zoom" expression may only be used as input to a top-level "step" or "interpolate" expression.')]);if(a instanceof ha)return oo([a]);if(a instanceof Fe&&!_r.supportsInterpolation(e))return oo([new ha("",'"interpolate" expressions cannot be used with this property')]);if(!a)return ao(new To(n?"constant":"source",t.value));var o=a instanceof Fe?a.interpolation:void 0;return ao(new Ao(n?"camera":"composite",t.value,a.labels,o))}function Io(t,e){var r,n,i,a="color"===e.type,o=t.stops&&"object"==typeof t.stops[0][0],s=o||void 0!==t.property,u=o||!s,l=t.type||(_r.supportsInterpolation(e)?"exponential":"interval");if(a&&((t=bo({},t)).stops&&(t.stops=t.stops.map((function(t){return[t[0],Z.parse(t[1])]}))),t.default?t.default=Z.parse(t.default):t.default=Z.parse(e.default)),t.colorSpace&&"rgb"!==t.colorSpace&&!no[t.colorSpace])throw new Error("Unknown color space: "+t.colorSpace);if("exponential"===l)r=_o;else if("interval"===l)r=evaluateIntervalFunction;else if("categorical"===l){r=evaluateCategoricalFunction,n=Object.create(null);for(var p=0,c=t.stops;p<c.length;p+=1){var f=c[p];n[f[0]]=f[1]}i=typeof t.stops[0][0]}else{if("identity"!==l)throw new Error('Unknown function type "'+l+'"');r=wo}if(o){for(var h={},d=[],y=0;y<t.stops.length;y++){var m=t.stops[y],v=m[0].zoom;void 0===h[v]&&(h[v]={zoom:v,type:t.type,property:t.property,default:t.default,stops:[]},d.push(v)),h[v].stops.push([m[0].value,m[1]])}for(var g=[],x=0,b=d;x<b.length;x+=1){var _=b[x];g.push([h[_].zoom,Io(h[_],e)])}var w={name:"linear"};return{kind:"composite",interpolationType:w,interpolationFactor:Fe.interpolationFactor.bind(void 0,w),zoomStops:g.map((function(t){return t[0]})),evaluate:function(r,n){var i=r.zoom;return _o({stops:g,base:t.base},e,i).evaluate(i,n)}}}if(u){var E="exponential"===l?{name:"exponential",base:void 0!==t.base?t.base:1}:null;return{kind:"camera",interpolationType:E,interpolationFactor:Fe.interpolationFactor.bind(void 0,E),zoomStops:t.stops.map((function(t){return t[0]})),evaluate:function(a){var o=a.zoom;return r(t,e,o,n,i)}}}return{kind:"source",evaluate:function(a,o){var s=o&&o.properties?o.properties[t.property]:void 0;return void 0===s?ko(t.default,e.default):r(t,e,s,n,i)}}}function ko(t,e,r){return void 0!==t?t:void 0!==e?e:void 0!==r?r:void 0}function Mo(t,e,r,n){var i=n-r,a=t-r;return 0===i?0:1===e?a/i:(Math.pow(e,a)-1)/(Math.pow(e,i)-1)}io.createExpression=function(t,e){var r=new va($e,[],e?vo(e):void 0),n=r.parse(t,void 0,void 0,void 0,e&&"string"===e.type?{typeAnnotation:"coerce"}:void 0);return n?ao(new Pa(n,e)):oo(r.errors)},To.prototype.evaluateWithoutErrorHandling=function(t,e,r,n,i,a){return this._styleExpression.evaluateWithoutErrorHandling(t,e,r,n,i,a)},To.prototype.evaluate=function(t,e,r,n,i,a){return this._styleExpression.evaluate(t,e,r,n,i,a)},tr.register("ZoomConstantExpression",To),Ao.prototype.evaluateWithoutErrorHandling=function(t,e,r,n,i,a){return this._styleExpression.evaluateWithoutErrorHandling(t,e,r,n,i,a)},Ao.prototype.evaluate=function(t,e,r,n,i,a){return this._styleExpression.evaluate(t,e,r,n,i,a)},Ao.prototype.interpolationFactor=function(t,e,r){return this.interpolationType?Fe.interpolationFactor(this.interpolationType,t,e,r):0},tr.register("ZoomDependentExpression",Ao);var Fo=function(t,e){this._parameters=t,this._specification=e,bo(this,Io(this._parameters,this._specification))};function Oo(t){return t instanceof Number?"number":t instanceof String?"string":t instanceof Boolean?"boolean":Array.isArray(t)?"array":null===t?"null":typeof t}function Po(){}Fo.deserialize=function(t){return new Fo(t._parameters,t._specification)},Fo.serialize=function(t){return{_parameters:t._parameters,_specification:t._specification}},io.normalizePropertyExpression=function(t,e){if(go(t))return new Fo(t,e);if(io.isExpression(t)){var r=So(t,e);if("error"===r.result)throw new Error(r.value.map((function(t){return t.key+": "+t.message})).join(", "));return r.value}var n=t;return"string"==typeof t&&"color"===e.type&&(n=Z.parse(t)),{kind:"constant",evaluate:function(){return n}}},Po.isExpressionFilter=function(t){if(!0===t||!1===t)return!0;if(!Array.isArray(t)||0===t.length)return!1;switch(t[0]){case"has":return t.length>=2&&"$id"!==t[1]&&"$type"!==t[1];case"in":return t.length>=3&&Array.isArray(t[2]);case"!in":case"!has":case"none":case"crossFields":return!1;case"==":case"!=":case">":case">=":case"<":case"<=":case"like":case"!like":return 3!==t.length||Array.isArray(t[1])||Array.isArray(t[2]);case"any":case"all":for(var e=0,r=t.slice(1);e<r.length;e+=1){var n=r[e];if(!Po.isExpressionFilter(n)&&"boolean"!=typeof n)return!1}return!0;default:return!0}};var zo={type:"boolean",default:!1,transition:!1,"property-type":"data-driven",expression:{interpolated:!1,parameters:["zoom","feature"]}};function Ro(t,e){return t<e?-1:t>e?1:0}function Co(t){if(!t)return!0;var e=t[0];return t.length<=1?"any"!==e:"=="===e?Bo(t[1],t[2],"=="):"!="===e?No(Bo(t[1],t[2],"==")):"<"===e||">"===e||"<="===e||">="===e?Bo(t[1],t[2],e):"any"===e?Do(t.slice(1)):"all"===e?["all"].concat(t.slice(1).map(Co)):"crossFields"===e?["crossFields"].concat(t.slice(1).map(Co)):"none"===e?["all"].concat(t.slice(1).map(Co).map(No)):"in"===e?Vo(t[1],t.slice(2)):"!in"===e?No(Vo(t[1],t.slice(2))):"has"===e?Uo(t[1]):"!has"===e?No(Uo(t[1])):"like"===e?Bo(t[1],t[2],"like"):"!like"!==e||No(Bo(t[1],t[2],"like"))}function Bo(t,e,r){switch(t){case"$type":return["filter-type-"+r,e];case"$id":return["filter-id-"+r,e];default:return["filter-"+r,t,e]}}function Do(t){return["any"].concat(t.map(Co))}function Vo(t,e){if(0===e.length)return!1;switch(t){case"$type":return["filter-type-in",["literal",e]];case"$id":return["filter-id-in",["literal",e]];default:return e.length>200&&!e.some((function(t){return typeof t!=typeof e[0]}))?["filter-in-large",t,["literal",e.sort(Ro)]]:["filter-in-small",t,["literal",e]]}}function Uo(t){switch(t){case"$type":return!0;case"$id":return["filter-has-id"];default:return["filter-has",t]}}function No(t){return["!",t]}Po.createFilter=function(t){if(null==t)return function(){return!0};Po.isExpressionFilter(t)||(t=Co(t));var e=io.createExpression(t,zo);if("error"===e.result)throw new Error(e.value.map((function(t){return t.key+": "+t.message})).join(", "));return function(t,r){return e.value.evaluate(t,r)}};var Lo=Ki.FeatureIndexArray,Xo=function(t,e,r){this.x=t.x,this.y=t.y,this.z=t.z,this.grid=e||new sa(or,16,0),this.featureIndexArray=r||new Lo};function qo(t){for(var e=1/0,r=1/0,n=-1/0,i=-1/0,a=0,o=t;a<o.length;a+=1){var s=o[a];e=Math.min(e,s.x),r=Math.min(r,s.y),n=Math.max(n,s.x),i=Math.max(i,s.y)}return{minX:e,minY:r,maxX:n,maxY:i}}function jo(t,e){return e-t}function Ho(t,r,n){if(e.defined(n)&&n.realtime&&e.defined(n.zoom)){var i=n.zoom-t,a=or/(r*Math.pow(2,i));return a*=r/512}return or/r}Xo.prototype.insert=function(t,e,r,n,i,a,o){var s=this.featureIndexArray.length;this.featureIndexArray.emplaceBack(r,n,i);var u=this.grid;o=o||0;for(var l=0;l<e.length;l++){for(var p=e[l],c=[1/0,1/0,-1/0,-1/0],f=0;f<p.length;f++){var h=p[f];c[0]=Math.min(c[0],h.x),c[1]=Math.min(c[1],h.y),c[2]=Math.max(c[2],h.x),c[3]=Math.max(c[3],h.y)}c[0]<or&&c[1]<or&&c[2]>=0&&c[3]>=0&&u.insert(s,c[0]-o,c[1]-o,c[2]+o,c[3]+o)}},Xo.prototype.loadVTLayers=function(){return this.vtLayers||(this.vtLayers=new ia(new o(this.rawTileData)).layers,this.sourceLayerCoder=new F(this.vtLayers?Object.keys(this.vtLayers).sort():["_geojsonTileLayer"])),this.vtLayers},Xo.prototype.query=function(t,r,n){var i=this;this.loadVTLayers();var a=t.params||{},o=Ho(this.z,t.tileSize,a),s=Po.createFilter(a.filter),u=t.queryGeometry,l=5,p=qo(u),c=[];e.defined(a.selectTolerance)&&(l+=o*a.selectTolerance),(c=this.grid.query(p.minX-l,p.minY-l,p.maxX+l,p.maxY+l)).sort(jo);for(var f,h={},d=function(e){var n=c[e];if(n!==f){f=n;var l=i.featureIndexArray.get(n),p=null;i.loadMatchingFeature(h,l.bucketIndex,l.sourceLayerIndex,l.featureIndex,s,a.layers,r,(function(e,r){p||(p=pr(e));return r.queryIntersectsFeature(u,e,{},p,i.z,t.transform,o,t.pixelPosMatrix,t.adjustScale)}))}},y=0;y<c.length;y++)d(y);return h},Xo.prototype.loadMatchingFeature=function(t,r,n,i,a,o,s,u){if(e.defined(r)&&e.defined(n)&&e.defined(i)){var l=this.bucketLayerIDs[r];if(!o||ua.arraysIntersect(o,l)){var p=this.sourceLayerCoder.decode(n),c=this.vtLayers[p].feature(i);if(a(new ar(this.z),c))for(var f=0;f<l.length;f++){var h=l[f];if(!(o&&o.indexOf(h)<0)){var d=s[h];if(d){var y=!u||u(c,d);if(y){c.layer=d.serialize();var m=t[h];void 0===m&&(m=t[h]=[]),m.push({featureIndex:i,feature:c,intersectionZ:y})}}}}}}},Xo.prototype.lookupSymbolFeatures=function(t,e,r,n,i,a){var o={};this.loadVTLayers();for(var s=createFilter(n),u=0,l=t;u<l.length;u+=1){var p=l[u];this.loadMatchingFeature(o,e,r,p,s,i,a)}return o},Xo.prototype.hasLayer=function(t){for(var e=0,r=this.bucketLayerIDs;e<r.length;e+=1)for(var n=0,i=r[e];n<i.length;n+=1){if(t===i[n])return!0}return!1},tr.register("FeatureIndex",Xo,{omit:["rawTileData","sourceLayerCoder","vtLayers"]});const Yo=["type","source","source-layer","minzoom","maxzoom","filter","layout"];function Qo(t){var e=typeof t;if("number"===e||"boolean"===e||"string"===e||null==t)return JSON.stringify(t);if(Array.isArray(t)){for(var r="[",n=0,i=t;n<i.length;n+=1){r+=Qo(i[n])+","}return r+"]"}for(var a=Object.keys(t).sort(),o="{",s=0;s<a.length;s++)o+=JSON.stringify(a[s])+":"+Qo(t[a[s]])+",";return o+"}"}function Ko(t){for(var e="",r=0,n=Yo;r<n.length;r+=1){e+="/"+Qo(t[n[r]])}return e}function Go(t,e){for(var r={},n=0;n<t.length;n++){var i=e&&e[t[n].id]||Ko(t[n]);e&&(e[t[n].id]=i);var a=r[i];a||(a=r[i]=[]),a.push(t[n])}var o=[];for(var s in r)o.push(r[s]);return o}var Wo=P([{name:"a_pos",components:2,type:"Int16"}],4),Jo=Wo.members;function Zo(t,e,r,n,i){t.emplaceBack(2*e+(n+1)/2,2*r+(i+1)/2)}var $o=function(t){this.zoom=t.zoom,this.overscaling=t.overscaling,this.layers=t.layers,this._sourceLayerIds={};var e=this;this.layerIds=this.layers.map((function(t,r){return e._sourceLayerIds[t.sourceLayer]=r,t.sourceLayer})),this.index=t.index,this.hasPattern=!1,this.layoutVertexArray=new un,this.indexArray=new mr,this.segments=new cr,this.programConfigurations=new Lr(Jo,t.layers,t.zoom),this.stateDependentLayerIds=this.layers.filter((function(t){return t.isStateDependent()})).map((function(t){return t.id}))};$o.prototype.populate=function(t,e){var r=this.layers[0],n=[],i=null;"circle"===r.type&&(i=r.layout.get("circle-sort-key"));for(var a=0,o=t;a<o.length;a+=1){var s=o[a],u=s.feature,l=s.index,p=s.sourceLayerIndex,c=s.sourceLayerId,f=this._sourceLayerIds[c],h=this.layers[f];if(h){var d=or/512,y=h.paint.get("circle-radius").value.value*d;if(this.layers[0]._featureFilter(new ar(0),u)){var m=pr(u),v=i?i.evaluate(u,{}):void 0,g={id:u.id,properties:u.properties,type:u.type,sourceLayerIndex:p,index:l,geometry:m,patterns:{},sortKey:v,circleRadius:y};n.push(g)}}}i&&n.sort((function(t,e){return t.sortKey-e.sortKey}));for(var x=0,b=n;x<b.length;x+=1){var _=b[x],w=_,E=w.geometry,T=w.index,A=w.sourceLayerIndex,S=t[T].feature;this.addFeature(_,E,T),e.featureIndex.insert(S,E,T,A,this.index,void 0,w.circleRadius)}},$o.prototype.update=function(t,e,r){this.stateDependentLayers.length&&this.programConfigurations.updatePaintArrays(t,e,this.stateDependentLayers,r)},$o.prototype.isEmpty=function(){return 0===this.layoutVertexArray.length},$o.prototype.uploadPending=function(){return!this.uploaded||this.programConfigurations.needsUpload},$o.prototype.upload=function(t){this.uploaded||(this.layoutVertexBuffer=t.createVertexBuffer(this.layoutVertexArray,Jo),this.indexBuffer=t.createIndexBuffer(this.indexArray)),this.programConfigurations.upload(t),this.uploaded=!0},$o.prototype.destroy=function(){this.layoutVertexBuffer&&(this.layoutVertexBuffer.destroy(),this.indexBuffer.destroy(),this.programConfigurations.destroy(),this.segments.destroy())},$o.prototype.clear=function(){e.defined(this.layoutVertexArray)&&(this.layoutVertexArray=null),e.defined(this.indexArray)&&(this.indexArray=null)},$o.prototype.addFeature=function(t,e,r){for(var n=0,i=e;n<i.length;n+=1)for(var a=0,o=i[n];a<o.length;a+=1){var s=o[a],u=s.x,l=s.y;if(!(u<0||u>=or||l<0||l>=or)){var p=this.segments.prepareSegment(4,this.layoutVertexArray,this.indexArray,t.sortKey),c=p.vertexLength;Zo(this.layoutVertexArray,u,l,-1,-1),Zo(this.layoutVertexArray,u,l,1,-1),Zo(this.layoutVertexArray,u,l,1,1),Zo(this.layoutVertexArray,u,l,-1,1),this.indexArray.emplaceBack(c,c+1,c+2),this.indexArray.emplaceBack(c,c+3,c+2),p.vertexLength+=4,p.primitiveLength+=2}}this.programConfigurations.populatePaintArrays(this.layoutVertexArray.length,t,r,{})},tr.register("CircleBucket",$o,{omit:["layers"]});var ts=function(t){this.specification=t};ts.prototype.possiblyEvaluate=function(t,e){return t.expression.evaluate(e)},ts.prototype.interpolate=function(t,e,r){var n=interpolate[this.specification.type];return n?n(t,e,r):t},tr.register("DataConstantProperty",ts);var es=function(t,e){this.specification=t,this.overrides=e};es.prototype.possiblyEvaluate=function(t,e,r){return"constant"===t.expression.kind||"camera"===t.expression.kind?new br(this,{kind:"constant",value:t.expression.evaluate(e,null,{},r)},e):new br(this,t.expression,e)},es.prototype.interpolate=function(t,e,r){if("constant"!==t.value.kind||"constant"!==e.value.kind)return t;if(void 0===t.value.value||void 0===e.value.value)return new br(this,{kind:"constant",value:void 0},t.parameters);var n=interpolate[this.specification.type];return n?new br(this,{kind:"constant",value:n(t.value.value,e.value.value,r)},t.parameters):t},es.prototype.evaluate=function(t,e,r,n,i){return"constant"===t.kind?t.value:t.evaluate(e,r,n,i)},tr.register("DataDrivenProperty",es);var rs=function(t,e){this.property=t,this.value=e,this.expression=io.normalizePropertyExpression(void 0===e?t.specification.default:e,t.specification)};function ns(t){if(t<=0)return 0;if(t>=1)return 1;var e=t*t,r=e*t;return 4*(t<.5?r:3*(t-e)+r-.75)}rs.prototype.isDataDriven=function(){return"source"===this.expression.kind||"composite"===this.expression.kind},rs.prototype.possiblyEvaluate=function(t,e){return this.property.possiblyEvaluate(this,t,e)};var is=function(t,e,r,n,i){this.property=t,this.value=e,this.begin=i+n.delay||0,this.end=this.begin+n.duration||0,t.specification.transition&&(n.delay||n.duration)&&(this.prior=r)};is.prototype.possiblyEvaluate=function(t,e){var r=t.now||0,n=this.value.possiblyEvaluate(t,e),i=this.prior;if(i){if(r>this.end)return this.prior=null,n;if(this.value.isDataDriven())return this.prior=null,n;if(r<this.begin)return i.possiblyEvaluate(t,e);var a=(r-this.begin)/(this.end-this.begin);return this.property.interpolate(i.possiblyEvaluate(t,e),n,ns(a))}return n};var as=function(t){this.property=t,this.value=new rs(t,void 0)};as.prototype.transitioned=function(t,e){return new is(this.property,this.value,e,ua.extend({},t.transition,this.transition),t.now)},as.prototype.untransitioned=function(){return new is(this.property,this.value,null,{},0)};var os=function(t){for(var e in this.properties=t,this.defaultPropertyValues={},this.defaultTransitionablePropertyValues={},this.defaultTransitioningPropertyValues={},this.defaultPossiblyEvaluatedValues={},this.overridableProperties=[],t){var r=t[e];r.specification.overridable&&this.overridableProperties.push(e);var n=this.defaultPropertyValues[e]=new rs(r,void 0),i=this.defaultTransitionablePropertyValues[e]=new as(r);this.defaultTransitioningPropertyValues[e]=i.untransitioned(),this.defaultPossiblyEvaluatedValues[e]=n.possiblyEvaluate({})}};function ss(){}ss.getMaximumPaintValue=function(t,e,r){var n=e.paint.get(t).value;return"constant"===n.kind?n.value:r.programConfigurations.get(e.id).binders[t].maxValue},ss.translateDistance=function(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])},ss.translate=function(t,e,r,n,i){if(!e[0]&&!e[1])return t;var a=Gi.convert(e)._mult(i);"viewport"===r&&a._rotate(-n);for(var o=[],s=0;s<t.length;s++){var u=t[s];o.push(u.sub(a))}return o};var us=function(t){this._properties=t,this._values=Object.create(t.defaultPossiblyEvaluatedValues)};us.prototype.get=function(t){return this._values[t]};var ls=function(t){this._properties=t,this._values=Object.create(t.defaultPropertyValues)};ls.prototype.getValue=function(t){return ua.clone(this._values[t].value)},ls.prototype.setValue=function(t,e){this._values[t]=new rs(this._values[t].property,null===e?void 0:ua.clone(e))},ls.prototype.serialize=function(){for(var t={},e=0,r=Object.keys(this._values);e<r.length;e+=1){var n=r[e],i=this.getValue(n);void 0!==i&&(t[n]=i)}return t},ls.prototype.possiblyEvaluate=function(t,e){for(var r=new us(this._properties),n=0,i=Object.keys(this._values);n<i.length;n+=1){var a=i[n];r._values[a]=this._values[a].possiblyEvaluate(t,e)}return r};var ps=function(t){this._properties=t,this._values=Object.create(t.defaultTransitioningPropertyValues)};ps.prototype.possiblyEvaluate=function(t,e){for(var r=new us(this._properties),n=0,i=Object.keys(this._values);n<i.length;n+=1){var a=i[n];r._values[a]=this._values[a].possiblyEvaluate(t,e)}return r},ps.prototype.hasTransition=function(){for(var t=0,e=Object.keys(this._values);t<e.length;t+=1){var r=e[t];if(this._values[r].prior)return!0}return!1};var cs=function(t){this._properties=t,this._values=Object.create(t.defaultTransitionablePropertyValues)};cs.prototype.getValue=function(t){return ua.clone(this._values[t].value.value)},cs.prototype.setValue=function(t,e){this._values.hasOwnProperty(t)||(this._values[t]=new as(this._values[t].property)),this._values[t].value=new rs(this._values[t].property,null===e?void 0:ua.clone(e))},cs.prototype.getTransition=function(t){return ua.clone(this._values[t].transition)},cs.prototype.setTransition=function(t,e){this._values.hasOwnProperty(t)||(this._values[t]=new as(this._values[t].property)),this._values[t].transition=ua.clone(e)||void 0},cs.prototype.serialize=function(){for(var t={},e=0,r=Object.keys(this._values);e<r.length;e+=1){var n=r[e],i=this.getValue(n);void 0!==i&&(t[n]=i);var a=this.getTransition(n);void 0!==a&&(t[n+"-transition"]=a)}return t},cs.prototype.transitioned=function(t,e){for(var r=new ps(this._properties),n=0,i=Object.keys(this._values);n<i.length;n+=1){var a=i[n];r._values[a]=this._values[a].transitioned(t,e._values[a])}return r},cs.prototype.untransitioned=function(){for(var t=new ps(this._properties),e=0,r=Object.keys(this._values);e<r.length;e+=1){var n=r[e];t._values[n]=this._values[n].untransitioned()}return t};var fs="-transition";function hs(t,e){return-1!==t.indexOf(e,t.length-e.length)}function ds(t,e){if(this.id=t.id,this.type=t.type,"custom"!==t.type&&(t=t,this.metadata=t.metadata,this.minzoom=t.minzoom,this.maxzoom=t.maxzoom,"background"!==t.type&&(this.source=t.source,this.sourceLayer=t["source-layer"],this.filter=t.filter),e.layout&&(this._unevaluatedLayout=new ls(e.layout)),e.paint)){for(var r in this._transitionablePaint=new cs(e.paint),t.paint)this.setPaintProperty(r,t.paint[r],{validate:!1});for(var n in t.layout)this.setLayoutProperty(n,t.layout[n],{validate:!1});this._transitioningPaint=this._transitionablePaint.untransitioned()}}ds.prototype.getCrossfadeParameters=function(){return this._crossfadeParameters},ds.prototype.getLayoutProperty=function(t){return"visibility"===t?this.visibility:this._unevaluatedLayout.getValue(t)},ds.prototype.setLayoutProperty=function(t,e,r){if(null!=e&&this.id,"visibility"===t)return this.visibility=e,void(this.config&&this.config.layout&&(this.config.layout.visibility=e));this._unevaluatedLayout.setValue(t,e)},ds.prototype.getPaintProperty=function(t){return hs(t,fs)?this._transitionablePaint.getTransition(t.slice(0,-fs.length)):this._transitionablePaint.getValue(t)},ds.prototype.setPaintProperty=function(t,e,r){if(null!=e&&this.id,hs(t,fs))return this._transitionablePaint.setTransition(t.slice(0,-fs.length),e||void 0),!1;var n=this._transitionablePaint._values[t],i="cross-faded-data-driven"===n.property.specification["property-type"],a=n.value.isDataDriven(),o=n.value;this._transitionablePaint.setValue(t,e),this._handleSpecialPaintPropertyUpdate(t);var s=this._transitionablePaint._values[t].value;return s.isDataDriven()||a||i||this._handleOverridablePaintPropertyUpdate(t,o,s)},ds.prototype._handleSpecialPaintPropertyUpdate=function(t){},ds.prototype._handleOverridablePaintPropertyUpdate=function(t,e,r){return!1},ds.prototype.isHidden=function(t){return!!(this.minzoom&&t<this.minzoom)||(!!(this.maxzoom&&t>=this.maxzoom)||"none"===this.visibility)},ds.prototype.updateTransitions=function(t){this._transitioningPaint=this._transitionablePaint.transitioned(t,this._transitioningPaint)},ds.prototype.hasTransition=function(){return this._transitioningPaint.hasTransition()},ds.prototype.recalculate=function(t,e){t.getCrossfadeParameters&&(this._crossfadeParameters=t.getCrossfadeParameters()),this._unevaluatedLayout&&(this.layout=this._unevaluatedLayout.possiblyEvaluate(t,e)),this.paint=this._transitioningPaint.possiblyEvaluate(t,e)},ds.prototype.serialize=function(){var t={id:this.id,type:this.type,source:this.source,"source-layer":this.sourceLayer,metadata:this.metadata,minzoom:this.minzoom,maxzoom:this.maxzoom,filter:this.filter,layout:this._unevaluatedLayout&&this._unevaluatedLayout.serialize(),paint:this._transitionablePaint&&this._transitionablePaint.serialize()};return this.visibility&&(t.layout=t.layout||{},t.layout.visibility=this.visibility),ua.filterObject(t,(function(t,e){return!(void 0===t||"layout"===e&&!Object.keys(t).length||"paint"===e&&!Object.keys(t).length)}))},ds.prototype._validate=function(t,e,r,n,i){return!0},ds.prototype.is3D=function(){return!1},ds.prototype.isTileClipped=function(){return!1},ds.prototype.hasOffscreenPass=function(){return!1},ds.prototype.resize=function(){},ds.prototype.isStateDependent=function(){return!0};var ys=8,ms={version:{required:!0,type:"enum",values:[8]},name:{type:"string"},metadata:{type:"*"},center:{type:"array",value:"number"},zoom:{type:"number"},bearing:{type:"number",default:0,period:360,units:"degrees"},pitch:{type:"number",default:0,units:"degrees"},light:{type:"light"},sources:{required:!0,type:"sources"},sprite:{type:"string"},glyphs:{type:"string"},transition:{type:"transition"},layers:{required:!0,type:"array",value:"layer"}},vs={"*":{type:"source"}},gs=["source_vector","source_raster","source_raster_dem","source_geojson","source_video","source_image"],xs={type:{required:!0,type:"enum",values:{vector:{}}},url:{type:"string"},tiles:{type:"array",value:"string"},bounds:{type:"array",value:"number",length:4,default:[-180,-85.051129,180,85.051129]},scheme:{type:"enum",values:{xyz:{},tms:{}},default:"xyz"},minzoom:{type:"number",default:0},maxzoom:{type:"number",default:22},attribution:{type:"string"},"*":{type:"*"}},bs={type:{required:!0,type:"enum",values:{raster:{}}},url:{type:"string"},tiles:{type:"array",value:"string"},bounds:{type:"array",value:"number",length:4,default:[-180,-85.051129,180,85.051129]},minzoom:{type:"number",default:0},maxzoom:{type:"number",default:22},tileSize:{type:"number",default:512,units:"pixels"},scheme:{type:"enum",values:{xyz:{},tms:{}},default:"xyz"},attribution:{type:"string"},"*":{type:"*"}},_s={type:{required:!0,type:"enum",values:{"raster-dem":{}}},url:{type:"string"},tiles:{type:"array",value:"string"},bounds:{type:"array",value:"number",length:4,default:[-180,-85.051129,180,85.051129]},minzoom:{type:"number",default:0},maxzoom:{type:"number",default:22},tileSize:{type:"number",default:512,units:"pixels"},attribution:{type:"string"},encoding:{type:"enum",values:{terrarium:{},mapbox:{}},default:"mapbox"},"*":{type:"*"}},ws={type:{required:!0,type:"enum",values:{geojson:{}}},data:{type:"*"},maxzoom:{type:"number",default:18},attribution:{type:"string"},buffer:{type:"number",default:128,maximum:512,minimum:0},tolerance:{type:"number",default:.375},cluster:{type:"boolean",default:!1},clusterRadius:{type:"number",default:50,minimum:0},clusterMaxZoom:{type:"number"},clusterProperties:{type:"*"},lineMetrics:{type:"boolean",default:!1},generateId:{type:"boolean",default:!1}},Es={type:{required:!0,type:"enum",values:{video:{}}},urls:{required:!0,type:"array",value:"string"},coordinates:{required:!0,type:"array",length:4,value:{type:"array",length:2,value:"number"}}},Ts={type:{required:!0,type:"enum",values:{image:{}}},url:{required:!0,type:"string"},coordinates:{required:!0,type:"array",length:4,value:{type:"array",length:2,value:"number"}}},As={id:{type:"string",required:!0},type:{type:"enum",values:{fill:{},line:{},symbol:{},circle:{},heatmap:{},"fill-extrusion":{},raster:{},hillshade:{},background:{}},required:!0},metadata:{type:"*"},source:{type:"string"},"source-layer":{type:"string"},minzoom:{type:"number",minimum:0,maximum:24},maxzoom:{type:"number",minimum:0,maximum:24},filter:{type:"filter"},layout:{type:"layout"},paint:{type:"paint"}},Ss=["layout_fill","layout_line","layout_circle","layout_heatmap","layout_fill-extrusion","layout_symbol","layout_raster","layout_hillshade","layout_background"],Is={visibility:{type:"enum",values:{visible:{},none:{}},default:"visible","property-type":"constant"}},ks={"fill-sort-key":{type:"number",expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},visibility:{type:"enum",values:{visible:{},none:{}},default:"visible","property-type":"constant"}},Ms={"circle-sort-key":{type:"number",expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},visibility:{type:"enum",values:{visible:{},none:{}},default:"visible","property-type":"constant"}},Fs={visibility:{type:"enum",values:{visible:{},none:{}},default:"visible","property-type":"constant"}},Os={"line-cap":{type:"enum",values:{butt:{},round:{},square:{}},default:"butt",expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"line-join":{type:"enum",values:{bevel:{},round:{},miter:{}},default:"miter",expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"line-miter-limit":{type:"number",default:2,requires:[{"line-join":"miter"}],expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"line-round-limit":{type:"number",default:1.05,requires:[{"line-join":"round"}],expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"line-sort-key":{type:"number",expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},visibility:{type:"enum",values:{visible:{},none:{}},default:"visible","property-type":"constant"}},Ps={"symbol-placement":{type:"enum",values:{point:{},line:{},"line-center":{}},default:"point",expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"symbol-spacing":{type:"number",default:250,minimum:1,units:"pixels",requires:[{"symbol-placement":"line"}],expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"symbol-avoid-edges":{type:"boolean",default:!1,expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"symbol-sort-key":{type:"number",expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"symbol-z-order":{type:"enum",values:{auto:{},"viewport-y":{},source:{}},default:"auto",expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"icon-allow-overlap":{type:"boolean",default:!1,requires:["icon-image"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"icon-ignore-placement":{type:"boolean",default:!1,requires:["icon-image"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"icon-optional":{type:"boolean",default:!1,requires:["icon-image","text-field"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"icon-rotation-alignment":{type:"enum",values:{map:{},viewport:{},auto:{}},default:"auto",requires:["icon-image"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"icon-size":{type:"number",default:1,minimum:0,units:"factor of the original icon size",requires:["icon-image"],expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"icon-text-fit":{type:"enum",values:{none:{},width:{},height:{},both:{}},default:"none",requires:["icon-image","text-field"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"icon-text-fit-padding":{type:"array",value:"number",length:4,default:[0,0,0,0],units:"pixels",requires:["icon-image","text-field",{"icon-text-fit":["both","width","height"]}],expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"icon-image":{type:"resolvedImage",tokens:!0,expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"icon-rotate":{type:"number",default:0,period:360,units:"degrees",requires:["icon-image"],expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"icon-padding":{type:"number",default:2,minimum:0,units:"pixels",requires:["icon-image"],expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"icon-keep-upright":{type:"boolean",default:!1,requires:["icon-image",{"icon-rotation-alignment":"map"},{"symbol-placement":["line","line-center"]}],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"icon-offset":{type:"array",value:"number",length:2,default:[0,0],requires:["icon-image"],expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"icon-anchor":{type:"enum",values:{center:{},left:{},right:{},top:{},bottom:{},"top-left":{},"top-right":{},"bottom-left":{},"bottom-right":{}},default:"center",requires:["icon-image"],expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"icon-pitch-alignment":{type:"enum",values:{map:{},viewport:{},auto:{}},default:"auto",requires:["icon-image"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-pitch-alignment":{type:"enum",values:{map:{},viewport:{},auto:{}},default:"auto",requires:["text-field"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-rotation-alignment":{type:"enum",values:{map:{},viewport:{},auto:{}},default:"auto",requires:["text-field"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-field":{type:"formatted",default:"",tokens:!0,expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-font":{type:"array",value:"string",default:["Open Sans Regular","Arial Unicode MS Regular"],requires:["text-field"],expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-size":{type:"number",default:16,minimum:0,units:"pixels",requires:["text-field"],expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-max-width":{type:"number",default:10,minimum:0,units:"ems",requires:["text-field"],expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-line-height":{type:"number",default:1.2,units:"ems",requires:["text-field"],expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"text-letter-spacing":{type:"number",default:0,units:"ems",requires:["text-field"],expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-justify":{type:"enum",values:{auto:{},left:{},center:{},right:{}},default:"center",requires:["text-field"],expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-radial-offset":{type:"number",units:"ems",default:0,requires:["text-field"],"property-type":"data-driven",expression:{interpolated:!0,parameters:["zoom","feature"]}},"text-variable-anchor":{type:"array",value:"enum",values:{center:{},left:{},right:{},top:{},bottom:{},"top-left":{},"top-right":{},"bottom-left":{},"bottom-right":{}},requires:["text-field",{"symbol-placement":["point"]}],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-anchor":{type:"enum",values:{center:{},left:{},right:{},top:{},bottom:{},"top-left":{},"top-right":{},"bottom-left":{},"bottom-right":{}},default:"center",requires:["text-field",{"!":"text-variable-anchor"}],expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-max-angle":{type:"number",default:45,units:"degrees",requires:["text-field",{"symbol-placement":["line","line-center"]}],expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"text-writing-mode":{type:"array",value:"enum",values:{horizontal:{},vertical:{}},requires:["text-field",{"symbol-placement":["point"]}],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-rotate":{type:"number",default:0,period:360,units:"degrees",requires:["text-field"],expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-padding":{type:"number",default:2,minimum:0,units:"pixels",requires:["text-field"],expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"text-keep-upright":{type:"boolean",default:!0,requires:["text-field",{"text-rotation-alignment":"map"},{"symbol-placement":["line","line-center"]}],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-transform":{type:"enum",values:{none:{},uppercase:{},lowercase:{}},default:"none",requires:["text-field"],expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-offset":{type:"array",value:"number",units:"ems",length:2,default:[0,0],requires:["text-field",{"!":"text-radial-offset"}],expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-allow-overlap":{type:"boolean",default:!1,requires:["text-field"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-ignore-placement":{type:"boolean",default:!1,requires:["text-field"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-optional":{type:"boolean",default:!1,requires:["text-field","icon-image"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},visibility:{type:"enum",values:{visible:{},none:{}},default:"visible","property-type":"constant"}},zs={visibility:{type:"enum",values:{visible:{},none:{}},default:"visible","property-type":"constant"}},Rs={visibility:{type:"enum",values:{visible:{},none:{}},default:"visible","property-type":"constant"}},Cs={type:"array",value:"*"},Bs={type:"enum",values:{"==":{},"!=":{},">":{},">=":{},"<":{},"<=":{},in:{},"!in":{},all:{},any:{},none:{},has:{},"!has":{}}},Ds={type:"enum",values:{Point:{},LineString:{},Polygon:{}}},Vs={type:"array",minimum:0,maximum:24,value:["number","color"],length:2},Us={type:"array",value:"*",minimum:1},Ns={type:"enum",values:{let:{group:"Variable binding"},var:{group:"Variable binding"},literal:{group:"Types"},array:{group:"Types"},at:{group:"Lookup"},in:{group:"Lookup"},case:{group:"Decision"},match:{group:"Decision"},coalesce:{group:"Decision"},step:{group:"Ramps, scales, curves"},interpolate:{group:"Ramps, scales, curves"},"interpolate-hcl":{group:"Ramps, scales, curves"},"interpolate-lab":{group:"Ramps, scales, curves"},ln2:{group:"Math"},pi:{group:"Math"},e:{group:"Math"},typeof:{group:"Types"},string:{group:"Types"},number:{group:"Types"},boolean:{group:"Types"},object:{group:"Types"},collator:{group:"Types"},format:{group:"Types"},image:{group:"Types"},"number-format":{group:"Types"},"to-string":{group:"Types"},"to-number":{group:"Types"},"to-boolean":{group:"Types"},"to-rgba":{group:"Color"},"to-color":{group:"Types"},rgb:{group:"Color"},rgba:{group:"Color"},get:{group:"Lookup"},has:{group:"Lookup"},length:{group:"Lookup"},properties:{group:"Feature data"},"feature-state":{group:"Feature data"},"geometry-type":{group:"Feature data"},id:{group:"Feature data"},zoom:{group:"Zoom"},"heatmap-density":{group:"Heatmap"},"line-progress":{group:"Feature data"},accumulated:{group:"Feature data"},"+":{group:"Math"},"*":{group:"Math"},"-":{group:"Math"},"/":{group:"Math"},"%":{group:"Math"},"^":{group:"Math"},sqrt:{group:"Math"},log10:{group:"Math"},ln:{group:"Math"},log2:{group:"Math"},sin:{group:"Math"},cos:{group:"Math"},tan:{group:"Math"},asin:{group:"Math"},acos:{group:"Math"},atan:{group:"Math"},min:{group:"Math"},max:{group:"Math"},round:{group:"Math"},abs:{group:"Math"},ceil:{group:"Math"},floor:{group:"Math"},"==":{group:"Decision"},"!=":{group:"Decision"},">":{group:"Decision"},"<":{group:"Decision"},">=":{group:"Decision"},"<=":{group:"Decision"},all:{group:"Decision"},any:{group:"Decision"},"!":{group:"Decision"},"is-supported-script":{group:"String"},upcase:{group:"String"},downcase:{group:"String"},concat:{group:"String"},"resolved-locale":{group:"String"}}},Ls={anchor:{type:"enum",default:"viewport",values:{map:{},viewport:{}},"property-type":"data-constant",transition:!1,expression:{interpolated:!1,parameters:["zoom"]}},position:{type:"array",default:[1.15,210,30],length:3,value:"number","property-type":"data-constant",transition:!0,expression:{interpolated:!0,parameters:["zoom"]}},color:{type:"color","property-type":"data-constant",default:"#ffffff",expression:{interpolated:!0,parameters:["zoom"]},transition:!0},intensity:{type:"number","property-type":"data-constant",default:.5,minimum:0,maximum:1,expression:{interpolated:!0,parameters:["zoom"]},transition:!0}},Xs=["paint_fill","paint_line","paint_circle","paint_heatmap","paint_fill-extrusion","paint_symbol","paint_raster","paint_hillshade","paint_background"],qs={"fill-antialias":{type:"boolean",default:!0,expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"fill-opacity":{type:"number",default:1,minimum:0,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"fill-color":{type:"color",default:"#000000",transition:!0,requires:[{"!":"fill-pattern"}],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"fill-outline-color":{type:"color",transition:!0,requires:[{"!":"fill-pattern"},{"fill-antialias":!0}],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"fill-translate":{type:"array",value:"number",length:2,default:[0,0],transition:!0,units:"pixels",expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"fill-translate-anchor":{type:"enum",values:{map:{},viewport:{}},default:"map",requires:["fill-translate"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"fill-pattern":{type:"resolvedImage",transition:!0,expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"cross-faded-data-driven"}},js={"line-opacity":{type:"number",default:1,minimum:0,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"line-color":{type:"color",default:"#000000",transition:!0,requires:[{"!":"line-pattern"}],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"line-translate":{type:"array",value:"number",length:2,default:[0,0],transition:!0,units:"pixels",expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"line-translate-anchor":{type:"enum",values:{map:{},viewport:{}},default:"map",requires:["line-translate"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"line-width":{type:"number",default:1,minimum:0,transition:!0,units:"pixels",expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"line-gap-width":{type:"number",default:0,minimum:0,transition:!0,units:"pixels",expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"line-offset":{type:"number",default:0,transition:!0,units:"pixels",expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"line-blur":{type:"number",default:0,minimum:0,transition:!0,units:"pixels",expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"line-dasharray":{type:"array",value:"number",minimum:0,transition:!0,units:"line widths",requires:[{"!":"line-pattern"}],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"cross-faded"},"line-pattern":{type:"resolvedImage",transition:!0,expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"cross-faded-data-driven"},"line-gradient":{type:"color",transition:!1,requires:[{"!":"line-dasharray"},{"!":"line-pattern"},{source:"geojson",has:{lineMetrics:!0}}],expression:{interpolated:!0,parameters:["line-progress"]},"property-type":"color-ramp"}},Hs={"circle-radius":{type:"number",default:5,minimum:0,transition:!0,units:"pixels",expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"circle-color":{type:"color",default:"#000000",transition:!0,expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"circle-blur":{type:"number",default:0,transition:!0,expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"circle-opacity":{type:"number",default:1,minimum:0,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"circle-translate":{type:"array",value:"number",length:2,default:[0,0],transition:!0,units:"pixels",expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"circle-translate-anchor":{type:"enum",values:{map:{},viewport:{}},default:"map",requires:["circle-translate"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"circle-pitch-scale":{type:"enum",values:{map:{},viewport:{}},default:"map",expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"circle-pitch-alignment":{type:"enum",values:{map:{},viewport:{}},default:"viewport",expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"circle-stroke-width":{type:"number",default:0,minimum:0,transition:!0,units:"pixels",expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"circle-stroke-color":{type:"color",default:"#000000",transition:!0,expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"circle-stroke-opacity":{type:"number",default:1,minimum:0,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"}},Ys={"heatmap-radius":{type:"number",default:30,minimum:1,transition:!0,units:"pixels",expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"heatmap-weight":{type:"number",default:1,minimum:0,transition:!1,expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"heatmap-intensity":{type:"number",default:1,minimum:0,transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"heatmap-color":{type:"color",default:["interpolate",["linear"],["heatmap-density"],0,"rgba(0, 0, 255, 0)",.1,"royalblue",.3,"cyan",.5,"lime",.7,"yellow",1,"red"],transition:!1,expression:{interpolated:!0,parameters:["heatmap-density"]},"property-type":"color-ramp"},"heatmap-opacity":{type:"number",default:1,minimum:0,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"}},Qs={"icon-opacity":{type:"number",default:1,minimum:0,maximum:1,transition:!0,requires:["icon-image"],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"icon-color":{type:"color",default:"#000000",transition:!0,requires:["icon-image"],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"icon-halo-color":{type:"color",default:"rgba(0, 0, 0, 0)",transition:!0,requires:["icon-image"],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"icon-halo-width":{type:"number",default:0,minimum:0,transition:!0,units:"pixels",requires:["icon-image"],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"icon-halo-blur":{type:"number",default:0,minimum:0,transition:!0,units:"pixels",requires:["icon-image"],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"icon-translate":{type:"array",value:"number",length:2,default:[0,0],transition:!0,units:"pixels",requires:["icon-image"],expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"icon-translate-anchor":{type:"enum",values:{map:{},viewport:{}},default:"map",requires:["icon-image","icon-translate"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-opacity":{type:"number",default:1,minimum:0,maximum:1,transition:!0,requires:["text-field"],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"text-color":{type:"color",default:"#000000",transition:!0,overridable:!0,requires:["text-field"],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"text-halo-color":{type:"color",default:"rgba(0, 0, 0, 0)",transition:!0,requires:["text-field"],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"text-halo-width":{type:"number",default:0,minimum:0,transition:!0,units:"pixels",requires:["text-field"],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"text-show-background":{type:"boolean",default:!1,transition:!1,expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-halo-blur":{type:"number",default:0,minimum:0,transition:!0,units:"pixels",requires:["text-field"],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"text-translate":{type:"array",value:"number",length:2,default:[0,0],transition:!0,units:"pixels",requires:["text-field"],expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"text-translate-anchor":{type:"enum",values:{map:{},viewport:{}},default:"map",requires:["text-field","text-translate"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"}},Ks={"raster-opacity":{type:"number",default:1,minimum:0,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"raster-hue-rotate":{type:"number",default:0,period:360,transition:!0,units:"degrees",expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"raster-brightness-min":{type:"number",default:0,minimum:0,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"raster-brightness-max":{type:"number",default:1,minimum:0,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"raster-saturation":{type:"number",default:0,minimum:-1,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"raster-contrast":{type:"number",default:0,minimum:-1,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"raster-resampling":{type:"enum",values:{linear:{},nearest:{}},default:"linear",expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"raster-fade-duration":{type:"number",default:300,minimum:0,transition:!1,units:"milliseconds",expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"}},Gs={"hillshade-illumination-direction":{type:"number",default:335,minimum:0,maximum:359,transition:!1,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"hillshade-illumination-anchor":{type:"enum",values:{map:{},viewport:{}},default:"viewport",expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"hillshade-exaggeration":{type:"number",default:.5,minimum:0,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"hillshade-shadow-color":{type:"color",default:"#000000",transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"hillshade-highlight-color":{type:"color",default:"#FFFFFF",transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"hillshade-accent-color":{type:"color",default:"#000000",transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"}},Ws={"background-color":{type:"color",default:"#000000",transition:!0,requires:[{"!":"background-pattern"}],expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"background-pattern":{type:"resolvedImage",transition:!0,expression:{interpolated:!1,parameters:["zoom"]},"property-type":"cross-faded"},"background-opacity":{type:"number",default:1,minimum:0,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"}},Js={duration:{type:"number",default:300,minimum:0,units:"milliseconds"},delay:{type:"number",default:0,minimum:0,units:"milliseconds"}},Zs={$version:ys,$root:ms,sources:vs,source:gs,source_vector:xs,source_raster:bs,source_raster_dem:_s,source_geojson:ws,source_video:Es,source_image:Ts,layer:As,layout:Ss,layout_background:Is,layout_fill:ks,layout_circle:Ms,layout_heatmap:Fs,"layout_fill-extrusion":{visibility:{type:"enum",values:{visible:{},none:{}},default:"visible","property-type":"constant"}},layout_line:Os,layout_symbol:Ps,layout_raster:zs,layout_hillshade:Rs,filter:Cs,filter_operator:Bs,geometry_type:Ds,function:{expression:{type:"expression"},stops:{type:"array",value:"function_stop"},base:{type:"number",default:1,minimum:0},property:{type:"string",default:"$zoom"},type:{type:"enum",values:{identity:{},exponential:{},interval:{},categorical:{}},default:"exponential"},colorSpace:{type:"enum",values:{rgb:{},lab:{},hcl:{}},default:"rgb"},default:{type:"*",required:!1}},function_stop:Vs,expression:Us,expression_name:Ns,light:Ls,paint:Xs,paint_fill:qs,"paint_fill-extrusion":{"fill-extrusion-opacity":{type:"number",default:1,minimum:0,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"fill-extrusion-color":{type:"color",default:"#000000",transition:!0,requires:[{"!":"fill-extrusion-pattern"}],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"fill-extrusion-translate":{type:"array",value:"number",length:2,default:[0,0],transition:!0,units:"pixels",expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"fill-extrusion-translate-anchor":{type:"enum",values:{map:{},viewport:{}},default:"map",requires:["fill-extrusion-translate"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"fill-extrusion-pattern":{type:"resolvedImage",transition:!0,expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"cross-faded-data-driven"},"fill-extrusion-height":{type:"number",default:0,minimum:0,units:"meters",transition:!0,expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"fill-extrusion-base":{type:"number",default:0,minimum:0,units:"meters",transition:!0,requires:["fill-extrusion-height"],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"fill-extrusion-vertical-gradient":{type:"boolean",default:!0,transition:!1,expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"}},paint_line:js,paint_circle:Hs,paint_heatmap:Ys,paint_symbol:Qs,paint_raster:Ks,paint_hillshade:Gs,paint_background:Ws,transition:Js,"property-type":{"data-driven":{type:"property-type"},"cross-faded":{type:"property-type"},"cross-faded-data-driven":{type:"property-type"},"color-ramp":{type:"property-type"},"data-constant":{type:"property-type"},constant:{type:"property-type"}}};function $s(){}function tu(t,e,r){if(t.length>1){if(eu(t,e))return!0;for(var n=0;n<e.length;n++)if(nu(e[n],t,r))return!0}for(var i=0;i<t.length;i++)if(nu(t[i],e,r))return!0;return!1}function eu(t,e){if(0===t.length||0===e.length)return!1;for(var r=0;r<t.length-1;r++)for(var n=t[r],i=t[r+1],a=0;a<e.length-1;a++){if(ru(n,i,e[a],e[a+1]))return!0}return!1}function ru(t,e,r,n){return ua.isCounterClockwise(t,r,n)!==ua.isCounterClockwise(e,r,n)&&ua.isCounterClockwise(t,e,r)!==ua.isCounterClockwise(t,e,n)}function nu(t,e,r){var n=r*r;if(1===e.length)return t.distSqr(e[0])<n;for(var i=1;i<e.length;i++){var a=e[i-1],o=e[i];if($s.distToSegmentSquared(t,a,o)<n)return!0}return!1}function iu(t,e){for(var r,n,i,a=!1,o=0;o<t.length;o++)for(var s=0,u=(r=t[o]).length-1;s<r.length;u=s++)n=r[s],i=r[u],n.y>e.y!=i.y>e.y&&e.x<(i.x-n.x)*(e.y-n.y)/(i.y-n.y)+n.x&&(a=!a);return a}function au(t,e){for(var r=!1,n=0,i=t.length-1;n<t.length;i=n++){var a=t[n],o=t[i];a.y>e.y!=o.y>e.y&&e.x<(o.x-a.x)*(e.y-a.y)/(o.y-a.y)+a.x&&(r=!r)}return r}$s.polygonIntersectsPolygon=function(t,e){for(var r=0;r<t.length;r++)if(au(e,t[r]))return!0;for(var n=0;n<e.length;n++)if(au(t,e[n]))return!0;return!!eu(t,e)},$s.polygonIntersectsBufferedPoint=function(t,e,r){return!!au(t,e)||!!nu(e,t,r)},$s.polygonIntersectsMultiPolygon=function(t,e){if(1===t.length)return iu(e,t[0]);for(var r=0;r<e.length;r++)for(var n=e[r],i=0;i<n.length;i++)if(au(t,n[i]))return!0;for(var a=0;a<t.length;a++)if(iu(e,t[a]))return!0;for(var o=0;o<e.length;o++)if(eu(t,e[o]))return!0;return!1},$s.polygonIntersectsBufferedMultiLine=function(t,e,r){for(var n=0;n<e.length;n++){var i=e[n];if(t.length>=3)for(var a=0;a<i.length;a++)if(au(t,i[a]))return!0;if(tu(t,i,r))return!0}return!1},$s.distToSegmentSquared=function(t,e,r){var n=e.distSqr(r);if(0===n)return t.distSqr(e);var i=((t.x-e.x)*(r.x-e.x)+(t.y-e.y)*(r.y-e.y))/n;return i<0?t.distSqr(e):i>1?t.distSqr(r):t.distSqr(r.sub(e)._mult(i)._add(e))};var ou=new os({"circle-sort-key":new es(Zs.layout_circle["circle-sort-key"])}),su=new os({"circle-radius":new es(Zs.paint_circle["circle-radius"]),"circle-color":new es(Zs.paint_circle["circle-color"]),"circle-blur":new es(Zs.paint_circle["circle-blur"]),"circle-opacity":new es(Zs.paint_circle["circle-opacity"]),"circle-translate":new ts(Zs.paint_circle["circle-translate"]),"circle-translate-anchor":new ts(Zs.paint_circle["circle-translate-anchor"]),"circle-pitch-scale":new ts(Zs.paint_circle["circle-pitch-scale"]),"circle-pitch-alignment":new ts(Zs.paint_circle["circle-pitch-alignment"]),"circle-stroke-width":new es(Zs.paint_circle["circle-stroke-width"]),"circle-stroke-color":new es(Zs.paint_circle["circle-stroke-color"]),"circle-stroke-opacity":new es(Zs.paint_circle["circle-stroke-opacity"])}),uu={paint:su,layout:ou},lu=function(t){function r(e){t.call(this,e,uu)}return t&&(r.__proto__=t),r.prototype=Object.create(t&&t.prototype),r.prototype.constructor=r,r.prototype.createBucket=function(t){return new $o(t)},r.prototype.queryRadius=function(t){var e=t;return ss.getMaximumPaintValue("circle-radius",this,e)+ss.getMaximumPaintValue("circle-stroke-width",this,e)+ss.translateDistance(this.paint.get("circle-translate"))},r.prototype.queryIntersectsFeature=function(t,r,n,i,a,o,s,u,l){u=fu();for(var p=ss.translate(t,this.paint.get("circle-translate"),this.paint.get("circle-translate-anchor"),0,s),c=this.paint.get("circle-radius").evaluate(r,n)+this.paint.get("circle-stroke-width").evaluate(r,n),f="map"===this.paint.get("circle-pitch-alignment"),h=f?p:cu(p,u),d=f?c*s:c,y=0,m=i;y<m.length;y+=1)for(var v=0,g=m[y];v<g.length;v+=1){var x=g[v],b=f?x:pu(x,u),_=d;if(hu([],[x.x,x.y,0,1],u),"viewport"===this.paint.get("circle-pitch-scale")&&"map"===this.paint.get("circle-pitch-alignment")||"map"===this.paint.get("circle-pitch-scale")&&this.paint.get("circle-pitch-alignment"),l=e.defined(l)?l:10,$s.polygonIntersectsBufferedPoint(h,b,_*l))return!0}return!1},r}(ds);function pu(t,e){var r=hu([],[t.x,t.y,0,1],e);return new Gi(r[0],r[1])}function cu(t,e){return t.map((function(t){return pu(t,e)}))}function fu(){var t=new Float32Array(16);return t[0]=1,t[5]=1,t[10]=1,t[15]=1,t}function hu(t,e,r){var n=e[0],i=e[1],a=e[2],o=r[3]*n+r[7]*i+r[11]*a+r[15];return o=o||1,t[0]=(r[0]*n+r[4]*i+r[8]*a+r[12])/o,t[1]=(r[1]*n+r[5]*i+r[9]*a+r[13])/o,t[2]=(r[2]*n+r[6]*i+r[10]*a+r[14])/o,t}var du=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.possiblyEvaluate=function(t,e,r){if(void 0===t.value)return new br(this,{kind:"constant",value:void 0},e);if("constant"===t.expression.kind){var n=t.expression.evaluate(e,null,{},r),i="resolvedImage"===t.property.specification.type&&"string"!=typeof n?n.name:n,a=this._calculate(i,i,i,e);return new br(this,{kind:"constant",value:a},e)}if("camera"===t.expression.kind){var o=this._calculate(t.expression.evaluate({zoom:e.zoom-1}),t.expression.evaluate({zoom:e.zoom}),t.expression.evaluate({zoom:e.zoom+1}),e);return new br(this,{kind:"constant",value:o},e)}return new br(this,t.expression,e)},e.prototype.evaluate=function(t,e,r,n,i){if("source"===t.kind){var a=t.evaluate(e,r,n,i);return this._calculate(a,a,a,e)}return"composite"===t.kind?this._calculate(t.evaluate({zoom:Math.floor(e.zoom)-1},r,n),t.evaluate({zoom:Math.floor(e.zoom)},r,n),t.evaluate({zoom:Math.floor(e.zoom)+1},r,n),e):t.value},e.prototype._calculate=function(t,e,r,n){return n.zoom>n.zoomHistory.lastIntegerZoom?{from:t,to:e}:{from:r,to:e}},e.prototype.interpolate=function(t){return t},e}(es);tr.register("DataDrivenProperty",es);var yu=new os({"fill-sort-key":new es(Zs.layout_fill["fill-sort-key"])}),mu=new os({"fill-antialias":new ts(Zs.paint_fill["fill-antialias"]),"fill-opacity":new es(Zs.paint_fill["fill-opacity"]),"fill-color":new es(Zs.paint_fill["fill-color"]),"fill-outline-color":new es(Zs.paint_fill["fill-outline-color"]),"fill-translate":new ts(Zs.paint_fill["fill-translate"]),"fill-translate-anchor":new ts(Zs.paint_fill["fill-translate-anchor"]),"fill-pattern":new du(Zs.paint_fill["fill-pattern"])}),vu={paint:mu,layout:yu},gu=function(t){function e(e){t.call(this,e,vu)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.recalculate=function(e,r){t.prototype.recalculate.call(this,e,r);var n=this.paint._values["fill-outline-color"];"constant"===n.value.kind&&void 0===n.value.value&&(this.paint._values["fill-outline-color"]=this.paint._values["fill-color"])},e.prototype.createBucket=function(t){return new Ni(t)},e.prototype.queryRadius=function(){return ss.translateDistance(this.paint.get("fill-translate"))},e.prototype.queryIntersectsFeature=function(t,e,r,n,i,a,o){var s=ss.translate(t,this.paint.get("fill-translate"),this.paint.get("fill-translate-anchor"),0,o);return $s.polygonIntersectsMultiPolygon(s,n)},e.prototype.isTileClipped=function(){return!0},e}(ds),xu=function(t){this.specification=t};xu.prototype.possiblyEvaluate=function(t,e,r){if(void 0!==t.value){if("constant"===t.expression.kind){var n=t.expression.evaluate(e,null,{},r);return this._calculate(n,n,n,e)}return this._calculate(t.expression.evaluate(new ar(Math.floor(e.zoom-1),e)),t.expression.evaluate(new ar(Math.floor(e.zoom),e)),t.expression.evaluate(new ar(Math.floor(e.zoom+1),e)),e)}},xu.prototype._calculate=function(t,e,r,n){return n.zoom>n.zoomHistory.lastIntegerZoom?{from:t,to:e}:{from:r,to:e}},xu.prototype.interpolate=function(t){return t},tr.register("CrossFadedProperty",xu);var bu=function(t){this.specification=t};bu.prototype.possiblyEvaluate=function(t,e,r){return!!t.expression.evaluate(e,null,{},r)},bu.prototype.interpolate=function(){return!1},tr.register("ColorRampProperty",bu);var _u=new os({"line-cap":new ts(Zs.layout_line["line-cap"]),"line-join":new es(Zs.layout_line["line-join"]),"line-miter-limit":new ts(Zs.layout_line["line-miter-limit"]),"line-round-limit":new ts(Zs.layout_line["line-round-limit"]),"line-sort-key":new es(Zs.layout_line["line-sort-key"])}),wu=new os({"line-opacity":new es(Zs.paint_line["line-opacity"]),"line-color":new es(Zs.paint_line["line-color"]),"line-translate":new ts(Zs.paint_line["line-translate"]),"line-translate-anchor":new ts(Zs.paint_line["line-translate-anchor"]),"line-width":new es(Zs.paint_line["line-width"]),"line-gap-width":new es(Zs.paint_line["line-gap-width"]),"line-offset":new es(Zs.paint_line["line-offset"]),"line-blur":new es(Zs.paint_line["line-blur"]),"line-dasharray":new xu(Zs.paint_line["line-dasharray"]),"line-pattern":new du(Zs.paint_line["line-pattern"]),"line-gradient":new bu(Zs.paint_line["line-gradient"])}),Eu={paint:wu,layout:_u},Tu=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.possiblyEvaluate=function(e,r){return r=new ar(Math.floor(r.zoom),{now:r.now,fadeDuration:r.fadeDuration,zoomHistory:r.zoomHistory,transition:r.transition}),t.prototype.possiblyEvaluate.call(this,e,r)},e.prototype.evaluate=function(e,r,n,i){return r=extend({},r,{zoom:Math.floor(r.zoom)}),t.prototype.evaluate.call(this,e,r,n,i)},e}(es),Au=new Tu(Eu.paint.properties["line-width"].specification);Au.useIntegerZoom=!0;var Su=function(t){function e(e){t.call(this,e,Eu)}function r(t,e){return e>0?e+2*t:t}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype._handleSpecialPaintPropertyUpdate=function(t){"line-gradient"===t&&this._updateGradient()},e.prototype._updateGradient=function(){var t=this._transitionablePaint._values["line-gradient"].value.expression;this.gradient=renderColorRamp(t,"lineProgress"),this.gradientTexture=null},e.prototype.recalculate=function(e,r){t.prototype.recalculate.call(this,e,r),this.paint._values["line-floorwidth"]=Au.possiblyEvaluate(this._transitioningPaint._values["line-width"].value,e)},e.prototype.createBucket=function(t){return new $r(t)},e.prototype.queryRadius=function(t){var e=t,n=r(ss.getMaximumPaintValue("line-width",this,e),ss.getMaximumPaintValue("line-gap-width",this,e)),i=ss.getMaximumPaintValue("line-offset",this,e);return n/2+Math.abs(i)+ss.translateDistance(this.paint.get("line-translate"))},e.prototype.queryIntersectsFeature=function(t,e,n,i,a,o,s){var u=ss.translate(t,this.paint.get("line-translate"),this.paint.get("line-translate-anchor"),0,s),l=r(this.paint.get("line-width").evaluate(e,n),this.paint.get("line-gap-width").evaluate(e,n)),p=s/2*(l=Math.max(l,5)),c=this.paint.get("line-offset").evaluate(e,n);return c&&(i=function(t,e){for(var r=[],n=new Gi(0,0),i=0;i<t.length;i++){for(var a=t[i],o=[],s=0;s<a.length;s++){var u=a[s-1],l=a[s],p=a[s+1],c=0===s?n:l.sub(u)._unit()._perp(),f=s===a.length-1?n:p.sub(l)._unit()._perp(),h=c._add(f)._unit(),d=h.x*f.x+h.y*f.y;h._mult(1/d),o.push(h._mult(e)._add(l))}r.push(o)}return r}(i,c*s)),$s.polygonIntersectsBufferedMultiLine(u,i,p)},e.prototype.isTileClipped=function(){return!0},e}(ds);function Iu(){}function ku(t){var e={},r={},n=[],i=0;function a(e){n.push(t[e]),i++}function o(t,e,i){var a=r[t];return delete r[t],r[e]=a,n[a].geometry[0].pop(),n[a].geometry[0]=n[a].geometry[0].concat(i[0]),a}function s(t,r,i){var a=e[r];return delete e[r],e[t]=a,n[a].geometry[0].shift(),n[a].geometry[0]=i[0].concat(n[a].geometry[0]),a}function u(t,e,r){var n=r?e[0][e[0].length-1]:e[0][0];return t+":"+n.x+":"+n.y}for(var l=0;l<t.length;l++){var p=t[l],c=p.geometry,f=p.text?p.text.toString():null;if(f){var h=u(f,c),d=u(f,c,!0);if(h in r&&d in e&&r[h]!==e[d]){var y=s(h,d,c),m=o(h,d,n[y].geometry);delete e[h],delete r[d],r[u(f,n[m].geometry,!0)]=m,n[y].geometry=null}else h in r?o(h,d,c):d in e?s(h,d,c):(a(l),e[h]=i-1,r[d]=i-1)}else a(l)}return n.filter((function(t){return t.geometry}))}Iu.symbolLayoutAttributes=P([{name:"a_pos_offset",components:4,type:"Int16"},{name:"a_data",components:4,type:"Uint16"},{name:"a_pixeloffset",components:4,type:"Int16"}],4),Iu.dynamicLayoutAttributes=P([{name:"a_projected_pos",components:3,type:"Float32"}],4),Iu.placementOpacityAttributes=P([{name:"a_fade_opacity",components:1,type:"Uint32"}],4),Iu.collisionVertexAttributes=P([{name:"a_placed",components:2,type:"Uint8"},{name:"a_shift",components:2,type:"Float32"}]),Iu.collisionBox=P([{type:"Int16",name:"anchorPointX"},{type:"Int16",name:"anchorPointY"},{type:"Int16",name:"x1"},{type:"Int16",name:"y1"},{type:"Int16",name:"x2"},{type:"Int16",name:"y2"},{type:"Uint32",name:"featureIndex"},{type:"Uint16",name:"sourceLayerIndex"},{type:"Uint16",name:"bucketIndex"},{type:"Int16",name:"radius"},{type:"Int16",name:"signedDistanceFromAnchor"}]),Iu.collisionBoxLayout=P([{name:"a_pos",components:2,type:"Int16"},{name:"a_anchor_pos",components:2,type:"Int16"},{name:"a_extrude",components:2,type:"Int16"}],4),Iu.collisionCircleLayout=P([{name:"a_pos",components:2,type:"Int16"},{name:"a_anchor_pos",components:2,type:"Int16"},{name:"a_extrude",components:2,type:"Int16"}],4),Iu.placement=P([{type:"Int16",name:"anchorX"},{type:"Int16",name:"anchorY"},{type:"Uint16",name:"glyphStartIndex"},{type:"Uint16",name:"numGlyphs"},{type:"Uint32",name:"vertexStartIndex"},{type:"Uint32",name:"lineStartIndex"},{type:"Uint32",name:"lineLength"},{type:"Uint16",name:"segment"},{type:"Uint16",name:"lowerSize"},{type:"Uint16",name:"upperSize"},{type:"Float32",name:"lineOffsetX"},{type:"Float32",name:"lineOffsetY"},{type:"Uint8",name:"writingMode"},{type:"Uint8",name:"placedOrientation"},{type:"Uint8",name:"hidden"},{type:"Uint32",name:"crossTileID"},{type:"Int16",name:"associatedIconIndex"}]),Iu.symbolInstance=P([{type:"Int16",name:"anchorX"},{type:"Int16",name:"anchorY"},{type:"Int16",name:"rightJustifiedTextSymbolIndex"},{type:"Int16",name:"centerJustifiedTextSymbolIndex"},{type:"Int16",name:"leftJustifiedTextSymbolIndex"},{type:"Int16",name:"verticalPlacedTextSymbolIndex"},{type:"Int16",name:"placedIconSymbolIndex"},{type:"Int16",name:"verticalPlacedIconSymbolIndex"},{type:"Uint16",name:"key"},{type:"Uint16",name:"textBoxStartIndex"},{type:"Uint16",name:"textBoxEndIndex"},{type:"Uint16",name:"verticalTextBoxStartIndex"},{type:"Uint16",name:"verticalTextBoxEndIndex"},{type:"Uint16",name:"iconBoxStartIndex"},{type:"Uint16",name:"iconBoxEndIndex"},{type:"Uint16",name:"verticalIconBoxStartIndex"},{type:"Uint16",name:"verticalIconBoxEndIndex"},{type:"Uint16",name:"featureIndex"},{type:"Uint16",name:"numHorizontalGlyphVertices"},{type:"Uint16",name:"numVerticalGlyphVertices"},{type:"Uint16",name:"numIconVertices"},{type:"Uint16",name:"numVerticalIconVertices"},{type:"Uint32",name:"crossTileID"},{type:"Float32",name:"textBoxScale"},{type:"Float32",components:2,name:"textOffset"}]),Iu.glyphOffset=P([{type:"Float32",name:"offsetX"}]),Iu.lineVertex=P([{type:"Int16",name:"x"},{type:"Int16",name:"y"},{type:"Int16",name:"tileUnitDistanceFromAnchor"}]);var Mu=function(){};function Fu(t){for(var e=0,r=t;e<r.length;e+=1){if(0!==r[e].positionedGlyphs.length)return!1}return!0}Mu.WritingMode={horizontal:1,vertical:2,horizontalOnly:3};var Ou=57344,Pu=63743,zu=function(){this.scale=1,this.fontStack="",this.imageName=null};zu.forText=function(t,e){var r=new zu;return r.scale=t||1,r.fontStack=e,r},zu.forImage=function(t){var e=new zu;return e.imageName=t,e};var Ru=function(){this.text="",this.sectionIndex=[],this.sections=[],this.imageSectionID=null};function Cu(t,e){for(var r=[],n=t.text,i=0,a=0,o=e;a<o.length;a+=1){var s=o[a];r.push(t.substring(i,s)),i=s}return i<n.length&&r.push(t.substring(i,n.length)),r}Ru.fromFeature=function(t,e){for(var r=new Ru,n=0;n<t.sections.length;n++){var i=t.sections[n];i.image?r.addImageSection(i):r.addTextSection(i,e)}return r},Ru.prototype.length=function(){return this.text.length},Ru.prototype.getSection=function(t){return this.sections[this.sectionIndex[t]]},Ru.prototype.getSectionIndex=function(t){return this.sectionIndex[t]},Ru.prototype.getCharCode=function(t){return this.text.charCodeAt(t)},Ru.prototype.verticalizePunctuation=function(){},Ru.prototype.trim=function(){for(var t=0,e=0;e<this.text.length&&Bu[this.text.charCodeAt(e)];e++)t++;for(var r=this.text.length,n=this.text.length-1;n>=0&&n>=t&&Bu[this.text.charCodeAt(n)];n--)r--;this.text=this.text.substring(t,r),this.sectionIndex=this.sectionIndex.slice(t,r)},Ru.prototype.substring=function(t,e){var r=new Ru;return r.text=this.text.substring(t,e),r.sectionIndex=this.sectionIndex.slice(t,e),r.sections=this.sections,r},Ru.prototype.toString=function(){return this.text},Ru.prototype.getMaxScale=function(){var t=this;return this.sectionIndex.reduce((function(e,r){return Math.max(e,t.sections[r].scale)}),0)},Ru.prototype.addTextSection=function(t,e){this.text+=t.text,this.sections.push(zu.forText(t.scale,t.fontStack||e));for(var r=this.sections.length-1,n=0;n<t.text.length;++n)this.sectionIndex.push(r)},Ru.prototype.addImageSection=function(t){var e=t.image?t.image.name:"";if(0!==e.length){var r=this.getNextImageSectionCharCode();r&&(this.text+=String.fromCharCode(r),this.sections.push(zu.forImage(e)),this.sectionIndex.push(this.sections.length-1))}},Ru.prototype.getNextImageSectionCharCode=function(){return this.imageSectionID?this.imageSectionID>=Pu?null:++this.imageSectionID:(this.imageSectionID=Ou,this.imageSectionID)},Mu.shapeText=function(t,e,r,n,i,a,o,s,u,l,p,c,f,h,d,y){var m=Ru.fromFeature(t,i);c===Mu.WritingMode.vertical&&m.verticalizePunctuation(),Cu(m,void 0);var v=[],g={positionedLines:v,text:m.toString(),top:p[1],bottom:p[1],left:p[0],right:p[0],writingMode:c,iconsInText:!1,verticalizable:!1};return!Fu(v)&&g};var Bu={};function Du(t){var e=.5,r=.5;switch(t){case"right":case"top-right":case"bottom-right":e=1;break;case"left":case"top-left":case"bottom-left":e=0}switch(t){case"bottom":case"bottom-right":case"bottom-left":r=1;break;case"top":case"top-right":case"top-left":r=0}return{horizontalAlign:e,verticalAlign:r}}Bu[9]=!0,Bu[10]=!0,Bu[11]=!0,Bu[12]=!0,Bu[13]=!0,Bu[32]=!0,Mu.shapeIcon=function(t,e,r){var n=Du(r),i=n.horizontalAlign,a=n.verticalAlign,o=e[0],s=e[1],u=o-t.displaySize[0]*i,l=u+t.displaySize[0],p=s-t.displaySize[1]*a;return{image:t,top:p,bottom:p+t.displaySize[1],left:u,right:l}},Mu.fitIconToText=function(t,e,r,n,i,a){var o,s=t.image;if(s.content){var u=s.content,l=s.pixelRatio||1;o=[u[0]/l,u[1]/l,s.displaySize[0]-u[2]/l,s.displaySize[1]-u[3]/l]}var p,c,f,h,d=e.left*a,y=e.right*a;"width"===r||"both"===r?(h=i[0]+d-n[3],c=i[0]+y+n[1]):c=(h=i[0]+(d+y-s.displaySize[0])/2)+s.displaySize[0];var m=e.top*a,v=e.bottom*a;return"height"===r||"both"===r?(p=i[1]+m-n[0],f=i[1]+v+n[2]):f=(p=i[1]+(m+v-s.displaySize[1])/2)+s.displaySize[1],{image:s,top:p,right:c,bottom:f,left:h,collisionPadding:o}};var Vu=128;function Uu(t,e){var r=e.expression;if("constant"===r.kind)return{kind:"constant",layoutSize:r.evaluate(new ar(t+1))};if("source"===r.kind)return{kind:"source"};for(var n=r.zoomStops,i=r.interpolationType,a=0;a<n.length&&n[a]<=t;)a++;for(var o=a=Math.max(0,a-1);o<n.length&&n[o]<t+1;)o++;o=Math.min(n.length-1,o);var s=n[a],u=n[o];return"composite"===r.kind?{kind:"composite",minZoom:s,maxZoom:u,interpolationType:i}:{kind:"camera",minZoom:s,maxZoom:u,minSize:r.evaluate(new ar(s)),maxSize:r.evaluate(new ar(u)),interpolationType:i}}function Nu(t,e,r){var n=e.uSize,i=e.uSizeT,a=r.lowerSize,o=r.upperSize;return"source"===t.kind?a/Vu:"composite"===t.kind?number(a/Vu,o/Vu,i):n}function Lu(t,e){var r=0,n=0;if("constant"===t.kind)n=t.layoutSize;else if("source"!==t.kind){t.interpolationType,t.minZoom,t.maxZoom;"camera"===t.kind?n=number(t.minSize,t.maxSize,0):r=0}return{uSizeT:r,uSize:n}}var Xu=Object.freeze({__proto__:null,getSizeData:Uu,evaluateSizeForFeature:Nu,evaluateSizeForZoom:Lu,SIZE_PACK_FACTOR:Vu});function qu(t,e,r){var n=e.layout.get("text-transform").evaluate(r,{});return"uppercase"===n?t=t.toLocaleUpperCase():"lowercase"===n&&(t=t.toLocaleLowerCase()),t}function ju(t,e,r){return t.sections.forEach((function(t){t.text=qu(t.text,e,r)})),t}var Hu={"Latin-1 Supplement":function(t){return t>=128&&t<=255},Arabic:function(t){return t>=1536&&t<=1791},"Arabic Supplement":function(t){return t>=1872&&t<=1919},"Arabic Extended-A":function(t){return t>=2208&&t<=2303},"Hangul Jamo":function(t){return t>=4352&&t<=4607},"Unified Canadian Aboriginal Syllabics":function(t){return t>=5120&&t<=5759},Khmer:function(t){return t>=6016&&t<=6143},"Unified Canadian Aboriginal Syllabics Extended":function(t){return t>=6320&&t<=6399},"General Punctuation":function(t){return t>=8192&&t<=8303},"Letterlike Symbols":function(t){return t>=8448&&t<=8527},"Number Forms":function(t){return t>=8528&&t<=8591},"Miscellaneous Technical":function(t){return t>=8960&&t<=9215},"Control Pictures":function(t){return t>=9216&&t<=9279},"Optical Character Recognition":function(t){return t>=9280&&t<=9311},"Enclosed Alphanumerics":function(t){return t>=9312&&t<=9471},"Geometric Shapes":function(t){return t>=9632&&t<=9727},"Miscellaneous Symbols":function(t){return t>=9728&&t<=9983},"Miscellaneous Symbols and Arrows":function(t){return t>=11008&&t<=11263},"CJK Radicals Supplement":function(t){return t>=11904&&t<=12031},"Kangxi Radicals":function(t){return t>=12032&&t<=12255},"Ideographic Description Characters":function(t){return t>=12272&&t<=12287},"CJK Symbols and Punctuation":function(t){return t>=12288&&t<=12351},Hiragana:function(t){return t>=12352&&t<=12447},Katakana:function(t){return t>=12448&&t<=12543},Bopomofo:function(t){return t>=12544&&t<=12591},"Hangul Compatibility Jamo":function(t){return t>=12592&&t<=12687},Kanbun:function(t){return t>=12688&&t<=12703},"Bopomofo Extended":function(t){return t>=12704&&t<=12735},"CJK Strokes":function(t){return t>=12736&&t<=12783},"Katakana Phonetic Extensions":function(t){return t>=12784&&t<=12799},"Enclosed CJK Letters and Months":function(t){return t>=12800&&t<=13055},"CJK Compatibility":function(t){return t>=13056&&t<=13311},"CJK Unified Ideographs Extension A":function(t){return t>=13312&&t<=19903},"Yijing Hexagram Symbols":function(t){return t>=19904&&t<=19967},"CJK Unified Ideographs":function(t){return t>=19968&&t<=40959},"Yi Syllables":function(t){return t>=40960&&t<=42127},"Yi Radicals":function(t){return t>=42128&&t<=42191},"Hangul Jamo Extended-A":function(t){return t>=43360&&t<=43391},"Hangul Syllables":function(t){return t>=44032&&t<=55215},"Hangul Jamo Extended-B":function(t){return t>=55216&&t<=55295},"Private Use Area":function(t){return t>=57344&&t<=63743},"CJK Compatibility Ideographs":function(t){return t>=63744&&t<=64255},"Arabic Presentation Forms-A":function(t){return t>=64336&&t<=65023},"Vertical Forms":function(t){return t>=65040&&t<=65055},"CJK Compatibility Forms":function(t){return t>=65072&&t<=65103},"Small Form Variants":function(t){return t>=65104&&t<=65135},"Arabic Presentation Forms-B":function(t){return t>=65136&&t<=65279},"Halfwidth and Fullwidth Forms":function(t){return t>=65280&&t<=65519}},Yu=function(){};function Qu(t){return!Hu.Arabic(t)&&(!Hu["Arabic Supplement"](t)&&(!Hu["Arabic Extended-A"](t)&&(!Hu["Arabic Presentation Forms-A"](t)&&!Hu["Arabic Presentation Forms-B"](t))))}function Ku(t){return 746===t||747===t||!(t<4352)&&(!!Hu["Bopomofo Extended"](t)||(!!Hu.Bopomofo(t)||(!(!Hu["CJK Compatibility Forms"](t)||t>=65097&&t<=65103)||(!!Hu["CJK Compatibility Ideographs"](t)||(!!Hu["CJK Compatibility"](t)||(!!Hu["CJK Radicals Supplement"](t)||(!!Hu["CJK Strokes"](t)||(!(!Hu["CJK Symbols and Punctuation"](t)||t>=12296&&t<=12305||t>=12308&&t<=12319||12336===t)||(!!Hu["CJK Unified Ideographs Extension A"](t)||(!!Hu["CJK Unified Ideographs"](t)||(!!Hu["Enclosed CJK Letters and Months"](t)||(!!Hu["Hangul Compatibility Jamo"](t)||(!!Hu["Hangul Jamo Extended-A"](t)||(!!Hu["Hangul Jamo Extended-B"](t)||(!!Hu["Hangul Jamo"](t)||(!!Hu["Hangul Syllables"](t)||(!!Hu.Hiragana(t)||(!!Hu["Ideographic Description Characters"](t)||(!!Hu.Kanbun(t)||(!!Hu["Kangxi Radicals"](t)||(!!Hu["Katakana Phonetic Extensions"](t)||(!(!Hu.Katakana(t)||12540===t)||(!(!Hu["Halfwidth and Fullwidth Forms"](t)||65288===t||65289===t||65293===t||t>=65306&&t<=65310||65339===t||65341===t||65343===t||t>=65371&&t<=65503||65507===t||t>=65512&&t<=65519)||(!(!Hu["Small Form Variants"](t)||t>=65112&&t<=65118||t>=65123&&t<=65126)||(!!Hu["Unified Canadian Aboriginal Syllabics"](t)||(!!Hu["Unified Canadian Aboriginal Syllabics Extended"](t)||(!!Hu["Vertical Forms"](t)||(!!Hu["Yijing Hexagram Symbols"](t)||(!!Hu["Yi Syllables"](t)||!!Hu["Yi Radicals"](t))))))))))))))))))))))))))))))}function Gu(t){return t>=1424&&t<=2303||Hu["Arabic Presentation Forms-A"](t)||Hu["Arabic Presentation Forms-B"](t)}Yu.allowsVerticalWritingMode=function(t){for(var e=0,r=t;e<r.length;e+=1){if(Ku(r[e].charCodeAt(0)))return!0}return!1},Yu.allowsLetterSpacing=function(t){for(var e=0,r=t;e<r.length;e+=1){if(!Qu(r[e].charCodeAt(0)))return!1}return!0},Yu.stringContainsRTLText=function(t){for(var e=0,r=t;e<r.length;e+=1){if(Gu(r[e].charCodeAt(0)))return!0}return!1};var Wu=["Unknown","Point","LineString","Polygon"],Ju=[{name:"a_fade_opacity",components:1,type:"Uint8",offset:0}];function Zu(t,e,r,n,i,a,o,s,u,l,p,c,f){var h=s?Math.min(MAX_PACKED_SIZE,Math.round(s[0])):0,d=s?Math.min(MAX_PACKED_SIZE,Math.round(s[1])):0;t.emplaceBack(e,r,Math.round(32*n),Math.round(32*i),a,o,(h<<1)+(u?1:0),d,16*l,16*p,256*c,256*f)}function $u(t,e,r){t.emplaceBack(e.x,e.y,r),t.emplaceBack(e.x,e.y,r),t.emplaceBack(e.x,e.y,r),t.emplaceBack(e.x,e.y,r)}function tl(t){for(var e=0,r=t.sections;e<r.length;e+=1){var n=r[e];if(Yu.stringContainsRTLText(n.text))return!0}return!1}var el=function(t){this.layoutVertexArray=new StructArrayLayout4i4ui4i24,this.indexArray=new mr,this.programConfigurations=t,this.segments=new cr,this.dynamicLayoutVertexArray=new StructArrayLayout3f12,this.opacityVertexArray=new StructArrayLayout1ul4,this.placedSymbolArray=new PlacedSymbolArray};el.prototype.upload=function(t,e,r,n){r&&(this.layoutVertexBuffer=t.createVertexBuffer(this.layoutVertexArray,Iu.symbolLayoutAttributes.members),this.indexBuffer=t.createIndexBuffer(this.indexArray,e),this.dynamicLayoutVertexBuffer=t.createVertexBuffer(this.dynamicLayoutVertexArray,Iu.dynamicLayoutAttributes.members,!0),this.opacityVertexBuffer=t.createVertexBuffer(this.opacityVertexArray,Ju,!0),this.opacityVertexBuffer.itemSize=1),(r||n)&&this.programConfigurations.upload(t)},el.prototype.destroy=function(){this.layoutVertexBuffer&&(this.layoutVertexBuffer.destroy(),this.indexBuffer.destroy(),this.programConfigurations.destroy(),this.segments.destroy(),this.dynamicLayoutVertexBuffer.destroy(),this.opacityVertexBuffer.destroy())},el.prototype.clear=function(){},tr.register("SymbolBuffers",el);var rl=function(t,e,r){this.layoutVertexArray=new t,this.layoutAttributes=e,this.indexArray=new r,this.segments=new cr,this.collisionVertexArray=new StructArrayLayout2ub2f12};rl.prototype.upload=function(t){this.layoutVertexBuffer=t.createVertexBuffer(this.layoutVertexArray,this.layoutAttributes),this.indexBuffer=t.createIndexBuffer(this.indexArray),this.collisionVertexBuffer=t.createVertexBuffer(this.collisionVertexArray,collisionVertexAttributes.members,!0)},rl.prototype.destroy=function(){this.layoutVertexBuffer&&(this.layoutVertexBuffer.destroy(),this.indexBuffer.destroy(),this.segments.destroy(),this.collisionVertexBuffer.destroy())},tr.register("CollisionBuffers",rl);var nl=function(t){this.collisionBoxArray=t.collisionBoxArray,this.zoom=t.zoom,this.overscaling=t.overscaling,this.layers=t.layers,this.layerIds=this.layers.map((function(t){return t.id})),this.index=t.index,this.pixelRatio=t.pixelRatio,this.sourceLayerIndex=t.sourceLayerIndex,this.hasPattern=!1,this.hasPaintOverrides=!1,this.hasRTLText=!1;var e=this.layers[0]._unevaluatedLayout._values;this.textSizeData=Xu.getSizeData(this.zoom,e["text-size"]),this.iconSizeData=Xu.getSizeData(this.zoom,e["icon-size"]);var r=this.layers[0].layout,n=r.get("symbol-sort-key"),i=r.get("symbol-z-order");this.sortFeaturesByKey="viewport-y"!==i&&void 0!==n.constantOr(1);var a="viewport-y"===i||"auto"===i&&!this.sortFeaturesByKey;this.sortFeaturesByY=a&&(r.get("text-allow-overlap")||r.get("icon-allow-overlap")||r.get("text-ignore-placement")||r.get("icon-ignore-placement")),"point"===r.get("symbol-placement")&&(this.writingModes=r.get("text-writing-mode").map((function(t){return Mu.WritingMode[t]}))),this.stateDependentLayerIds=this.layers.filter((function(t){return t.isStateDependent()})).map((function(t){return t.id})),this.sourceID=t.sourceID};nl.prototype.createArrays=function(){},nl.prototype.calculateGlyphDependencies=function(t,e,r,n,i){for(var a=0;a<t.length;a++)e[t.charCodeAt(a)]=!0},nl.prototype.populate=function(t,e){var r=this.layers[0],n=r.layout,i=n.get("text-font"),a=n.get("text-field"),o=n.get("icon-image"),s=("constant"!==a.value.kind||a.value.value instanceof dt&&!a.value.value.isEmpty()||a.value.value.toString().length>0)&&("constant"!==i.value.kind||i.value.value.length>0),u=("constant"!==o.value.kind||!!o.value.value)&&Object.keys(o.parameters).length>0,l=n.get("symbol-sort-key");if(this.features=[],s||u){for(var p=e.iconDependencies,c=e.glyphDependencies,f=e.availableImages,h=new ar(this.zoom),d=0,y=t;d<y.length;d+=1){var m=y[d],v=m.feature,g=m.index,x=m.sourceLayerIndex;if(r._featureFilter(h,v)){var b=void 0;if(s){var _=r.getValueAndResolveTokens("text-field",v,f),w=dt.factory(_);tl(w)&&(this.hasRTLText=!0),(!this.hasRTLText||"unavailable"===getRTLTextPluginStatus()||this.hasRTLText&&plugin.isParsed())&&(b=ju(w,r,v))}var E=void 0;if(u){var T=r.getValueAndResolveTokens("icon-image",v,f);E=T instanceof yt?T:yt.fromString(T)}if(b||E){var A=this.sortFeaturesByKey?l.evaluate(v,{}):void 0,S={text:b,icon:E,index:g,sourceLayerIndex:x,geometry:pr(v),properties:v.properties,type:Wu[v.type],sortKey:A};if(void 0!==v.id&&(S.id=v.id),this.features.push(S),E&&(p[E.name]=!0),b){var I=i.evaluate(v,{}).join(","),k="map"===n.get("text-rotation-alignment")&&"point"!==n.get("symbol-placement");this.allowVerticalPlacement=this.writingModes&&this.writingModes.indexOf(Mu.WritingMode.vertical)>=0;for(var M=0,F=b.sections;M<F.length;M+=1){var O=F[M];if(O.image)p[O.image.name]=!0;else{var P=Yu.allowsVerticalWritingMode(b.toString()),z=O.fontStack||I,R=c[z]=c[z]||{};this.calculateGlyphDependencies(O.text,R,k,this.allowVerticalPlacement,P)}}}}}}"line"===n.get("symbol-placement")&&(this.features=ku(this.features)),this.sortFeaturesByKey&&this.features.sort((function(t,e){return t.sortKey-e.sortKey}))}},nl.prototype.update=function(t,e,r){this.stateDependentLayers.length&&(this.text.programConfigurations.updatePaintArrays(t,e,this.layers,r),this.icon.programConfigurations.updatePaintArrays(t,e,this.layers,r))},nl.prototype.isEmpty=function(){return 0===this.symbolInstances.length&&!this.hasRTLText},nl.prototype.uploadPending=function(){return!this.uploaded||this.text.programConfigurations.needsUpload||this.icon.programConfigurations.needsUpload},nl.prototype.upload=function(t){},nl.prototype.destroy=function(){},nl.prototype.clear=function(){},nl.prototype.addToLineVertexArray=function(t,e){var r=this.lineVertexArray.length;if(void 0!==t.segment){for(var n=t.dist(e[t.segment+1]),i=t.dist(e[t.segment]),a={},o=t.segment+1;o<e.length;o++)a[o]={x:e[o].x,y:e[o].y,tileUnitDistanceFromAnchor:n},o<e.length-1&&(n+=e[o+1].dist(e[o]));for(var s=t.segment||0;s>=0;s--)a[s]={x:e[s].x,y:e[s].y,tileUnitDistanceFromAnchor:i},s>0&&(i+=e[s-1].dist(e[s]));for(var u=0;u<e.length;u++){var l=a[u];this.lineVertexArray.emplaceBack(l.x,l.y,l.tileUnitDistanceFromAnchor)}}return{lineStartIndex:r,lineLength:this.lineVertexArray.length-r}},nl.prototype.addSymbols=function(t,e,r,n,i,a,o,s,u,l,p){var c=this,f=t.indexArray,h=t.layoutVertexArray,d=t.dynamicLayoutVertexArray,y=t.segments.prepareSegment(4*e.length,t.layoutVertexArray,t.indexArray,a.sortKey),m=this.glyphOffsetArray.length,v=y.vertexLength,g=this.allowVerticalPlacement&&o===Mu.WritingMode.vertical?Math.PI/2:0,x=function(t){var e=t.tl,n=t.tr,i=t.bl,a=t.br,o=t.tex,u=t.pixelOffsetTL,l=t.pixelOffsetBR,p=t.minFontScaleX,m=t.minFontScaleY,v=y.vertexLength,x=t.glyphOffset[1];Zu(h,s.x,s.y,e.x,x+e.y,o.x,o.y,r,t.isSDF,u.x,u.y,p,m),Zu(h,s.x,s.y,n.x,x+n.y,o.x+o.w,o.y,r,t.isSDF,l.x,u.y,p,m),Zu(h,s.x,s.y,i.x,x+i.y,o.x,o.y+o.h,r,t.isSDF,u.x,l.y,p,m),Zu(h,s.x,s.y,a.x,x+a.y,o.x+o.w,o.y+o.h,r,t.isSDF,l.x,l.y,p,m),$u(d,s,g),f.emplaceBack(v,v+1,v+2),f.emplaceBack(v+1,v+2,v+3),y.vertexLength+=4,y.primitiveLength+=2,c.glyphOffsetArray.emplaceBack(t.glyphOffset[0])};if(a.text&&a.text.sections){var b=a.text.sections;if(this.hasPaintOverrides){for(var _,w=function(e,r){void 0===_||_===e&&!r||t.programConfigurations.populatePaintArrays(t.layoutVertexArray.length,a,a.index,{},b[_]),_=e},E=0,T=e;E<T.length;E+=1){var A=T[E];w(A.sectionIndex,!1),x(A)}w(_,!0)}else{for(var S=0,I=e;S<I.length;S+=1){x(I[S])}t.programConfigurations.populatePaintArrays(t.layoutVertexArray.length,a,a.index,{},b[0])}}else{for(var k=0,M=e;k<M.length;k+=1){x(M[k])}t.programConfigurations.populatePaintArrays(t.layoutVertexArray.length,a,a.index,{})}t.placedSymbolArray.emplaceBack(s.x,s.y,m,this.glyphOffsetArray.length-m,v,u,l,s.segment,r?r[0]:0,r?r[1]:0,n[0],n[1],o,0,!1,0,p)},nl.prototype._addCollisionDebugVertex=function(t,e,r,n,i,a){return e.emplaceBack(0,0),t.emplaceBack(r.x,r.y,n,i,Math.round(a.x),Math.round(a.y))},nl.prototype.addCollisionDebugVertices=function(t,e,r,n,i,a,o,s){var u=i.segments.prepareSegment(4,i.layoutVertexArray,i.indexArray),l=u.vertexLength,p=i.layoutVertexArray,c=i.collisionVertexArray,f=o.anchorX,h=o.anchorY;if(this._addCollisionDebugVertex(p,c,a,f,h,new pointGeometry(t,e)),this._addCollisionDebugVertex(p,c,a,f,h,new pointGeometry(r,e)),this._addCollisionDebugVertex(p,c,a,f,h,new pointGeometry(r,n)),this._addCollisionDebugVertex(p,c,a,f,h,new pointGeometry(t,n)),u.vertexLength+=4,s){var d=i.indexArray;d.emplaceBack(l,l+1,l+2),d.emplaceBack(l,l+2,l+3),u.primitiveLength+=2}else{var y=i.indexArray;y.emplaceBack(l,l+1),y.emplaceBack(l+1,l+2),y.emplaceBack(l+2,l+3),y.emplaceBack(l+3,l),u.primitiveLength+=4}},nl.prototype.addDebugCollisionBoxes=function(t,e,r,n){for(var i=t;i<e;i++){var a=this.collisionBoxArray.get(i),o=a.x1,s=a.y1,u=a.x2,l=a.y2,p=a.radius>0;this.addCollisionDebugVertices(o,s,u,l,p?n?this.textCollisionCircle:this.iconCollisionCircle:n?this.textCollisionBox:this.iconCollisionBox,a.anchorPoint,r,p)}},nl.prototype.generateCollisionDebugBuffers=function(){for(var t=0;t<this.symbolInstances.length;t++){var e=this.symbolInstances.get(t);this.addDebugCollisionBoxes(e.textBoxStartIndex,e.textBoxEndIndex,e,!0),this.addDebugCollisionBoxes(e.verticalTextBoxStartIndex,e.verticalTextBoxEndIndex,e,!0),this.addDebugCollisionBoxes(e.iconBoxStartIndex,e.iconBoxEndIndex,e,!1),this.addDebugCollisionBoxes(e.verticalIconBoxStartIndex,e.verticalIconBoxEndIndex,e,!1)}},nl.prototype._deserializeCollisionBoxesForSymbol=function(t,e,r,n,i,a,o,s,u){for(var l={},p=e;p<r;p++){var c=t.get(p);if(0===c.radius){l.textBox={x1:c.x1,y1:c.y1,x2:c.x2,y2:c.y2,anchorPointX:c.anchorPointX,anchorPointY:c.anchorPointY},l.textFeatureIndex=c.featureIndex;break}l.textCircles||(l.textCircles=[],l.textFeatureIndex=c.featureIndex);l.textCircles.push(c.anchorPointX,c.anchorPointY,c.radius,c.signedDistanceFromAnchor,1)}for(var f=n;f<i;f++){var h=t.get(f);if(0===h.radius){l.verticalTextBox={x1:h.x1,y1:h.y1,x2:h.x2,y2:h.y2,anchorPointX:h.anchorPointX,anchorPointY:h.anchorPointY},l.verticalTextFeatureIndex=h.featureIndex;break}}for(var d=a;d<o;d++){var y=t.get(d);if(0===y.radius){l.iconBox={x1:y.x1,y1:y.y1,x2:y.x2,y2:y.y2,anchorPointX:y.anchorPointX,anchorPointY:y.anchorPointY},l.iconFeatureIndex=y.featureIndex;break}}for(var m=s;m<u;m++){var v=t.get(m);if(0===v.radius){l.verticalIconBox={x1:v.x1,y1:v.y1,x2:v.x2,y2:v.y2,anchorPointX:v.anchorPointX,anchorPointY:v.anchorPointY},l.verticalIconFeatureIndex=v.featureIndex;break}}return l},nl.prototype.deserializeCollisionBoxes=function(t){this.collisionArrays=[];for(var e=0;e<this.symbolInstances.length;e++){var r=this.symbolInstances.get(e);this.collisionArrays.push(this._deserializeCollisionBoxesForSymbol(t,r.textBoxStartIndex,r.textBoxEndIndex,r.verticalTextBoxStartIndex,r.verticalTextBoxEndIndex,r.iconBoxStartIndex,r.iconBoxEndIndex,r.verticalIconBoxStartIndex,r.verticalIconBoxEndIndex))}},nl.prototype.hasTextData=function(){return this.text.segments.get().length>0},nl.prototype.hasIconData=function(){return this.icon.segments.get().length>0},nl.prototype.hasTextCollisionBoxData=function(){return this.textCollisionBox.segments.get().length>0},nl.prototype.hasIconCollisionBoxData=function(){return this.iconCollisionBox.segments.get().length>0},nl.prototype.hasTextCollisionCircleData=function(){return this.textCollisionCircle.segments.get().length>0},nl.prototype.hasIconCollisionCircleData=function(){return this.iconCollisionCircle.segments.get().length>0},nl.prototype.addIndicesForPlacedSymbol=function(t,e){for(var r=t.placedSymbolArray.get(e),n=r.vertexStartIndex+4*r.numGlyphs,i=r.vertexStartIndex;i<n;i+=4)t.indexArray.emplaceBack(i,i+1,i+2),t.indexArray.emplaceBack(i+1,i+2,i+3)},nl.prototype.getSortedSymbolIndexes=function(t){if(this.sortedAngle===t&&void 0!==this.symbolInstanceIndexes)return this.symbolInstanceIndexes;for(var e=Math.sin(t),r=Math.cos(t),n=[],i=[],a=[],o=0;o<this.symbolInstances.length;++o){a.push(o);var s=this.symbolInstances.get(o);n.push(0|Math.round(e*s.anchorX+r*s.anchorY)),i.push(s.featureIndex)}return a.sort((function(t,e){return n[t]-n[e]||i[e]-i[t]})),a},nl.prototype.sortFeatures=function(t){var e=this;if(this.sortFeaturesByY&&this.sortedAngle!==t&&!(this.text.segments.get().length>1||this.icon.segments.get().length>1)){this.symbolInstanceIndexes=this.getSortedSymbolIndexes(t),this.sortedAngle=t,this.text.indexArray.clear(),this.icon.indexArray.clear(),this.featureSortOrder=[];for(var r=0,n=this.symbolInstanceIndexes;r<n.length;r+=1){var i=n[r],a=this.symbolInstances.get(i);this.featureSortOrder.push(a.featureIndex),[a.rightJustifiedTextSymbolIndex,a.centerJustifiedTextSymbolIndex,a.leftJustifiedTextSymbolIndex].forEach((function(t,r,n){t>=0&&n.indexOf(t)===r&&e.addIndicesForPlacedSymbol(e.text,t)})),a.verticalPlacedTextSymbolIndex>=0&&this.addIndicesForPlacedSymbol(this.text,a.verticalPlacedTextSymbolIndex),a.placedIconSymbolIndex>=0&&this.addIndicesForPlacedSymbol(this.icon,a.placedIconSymbolIndex),a.verticalPlacedIconSymbolIndex>=0&&this.addIndicesForPlacedSymbol(this.icon,a.verticalPlacedIconSymbolIndex)}this.text.indexBuffer&&this.text.indexBuffer.updateData(this.text.indexArray),this.icon.indexBuffer&&this.icon.indexBuffer.updateData(this.icon.indexArray)}},tr.register("SymbolBucket",nl,{omit:["layers","collisionBoxArray","compareText"]}),nl.MAX_GLYPHS=65535,nl.addDynamicAttributes=$u;var il={kind:"color"},al={kind:"formatted"},ol=new os({"symbol-placement":new ts(Zs.layout_symbol["symbol-placement"]),"symbol-spacing":new ts(Zs.layout_symbol["symbol-spacing"]),"symbol-avoid-edges":new ts(Zs.layout_symbol["symbol-avoid-edges"]),"symbol-sort-key":new es(Zs.layout_symbol["symbol-sort-key"]),"symbol-z-order":new ts(Zs.layout_symbol["symbol-z-order"]),"icon-allow-overlap":new ts(Zs.layout_symbol["icon-allow-overlap"]),"icon-ignore-placement":new ts(Zs.layout_symbol["icon-ignore-placement"]),"icon-optional":new ts(Zs.layout_symbol["icon-optional"]),"icon-rotation-alignment":new ts(Zs.layout_symbol["icon-rotation-alignment"]),"icon-size":new es(Zs.layout_symbol["icon-size"]),"icon-text-fit":new ts(Zs.layout_symbol["icon-text-fit"]),"icon-text-fit-padding":new ts(Zs.layout_symbol["icon-text-fit-padding"]),"icon-image":new es(Zs.layout_symbol["icon-image"]),"icon-rotate":new es(Zs.layout_symbol["icon-rotate"]),"icon-padding":new ts(Zs.layout_symbol["icon-padding"]),"icon-keep-upright":new ts(Zs.layout_symbol["icon-keep-upright"]),"icon-offset":new es(Zs.layout_symbol["icon-offset"]),"icon-anchor":new es(Zs.layout_symbol["icon-anchor"]),"icon-pitch-alignment":new ts(Zs.layout_symbol["icon-pitch-alignment"]),"text-pitch-alignment":new ts(Zs.layout_symbol["text-pitch-alignment"]),"text-rotation-alignment":new ts(Zs.layout_symbol["text-rotation-alignment"]),"text-field":new es(Zs.layout_symbol["text-field"]),"text-font":new es(Zs.layout_symbol["text-font"]),"text-size":new es(Zs.layout_symbol["text-size"]),"text-max-width":new es(Zs.layout_symbol["text-max-width"]),"text-line-height":new ts(Zs.layout_symbol["text-line-height"]),"text-letter-spacing":new es(Zs.layout_symbol["text-letter-spacing"]),"text-justify":new es(Zs.layout_symbol["text-justify"]),"text-radial-offset":new es(Zs.layout_symbol["text-radial-offset"]),"text-variable-anchor":new ts(Zs.layout_symbol["text-variable-anchor"]),"text-anchor":new es(Zs.layout_symbol["text-anchor"]),"text-max-angle":new ts(Zs.layout_symbol["text-max-angle"]),"text-writing-mode":new ts(Zs.layout_symbol["text-writing-mode"]),"text-rotate":new es(Zs.layout_symbol["text-rotate"]),"text-padding":new ts(Zs.layout_symbol["text-padding"]),"text-keep-upright":new ts(Zs.layout_symbol["text-keep-upright"]),"text-transform":new es(Zs.layout_symbol["text-transform"]),"text-offset":new es(Zs.layout_symbol["text-offset"]),"text-allow-overlap":new ts(Zs.layout_symbol["text-allow-overlap"]),"text-ignore-placement":new ts(Zs.layout_symbol["text-ignore-placement"]),"text-optional":new ts(Zs.layout_symbol["text-optional"])}),sl=new os({"icon-opacity":new es(Zs.paint_symbol["icon-opacity"]),"icon-color":new es(Zs.paint_symbol["icon-color"]),"icon-halo-color":new es(Zs.paint_symbol["icon-halo-color"]),"icon-halo-width":new es(Zs.paint_symbol["icon-halo-width"]),"icon-halo-blur":new es(Zs.paint_symbol["icon-halo-blur"]),"icon-translate":new ts(Zs.paint_symbol["icon-translate"]),"icon-translate-anchor":new ts(Zs.paint_symbol["icon-translate-anchor"]),"text-opacity":new es(Zs.paint_symbol["text-opacity"]),"text-color":new es(Zs.paint_symbol["text-color"],{runtimeType:il,getOverride:function(t){return t.textColor},hasOverride:function(t){return!!t.textColor}}),"text-halo-color":new es(Zs.paint_symbol["text-halo-color"]),"text-halo-width":new es(Zs.paint_symbol["text-halo-width"]),"text-halo-blur":new es(Zs.paint_symbol["text-halo-blur"]),"text-show-background":new es(Zs.paint_symbol["text-show-background"]),"text-translate":new ts(Zs.paint_symbol["text-translate"]),"text-translate-anchor":new ts(Zs.paint_symbol["text-translate-anchor"])}),ul={paint:sl,layout:ol},ll=function(t){function e(e){t.call(this,e,ul)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.recalculate=function(e,r){if(t.prototype.recalculate.call(this,e,r),"auto"===this.layout.get("icon-rotation-alignment")&&("point"!==this.layout.get("symbol-placement")?this.layout._values["icon-rotation-alignment"]="map":this.layout._values["icon-rotation-alignment"]="viewport"),"auto"===this.layout.get("text-rotation-alignment")&&("point"!==this.layout.get("symbol-placement")?this.layout._values["text-rotation-alignment"]="map":this.layout._values["text-rotation-alignment"]="viewport"),"auto"===this.layout.get("text-pitch-alignment")&&(this.layout._values["text-pitch-alignment"]=this.layout.get("text-rotation-alignment")),"auto"===this.layout.get("icon-pitch-alignment")&&(this.layout._values["icon-pitch-alignment"]=this.layout.get("icon-rotation-alignment")),"point"===this.layout.get("symbol-placement")){var n=this.layout.get("text-writing-mode");if(n){for(var i=[],a=0,o=n;a<o.length;a+=1){var s=o[a];i.indexOf(s)<0&&i.push(s)}this.layout._values["text-writing-mode"]=i}else this.layout._values["text-writing-mode"]=["horizontal"]}this._setPaintOverrides()},e.prototype.getValueAndResolveTokens=function(t,e,r){var n=this.layout.get(t).evaluate(e,{},r),i=this._unevaluatedLayout._values[t];return i.isDataDriven()||io.isExpression(i.value)||!n?n:ua.resolveTokens(e.properties,n)},e.prototype.createBucket=function(t){return new nl(t)},e.prototype.queryRadius=function(){return 0},e.prototype.queryIntersectsFeature=function(){return!1},e.prototype._setPaintOverrides=function(){for(var t=0,r=ul.paint.overridableProperties;t<r.length;t+=1){var n=r[t];if(e.hasPaintOverride(this.layout,n)){var i=this.paint.get(n);new FormatSectionOverride(i);"constant"===i.value.kind||i.value.kind,this.paint._values[n]=new PossiblyEvaluatedPropertyValue(i.property,null,i.parameters)}}},e.prototype._handleOverridablePaintPropertyUpdate=function(t,r,n){return!(!this.layout||r.isDataDriven()||n.isDataDriven())&&e.hasPaintOverride(this.layout,t)},e.hasPaintOverride=function(t,e){var r=t.get("text-field"),n=ul.paint.properties[e],i=!1,a=function(t){for(var e=0,r=t;e<r.length;e+=1){var a=r[e];if(n.overrides&&n.overrides.hasOverride(a))return void(i=!0)}};if("constant"===r.value.kind&&r.value.value instanceof dt)a(r.value.value.sections);else if("source"===r.value.kind){var o=function(t){if(!i)if(t instanceof qe&&It.typeOf(t.value)===al){var e=t.value;a(e.sections)}else t instanceof Se?a(t.sections):t.eachChild(o)},s=r.value;s._styleExpression&&o(s._styleExpression.expression)}return i},e.hasPaintOverrides=function(t){for(var r=0,n=ul.paint.overridableProperties;r<n.length;r+=1){var i=n[r];if(e.hasPaintOverride(t,i))return!0}return!1},e}(ds),pl={circle:lu,fill:gu,line:Su,symbol:ll};function cl(t){return pl[t.type]?new pl[t.type](t):null}var fl=function(t){this.keyCache={},t&&this.replace(t)};function hl(t){var e=[];for(var r in t)e.push(t[r]);return e}function dl(t,r){var n=t.pbfData,i=t.layers,a=t.imageMap,o=tr.deserialize(t.serializeObj).featureIndex,s=t.tileID.z;e.defined(o)||((o=new Xo(t.tileID)).bucketLayerIDs=[]);var u={};try{var l=new fl(i);u=ml(yl(n),l,a,o,s,t.indexData),u=tr.serialize(u,r)}catch(t){}return u.pickId=t.pickId,u}function yl(t){if(e.defined(t))return new ia(new o(t))}function ml(t,r,n,i,a,o){var s=new F(Object.keys(t.layers).sort()),u={},l={featureIndex:i,iconDependencies:{},patternDependencies:{},glyphDependencies:{}};for(var p in r.familiesBySource){var c=r.familiesBySource[p];for(var f in c){var h=t.layers[f];if(h){for(var d=s.encode(f),y=[],m=0;m<h.length;m++){var v=h.feature(m);y.push({feature:v,index:m,sourceLayerIndex:d,sourceLayerId:f})}for(var g=0,x=c[f];g<x.length;g+=1){var b=x[g],_=b[0];if("none"!==_.visibility){vl(b,0,null);var w=u[_.id]=_.createBucket({index:i.bucketLayerIDs.length,layers:b,sourceLayerIndex:d});e.defined(o)&&e.defined(o[_.id])&&(l.indexData=o[_.id]),w.populate(y,l),i.bucketLayerIDs.push(b.map((function(t){return t.id})))}}}}}var E=null;for(var T in u){(w=u[T]).hasPattern&&(w instanceof $r||w instanceof Ni)&&(null==E&&(E=new j({},n)),w.addFeatures(l,E.patternPositions))}return{buckets:u,imageAtlas:E,featureIndex:i}}function vl(t,e,r){for(var n=new ar(e),i=0,a=t;i<a.length;i+=1){a[i].recalculate(n,r)}}fl.prototype.replace=function(t){this._layerConfigs={},this._layers={},this.update(t,[])},fl.prototype.update=function(t,e){for(var r=this,n=0,i=t;n<i.length;n+=1){var a=i[n];this._layerConfigs[a.id]=a;var o=cl(a);null!=o&&(this._layers[a.id]=o,o._featureFilter=Po.createFilter(o.filter),this.keyCache[a.id]&&delete this.keyCache[a.id])}for(var s=0,u=e;s<u.length;s+=1){var l=u[s];delete this.keyCache[l],delete this._layerConfigs[l],delete this._layers[l]}this.familiesBySource={};for(var p=0,c=Go(hl(this._layerConfigs),this.keyCache);p<c.length;p+=1){var f=c[p].map((function(t){return r._layers[t.id]})),h=f[0];if(null!=h&&"none"!==h.visibility){var d=h.source||"",y=this.familiesBySource[d];y||(y=this.familiesBySource[d]={});var m=h.sourceLayer,v=y[m];v||(v=y[m]=[]),v.push(f)}}},tr.register("FeatureIndex",Xo,{omit:["rawTileData","sourceLayerCoder"]});var gl=t(dl);return gl}));
