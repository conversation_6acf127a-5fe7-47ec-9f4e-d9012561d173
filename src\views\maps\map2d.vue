<template>
  <div class="map-2d-wrap">
    <div
      class="map-fun-bar"
      ref="mapFunRef"
    >
      <div
        v-for="(menu, index) in menus"
        :key="index" style="width: 100%"
      >
        <div
          class="map-fun-item"
          :class="activeIndex === index ? 'map-fun-item-active' : ''"
          @click="openMenu(index)"
        >
          <svg-icon
            :icon-class="menu.icon"
            class-name="tool-btn-icon"
          />
          <span class="btn-tips">{{ menu.name }}</span>
        </div>
        <el-divider v-if="index < menus.length - 1" />
      </div>
    </div>
    <Viewer2d
      ref="viewer2dRef"
      @on-create-viewer2d="initMap"
    />
    <template v-for="(comp,index) in menus">
      <component
        :is="comp.componentId" v-if="activeIndex === index && hasContent" :ref="comp.ref"
        :key="index" class="resource other-source"
      />
    </template>
    <div
      v-show="hasContent" class="collapse-div" :class="hasContent ? 'collapse-div-active':''"
      @click="menuFoldHandler"
    >
      <svg-icon
        class="icon-right"
        :icon-class="'arrow'"
      />
    </div>
    <ZoomBar />
    <ToolBar v-if="isShowOtherComp"/>
  </div>
</template>

<script setup name="Map2d">
import PropertyQuery from "@/components/GISTools/2d/MapQuery/PropertyQuery.vue"
import Location from "@/components/GISTools/2d/Location/Location.vue"
import UseViewer2d from "@/components/GISTools/Viewer/UseViewer2d.js"
import { removeAllLayer } from "@/utils/OpenLayers/olTool.js"
import Viewer2d from "@/components/GISTools/Viewer/Viewer2d.vue"

// 使用异步方式加载组件
const Resource = defineAsyncComponent(() =>
  import('@/components/GISTools/2d/Resource/Resource')
)
const CompareAnalysis = defineAsyncComponent(() =>
  import('@/components/GISTools/2d/CompareAnalysis/CompareAnalysis')
)
const ZoomBar = defineAsyncComponent(() =>
  import('@/components/GISTools/2d/ToolBar/ZoomBar')
)
const ToolBar = defineAsyncComponent(() =>
  import('@/components/GISTools/2d/ToolBar/ToolBar')
)


const hasContent = ref(true)
const activeIndex = ref(0)
const viewer2dRef = ref(null)
const mapFunRef = ref(null)
const isShowOtherComp = ref(false)
const menus = ref([
  {
    icon: 'resources',
    name: '资源浏览',
    componentId: shallowRef(Resource),
    ref: 'resource'
  },
  {
    icon: 'analysis',
    name: '对比分析',
    componentId: shallowRef(CompareAnalysis),
    ref: 'compareAnalyse'
  },
  {
    icon: 'massif',
    name: '一键查询',
    componentId: shallowRef(PropertyQuery),
    ref: 'attrQuery'
  },
  {
    icon: 'map-positioning',
    name: '地图定位',
    componentId: shallowRef(Location),
    ref: 'mapLocation'
  }
])

const openMenu = (index)=> {
  activeIndex.value = index
  hasContent.value = true
}
const menuFoldHandler = ()=> {
  if (activeIndex.value !== undefined && !hasContent.value) {
    hasContent.value = true
  } else {
    hasContent.value = false
  }
}

const initMap = async (viewer2dId,mapInitStore,mapViewStore)=>{
  const useViewer2d = new UseViewer2d(viewer2dId,mapInitStore,mapViewStore)
  await useViewer2d.createViewer2d()
  isShowOtherComp.value = true
}
onUnmounted(()=>{
  // 清除所有图层
  removeAllLayer()
})
</script>

<style scoped lang="scss">
@import "@/styles/map2d.scss";
@import "@/styles/variables.module.scss";
.resource {
  position: absolute;
  top: 0;
  height: 100vh;
  z-index: 999;
  background: rgba(0, 19, 46, 0.67843137254902);
  color: $mapMenuText;
}
.other-source{
  background: #00132ead !important;
}
:deep(.el-tree-node__content) {
  color: #fff;
}
.map-2d-wrap {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  :deep(.el-divider) {
    background-color: #8f8f8f;
  }
  :deep(.el-checkbox__inner) {
    border: 1px solid #939395;
  }
  .map-fun-bar {
    position: absolute;
    display: flex;
    flex-direction: column;
    background: #00132ead !important;
    z-index: 999;
    width: $navbarWidth;
    height: 100%;
    border-right: 1px solid #1b76d1;
    align-items: center;
    // overflow-y: scroll;
    .map-fun-item {
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      font: 16px "微软雅黑";
      color: #fff;
      margin: 10px 0;
      &:hover {
        cursor: pointer;
        color: #47a0f9;
      }

      .tool-btn-icon {
        margin: 7px 0;
        width: 31px;
        height: 31px;
      }
      .btn-tips {
        margin-bottom: 8px;
      }
    }
    .map-fun-item-active {
      // background-color: #185ace;
      fill: #ffffff;
      color: #00ffff;
    }
  }
  .resource {
    position: absolute;
    left: 90px;
    z-index: 999;
    // background: $mapMenuBg;
    background: #ffffffe3;
    // opacity: 0.85;6
    height: 100%;
    color: $mapMenuText;
    // border-right: 1px solid #0167CC;
  }
  .legend-div{
    z-index: 400;
    position: absolute;
    margin-left: calc(#{$navbarWidth} + 10px);
    bottom: 60px;
    padding: 5px;
    border-radius: 5px;
  }
  .legend-div-active{
    z-index: 400;
    position: absolute;
    margin-left: calc(#{$navbarWidth} + #{$functionContentWidth} + 10px);
    bottom: 60px;
    padding: 5px;
    border-radius: 5px;
  }
  .map-status-bar-active {
    width: $mainWidthWithContent !important;
    margin-left: calc(#{$navbarWidth} + #{$functionContentWidth}) !important;
  }
  .collapse-div{
    position: absolute;
    height: 80px;
    z-index: 990;
    left: $navbarWidth;
    top: 50%;
    transform: translateY(-50%);
    border-top:17px solid transparent;
    border-bottom:15px solid transparent;
    border-left:25px solid #001529d4;
    border-right:0px solid #fff;
    &:hover{
      cursor: pointer;
    }
    .icon-right{
      position: absolute;
      font-size: 23px;
      color: #6ba9e6;
      font-weight: bold;
      top: 50%;
      transform: translate(-115%,-50%) rotate(-180deg);
    }
  }
}
.el-divider--horizontal {
  width: 80% !important;
  margin: 2px 10% !important;
}
.collapse-div-active {
  left: calc(#{$navbarWidth} + #{$functionContentWidth}) !important;
}
:deep(p.over-draw) {
  background: #023464;
  color: #f5f71e;
}
</style>
