<template>
  <div>
    <el-dialog
      v-model="dialogTableVisible"
      title="数据选择"
      width="75%"
      @close="handleClose"
    >
      <div class="table-box">
        <ProTable
          ref="proTable"
          :columns="columns"
          :request-api="getTableList"
          :init-param="initParam"
          :data-callback="dataCallback"
          @drag-sort="sortTable"
        >
          <!-- 表格 header 按钮 -->
          <template #tableHeader="scope">
            <el-button
              type="primary"
              plain
              :disabled="!scope.isSelected"
              @click="emitData(scope.selectedList)"
            >
              确定数据选择
            </el-button>
          </template>
        </ProTable>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { reactive, ref } from "vue";
import { getUnSendPageQuery } from "@/api/document/receipt";
import { getDicts } from "@/api/system/dict/data";
import ProTable from "@/components/ProTable/index.vue";
import { ElMessage } from "element-plus";

const emit = defineEmits();

const dialogTableVisible = ref(true);
const handleClose = () => {
  dialogTableVisible.value = false;
  emit("close", dialogTableVisible.value);
};

const columns = reactive([
  { type: "selection", fixed: "left", width: 70 },
  { type: "index", label: "序号", width: 80 },
  {
    prop: "num",
    label: "案卷号",
    search: { el: "input" },
  },
  {
    prop: "unitName",
    label: "来文单位"
  },
  {
    prop: "title",
    label: "收发文标题",
    search: { el: "input" },
  },
  {
    prop: "receiptTime",
    label: "收文时间",
    search: {
      el: "date-picker",
      span: 2,
      props: { type: "datetimerange", valueFormat: "YYYY-MM-DD" }
    },
  },
]);

// 表格拖拽排序
const sortTable = (newIndex, oldIndex) => {
  console.log(newIndex, oldIndex);
  console.log(proTable.value?.tableData);
  ElMessage.success("修改列表排序成功");
};

// 如果表格需要初始化请求参数，直接定义传给 ProTable (之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({});

// dataCallback 是对于返回的表格数据做处理，如果你后台返回的数据不是 list && total 这些字段，可以在这里进行处理成这些字段
// 或者直接去 hooks/useTable.ts 文件中把字段改为你后端对应的就行
const dataCallback = (data) => {
  return {
    list: data.list,
    total: data.total,
  };
};

const getTableList = (params) => {
  let newParams = JSON.parse(JSON.stringify(params));
  newParams.receiptTime && (newParams.queryStartDate = newParams.receiptTime[0]);
  newParams.receiptTime && (newParams.queryEndDate = newParams.receiptTime[1]);
  delete newParams.receiptTime;
  return getUnSendPageQuery(newParams);
};

//emit
const emitData = (item) => {
  console.log("选择数据", item);
  if (item && item.length > 1) {
    ElMessage({
      message: "请确保只选择一条收文数据",
      type: "error",
      plain: true,
    });
    return;
  } else {
    dialogTableVisible.value = false;
    let data = {
      close: dialogTableVisible.value,
      list: item,
    };
    emit("selectReciptData", data);
  }
};
</script>

<style scoped>
</style>