define(["exports","./Cartographic-3309dd0d","./Math-119be1a3","./FeatureDetection-806b12f0","./GeometryAttribute-c65394ac"],(function(a,e,r,t,i){"use strict";var n={},s=new e.Cartesian3,o=new e.Cartesian3,l=new i.Quaternion,C=new t.Matrix3;function y(a,r,n,y,u,c,m,h,x,M){var z=a+r;e.Cartesian3.multiplyByScalar(y,Math.cos(z),s),e.Cartesian3.multiplyByScalar(n,Math.sin(z),o),e.Cartesian3.add(s,o,s);var d=Math.cos(a);d*=d;var f=Math.sin(a);f*=f;var _=c/Math.sqrt(m*d+u*f)/h;return i.Quaternion.fromAxisAngle(s,_,l),t.Matrix3.fromQuaternion(l,C),t.Matrix3.multiplyByVector(C,x,M),e.<PERSON>.normalize(M,M),e.Car<PERSON>ian3.multiplyByScalar(M,h,M),M}var u=new e.Cartesian3,c=new e.Cartesian3,m=new e.Cartesian3,h=new e.Cartesian3;n.raisePositionsToHeight=function(a,r,t){for(var i=r.ellipsoid,n=r.height,s=r.extrudedHeight,o=t?a.length/3*2:a.length/3,l=new Float64Array(3*o),C=a.length,y=t?C:0,x=0;x<C;x+=3){var M=x+1,z=x+2,d=e.Cartesian3.fromArray(a,x,u);i.scaleToGeodeticSurface(d,d);var f=e.Cartesian3.clone(d,c),_=i.geodeticSurfaceNormal(d,h),v=e.Cartesian3.multiplyByScalar(_,n,m);e.Cartesian3.add(d,v,d),t&&(e.Cartesian3.multiplyByScalar(_,s,v),e.Cartesian3.add(f,v,f),l[x+y]=f.x,l[M+y]=f.y,l[z+y]=f.z),l[x]=d.x,l[M]=d.y,l[z]=d.z}return l};var x=new e.Cartesian3,M=new e.Cartesian3,z=new e.Cartesian3;n.computeEllipsePositions=function(a,t,i){var n=a.semiMinorAxis,s=a.semiMajorAxis,o=a.rotation,l=a.center,C=8*a.granularity,h=n*n,d=s*s,f=s*n,_=e.Cartesian3.magnitude(l),v=e.Cartesian3.normalize(l,x),O=e.Cartesian3.cross(e.Cartesian3.UNIT_Z,l,M);O=e.Cartesian3.normalize(O,O);var p=e.Cartesian3.cross(v,O,z),w=1+Math.ceil(r.CesiumMath.PI_OVER_TWO/C),P=r.CesiumMath.PI_OVER_TWO/(w-1),g=r.CesiumMath.PI_OVER_TWO-w*P;g<0&&(w-=Math.ceil(Math.abs(g)/P));var I,T,E,A,V,R=t?new Array(3*(w*(w+2)*2)):void 0,W=0,S=u,B=c,b=4*w*3,G=b-1,Q=0,F=i?new Array(b):void 0;for(S=y(g=r.CesiumMath.PI_OVER_TWO,o,p,O,h,f,d,_,v,S),t&&(R[W++]=S.x,R[W++]=S.y,R[W++]=S.z),i&&(F[G--]=S.z,F[G--]=S.y,F[G--]=S.x),g=r.CesiumMath.PI_OVER_TWO-P,I=1;I<w+1;++I){if(S=y(g,o,p,O,h,f,d,_,v,S),B=y(Math.PI-g,o,p,O,h,f,d,_,v,B),t){for(R[W++]=S.x,R[W++]=S.y,R[W++]=S.z,E=2*I+2,T=1;T<E-1;++T)A=T/(E-1),V=e.Cartesian3.lerp(S,B,A,m),R[W++]=V.x,R[W++]=V.y,R[W++]=V.z;R[W++]=B.x,R[W++]=B.y,R[W++]=B.z}i&&(F[G--]=S.z,F[G--]=S.y,F[G--]=S.x,F[Q++]=B.x,F[Q++]=B.y,F[Q++]=B.z),g=r.CesiumMath.PI_OVER_TWO-(I+1)*P}for(I=w;I>1;--I){if(S=y(-(g=r.CesiumMath.PI_OVER_TWO-(I-1)*P),o,p,O,h,f,d,_,v,S),B=y(g+Math.PI,o,p,O,h,f,d,_,v,B),t){for(R[W++]=S.x,R[W++]=S.y,R[W++]=S.z,E=2*(I-1)+2,T=1;T<E-1;++T)A=T/(E-1),V=e.Cartesian3.lerp(S,B,A,m),R[W++]=V.x,R[W++]=V.y,R[W++]=V.z;R[W++]=B.x,R[W++]=B.y,R[W++]=B.z}i&&(F[G--]=S.z,F[G--]=S.y,F[G--]=S.x,F[Q++]=B.x,F[Q++]=B.y,F[Q++]=B.z)}S=y(-(g=r.CesiumMath.PI_OVER_TWO),o,p,O,h,f,d,_,v,S);var H={};return t&&(R[W++]=S.x,R[W++]=S.y,R[W++]=S.z,H.positions=R,H.numPts=w),i&&(F[G--]=S.z,F[G--]=S.y,F[G--]=S.x,H.outerPositions=F),H},a.EllipseGeometryLibrary=n}));
