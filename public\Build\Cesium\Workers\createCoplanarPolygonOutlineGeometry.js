define(["./arrayRemoveDuplicates-d2f048c5","./buildModuleUrl-9085faaa","./Cartographic-3309dd0d","./Check-7b2a090c","./ComponentDatatype-c140a87d","./CoplanarPolygonGeometryLibrary-e6863e11","./when-b60132fc","./GeometryAttribute-c65394ac","./GeometryAttributes-252e9929","./GeometryInstance-6bd4503d","./GeometryPipeline-7a733318","./IndexDatatype-8a5eead4","./PolygonGeometryLibrary-8b220fb0","./FeatureDetection-806b12f0","./Rectangle-dee65d21","./Math-119be1a3","./Event-16a2dfbf","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Cartesian2-db21342c","./OrientedBoundingBox-3b145304","./Cartesian4-3ca25aab","./EllipsoidTangentPlane-1dfa0a87","./IntersectionTests-0d6905a3","./Plane-a3d8b3d2","./PolygonPipeline-d83979ed","./earcut-2.2.1-20c8012f","./EllipsoidRhumbLine-30b5229b","./AttributeCompression-0a087f75","./EncodedCartesian3-f1396b05","./ArcType-29cf2197"],(function(e,t,r,n,a,o,i,y,l,c,p,u,d,s,m,b,g,f,h,P,v,G,C,L,E,T,k,H,w,A,D){"use strict";function I(e){for(var t=e.length,r=new Float64Array(3*t),n=u.IndexDatatype.createTypedArray(t,2*t),o=0,i=0,c=0;c<t;c++){var p=e[c];r[o++]=p.x,r[o++]=p.y,r[o++]=p.z,n[i++]=c,n[i++]=(c+1)%t}var d=new l.GeometryAttributes({position:new y.GeometryAttribute({componentDatatype:a.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:r})});return new y.Geometry({attributes:d,indices:n,primitiveType:s.PrimitiveType.LINES})}function _(e){var t=(e=i.defaultValue(e,i.defaultValue.EMPTY_OBJECT)).polygonHierarchy;this._polygonHierarchy=t,this._workerName="createCoplanarPolygonOutlineGeometry",this.packedLength=d.PolygonGeometryLibrary.computeHierarchyPackedLength(t)+1}_.fromPositions=function(e){return new _({polygonHierarchy:{positions:(e=i.defaultValue(e,i.defaultValue.EMPTY_OBJECT)).positions}})},_.pack=function(e,t,r){return r=i.defaultValue(r,0),t[r=d.PolygonGeometryLibrary.packPolygonHierarchy(e._polygonHierarchy,t,r)]=e.packedLength,t};var O={polygonHierarchy:{}};return _.unpack=function(e,t,r){t=i.defaultValue(t,0);var n=d.PolygonGeometryLibrary.unpackPolygonHierarchy(e,t);t=n.startingIndex,delete n.startingIndex;var a=e[t];return i.defined(r)||(r=new _(O)),r._polygonHierarchy=n,r.packedLength=a,r},_.createGeometry=function(n){var a=n._polygonHierarchy,i=a.positions;if(!((i=e.arrayRemoveDuplicates(i,r.Cartesian3.equalsEpsilon,!0)).length<3)&&o.CoplanarPolygonGeometryLibrary.validOutline(i)){var l=d.PolygonGeometryLibrary.polygonOutlinesFromHierarchy(a,!1);if(0!==l.length){for(var u=[],s=0;s<l.length;s++){var m=new c.GeometryInstance({geometry:I(l[s])});u.push(m)}var b=p.GeometryPipeline.combineInstances(u)[0],g=t.BoundingSphere.fromPoints(a.positions);return new y.Geometry({attributes:b.attributes,indices:b.indices,primitiveType:b.primitiveType,boundingSphere:g})}}},function(e,t){return i.defined(t)&&(e=_.unpack(e,t)),e._ellipsoid=m.Ellipsoid.clone(e._ellipsoid),_.createGeometry(e)}}));
