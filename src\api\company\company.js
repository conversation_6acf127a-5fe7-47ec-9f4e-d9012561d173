import request from "@/utils/request";

/**
 * 查询公司名录列表
 * @param query
 * @returns {*}
 */

export const listTopicCompany = (query) => {
  return request({
    url: "/patrol/topicCompany/list",
    method: "get",
    params: query,
  });
};

/**
 * 查询公司名录详细
 * @param id
 */
export const getTopicCompany = (id) => {
  return request({
    url: "/patrol/topicCompany/" + id,
    method: "get",
  });
};

/**
 * 新增公司名录
 * @param data
 */
export const addTopicCompany = (data) => {
  return request({
    url: "/patrol/topicCompany",
    method: "post",
    data: data,
  });
};

/**
 * 修改公司名录
 * @param data
 */
export const updateTopicCompany = (data) => {
  return request({
    url: "/patrol/topicCompany",
    method: "put",
    data: data,
  });
};

/**
 * 删除公司名录
 * @param id
 */
export const delTopicCompany = (id) => {
  return request({
    url: "/patrol/topicCompany/" + id,
    method: "delete",
  });
};
