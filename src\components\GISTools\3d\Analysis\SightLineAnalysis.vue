<template>
  <div
    v-if="isShow"
    class="analyse-wrap"
  >
    <!-- <Header :header-info="headerInfo" @closePanel="closePanel" /> -->
    <div class="tab-container">
      <el-button
        type="primary"
        class="butttonone" @click="addViewPoint"
      >
        <svg-icon icon-class="sightline" />
        <span class="sight-btn">添加观察点</span>
      </el-button>
      <el-button
        type="primary"
        class="butttonone" @click="addTargetPoint"
      >
        <svg-icon icon-class="sightline" />
        <span class="sight-btn">添加目标点</span>
      </el-button>
    </div>

    <el-button
      type="primary"
      class="clean" @click="clearResult"
    >
      <svg-icon icon-class="clear" />
      <span class="sight-btn">清除</span>
    </el-button>
  </div>
</template>

<script setup name="SightLineAnalysis" text="通视分析">
import useMapViewStore from "@/store/modules/map/mapView.js"
import { createTooltip, destroyEvtHandler, setCursor, setLayerSelectStatus } from "@/utils/Cesium/CesiumTool.js"
import { ElMessage } from "element-plus"


defineProps({
  isShow: {
    type: Boolean,
    default: true
  },
  headerInfo: {
    type: Object
  }
})

const sightline = ref(undefined)
const handlerPoint = ref(undefined)
const handlerEvtPoint = ref(undefined)
const toolTip = ref(undefined)
const pointNum = ref(0)

const viewer3d = computed(()=>useMapViewStore().viewer3d)

const emits = defineEmits(['closePanel'])

const closePanel = ()=>{
  emits("closePanel")
}

/**
 * 通视分析场景初始化
 */
const initSight = ()=> {
  toolTip.value = createTooltip(document.body);
  const scene = viewer3d.value.scene;
  sightline.value = new Cesium.Sightline(scene);

  // 设置线条宽度
  sightline.value.lineWidth = 10

  // 设置视域颜色
  const hiddenColor = new Cesium.Color(255,0,0);
  const visibleColor = new Cesium.Color(0,128,0);
  sightline.value.hiddenColor = hiddenColor;
  sightline.value.visibleColor = visibleColor;

  // 执行通视分析
  sightline.value.build();

  handlerPoint.value = new Cesium.DrawHandler(
    viewer3d.value,
    Cesium.DrawMode.Point
  );
  handlerEvtPoint.value = new Cesium.ScreenSpaceEventHandler(
    scene.canvas
  );
}


/**
 * 添加观察点
 */
const addViewPoint = ()=> {
  clearResult();
  setLayerSelectStatus(viewer3d.value, false);

  handlerPoint.value.activate();

  handlerPoint.value.movingEvt.addEventListener((windowPosition) => {
    setCursor(viewer3d.value, "crosshair");
    toolTip.value.showAt(windowPosition, "<p>左键添加观察点，右键结束</p>");
  });

  handlerPoint.value.drawEvt.addEventListener((result) => {
    setCursor(viewer3d.value, "pointer");
    const position = result.object.position;
    // 将获取的点的位置转化成经纬度
    const cartographic = cartesian2ToDegrees(position);
    // 设置观察点
    sightline.value.viewPosition = cartographic;
    handlerPoint.value.deactivate();
    toolTip.value.setVisible(false);
  });
}

/**
 * 添加目标点
 */
const addTargetPoint = ()=> {
  // 判断是否添加观察点
  if (!sightline.value.viewPosition[0]) {
    ElMessage.warning("请先添加观察点");
    return;
  }
  //鼠标点击事件，添加点
  const scene = viewer3d.value.scene;
  handlerEvtPoint.value.setInputAction((e) => {
    const position = scene.pickPosition(e.position)
    pointNum.value += 1;
    //将获取的点的位置转化成经纬度
    const cartographic = cartesian2ToDegrees(position);
    const name = "point" + pointNum.value;
    sightline.value.addTargetPoint({
      position: cartographic,
      name: name
    });
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

  //鼠标移动事件，更新点
  handlerEvtPoint.value.setInputAction((evt) => {
    setCursor(viewer3d.value, "crosshair");
    toolTip.value.showAt(evt.endPosition, "<p>左键添加目标点，右键结束</p>");
    //鼠标移动，更新最后一次添加的目标点的位置
    const position = scene.pickPosition(evt.endPosition)
    console.log("坐标位置：",position)
    if (pointNum.value > 0) {
      sightline.value.removeTargetPoint("point0");
      const cartographic = cartesian2ToDegrees(position);
      sightline.value.addTargetPoint({
        position: cartographic,
        name: "point0"
      });
    }
  }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

  //鼠标右键事件，结束
  handlerEvtPoint.value.setInputAction(() => {
    setCursor(viewer3d.value, "pointer");
    toolTip.value.setVisible(false);
    viewer3d.value.enableCursorStyle = true;

    handlerEvtPoint.value.removeInputAction(
      Cesium.ScreenSpaceEventType.MOUSE_MOVE
    );
    handlerEvtPoint.value.removeInputAction(
      Cesium.ScreenSpaceEventType.LEFT_CLICK
    );
  }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
}

/**
 * 将Cartesian2坐标转换为经纬度坐标
 */
const cartesian2ToDegrees = (cartesian2)=> {
  const cartographic = Cesium.Cartographic.fromCartesian(cartesian2);
  const longitude = Cesium.Math.toDegrees(cartographic.longitude);
  const latitude = Cesium.Math.toDegrees(cartographic.latitude);
  const height = cartographic.height;
  return [longitude, latitude, height];
}

/**
 * 清除结果
 */
const clearResult = ()=> {
  viewer3d.value.entities.removeAll();
  // 移除所有目标点
  sightline.value.removeAllTargetPoint();
  sightline.value.viewPosition = [];
  // 移除绘制图元
  handlerPoint.value.clear();
  handlerPoint.value.deactivate();
  // 移除鼠标移动和鼠标单击事件
  handlerEvtPoint.value.removeInputAction(
    Cesium.ScreenSpaceEventType.MOUSE_MOVE
  );
  handlerEvtPoint.value.removeInputAction(
    Cesium.ScreenSpaceEventType.LEFT_CLICK
  );
}

onBeforeMount(()=>{
  initSight()
})

onBeforeUnmount(()=>{
  clearResult();
  setLayerSelectStatus(viewer3d.value, true);
  destroyEvtHandler(handlerEvtPoint.value);
  const toolTipEle = document.querySelector(".twipsy")
  toolTipEle.parentNode.removeChild(toolTipEle);
})
</script>

<style scoped lang="scss">
.analyse-wrap {
  color: #fff;
  background-image: url(@/assets/images/map/tool.png);
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-size: cover;
  box-shadow: 0 0 8px 0 #057595;
}
.tool-tabs {
  display: flex;
  justify-items: center;
  justify-content: space-between;
  background-color: #4f7287;
  span {
    display: inline-block;
    width: 50%;
    padding: 10px 5px;
    text-align: center;
    color: #fff;
    &:hover {
      cursor: pointer;
      background-color: #00baff;
      filter: brightness(110%);
      color: #fff;
      transition: background-color 0.25s;
    }
  }
}
.tab-active {
  color: #fff;
  background-color: #00baff;
}
.tool-tab-content {
  padding-top: 20px;
}
:deep(.el-form-item__label) {
  width: 60px !important;
  color: #b5b5b5;
}
.tab-container {
  // display: flex;
  display: -webkit-box;
  padding: 10px;
  flex-direction: column;
  column-count: 2;
}
.tool-btn {
  margin-top: 20px;
  padding: 10px;
  width: 48%;
  background-color: #00cffa;
  border-color: #fff;
  text-align: center;
  border-radius: 4px;
  color: #ffffff;
  &:hover {
    cursor: pointer;
    color: #fff;
    filter: brightness(110%) opacity(100%);
    transition: all 0.5s ease-in;
    background: linear-gradient(to bottom right, #00baff, #00cffa);
  }
  svg {
    margin-right: 10px;
  }
}
.sight-btn {
  margin-left: 5px;
}
.el-button--primary {
  margin: 10px;
}
.butttonone {
  background-image: url("@/assets/images/map/measureBtn.png");
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  &:hover {
    background-image: url("@/assets/images/map/measureBtnIs.png");
  }
}
.butttonone:active {
  cursor: pointer;
  background-image: url("@/assets/images/map/measureBtnIs.png");
  transition: background-color 0s;
  filter: brightness(110%) opacity(100%);
  transition: all 0.5s ease-in;
}
.clean {
  width: 86%;
  margin-left: 20px !important;
}
</style>
