/**
 * 内容管理-新闻
 */

import request from '@/utils/request'

// 查询新闻通知列表
export function getNewsList(query) {
    return request({
      url: '/document/news/list',
      method: 'get',
      params: query
    })
  }

// 新增新闻通知
export function addDocumentNews(data) {
  return request({
    url: '/document/news',
    method: 'post',
    data: data
  })
}

// 修改新闻通知
export function updateDocumentNews(data) {
  return request({
    url: '/document/news',
    method: 'put',
    data: data
  })
}

// 获取新闻通知详细信息
export function getNewsById(id) {
  return request({
    url: '/document/news/' + id,
    method: 'get'
  })
}

// 删除新闻通知
export function delNewsById(id) {
  return request({
    url: '/document/news/' + id,
    method: 'delete'
  })
}