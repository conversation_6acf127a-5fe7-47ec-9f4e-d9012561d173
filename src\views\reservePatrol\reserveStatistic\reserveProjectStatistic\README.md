# 储备项目统计页面

## 功能概述

这是一个储备项目统计数据展示页面，提供以下功能：

1. **年份筛选查询** - 可以按年份筛选统计数据
2. **统计卡片展示** - 显示项目总数、总面积、平均面积等关键指标
3. **图表可视化** - 提供柱状图和折线图展示年度数据趋势
4. **详细数据表格** - 展示具体的统计数据，包括占比分析
5. **数据导出** - 支持将统计数据导出为Excel文件

## 数据结构

### API返回数据格式

```json
{
    "code": 200,
    "bounds": null,
    "msg": "操作成功",
    "data": [
        {
            "yzdzmj": "3076.35",    // 用地总面积(亩)
            "num": 1,               // 项目数量
            "nf": "2024"           // 年份
        },
        {
            "yzdzmj": "823611.87",
            "num": 8,
            "nf": "2025"
        }
    ]
}
```

### 字段说明

- `yzdzmj`: 用地总面积，单位为亩
- `num`: 项目数量
- `nf`: 年份

## API接口

### 获取统计数据
- **接口地址**: `/reserve/statistic/project`
- **请求方法**: GET
- **请求参数**: 
  - `nf`: 年份(可选)

### 导出统计数据
- **接口地址**: `/reserve/statistic/project/export`
- **请求方法**: GET
- **请求参数**: 
  - `nf`: 年份(可选)
- **返回类型**: Excel文件流

## 页面组件

### 查询条件区域
- 年份选择器：支持按年份筛选数据
- 查询按钮：执行查询操作
- 重置按钮：清空查询条件
- 导出按钮：导出当前数据

### 统计卡片
- 项目总数：显示所有项目的总数量
- 总面积：显示所有项目的用地总面积
- 平均面积：显示平均每个项目的用地面积

### 图表展示
- 年度项目数量统计：柱状图展示各年度项目数量
- 年度面积统计：折线图展示各年度用地面积趋势

### 数据表格
- 序号：自动编号
- 年份：显示统计年份
- 项目数量：显示该年度项目数量，带标签样式
- 用地总面积：显示该年度用地总面积，格式化显示
- 平均面积：计算并显示该年度平均面积
- 占比：显示该年度面积占总面积的百分比，带进度条

## 使用说明

1. **页面加载**：页面加载时自动获取所有年份的统计数据
2. **年份筛选**：选择特定年份后点击查询按钮可筛选该年份的数据
3. **数据重置**：点击重置按钮清空筛选条件，显示所有数据
4. **数据导出**：点击导出按钮可将当前显示的数据导出为Excel文件
5. **图表交互**：图表支持鼠标悬停查看详细数据

## 技术特性

- **响应式设计**：支持不同屏幕尺寸的自适应显示
- **图表可视化**：使用ECharts实现数据可视化
- **数据格式化**：数字格式化显示，提升可读性
- **错误处理**：完善的错误处理和用户提示
- **开发模式**：开发环境下提供模拟数据支持

## 文件结构

```
reserveProjectStatistic/
├── index.vue           # 主页面组件
└── README.md          # 说明文档
```

## 依赖说明

- Vue 3 Composition API
- Element Plus UI组件库
- ECharts 图表库
- 自定义API接口模块
