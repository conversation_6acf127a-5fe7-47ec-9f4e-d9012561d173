<template>
  <div class="main-content">
    <div v-if="!showDetail">
      <transition>
        <div
          v-show="showSearch"
          class="mb-[10px] top"
        >
          <el-card shadow="hover">
            <el-form
              ref="queryFormRef"
              :model="queryParams" :inline="true"
              class="query-form"
            >
              <el-form-item
                label="巡查人员"
                prop="xcry"
              >
                <el-input
                  v-model="queryParams.xcry"
                  placeholder="请输入巡查人员"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item
                label="提交时间"
                prop="tjsj"
              >
                <el-date-picker
                  v-model="queryParams.tjsj"
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="请选择提交时间"
                />
              </el-form-item>
              <el-form-item
                label="审核状态"
                prop="shzt"
              >
                <el-select
                  v-model="queryParams.shzt"
                  filterable
                  placeholder="请选择"
                  @change="changeSelect"
                  class="state"
                >
                  <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  icon="Search" @click="handleQuery"
                >搜索
                </el-button>
                <el-button
                  icon="Refresh"
                  @click="resetQuery"
                >重置</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </div>
      </transition>

      <el-card class="result-wrap">
        <el-table
          v-loading="loading"
          :data="patrolRecordList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            label="区域"
            align="center" prop="land.xzqmc"
          />
          <el-table-column
            label="任务名称"
            align="center" prop="task.name"
          />
          <el-table-column
            label="任务发布时间"
            align="center" prop="task.releaseTime"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.task.releaseTime, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="合同编号"
            align="center" prop="temp.htbh"
          />
          <el-table-column
            label="临时用途"
            align="center" prop="temp.lslyyt"
          />
          <el-table-column
            label="地块编号"
            align="center" prop="land.dkbh"
          />
          <el-table-column
            label="地块面积(公顷)"
            align="center" prop="land.dkmj"
          />
          <el-table-column
            label="地块坐落"
            align="center" prop="land.dz"
          />
          <el-table-column
            label="巡查人员"
            align="center" prop="xcry"
          />
          <el-table-column
            label="巡查时间"
            align="center"
            prop="xcsj"
            width="180"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.xcsj, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="审核状态"
            align="center" prop="shzt"
          />
          <el-table-column
            label="操作"
            align="center"
            width="180"
          >
            <template #default="scope">
              <el-button
                plain
                type="primary" size="small" @click="handleUpdate(scope.row)"
              >查看</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>
    <!-- 添加或修改巡查记录对话框 -->
    <div
      v-else
      class="content"
    >
      <div class="add-header-title">
        <div class="add-title">{{ ghxcrwTitle }}</div>
        <div
          class="add-title-return"
          @click="cancel"
        >
          <img
            src="@/assets/images/img-return.png"
            class="back"
          >
          <div class="backlist">返回列表</div>
        </div>
      </div>
      <div class="add-content-rev">
        <el-row :gutter="10">
          <el-col
            :span="18"
            class="contentlist"
          >
            <div class="laberone">
              <div
                style="height: 100%; width: 120px; text-align: center"
                @click="handleTabSelect(1)"
              >
                <div
                  :class="showContent === 1 ? 'tab-active' : ''"
                  style="margin-top: 16px; cursor: pointer; font-size: 16px"
                >
                  巡查审查
                </div>
                <div
                  class="information-rev"
                  v-if="showContent === 1"
                >
                  <img src="@/assets/images/tabs.png">
                </div>
              </div>
              <div
                class="titleone"
                @click="handleTabSelect(0)"
              >
                <div
                  :class="showContent === 0 ? 'tab-active' : ''"
                  style="margin-top: 16px; cursor: pointer; font-size: 16px"
                >
                  基本信息
                </div>
                <div
                  class="information-rev"
                  v-if="showContent === 0"
                >
                  <img src="@/assets/images/tabs.png">
                </div>
              </div>
            </div>
            <div
              v-if="showContent === 0"
              class="two"
            >
              <!--项目基本信息-->
              <div>
                <div class="content-project-rev">
                  <img src="@/assets/images/left.png">
                  <p>临时管护信息</p>
                </div>
                <el-form
                  ref="reserveProjectFormRef"
                  :model="form"
                  :rules="rules"
                  label-width="180px"
                  class="information-rev"
                  disabled
                >
                  <el-row>
                    <el-col :span="12">
                      <el-form-item
                        label="合同编号"
                        prop="temp.htbh"
                      >
                        <el-input
                          v-model="form.htbh"
                        />
                      </el-form-item>
                      <el-form-item
                        label="合同金额"
                        prop="temp.htje"
                      >
                        <el-input
                          v-model="form.htje"
                        >
                          <template #append>万元</template>
                        </el-input>
                      </el-form-item>
                      <el-form-item
                        label="临时利用期限起"
                        prop="temp.lslyqxq"
                      >
                        <el-date-picker
                          v-model="form.lslyqxq"
                          type="date"
                          value-format="YYYY-MM-DD"
                          style="width: 100%"
                        />
                      </el-form-item>

                      <el-form-item
                        label="临时利用用途"
                        prop="temp.lslyyt"
                      >
                        <el-input
                          v-model="form.lslyyt"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item
                        label="乙方单位"
                        prop="temp.yfdw"
                      >
                        <el-input
                          v-model="form.yfdw"
                        />
                      </el-form-item>
                      <el-form-item
                        label="临时利用成本"
                        prop="temp.tdlslycb"
                      >
                        <el-input
                          v-model="form.tdlslycb"
                        >
                          <template #append>万元</template>
                        </el-input>
                      </el-form-item>
                      <el-form-item
                        label="临时利用期限止"
                        prop="temp.lslyqxz"
                      >
                        <el-date-picker
                          v-model="form.lslyqxz"
                          type="date"
                          value-format="YYYY-MM-DD"
                          style="width: 100%"
                        />
                      </el-form-item>
                      <el-form-item
                        label="是否已收回"
                        prop="temp.sfysh"
                      >
                        <el-input
                          v-model="form.sfysh"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="24">
                      <el-form-item
                        label="备注"
                        prop="temp.bz"
                      >
                        <el-input
                          v-model="form.bz"
                          :rows="3"
                          type="textarea"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </div>
              <!--地块信息-->
              <div>
                <div class="content-title">
                  <div class="content-project-rev">
                    <img src="@/assets/images/left.png">
                    <p>管护地块信息</p>
                  </div>
                </div>
                <el-form
                  ref="reserveProjectFormRef"
                  :model="form"
                  :rules="rules"
                  label-width="180px"
                  class="information-rev"
                  disabled
                >
                  <el-row>
                    <el-col :span="12">
                      <el-form-item
                        label="地块编号"
                        prop="land.dkbh"
                      >
                        <el-input
                          v-model="form.dkbh"
                        />
                      </el-form-item>
                      <el-form-item
                        label="规划用途"
                        prop="land.ghyt"
                      >
                        <el-input
                          v-model="form.ghyt"
                        />
                      </el-form-item>
                      <el-form-item
                        label="地址"
                        prop="land.dz"
                      >
                        <el-input
                          v-model="form.dz"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item
                        label="地块名称"
                        prop="land.dkmc"
                      >
                        <el-input
                          v-model="form.dkmc"
                        />
                      </el-form-item>
                      <el-form-item
                        label="地块面积"
                        prop="land.dkmj"
                      >
                        <el-input
                          v-model="form.dkmj"
                        >
                          <template #append>公顷</template>
                        </el-input>
                      </el-form-item>
                      <el-form-item
                        label="行政区名称"
                        prop="land.xzqmc"
                      >
                        <el-input
                          v-model="form.xzqmc"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="24">
                      <el-form-item
                        label="备注"
                        prop="land.bz"
                      >
                        <el-input
                          v-model="form.bz"
                          :rows="3"
                          type="textarea"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </div>
            </div>
            <div
              v-if="showContent === 1"
              class="three"
            >
              <el-row>
                <div class="four">
                  <div>
                    <div class="patrol">
                      <div class="content-project-rev">
                        <img src="@/assets/images/left.png">
                        <p>巡查信息</p>
                      </div>
                      <div class="xcxx">
                        <span>巡查日期：{{ parseTime(form.xcsj, "{y}-{m}-{d}") }}</span>
                      </div>
                    </div>
                    <div class="personnel">
                      <div class="xcry">巡查人员</div>
                      <div class="xcrymz">{{ form.xcry }}</div>
                    </div>
                    <div class="personnel">
                      <div class="xcry-none">是否改变用途</div>
                      <div class="xcrymz-none">
                        <el-switch
                          v-model="form.sfgbyt"
                          inline-prompt
                          active-text="是"
                          inactive-text="否"
                        />
                      </div>
                    </div>
                    <div class="personnel">
                      <div class="xcry-none">是否建设永久性建筑</div>
                      <div class="xcrymz-none">
                        <el-switch
                          v-model="form.sfjsyjxjz"
                          inline-prompt
                          active-text="是"
                          inactive-text="否"
                        />
                      </div>
                    </div>
                    <div class="personnel">
                      <div class="xcry-none">其他情况说明</div>
                      <div class="xcrymz-none">
                        <el-input
                          v-model="form.qtqksm"
                        />
                      </div>
                    </div>
                  </div>
                  <div>
                    <div class="patrol xx">
                      <div class="content-project-rev">
                        <img src="@/assets/images/left.png">
                        <p>巡查照片</p>
                      </div>
                      <div class="xcxx">
                        <span>上传日期：{{ parseTime(form.tjsj, "{y}-{m}-{d}") }}</span>
                      </div>
                    </div>
                    <div v-if="true">
                      <el-empty description="还没有上传照片"/>
                    </div>
                  </div>
                </div>
                <Viewer3d
                  ref="viewer3d"
                  @onload="createViewer"
                  class="viewer3d"
                />
              </el-row>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="content-button">
              <el-button
                type="primary"
                plain @click=""
              >审查</el-button>
              <el-button
                type="primary"
                plain @click="submitForm"
              >保存</el-button>
              <el-button
                type="primary"
                plain @click=""
              >提交</el-button>
            </div>
            <div class="attachment">
              <div>
                <div class="related_accessories"/>
                <div class="related">相关附件</div>
              </div>
              <file-tree/>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup name="TempPatrolTaskReview">
/**
 * CHN：临时管护巡查信息
 */
import {
  listPatrolRecord,
  getPatrolRecord,
  delPatrolRecord,
  addPatrolRecord,
  updatePatrolRecord
} from "@/api/patrol/patrolTaskReview.js";
import FileTree from "@/components/FileTree";
import { flyToPoint, setSceneEffect } from "@/utils/Cesium/CesiumTool.js";
import { addTdtImageLayer } from "@/utils/Cesium/CesiumLayer.js";
import useMapViewStore from "@/store/modules/map/mapView.js";
import Viewer3d from "@/components/GISTools/Viewer/Viewer3d.vue"
const { proxy } = getCurrentInstance();

const showContent = ref(0);

const patrolRecordList = ref([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref();
const patrolRecordFormRef = ref();
const showDetail = ref(false);

const initFormData = {
  xcsj: undefined,
  tjsj: undefined,
  xcry: undefined,
  sfgbyt: undefined,
  sfjsyjxjz: undefined,
  qtqksm: undefined,
  xcjlxh: undefined,
  shzt: undefined,
  shsm: undefined,
  shsj: undefined,
  shry: undefined,
  bz: undefined
};

const options = ref([
  // 下拉菜单选项列表
  { value: "0", label: "待提交" },
  { value: "1", label: "待审查" },
  { value: "2", label: "审查通过" },
  { value: "3", label: "驳回" }
  // 更多选项...
]);
const data = reactive({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    xcsj: undefined,
    tjsj: undefined,
    xcry: undefined,
    sfgbyt: undefined,
    sfjsyjxjz: undefined,
    qtqksm: undefined,
    xcjlxh: undefined,
    shzt: undefined,
    shsm: undefined,
    shsj: undefined,
    shry: undefined,
    bz: undefined,
    params: {}
  },
  rules: {}
});

const { queryParams, form, rules } = toRefs(data);

/** 下拉选择 */
const changeSelect = (value) => {
  queryParams.value.shzt = value;
};
/** 查询巡查记录列表 */
const getList = async () => {
  loading.value = true;
  const res = await listPatrolRecord(queryParams.value);
  patrolRecordList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  showDetail.value = false;
  showContent.value = 0;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  patrolRecordFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  console.log(queryParams.value)
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};
//标题变量
const ghxcrwTitle = ref("管护巡查任务详情");
/** 多选框选中数据 */
const handleSelectionChange = (selection) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  ghxcrwTitle.value = "巡查任务详情";
};

/** 修改按钮操作 */
const handleUpdate = async (row) => {
  reset();
  ghxcrwTitle.value = "修改巡查信息";
  const _id = row?.id || ids.value[0];
  const res = await getPatrolRecord(_id);
  console.log(res)
  Object.assign(form.value, res.data);
  showDetail.value = true;
};

/** 提交按钮 */
const submitForm = () => {
  buttonLoading.value = true;
  if (form.value.id) {
    updatePatrolRecord(form.value).finally(
      () => (buttonLoading.value = false)
    );
  } else {
    addPatrolRecord(form.value).finally(
      () => (buttonLoading.value = false)
    );
  }
  proxy?.$modal.msgSuccess("操作成功");
  showDetail.value = false;
  showContent.value = 0;
  getList();
};

/** 删除按钮操作 */
const handleDelete = async (row) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal
    .confirm('是否确认删除巡查记录编号为"' + _ids + '"的数据项？')
    .finally(() => (loading.value = false));
  await delPatrolRecord(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    "patrol/patrolRecord/export",
    {
      ...queryParams.value
    },
    `patrolRecord_${new Date().getTime()}.xlsx`
  );
};

const handleTabSelect = (index) => {
  showContent.value = index;
};

//加载地图
/**
 * 响应数据
 */
const viewer3d = reactive({});

// 用户信息
const store = useMapViewStore();

function createViewer(params) {
  window._viewer3d = null;
  viewer3d.value = null;
  const viewer = new Cesium.Viewer(params.viewerId, {
    selectionIndicator: false,
    timeline: false,
    baseLayerPicker: false,
    shadows: true,
    infoBox: false,
    geocoder: false,
    skyBox: false, // 关闭天空盒会一同关闭太阳，场景会变暗
    navigation: true // 初始化导航控件
  });
  viewer3d.value = viewer;
  const scene = viewer.scene;
  // console.log(" map -----~~~ store", store)
  store.viewer3d = viewer3d;
  // console.log("3d视图对象：",viewer3d.value)
  // 去除supermap SuperMap3D 超图logo--Cesium
  viewer._cesiumWidget._creditContainer.style.display = "none";

  flyToPoint(viewer);

  // 禁用双击事件
  viewer.trackedEntity = undefined;
  viewer.screenSpaceEventHandler.removeInputAction(
    Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK
  );

  // 设置是否贴地飞行，默认为flase,设置为true时始终在地形之上飞行，不会钻进地形内部。
  viewer.camera.flyClampToGround = true;
  // 渲染分辨率缩放因子
  viewer.resolutionScale = window.devicePixelRatio;

  /**===============================================添加影像底图开始==========================================**/
  addTdtImageLayer(viewer);
  // const wmtsOption = {
  //     url: params.mapInfo.url,
  //     layerName: params.mapInfo.layerName
  //   }
  // addWMTSLayer(viewer3d,wmtsOption)
  // addIserverLayer(viewer3d,wmtsOption)
  /**================================================添加影像底图结束=========================================**/
  /**================================================添加地形设置开始=========================================**/
  scene.undergroundMode = true;
  scene.globe.globeAlpha = 0.75;
  scene.screenSpaceCameraController.minimumZoomDistance = -1000;
  scene.globe.depthTestAgainstTerrain = true; // 应该地形深度测试
  /**================================================添加地形设置结束=========================================**/

  /**================================================设置场景参数开始=========================================**/
  setSceneEffect(viewer);
  /**===============================================设置场景参数结束==========================================**/
}

onMounted(() => {
  getList();
});
</script>
<style lang="scss" scoped>
@import "@/styles/variables.module.scss";

.main-content {
  padding: 10px;
}

.result-wrap {
  margin-top: 10px;
}

.add-header-title {
  padding: 10px;
  display: flex;
  justify-content: space-between;
  height: 50px;
  background-color: rgb(222, 239, 255);
  box-sizing: border-box;
  border-bottom: 1px solid rgb(233, 233, 233);
  font-weight: 700;
  font-size: 14px;
  line-height: 28px;
}

.add-title-return {
  display: flex;
  align-content: center;
  color: rgb(32, 119, 255);
  cursor: pointer;
  font-weight: normal;
}

.add-content-rev {
  padding: 0px 10px;
}

.content-project-rev {
  display: flex;
  align-items: center;

  p {
    color: #333333;
    font-weight: bold;
    margin-left: 8px;
  }
}

.tab-active {
  color: #0f7dff;
  font-weight: bold;
}

.map-container-3d {
  width: 100vw;
  height: $resourceContentHeight;
}

.back {
  height: 18px;
  width: 18px;
  margin-top: 5px;
}

.backlist {
  padding-left: 6px;
  font-size: 14px;
}

.top {
  margin-bottom: 15px;
}

.state {
  width: 250px;
}

.el-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.content {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #dadada;
}

.contentlist {
  padding: 10px;

  .laberone {
    height: 60px;
    width: 100%;
    display: flex;
  }

  .titleone {
    height: 100%;
    width: 120px;
    text-align: center;
  }

  .information-rev {
    margin-top: 4px;

    img {
      width: 30px;
      height: auto;
      transform: rotate(-1deg);
    }
  }

  .two {
    border: 1px solid rgb(173, 211, 246);
    padding: 10px 10px 10px 20px;
    border-radius: 4px;
  }

  .three {
    border: 1px solid rgb(173, 211, 246);
    padding: 10px 10px 10px 20px;
    border-radius: 4px;
  }

  .four {
    background-color: white;
    z-index: 999;
    width: 300px;
  }

  .patrol {
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid rgb(230, 230, 230);
  }

  .xcxx {
    text-align: center;
    line-height: 51px;
  }

  .xx {
    margin-top: 15px;
  }

  .xcxx span {
    font-size: 12px;
    color: rgb(140, 140, 140);
  }

  .personnel {
    display: flex;
    height: 40px;
    text-align: center;
    line-height: 40px;
  }

  .xcry {
    width: 120px;
    font-size: 13px;
    border: 1px solid #e9e9e9;
    font-weight: bold;
    background-color: #fafafa;
  }

  .xcry-none {
    width: 120px;
    font-size: 13px;
    border: 1px solid #e9e9e9;
    font-weight: bold;
    background-color: #fafafa;
    border-top: none !important;
  }

  .xcrymz {
    width: 180px;
    font-size: 14px;
    border: 1px solid #e9e9e9;
    border-left: none;
    color: rgb(85, 85, 85);
  }

  .xcrymz-none {
    width: 180px;
    font-size: 14px;
    border: 1px solid #e9e9e9;
    border-left: none;
    color: rgb(85, 85, 85);
    border-top: none !important;

    :deep(.el-input__wrapper) {
      box-shadow: none;
    }
  }

  .viewer3d {
    padding-left: 310px;
  }
}

.content-button {
  height: 68px;
  line-height: 68px;
}

.attachment {
  border: 1px solid #e6e6e6;
  border-radius: 4px;
}

.related_accessories {
  height: 8px;
  background-color: rgba(33, 120, 255, 1);
  border-radius: 4px 4px 0 0;
}

.related {
  border-bottom: 1px solid rgb(230, 230, 230);
  height: 40px;
  line-height: 40px;
  align-content: center;
  padding-left: 10px;
  font-size: small;
  font-weight: bold;
}
@media(max-width: 1000px){
  .query-form{
    :deep(.el-form-item) {
      margin-bottom: 18px !important;
    }
  }
}
@media(min-width: 1500px){
  .query-form{
    :deep(.el-form-item) {
      margin-bottom: 0 !important;
    }
  }
}
</style>
