<template>
  <div class="query-wrap">
    <div>
      <div class="header-bg" >
        <AnalysisHeader
          ref="Header"
          :title="'属性查询'"
          :header-clicked="isGeoHeader"
          :show-icon="false"
          @click="isGeoHeader = !isGeoHeader"
        />
      </div>
      <Title :title="'查询图层'" />
      <div class="select select-layer">
        <span style="color: #fff;width: 35%;">选择图层：</span>
        <el-select
          v-model="layerName"
          clearable
          placeholder="请选择查询图层"
          @change="selectChange"
        >
          <el-option
            v-for="item in queryList"
            :key="item.label"
            :label="item.label"
            :value="item.dataSet"
          />
        </el-select>
      </div>
    </div>
    <div>
      <Title :title="'查询条件'" />
      <div class="select-wrap">
        <div class="select-tabs">
          <div
            class="select-tab"
            :class="isProperty?'select-tab-active':''" @click="selectRegion"
          >属性查询</div>
          <div
            class="select-tab"
            :class="isGeometry?'select-tab-active':''" @click="selectPolygon"
          >几何查询</div>
        </div>
      </div>
      <div
        v-if="isProperty"
        class="property-query"
      >
        <div class="select select-fields">
          <span style="color: #fff;width: 35%;">选择字段：</span>
          <el-select
            v-model="fieldName"
            clearable
            placeholder="请选择查询字段"
          >
            <el-option
              v-for="item in fieldOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="select search-div">
          <el-input
            v-model="input"
            class="search-input"
            placeholder="请输入查询内容"
            clearable
          />
          <el-button
            :disabled="getQueryStatus"
            class="search-btn"
            @click="getSearch"
          >
            查询
          </el-button>
        </div>
      </div>

      <div
        v-show="isGeometry"
        class="geo-query"
      >
        <el-button
          class="draw-btn"
          :class="{ active: isRectangleClicked }"
          @click="drawRectangle"
        >
          框选
        </el-button>
        <el-button
          class="draw-btn"
          :class="{ active: isPolygonClicked }"
          @click="drawPolygon"
        >
          面选
        </el-button>
        <el-button
          :class="{ active: (isPolygonClicked || isRectangleClicked) && layerName }"
          class="geo-query-btn"
          :disabled="!geometry || !layerName"
          @click="geometryQuery"
        >
          查询
        </el-button>
      </div>
    </div>
    <div
      v-if="tableData.length"
      class="result-container"
    >
      <header
        class="title"
        @click="isResultHeader = !isResultHeader"
      >
        <span>查询结果</span>
        <span class="result-count">
          ：<span style="font-weight: bold">{{ queryParams.total }}</span>
          条记录
        </span>
        <i
          class="iconfont"
          :class="isResultHeader ? 'icon-down2' : 'icon-up'"
        />
      </header>
      <transition name="fade">
        <div v-show="!isResultHeader">
          <div class="result-div">
            <el-table
              class="result-table"
              :data="tableData"
              style="width: 100%"
              max-height="360"
              @row-click="location"
            >
              <el-table-column
                type="index"
                label="序号"
                :index="indexMethod"
                width="50%"
                align="center"
              />
              <el-table-column
                :prop="tablePropObj.field"
                :label="tablePropObj.label"
                width=""
                align="center"
              />
            </el-table>
            <div class="pagination">
              <el-pagination
                background
                layout="prev, pager, next"
                :total="queryParams.total"
                :pager-count="queryParams.pagerCount"
                :current-page="queryParams.pageNum"
                :page-size="queryParams.pageSize"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>
        </div>
      </transition>
    </div>
    <div
      v-show="isShow && !queryParams.total"
      class="no-data-tip"
    >
      <i class="iconfont icon-zanwushuju" />
      <span>暂无数据</span>
    </div>
  </div>
</template>

<script setup name="属性查询">
import { getConfigKey } from '@/api/system/config';
import { getMapQueryList } from '@/api/gis/mapQuery';
import { queryListByProperty } from '@/api/gis/postgisInterface'
import { getCommentList } from '@/utils/mapFunction/globalFunc'
import { setCursor, setPopup, removeLayerByName } from '@/utils/OpenLayers/olTool';
import Title from '@/components/GISTools/2d/Common/SidebarHeader'
import { addGeoJSON2Map } from "@/utils/OpenLayers/olLayer";
import GeoJSON from 'ol/format/GeoJSON'
import OlDraw from "@/utils/OpenLayers/olDraw";
import Select from "ol/interaction/Select";
import Vector from "ol/layer/Vector";
import Source from "ol/source/Vector";
import eventBus from "@/utils/eventBus.js";
import { defaultStyle, geometrySelectStyle } from "@/utils/OpenLayers/olStyles";
import useMapViewStore from "@/store/modules/map/mapView.js"
import { ElMessage } from "element-plus"
import AnalysisHeader from "@/components/GISTools/2d/Common/AnalysisHeader.vue"


const fieldOptions = ref([])
const layerName = ref('') // 空间表名称
const fieldName = ref('')
const input = ref('') // 查询关键字
const queryList = ref([]) // 查询列表
const isGeoQuery = ref(true)
const tableData = ref([])
// 属性查询参数
const queryParams = reactive({
  tableName: "", // 空间表名
  geojson: "",// 查询范围
  mapkey: "", // 字段名
  mapValue: "", // 字段值
  total: 0, // 查询结果数
  pagerCount: 5, // 分页组件页码按钮数量
  pageNum: 1, // 页码数
  pageSize: 10 // 每页结果数
})
const isResultHeader = ref(false) // 是否点击结果标题
const isGeoHeader = ref(false) // 是否点击几何查询标题
const geometry = ref(undefined) // 查询的几何对象
const isShow = ref(false) // 是否显示暂无数据
const isRectangleClicked = ref(false) // 是否点击框选按钮
const isPolygonClicked = ref(false) // 是否点击面选按钮
const isProperty = ref(true)
const isGeometry = ref(false)
const columnInfos = ref([]) // 字段信息
const olDraw = ref(undefined)
const selectEvt = ref(undefined)
const tablePropObj = ref({})

// 显示表格字段
const layerNameObj = reactive({
  // 土地利用现状2009
  "td_tddc_g_2009_gcs2000": {
    "field": "dlmc",
    "label": "地类名称"
  },
  // 土地利用现状2019
  "td_tddc_g_2019_gcs2000": {
    "field": "dlmc",
    "label": "地类名称"
  },
  // 土地利用总体规划2018
  "th_ztgh_g_2018_gcs2000": {
    "field": "ghdlmc",
    "label": "地类名称"
  },
  // 生态保护红线
  "th_stbhhx_g_2022_gcs2000": {
    "field": "hxmc",
    "label": "红线名称"
  },
  // 永久基本农田保护图斑
  "th_yjjbntbhtb_g_2022_gcs2000": {
    "field": "dlmc",
    "label": "地类名称"
  },
  // 城镇开发边界
  "th_czkfbj_g_2022_gcs2000": {
    "field": "ghfqmc",
    "label": "规划分区名称"
  },
  // 耕地后备资源
  "td_gdhbzy_g_2021_gcs2000_1": {
    "field": "dlmc",
    "label": "地类名称"
  },
  // 卫片执法图斑
  "td_wpzftb_g_2019_gcs2000": {
    "field": "dlmc",
    "label": "地类名称"
  },
  // 执法案件
  "zfaj": {
    "field": "xmmc",
    "label": "项目名称"
  },
  // 用地预审
  "td_ydys_g_2021_gcs2000": {
    "field": "xmmc",
    "label": "项目名称"
  }
})

const map = computed(()=>useMapViewStore().map)
const view = computed(()=>useMapViewStore().viewer2d)
const getQueryStatus = computed(()=> {
  return !(layerName.value && fieldName.value && input.value)
})

watch(layerName,(value)=> {
  tableData.value = []
  if (!value){
    console.info("未配置图层名称")
    return
  }
  getLayerOption(value)
})

watch(isProperty,()=>{
  tableData.value = []
  const queryObj = {
    total: 0, // 查询结果数
    pagerCount: 5, // 分页组件页码按钮数量
    pageNum: 1, // 页码数
    pageFrom: 0, // 开始索引
    pageSize: 10, // 每页结果数
    pageEnd: 9 // 结束索引
  };
  Object.assign(queryParams,queryObj)
})

watch(isGeometry,()=>{
  fieldName.value = ''
  input.value = ''
  const queryObj = {
    total: 0, // 查询结果数
    pagerCount: 5, // 分页组件页码按钮数量
    pageNum: 1, // 页码数
    pageFrom: 0, // 开始索引
    pageSize: 10, // 每页结果数
    pageEnd: 9 // 结束索引
  };
  Object.assign(queryParams,queryObj)
})


// 设置表格序号
const indexMethod = (index)=> {
  return index + 1;
}

const selectChange = (layer)=>{
  tablePropObj.value = {}
  tablePropObj.value = layerNameObj.value[layer]
}

// 获取查询服务列表
const getMapQueryData = ()=> {
  getConfigKey('sys.id').then((res) => {
    // 获取基础查询数据列表
    getMapQueryList(res.data).then((res) => {
      const basicList = res.data.find(layer => layer.label === '基础查询')
      if (!basicList){
        console.error("未查询到数据查询服务，请先在后台配置数据服务！")
        return
      }
      queryList.value = basicList.children;
    });
  });
}

// 配置字段数据
const getLayerOption = async (spatialTableName)=> {
  columnInfos.value = await getCommentList(spatialTableName)
  fieldOptions.value = columnInfos.value.map(column => {
    const fieldObj = {}
    fieldObj.label = column.comment + "（" + column.name.toUpperCase() + "）"
    fieldObj.value = column.name
    return fieldObj
  })
}

// 处理分页数据
const handleCurrentChange = (val)=> {
  tableData.value = [];
  queryParams.pageNum = val;
  if (!this.getQueryStatus) {
    this.getSearch();
  } else {
    this.geometryQuery()
  }
}

const selectRegion = ()=>{
  isProperty.value = true
  isGeometry.value = false
}
const selectPolygon = ()=>{
  isGeometry.value = true
  isProperty.value = false
}

// 搜索
const getSearch = ()=> {
  eventBus.emit('showLoading', true)
  tableData.value = []

  queryParams.tableName = this.layerName
  queryParams.mapkey = this.fieldName
  queryParams.mapValue = this.input

  queryListByProperty(queryParams).then(res => {
    if (res.code !== 200){
      console.error(layerName.value + "-数据查询出错！~~~~~")
      return
    }
    if (!res.total){
      this.isShow = true
      ElMessage.warning("未查询到-" + layerName.value + "-图层数据")
    }
    queryParams.total = res.total
    tableData.value = res.rows.map(feature => {
      const properties = feature.properties
      properties.feature = feature
      return properties
    })
    eventBus.emit('showLoading', false)
  })
}

// 定位到地图
const location = (json)=> {
  removeLayerByName("tempPolygon")
  const feature = json.feature
  addGeoJSON2Map(feature,undefined,undefined)
  const feats = new GeoJSON().readFeatures(feature)
  setPopup(feature.properties,columnInfos.value, feats[0].getGeometry(), window._map)
  openPopup(window._map)
}

// 绘制矩形
const drawRectangle = ()=> {
  // 改变指针样式
  setCursor('crosshair');
  if (olDraw.value?.getDrawSource()){
    // 结束上一次的绘制并清除绘制对象
    const drawSource = olDraw.value.getDrawSource()
    olDraw.value.clearGeometry(drawSource)
  }
  isRectangleClicked.value = !isRectangleClicked.value;
  if (!isRectangleClicked.value) {
    setCursor('pointer');
    return;
  }

  setCursor('crosshair')
  olDraw.value = new OlDraw(map.value, view.value)
  olDraw.value.drawGeometry("Box")
  const drawAction = olDraw.value.getDrawAction()
  drawAction.on('drawend',evt => {
    geometry.value = new GeoJSON().writeGeometryObject(evt.feature.getGeometry())
    olDraw.value.deactiveAction(drawAction)
    setCursor('pointer')
  })
  isGeoQuery.value = false;
}
// 绘制面
const drawPolygon = ()=> {
  if (selectEvt.value){
    map.value.removeInteraction(selectEvt.value)
  }

  if (olDraw.value?.getDrawSource()){
    // 结束上一次的绘制并清除绘制对象
    const drawSource = olDraw.value.getDrawSource()
    olDraw.value.clearGeometry(drawSource)
  }
  isPolygonClicked.value = !isPolygonClicked.value;
  if (!isPolygonClicked.value) {
    setCursor('pointer');
    return;
  }
  setCursor('crosshair')
  olDraw.value = new OlDraw(map.value, view.value)
  olDraw.value.drawGeometry("Polygon")
  const drawAction = olDraw.value.getDrawAction()
  drawAction.on('drawend',evt => {
    geometry.value = new GeoJSON().writeGeometryObject(evt.feature.getGeometry())
    olDraw.value.deactiveAction(drawAction)
    setCursor('pointer')
  })
  isGeoQuery.value = false;
}
// 几何查询
const geometryQuery = ()=> {
  tableData.value = []

  queryParams.tableName = this.layerName
  geometry.value.crs = { "type": "name","properties": { "name": "EPSG:4490" } }
  queryParams.geojson = JSON.stringify(geometry.value)

  // console.log("查询参数~~~~~:",this.queryParams)
  queryListByProperty(queryParams).then(res => {
    if (res.code !== 200){
      console.error(layerName.value + "查询出错！~~~~~")
      return
    }
    if (!res.total){
      isShow.value = true
      ElMessage.warning("未查询到相关数据")
    }
    queryParams.total = res.total
    const features = []
    tableData.value = res.rows.map(feature => {
      const tempObj = {}
      tempObj.dlmc = feature.properties["dlmc"]
      tempObj.feature = feature
      feature.type = "Feature"
      features.push(feature)
      return tempObj
    })
    const featureCollection = {
      "type": "FeatureCollection",
      "features": features
    }
    addGeoJSON2Map(featureCollection,defaultStyle)
    openPopup(window._map)
  })
  isRectangleClicked.value = false
  isPolygonClicked.value = false
}

// 打开popup信息弹窗
const openPopup = (map)=> {
  map.on('click',evt => {
    map.addInteraction(selectEvt.value)
  })
  const selectEvent = new Select({
    style: geometrySelectStyle["Polygon"]
  })
  selectEvt.value = selectEvent
  map.addInteraction(selectEvt.value)
  selectEvt.value.on('select',evt => {
    removeLayerByName('popupLayer',map)
    removeLayerByName('locationLayer',map)
    const features = evt.target.getFeatures().getArray()
    const feature = features[0]
    if (!feature){
      return
    }
    const vectorLayer = new Vector({
      source: new Source({
        features: features
      }),
      style: geometrySelectStyle["Polygon"]
    })

    vectorLayer.setProperties({ zIndex: 100,layerName: "popupLayer" })
    map.addLayer(vectorLayer)

    const props = feature.getProperties()
    const geometry = feature.getGeometry()
    setPopup(props,columnInfos.value, geometry, map)
    map.getView().fit(geometry)
  })
}

const clearGeometry = (isActive)=>{
  if (olDraw.value?.getDrawSource()){
    // 结束上一次的绘制并清除绘制对象
    const drawSource = olDraw.value.getDrawSource()
    const drawAction = olDraw.value.getDrawAction()
    olDraw.value.clearGeometry(drawSource)
    olDraw.value.deactiveAction(drawAction)
  }
  if (isActive) {
    setCursor('crosshair')
  } else {
    setCursor('pointer')
  }
}


onBeforeMount(()=>{
  getMapQueryData()
})

onMounted(()=>{
  olDraw.value = new OlDraw(map.value, view.value)
})

onBeforeUnmount(()=>{
  selectEvt.value = null
})



</script>

<style scoped lang="scss">
@import "@/styles/variables.module";
@import "@/styles/map2d.scss";
.query-wrap {
  width: $functionContentWidth;
  font-size: 14px;
  font-family: "微软雅黑";
  color: $mapMenuText;
  .iconfont {
    float: right;
    margin-right: 10px;
  }
  .title {
    width: 100%;
    height: 40px;
    line-height: 40px;
    color: #fff;
    background-color: #0167ccab;
    padding-left: 10px;
    &:hover {
      cursor: pointer;
    }
  }
  .property-query{
    background: #409eff21;
  }
  .select-layer{
    background: #409eff21;
  }
  .select {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    line-height: 35px;
    padding: 10px 5px;
  }
  .search-div {
    margin-bottom: 0;
    .search-input {
      width: 190px;
      height: 35px;
    }
  }
  .select-wrap{}
  .select-tabs{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 10px 0;
    .select-tab{
      width: 50%;
      padding: 10px;
      text-align: center;
      color: rgb(85, 165, 246);
      &:hover{
        cursor: pointer;
        filter: brightness(110%);
        color: #fff;
        background: #0167ccd4;
      }
    }
    .select-tab-active{
      background: #0167ccd4;
      color: #fff;
    }
  }
  .select-tab-active{
    background: #299bff;
  }
  :deep(.el-button.is-disabled) {
    color: #000;
    border-color: #a7adb2;
    :hover{
      color: #fff;}
  }
  .geo-query {
    padding: 15px;
    background: #409eff21;
    .geo-query-btn {
      float: right;
    }
    :deep(.el-button.is-disabled) {
      color: #000;
    }
    :deep(.el-button.is-disabled,
    .el-button.is-disabled:hover,
    .el-button.is-disabled:focus) {
      color: #454545;
      cursor: not-allowed;
      background-image: none;
      border-color: #a7adb2;
    }
    :deep(.el-button.is-disabled:hover,
    .el-button.is-disabled:focus,
    .el-button.is-disabled:active) {
      color: #fff;
      border-color: #0167ccab;
      background-color: #0167cc;
    }

    .draw-btn {
      padding: 12px 20px;
      height: 35px;
      line-height: 5px;
      margin-right: 10px;
      white-space: nowrap;
      cursor: pointer;
      background: #fff;
      // border: 1px solid #DCDFE6;
      border: 1px solid #a7adb2;
      color: #000;
      text-align: center;
      box-sizing: border-box;
      outline: none;
      font-weight: 500;
      font-size: 14px;
      border-radius: 4px;
    }
    //几何查询按钮激活样式
    .active {
      color: #fff;
      border-color: #0167ccab;
      background-color: #0167cc;
    }
  }
  .result-container {
    .result-table {
      overflow-y: scroll;
    }
  }
  .no-data-tip {
    background: #0000001f;
    height: 605px;
    padding-top: 284px;
    text-align: center;
    color: #fff;
    .icon-zanwushuju {
      display: block;
      float: none;
      margin-right: 0;
      font-size: 100px;
    }
    span {
      display: block;
      color: #fff;
      font-size: 16px;
      margin-top: 50px;
    }
  }
}
:deep(.el-input__inner) {
  height: 35px;
  line-height: 35px;
}
:deep(.el-select__caret) {
  line-height: 35px;
}
:deep(.el-button) {
  height: 35px;
  line-height: 5px;
}

// 分页按钮居中
.pagination {
  margin-top: 10px;
  text-align: center;
}
:deep(.el-pagination.is-background .btn-next) {
  min-width: 20px;
  height: 20px;
  line-height: 20px;
}
:deep(.el-pagination.is-background .btn-prev) {
  min-width: 20px;
  height: 20px;
  line-height: 20px;
}
:deep(.el-pagination.is-background .el-pager li) {
  min-width: 20px;
  height: 20px;
  line-height: 20px;
}
:deep(.el-pagination.is-background .el-pager li:not(.disabled).active) {
  background-color: #0166ca !important;
}
:deep(.el-table .el-table__header-wrapper th,
.el-table .el-table__fixed-header-wrapper th) {
  background-color: #083351f0;
  color: #fff;
}
:deep(.el-table tr) {
  background-color: #0b3451bf;
  color: #fff;
}
:deep(.el-table--enable-row-hover .el-table__body tr:hover > td) {
  background-color: #08345491;
}
:deep(.el-table th.is-leaf,
.el-table td) {
  border-bottom: 1px solid #ffffff7a;
}
:deep(.el-button:hover,
.el-button:focus) {
  color: #fff;
  border-color: #0167ccab;
  background-color: #0167cc;
}
:deep(.el-select-dropdown__item:hover) {
  background-color: #0167ccab;
  color: #fff;
}
:deep(.header-bg){
  height: 40px;
  background: url(/src/assets/icons/svg/u218.svg) no-repeat center center /
    cover;
}
:deep(.container .title){
    color: #95caff;
    font-size: 15px;
    }
</style>
