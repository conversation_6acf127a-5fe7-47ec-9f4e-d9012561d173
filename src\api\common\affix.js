/**
 * 全域附件
 */

import request from '@/utils/request'

// 新增全域附件
export function addAffix(data) {
  return request({
    url: '/document/affix',
    method: 'POST',
    data: data
  })
}

// 修改全域附件
export function updataAffix(data) {
  return request({
    url: '/document/affix',
    method: 'PUT',
    data: data
  })
}

/**
 * 上传附件
 * @param {string} file
  * @returns
 */
export function uploadAffix(file) {
  return request({
    url: '/document/affix/upload',
    method: 'POST',
    file: file
  })
}

/**
 * 下载附件
 * @param {string} url
  * @param {string} name
  * @returns
 */
export function download(url, name) {
  return request({
    url: `/document/affix/download?url=${url}&name=${name}`,
    method: 'GET',
    responseType: 'arraybuffer',
    timeout: 60000
  });
}

/**
 * 导出对应的业务的word文档
 * @param {string} type
  * @param {string} id
  * @returns
 */
export function exportWord(type, id) {
  return request({
    url: `/document/tz/export/word/${type}/${id}`,
    method: 'POST',
    responseType: 'blob'
  });
}
