define(["./AttributeCompression-0a087f75","./Cartographic-3309dd0d","./Rectangle-dee65d21","./IndexDatatype-8a5eead4","./Math-119be1a3","./createTaskProcessorWorker","./Cartesian2-db21342c","./Check-7b2a090c","./when-b60132fc","./WebGLConstants-4ae0db90"],(function(a,e,r,t,n,i,s,u,c,o){"use strict";var f=32767,p=new e.Cartographic,C=new e.Cartesian3;var d=new r.Rectangle,b=new r.Ellipsoid,l=new e.Cartesian3,h={min:void 0,max:void 0};var w=new e.Cartesian3,y=new e.Cartesian3,k=new e.Cartesian3,v=new e.Cartesian3,g=new e.Cartesian3;return i((function(i,s){var u=new Uint16Array(i.positions),c=new Uint16Array(i.widths),o=new Uint32Array(i.counts),A=new Uint16Array(i.batchIds);!function(a){a=new Float64Array(a);var t=0;h.min=a[t++],h.max=a[t++],r.Rectangle.unpack(a,t,d),t+=r.Rectangle.packedLength,r.Ellipsoid.unpack(a,t,b),t+=r.Ellipsoid.packedLength,e.Cartesian3.unpack(a,t,l)}(i.packedBuffer);var m,x=b,E=l,D=function(r,t,i,s,u){var c=r.length/3,o=r.subarray(0,c),d=r.subarray(c,2*c),b=r.subarray(2*c,3*c);a.AttributeCompression.zigZagDeltaDecode(o,d,b);for(var l=new Float32Array(r.length),h=0;h<c;++h){var w=o[h],y=d[h],k=b[h],v=n.CesiumMath.lerp(t.west,t.east,w/f),g=n.CesiumMath.lerp(t.south,t.north,y/f),A=n.CesiumMath.lerp(i,s,k/f),m=e.Cartographic.fromRadians(v,g,A,p),x=u.cartographicToCartesian(m,C);e.Cartesian3.pack(x,l,3*h)}return l}(u,d,h.min,h.max,x),I=D.length/3,R=4*I-4,T=new Float32Array(3*R),U=new Float32Array(3*R),F=new Float32Array(3*R),N=new Float32Array(2*R),M=new Uint16Array(R),P=0,L=0,S=0,_=0,G=o.length;for(m=0;m<G;++m){for(var W=o[m],B=c[m],z=A[m],H=0;H<W;++H){var O;if(0===H){var Y=e.Cartesian3.unpack(D,3*_,w),Z=e.Cartesian3.unpack(D,3*(_+1),y);O=e.Cartesian3.subtract(Y,Z,k),e.Cartesian3.add(Y,O,O)}else O=e.Cartesian3.unpack(D,3*(_+H-1),k);var j,q=e.Cartesian3.unpack(D,3*(_+H),v);if(H===W-1){var J=e.Cartesian3.unpack(D,3*(_+W-1),w),K=e.Cartesian3.unpack(D,3*(_+W-2),y);j=e.Cartesian3.subtract(J,K,g),e.Cartesian3.add(J,j,j)}else j=e.Cartesian3.unpack(D,3*(_+H+1),g);e.Cartesian3.subtract(O,E,O),e.Cartesian3.subtract(q,E,q),e.Cartesian3.subtract(j,E,j);for(var Q=H===W-1?2:4,V=0===H?2:0;V<Q;++V){e.Cartesian3.pack(q,T,P),e.Cartesian3.pack(O,U,P),e.Cartesian3.pack(j,F,P),P+=3;var X=V-2<0?-1:1;N[L++]=V%2*2-1,N[L++]=X*B,M[S++]=z}}_+=W}var $=t.IndexDatatype.createTypedArray(R,6*I-6),aa=0,ea=0;for(G=I-1,m=0;m<G;++m)$[ea++]=aa,$[ea++]=aa+2,$[ea++]=aa+1,$[ea++]=aa+1,$[ea++]=aa+2,$[ea++]=aa+3,aa+=4;return s.push(T.buffer,U.buffer,F.buffer),s.push(N.buffer,M.buffer,$.buffer),{indexDatatype:2===$.BYTES_PER_ELEMENT?t.IndexDatatype.UNSIGNED_SHORT:t.IndexDatatype.UNSIGNED_INT,currentPositions:T.buffer,previousPositions:U.buffer,nextPositions:F.buffer,expandAndWidth:N.buffer,batchIds:M.buffer,indices:$.buffer}}))}));
