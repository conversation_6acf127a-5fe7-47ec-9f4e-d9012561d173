<template>
  <div class="main-content">
    <transition>
      <div v-show="showSearch">
        <el-card shadow="hover">
          <el-form
            ref="queryFormRef"
            :model="queryParams" :inline="true"
            class="patrol-form"
          >
            <el-form-item
              label="项目名称"
              prop="name"
            >
              <el-input
                v-model="queryParams.name"
                placeholder="请输入项目名称"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item
              label="地块编号"
              prop="dkbh"
            >
              <el-input
                v-model="queryParams.dkbh"
                placeholder="请输入地块编号"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item
              label="巡查状态"
              prop="rwzt"
            >
              <el-select
                v-model="queryParams.rwzt"
                filterable
                placeholder="选择巡查状态"
                @change="changeSelectPatrolStatues"
                class="stater"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                icon="Search" @click="handleQuery"
              >搜索</el-button>
              <el-button
                icon="Refresh"
                @click="resetQuery"
              >重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>
    <el-row
      :gutter="10"
      class="mb8 add"
    >
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Upload"
          @click="handleExport"
        >导出
        </el-button>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      />
    </el-row>
    <el-card class="result-wrap">
      <el-table
        v-loading="loading"
        :data="patrolTaskList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          label="项目名称"
          align="center" prop="name"
        />
        <el-table-column
          label="发布时间"
          align="center" prop="releaseTime"
        />
        <el-table-column
          label="巡查状态"
          align="center" prop="rwzt"
        >
          <template #default="scope">
            <el-button
              plain
              :type="scope.row.rwzt==='1'?'success':'primary'" size="small"
            >
              {{ scope.row.rwzt==='1'?"已巡查":"未巡查"}}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          width="180"
        >
          <template #default="scope">
            <el-button
              plain
              type="primary" size="small" @click="handleAdd(scope.row)"
            >巡查</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script setup name="RelPatrolTask">
/**
 * CHN：巡查任务管理
 */
import {
  listRelPatrolTask,
  exportRelPatrolTask
} from "@/api/relocationPatrol/patrolTask"
import { downloadFileByArrayBuffer } from "@/utils/common.js"
import { useRouter } from "vue-router"
const router = useRouter()

const patrolTaskList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref();
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    dkId: undefined,
    name: undefined,
    releaseTime: undefined,
    dkmj: undefined,
    dkbh: undefined,
    tbbh: undefined,
    dkzl: undefined,
    rwfbr: undefined,
    fbrId: undefined,
    rwjsr: undefined,
    jsrId: undefined,
    yqzxsj: undefined,
    sfyfb: undefined,
    rwzt: undefined,
    bz: undefined,
    params: {}
  }
});
const options = ref([
  // 下拉菜单选项列表
  { value: "0", label: "未巡查" },
  { value: "1", label: "已巡查" }
]);

const { queryParams } = toRefs(data);

/** 查询巡查任务管理列表 */
const getList = async () => {
  loading.value = true;
  const res = await listRelPatrolTask(queryParams.value);
  patrolTaskList.value = res.rows
  total.value = res.total;
  loading.value = false;
};
/** 下拉选择 */
const changeSelectPatrolStatues = (value) => {
  queryParams.value.rwzt = value;
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/**
 * 添加巡查记录
 */
const handleAdd = (row)=>{
  router.push({ path: "/relocationPatrol/patroTaskDetail",query: { id: row.id,isAdd: 1 } })
}

/** 导出按钮操作 */
const handleExport = () => {
  exportRelPatrolTask().then(res=>{
    const mineType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    downloadFileByArrayBuffer(res,"巡查任务" + new Date().getTime() + ".xlsx",mineType)
  })
};

onMounted(() => {
  getList();
});
</script>
<style lang="scss" scoped>
.main-content {
  padding: 10px;
}
.content {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #dadada;
}
.result-wrap {
  margin-top: 10px;
}
.stater {
  width: 200px;
}
.add {
  margin-top: 10px;
}
.el-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
.patrol-form{
  /**防止输入框出现清除按钮时输入框产生宽度变化**/
  :deep(.el-input--suffix) {
    // 固定宽度
    width: 200px !important;
  }
}
@media(max-width: 1000px){
  .patrol-form{
    :deep(.el-form-item) {
      margin-bottom: 18px !important;
    }
  }
}
@media(min-width: 1000px){
  .patrol-form{
    :deep(.el-form-item) {
      margin-bottom: 0 !important;
    }
  }
}
</style>
