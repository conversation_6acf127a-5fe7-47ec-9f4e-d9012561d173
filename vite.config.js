import { defineConfig, loadEnv } from "vite"
import path from "path"
import createVitePlugins from "./vite/plugins"


// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd())
  return {
    /**
     * 开发环境配置
     */
    server: {
      port: 8099,
      host: true,
      open: true,
      proxy: {
        // https://cn.vitejs.dev/config/#server-proxy
        [env.VITE_APP_BASE_API]: {
          // target: "http://************:8080",
          // target: 'http://*************/uav-prod-api/',
        //  target: "http://***********:8086",
          target: "http://localhost:8086",
          changeOrigin: true,
          rewrite: (p) => p.replace(new RegExp('^' + env.VITE_APP_BASE_API), '')
        },
        "/geoserver/": {
          target: "http://*************:8050/geoserver/",
          // target: "http://*************:8050/geoserver/gwc/service/wmts",
          changeOrigin: true
        }
      }
    },
    // 部署生产环境和开发环境下的URL。
    // 默认情况下，vite 会假设你的应用是被部署在一个域名的根路径上
    // 例如 https://www.ruoyi.vip/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.ruoyi.vip/admin/，则设置 baseUrl 为 /admin/。
    base: env.VITE_APP_CONTEXT_PATH,
    /**
     * 生产环境配置
     */
    // 输出目录
    build: {
      // 应用输出路径，与Nginx配置一致
      outDir: "land",
      // 静态资源存放路径
      assetsDir: "assets",
      assetsInlineLimit: 4096,
      cssCodeSplit: true,
      sourcemap: false,
      rollupOptions: {
        //input: 'src/main.js',
        input: {
          main: path.resolve(__dirname, "./index.html")
        },
        output: {
          // 可以配置输出选项
          // manualChunks(id) {
          //   if (id.includes("node_modules")) {
          //     // 让每个插件都打包成独立的文件
          //     return id
          //       .toString()
          //       .split("node_modules/")[1]
          //       .split("/")[0]
          //       .toString()
          //   }
          // },
          manualChunks: undefined, // 禁用自动分块
          chunkFileNames: "js/[name]-[hash].js", // 引入文件名的名称
          entryFileNames: "js/[name]-[hash].js", // 包的入口文件名称
          assetFileNames: "[ext]/[name]-[hash].[ext]" // 资源文件像 字体，图片等
        }
      },
      // 优化依赖
      optimizeDeps: {
        exclude: ['Cesium'],
        include: [
          'vue',
          'vue-router',
          'Build/Cesium'
        ]
      },
      minify: "terser", // 客户端默认构建是esbuild，需安装terser：`npm i -D terser`
      terserOptions: {
        // 生产环境移除console、debugger
        compress: {
          drop_console: false, // 默认false
          drop_debugger: true // 默认true
        }
      },
      // 增加块大小警告限制
      chunkSizeWarningLimit: 2000,
      emptyOutDir: true,
      manifest: false,
      ssrManifest: false,
      target: ["es2015"] // 'modules',//  ["es2015", "chrome85"] // 'modules',
    },
    plugins: createVitePlugins(env, command === "build"),
    resolve: {
      // https://cn.vitejs.dev/config/#resolve-alias
      alias: {
        // 设置路径
        "~": path.resolve(__dirname, "./"),
        // 设置别名
        "@": path.resolve(__dirname, "./src")
      },
      // https://cn.vitejs.dev/config/#resolve-extensions
      extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json", ".vue"]
    },
    //fix:error:stdin>:7356:1: warning: "@charset" must be the first rule in the file
    css: {
      postcss: {
        plugins: [
          {
            postcssPlugin: "internal:charset-removal",
            AtRule: {
              charset: (atRule) => {
                if (atRule.name === "charset") {
                  atRule.remove()
                }
              }
            }
          }
        ]
      },
      extract: false,
      sourceMap: false
    },
    esbuild: {
      // * 打包去除 console.log && debugger
      pure: ["console.log", "debugger"]
    }
  }
})
