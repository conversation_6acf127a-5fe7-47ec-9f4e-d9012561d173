<template>
  <div class="measure">
    <div v-if="distance>0" class="measure-div">
      <div class="unit">
        <p>单位</p>
        <el-select v-model="distanceUnit" placeholder="请选择">
          <el-option
            v-for="item in distanceOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
      <div class="show-res">
        <p>长度</p>
        <p>{{ Number(showDistance).toFixed(4)+" "+distanceUnit }}</p>
      </div>
    </div>
  </div>
</template>

<script setup name="距离测量">

const props = defineProps({
  distance: {
    type: Number,
    require: false,
    default: 0
  }
})

const { distance } = toRefs(props)

const distanceUnit = ref('km')
const distanceOptions = reactive([
  {
    value: 'm',
    label: '米'
  },
  {
    value: 'km',
    label: '公里'
  }
])

const showDistance = computed(()=>{
  if (distanceUnit.value !== '') {
    switch (distanceUnit.value) {
      case 'm':
        return distance.value * 1000
      case 'km':
        return distance.value
    }
  }
  return ''
})

</script>
<style scoped lang="scss">
.measure-div{
  .unit{
    display: flex;
    justify-content: space-between;
    padding: 10px;
    margin: 10px 0;
    .el-select{
      width: 180px;
    }
    p{
      margin-top: 0;
      margin-bottom: 10px;
    }
  }
  .show-res{
    margin: 10px 0;
    padding: 10px;
    p:first-child{
      margin-bottom: 10px;
    }
  }
}
</style>
