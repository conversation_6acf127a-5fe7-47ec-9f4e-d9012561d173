/**
 * @name: MeasureHeight
 * @description: 量测高度工具类
 * @author: zyc
 * @time: 2024-05-20
 **/

import CesiumMeasureBaseClass from "@/utils/Cesium/CesiumMeasureBaseClass";
import { setCursor } from "@/utils/Cesium/CesiumTool";

export default class CesiumMeasureHeightClass extends CesiumMeasureBaseClass{
  constructor(props) {
    super(props)
    this.positions = []
  }
  initEvent(){
    setCursor(this.viewer3d,'crosshair')
    // 注册测量事件
    this.handler = new Cesium.ScreenSpaceEventHandler(this.viewer3d.canvas)

    const polyline = {
      name: "绘制线段",
      show: true,
      polyline: {
        positions: new Cesium.CallbackProperty(() => {
          return this.positions;
        }, false),
        width: 2.5,
        show: true,
        material: Cesium.Color.BLANCHEDALMOND,
        clampToGround: this.clampToGround
      }
    }
    this.viewer3d.entities.add(polyline)

    this.registerEvent()
  }
  registerEvent(){
    this.mouseClick()
    this.mouseMove()
    this.mouseDoubleClick()
  }

  /**
   * 鼠标单击事件：开始绘制
   */
  mouseClick(){
    this.handler.setInputAction(evt => {
      const windowPosition = evt.position
      const worldPosition = this.viewer3d.scene.pickPosition(windowPosition)
      if (Cesium.defined(worldPosition)){
        this.positions.push(worldPosition)
        // 添加点
        this.viewer3d.entities.add(new Cesium.Entity({
          name: "测量点",
          position: worldPosition,
          point: new Cesium.PointGraphics({
            color: Cesium.Color.AQUA,
            pixelSize: 5,
            outlineColor: Cesium.Color.GHOSTWHITE,
            outlineWidth: 1,
            distanceDisplayCondition: 10,
            heightReference: this.heightMode
          }),
          label: new Cesium.LabelGraphics({
            // text: this.distance,
            font: "10px",
            pixelOffset: new Cesium.Cartesian3(0, -40,100000),
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            outlineWidth: 9,
            outlineColor: Cesium.Color.YELLOW
          })
        }))
        if (this.positions.length === 2){
          // 添加垂直线和水平线
          const posObj = this.computePosition(this.positions[0],this.positions[1])
          const computeRes = this.computeDistance(posObj)
          this.createLabel(posObj,computeRes)
          const heightRes = computeRes.height / 1000
          this.setArea(heightRes)
          // 竖直线
          const vLine = new Cesium.Entity({
            name: "mLine",
            polyline: {
              positions: new Cesium.CallbackProperty(() => {
                return [posObj.lowPosition,posObj.midPosition];
              }, false),
              width: 2,
              show: true,
              material: Cesium.Color.BLANCHEDALMOND,
              clampToGround: this.clampToGround
            }
          })
          // 添加显示临时线段：
          this.viewer3d.entities.add(vLine)
          // 水平线
          const hLine = new Cesium.Entity({
            name: "水平线",
            polyline: {
              positions: new Cesium.CallbackProperty(() => {
                return [posObj.highPosition,posObj.midPosition];
              }, false),
              width: 2,
              show: true,
              material: Cesium.Color.BLANCHEDALMOND,
              clampToGround: this.clampToGround
            }
          })
          this.viewer3d.entities.add(hLine)
          // 结束量测
          this.deactive()
        }
      }
    },Cesium.ScreenSpaceEventType.LEFT_CLICK)
  }
  /**
   *  3.鼠标双击事件：结束绘制
   */
  mouseMove(){
    this.handler.setInputAction(evt => {
      const windowPosition = evt.endPosition
      this.infoBox.hidden = true
      // this.infoBox.style.top = windowPosition.y - 10 + "px"
      // this.infoBox.style.left = windowPosition.x + 20 + "px"

      const worldPosition = this.viewer3d.scene.pickPosition(windowPosition)
      const hasTempLine = this.viewer3d.entities.getById('tempLine')
      const hasVLine = this.viewer3d.entities.getById('vLine')
      const hasHLine = this.viewer3d.entities.getById('hLine')

      if (hasTempLine){
        this.viewer3d.entities.remove(hasTempLine)
      }
      if (hasVLine && hasHLine){
        this.viewer3d.entities.remove(hasVLine)
        this.viewer3d.entities.remove(hasHLine)
      }
      // 添加临时线段
      if (this.positions.length){
        const polyline = new Cesium.Entity({
          id: "tempLine",
          name: 'tempLine',
          polyline: {
            positions: new Cesium.CallbackProperty(() => {
              return [].concat(this.positions[0],worldPosition);
            }, false),
            width: 2,
            show: true,
            material: Cesium.Color.BLANCHEDALMOND,
            clampToGround: this.clampToGround
          }
        })
        this.viewer3d.entities.add(polyline)

        // 添加垂直线和水平线
        const posObj = this.computePosition(this.positions[0],worldPosition)
        const computeRes = this.computeDistance(posObj)

        const heightRes = computeRes.height / 1000
        this.setArea(heightRes)
        // 竖直线
        const vLine = new Cesium.Entity({
          id: "vLine",
          name: "vLine",
          polyline: {
            positions: new Cesium.CallbackProperty(() => {
              return [posObj.lowPosition,posObj.midPosition];
            }, false),
            width: 2,
            show: true,
            material: Cesium.Color.BLANCHEDALMOND,
            clampToGround: this.clampToGround
          }
        })
        // 添加显示临时线段：
        this.viewer3d.entities.add(vLine)
        // 水平线
        const hLine = new Cesium.Entity({
          id: "hLine",
          name: "hLine",
          polyline: {
            positions: new Cesium.CallbackProperty(() => {
              return [posObj.highPosition,posObj.midPosition];
            }, false),
            width: 2,
            show: true,
            material: Cesium.Color.BLANCHEDALMOND,
            clampToGround: this.clampToGround
          }
        })
        this.viewer3d.entities.add(hLine)
        this.createLabel(posObj,computeRes)
        // 计算距离
      }
      // 绘制临时线段
    },Cesium.ScreenSpaceEventType.MOUSE_MOVE)
  }

  /**
   * 鼠标双击事件
   */
  mouseDoubleClick(){
    // 暂时无用
  }

  /**
   * 计算位置关系
   */
  computePosition(p1, p2) {
    const cartographic1 = Cesium.Cartographic.fromCartesian(p1.clone());
    const cartographic2 = Cesium.Cartographic.fromCartesian(p2.clone());

    const highPosition = cartographic1.height > cartographic2.height ? p1 : p2
    const lowPosition = cartographic1.height > cartographic2.height ? p2 : p1
    const midPosition = new Cesium.Cartesian3(lowPosition.x,lowPosition.y,highPosition.z)
    return {
      highPosition,midPosition,lowPosition
    }
  }

  /**
   * 计算水平距离和垂直距离
   */
  computeDistance(posObj){
    const h = Cesium.Cartographic.fromCartesian(posObj.midPosition).height -
      Cesium.Cartographic.fromCartesian(posObj.lowPosition).height
    const height = Number(h.toFixed(2))
    const d = Cesium.Cartesian3.distance(posObj.midPosition,posObj.highPosition)
    const distance = Number(d.toFixed(2))
    return {
      height,distance
    }
  }
  /**
   * 创建标签
   */
  createLabel(posObj,result){
    const hasHLabel = this.viewer3d.entities.getById('hLabel')
    const hasVLabel = this.viewer3d.entities.getById('vLabel')
    if (hasHLabel && hasVLabel){
      this.viewer3d.entities.remove(hasHLabel)
      this.viewer3d.entities.remove(hasVLabel)
    }
    // 计算水平中心点
    const hAddPoint = Cesium.Cartesian3.add(posObj.midPosition, posObj.highPosition, new Cesium.Cartesian3());
    const hPoint = Cesium.Cartesian3.divideByScalar(hAddPoint, 2.0, new Cesium.Cartesian3());
    // 计算竖直中心点
    const vAddPoint = Cesium.Cartesian3.add(posObj.midPosition, posObj.lowPosition, new Cesium.Cartesian3());
    const vPoint = Cesium.Cartesian3.divideByScalar(vAddPoint, 2.0, new Cesium.Cartesian3());

    // 添加水平标签
    this.viewer3d.entities.add({
      id: "hLabel",
      position: hPoint,
      type: "hPoint",
      label: {
        text: "水平距离 " + result.distance + "米",
        scale: 0.5,
        // eyeOffset: new Cartesian3(0.0, 0.0, 1000.0),
        heightReference: this.heightMode,
        font: 'normal 30px MicroSoft YaHei',
        // distanceDisplayCondition: new SuperMap3D.DistanceDisplayCondition(0, 5000),
        // scaleByDistance: new SuperMap3D.NearFarScalar(1000, 1, 3000, 0.4),
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian3(0, 0,hPoint.z + 10000),
        outlineWidth: 9,
        outlineColor: Cesium.Color.YELLOW
      }
    })

    // 添加竖直标签
    this.viewer3d.entities.add({
      id: "vLabel",
      position: vPoint,
      type: "vPoint",
      label: {
        text: "竖直距离 " + result.height + "米",
        scale: 0.5,
        // eyeOffset: new Cartesian3(0.0, 0.0, 1000.0),
        heightReference: this.heightMode,
        font: 'normal 30px MicroSoft YaHei',
        // distanceDisplayCondition: new SuperMap3D.DistanceDisplayCondition(0, 5000),
        // scaleByDistance: new SuperMap3D.NearFarScalar(1000, 1, 3000, 0.4),
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian3(-30, 0,100000),
        outlineWidth: 9,
        outlineColor: Cesium.Color.YELLOW
      }
    })
  }

  /**
   * 监听测量事件
   * @param value
   */
  setArea(value){
    // 注册一个自定义事件
    const event = new CustomEvent('valueChange',{
      detail: value
    })
    this.viewer3d.container.dispatchEvent(event)
  }
  /**
   * 结束绘制
   */
  deactive(){
    if (this.handler){
      this.handler.destroy()
    }
    this.infoBox.remove()
    setCursor(this.viewer3d,"pointer")
  }
  /**
   * 清除绘制
   */
  clearMeasure(){
    const isDestroyed = this.handler.isDestroyed()
    if (!isDestroyed){
      // 若事件未销毁，则销毁；若销毁，则不可用
      this.handler.destroy()
    }
    this.infoBox.remove()
    this.viewer3d.entities.removeAll()
  }
}
