import { reactive } from "vue"

const eventBus = reactive({
  events: {},
  emit(event,...args){
    if(this.events[event]){
      this.events[event].forEach(fn=>fn(...args))
    }
  },
  on(event,callback){
    if(!this.events[event]){
      this.events[event] = []
      this.events[event].push(callback)
    }
  },
  off(event,callback){
    if(this.events[event]){
      if(callback){
        this.events[event] = this.events[event].filter(fn=>fn !== callback)
      }else {
        delete this.events[event]
      }
    }
  }
})

export default eventBus
