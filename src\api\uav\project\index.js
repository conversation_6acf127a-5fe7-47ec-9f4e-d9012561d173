import request from "@/utils/request";

// 修改项目基础信息（包含项目的元数据，不包含空间信息）
export const putProject = (data) => {
    return request({
        url: "/uav/project",
        method: "put",
        data: data
    });
};

// 新增项目基础信息（包含项目的元数据，不包含空间信息）
export const addProject = (data) => {
    return request({
        url: "/uav/project",
        method: "post",
        data: data
    });
};

// 通过传入shp的ZIP压缩文件上传项目的基本信息和几何信息
export const projectImportShp = (data) => {
    return request({
        url: "/uav/project/import/shp",
        method: "post",
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        data: data
    });
};

// 导出项目基础信息（包含项目的元数据，空间信息）列表
export const projectExport = (data) => {
    return request({
        url: "/uav/project/export",
        method: "post",
        data: data
    });
};

// 获取项目基础信息（包含项目的元数据，不包含空间信息）详细信息
export const projectById = (projectId) => {
    return request({
        url: "/uav/project/" + projectId,
        method: "get"
    });
};

// 获取对应的项目的项目详情
export const projectByProjectId = (projectId) => {
    return request({
        url: "/uav/project/detail/" + projectId,
        method: "get"
    });
};

// 查询司空2数据同步情况
export const ProjectRecordList = (query) => {
    return request({
        url: "/uav/project/sync-record/list",
        method: "get",
        params: query
    });
};

// 查询对应项目对应时间段的航拍数据
export const ProjectPicList = (query) => {
    return request({
        url: "/uav/project/pic/list",
        method: "get",
        params: query
    });
};

// 查询项目基础信息（包含项目的元数据，不包含空间信息）列表
export const ProjectList = (query) => {
    return request({
        url: "/uav/project/list",
        method: "get",
        params: query
    });
};

// 删除项目基础信息（包含项目的元数据，不包含空间信息）
export const deleteProjectById = (projectIds) => {
    return request({
        url: "/uav/project/" + projectIds,
        method: "delete",
    });
};
