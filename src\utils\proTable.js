/**
 * @description 生成唯一 uuid
 * @returns {String}
 */
export function generateUUID() {
    let uuid = "";
    for (let i = 0; i < 32; i++) {
      let random = (Math.random() * 16) | 0;
      if (i === 8 || i === 12 || i === 16 || i === 20) uuid += "-";
      uuid += (i === 12 ? 4 : i === 16 ? (random & 3) | 8 : random).toString(16);
    }
    return uuid;
  }

  /**
 * @description 处理 prop 为多级嵌套的情况，返回的数据 (列如: prop: user.name)
 * @param {Object} row 当前行数据
 * @param {String} prop 当前 prop
 * @returns {*}
 * */
export function handleRowAccordingToProp(row, prop) {
  if (!prop.includes(".")) return row[prop] ?? "--";
  prop.split(".").forEach(item => (row = row[item] ?? "--"));
  return row;
}
  
  /**
   * @description 处理 prop，当 prop 为多级嵌套时 ==> 返回最后一级 prop
   * @param {String} prop 当前 prop
   * @returns {String}
   * */
  export function handleProp(prop) {
    const propArr = prop.split(".");
    if (propArr.length == 1) return prop;
    return propArr[propArr.length - 1];
  }

  /**
 * @description 根据枚举列表查询所需的数据（如果指定了 label 和 value 的 key 值，会自动识别格式化）
 * @param {String} callValue 当前单元格值
 * @param {Array} enumData 字典列表
 * @param {Object} fieldNames 包含 label、value 和 children 的 key 值
 * @param {String} type 过滤类型（目前只有 tag）
 * @returns {String}
 */
export function filterEnum(callValue, enumData, fieldNames, type) {
    const value = fieldNames && fieldNames.value ? fieldNames.value : "dictValue";
    const label = fieldNames && fieldNames.label ? fieldNames.label : "dictLabel";
    const children = fieldNames && fieldNames.children ? fieldNames.children : "children";
    let filterData = {};
  
    // 判断 enumData 是否为数组
    if (Array.isArray(enumData)) {
      filterData = findItemNested(enumData, callValue, value, children);
    }
  
    // 判断是否输出的结果为 tag 类型
    if (type === "tag") {
      return filterData && filterData.tagType ? filterData.tagType : "";
    } else {
      return filterData ? filterData[label] : "--";
    }
  }
  
  /**
   * @description 递归查找 callValue 对应的 enum 值
   * @param {Array} enumData 字典数据
   * @param {String} callValue 当前单元格值
   * @param {String} value 用于匹配的键名
   * @param {String} children 嵌套子级的键名
   * @returns {Object|null}
   */
  export function findItemNested(enumData, callValue, value, children) {
    return enumData.reduce((accumulator, current) => {
      if (accumulator) return accumulator;
      if (current[value] === callValue) return current;
      if (current[children]) {
        return findItemNested(current[children], callValue, value, children);
      }
      return null; // 这一行是需要返回 null 来处理在 reduce 方法中不符合条件的情况
    }, null);
  }

  /**
 * @description 处理 ProTable 值为数组 || 无数据
 * @param {*} callValue 需要处理的值
 * @returns {String}
 * */
export function formatValue(callValue) {
  // 如果当前值为数组，使用 / 拼接（根据需求自定义）
  if (Array.isArray(callValue)) return callValue.length ? callValue.join(" / ") : "--";
  return callValue ?? "--";
}