<template>
  <div class="main-content">
    <div v-if="!showDetail">
      <transition>
        <div
          v-show="showSearch"
          class="mb-[10px]"
        >
          <el-card shadow="hover">
            <el-form
              ref="queryFormRef"
              :model="queryParams" :inline="true" class="query-form"
            >
              <el-form-item
                label="项目名称"
                prop="xmmc"
              >
                <el-input
                  v-model="queryParams.xmmc"
                  placeholder="请输入项目名称"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item
                label="地块名称"
                prop="dkmc"
              >
                <el-input
                  v-model="queryParams.dkmc"
                  clearable
                  placeholder="请输入地块名称"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item
                label="详细地址"
                prop="dz"
              >
                <el-input
                  v-model="queryParams.dz"
                  placeholder="请输入详细地址"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  icon="Search" @click="handleQuery"
                >搜索
                </el-button>
                <el-button
                  icon="Refresh"
                  @click="resetQuery"
                >重置</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </div>
      </transition>

      <el-row
        :gutter="10"
        class="mb8 new"
      >
        <el-col :span="1.5">
          <el-button
            type="primary" plain icon="Plus"
            @click="handleAdd"
          >新增
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success" plain icon="Upload"
            @click="handleImport"
          > 批量导入（Shape）
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning" plain icon=""
            @click="handleExport"
          >导出
          </el-button>
        </el-col>
        <right-toolbar
          v-model:showSearch="showSearch"
          @queryTable="getList"
        />
      </el-row>
      <el-card class="result-wrap">
        <el-table
          v-loading="loading"
          :data="relLandList"
        >
          <el-table-column
            label="行政区名称"
            align="center" prop="xzqmc"
          />
          <el-table-column
            label="项目名称"
            align="center" prop="xmmc"
          />
          <el-table-column
            label="地块名称"
            align="center" prop="dkmc"
          />
          <el-table-column
            label="地块编号"
            align="center" prop="dkbh"
          />
          <el-table-column
            label="地块面积（公顷）"
            align="center" prop="dkmj"
          />
          <el-table-column
            label="管护方式"
            align="center" prop="jhghfs"
          >
            <template #default="scope">
              <el-button
                plain
                type="primary" size="small"
                v-if="scope.row.jhghfs === '1'"
              >自行管护</el-button>
              <el-button
                plain
                type="success" size="small"
                v-else-if="scope.row.jhghfs === '2'"
              >委托管护</el-button>
              <el-button
                plain
                type="success" size="small"
                v-else-if="scope.row.jhghfs === '3'"
              >其他</el-button>
              <el-button
                plain
                type="warning" size="small"
                v-else
              >暂无</el-button>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="180"
          >
            <template #default="scope">
              <el-button
                plain
                type="primary" size="small" @click="handleUpdate(scope.row)"
              >修改</el-button>
              <el-button
                plain
                type="danger" size="small" @click="handleDelete(scope.row)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>
    <!-- 添加或修改拆迁地块信息对话框 -->
    <div
      v-else
      class="content"
    >
      <div class="add-header-title-land">
        <div class="add-title">{{ ghTitle }}</div>
        <div
          class="add-title-return"
          @click="cancel"
        >
          <img
            src="@/assets/images/img-return.png"
            class="back"
          >
          <div class="backlist">返回列表</div>
        </div>
      </div>
      <div class="content-land">
        <el-row
          :span="24"
          style="width: 100%"
        >
          <el-col :span="14">
            <div class="add-content-land">
              <el-form
                ref="relLandFormRef"
                :rules="rules"
                label-width="180px"
                style="padding: 10px;"
                :model="form"
              >
                <div>
                  <div class="content-title-land">
                    <img src="@/assets/images/left.png">
                    <p>地块信息</p>
                  </div>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item
                        label="地块名称"
                        prop="dkmc"
                      >
                        <el-input v-model="form.dkmc"/>
                      </el-form-item>
                      <el-form-item
                        label="地块编号"
                        prop="dkbh"
                      >
                        <el-input
                          v-model="form.dkbh"
                          placeholder="请输入地块编号"
                        />
                      </el-form-item>
                      <el-form-item
                        label="地块面积"
                        prop="dkmj"
                      >
                        <el-input
                          v-model="form.dkmj"
                        >
                          <template #append>公顷</template>
                        </el-input>
                      </el-form-item>
                      <el-form-item
                        label="行政区域"
                        prop="xzqdm"
                      >
                        <el-tree-select
                          v-model="form.xzqdm" :data="region"
                          node-key="id"
                          :props="props"
                          check-strictly
                          @change="handleSelectionChange"
                          filterable style="width: 100%"
                        />
                      </el-form-item>
                      <el-form-item
                        label="储备机构"
                        prop="cbjg"
                      >
                        <el-input v-model="form.cbjg"/>
                      </el-form-item>
                      <el-form-item
                        label="计划利用方式"
                        prop="jhlyfs"
                      >
                        <el-select
                          v-model="form.jhlyfs"
                        >
                          <el-option
                            :key="1"
                            label="有偿" value="1"
                          />
                          <el-option
                            :key="2"
                            label="无偿" value="2"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item
                        label="地块标识码"
                        prop="dkbsm"
                      >
                        <el-input v-model="form.dkbsm"/>
                      </el-form-item>
                      <el-form-item
                        label="项目名称"
                        prop="xmmc"
                      >
                        <el-input v-model="form.xmmc"/>
                      </el-form-item>
                      <el-form-item
                        label="规划用途"
                        prop="ghyt"
                      >
                        <el-tree-select
                          v-model="form.ghyt" :data="ghdldm"
                          show-checkbox
                          check-strictly
                          filterable style="width: 100%"
                        />
                      </el-form-item>
                      <el-form-item
                        label="来源"
                        prop="ly"
                      >
                        <el-select
                          v-model="form.ly"
                        >
                          <el-option
                            :key="1"
                            label="依法收回" value="1"
                          />
                          <el-option
                            :key="2"
                            label="新增用地" value="2"
                          />
                          <el-option
                            :key="3"
                            label="其他" value="3"
                          />
                        </el-select>
                      </el-form-item>
                      <el-form-item
                        label="计划管护方式"
                        prop="jhghfs"
                      >
                        <el-select
                          v-model="form.jhghfs"
                        >
                          <el-option
                            :key="1"
                            label="自行管护" value="1"
                          />
                          <el-option
                            :key="2"
                            label="委托管护" value="2"
                          />
                          <el-option
                            :key="3"
                            label="其他" value="3"
                          />
                        </el-select>
                      </el-form-item>
                      <el-form-item
                        label="图斑编号"
                        prop="tbbh"
                      >
                        <el-input v-model="form.tbbh"/>
                      </el-form-item>
                    </el-col>
                    <el-col :span="24">
                      <el-form-item
                        label="备注"
                        prop="bz"
                      >
                        <el-input
                          v-model="form.bz"
                          :rows="2" type="textarea"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
                <div>
                  <div class="content-title-land">
                    <img src="@/assets/images/left.png">
                    <p>四至范围</p>
                  </div>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item
                        label="四至东"
                        prop="zdszd"
                      >
                        <el-input v-model="form.zdszd"/>
                      </el-form-item>
                      <el-form-item
                        label="四至西"
                        prop="zdszx"
                      >
                        <el-input v-model="form.zdszx"/>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item
                        label="四至南"
                        prop="zdszn"
                      >
                        <el-input v-model="form.zdszn"/>
                      </el-form-item>
                      <el-form-item
                        label="四至北"
                        prop="zdszb"
                      >
                        <el-input v-model="form.zdszb"/>
                      </el-form-item>
                    </el-col>
                    <el-col :span="24">
                      <el-form-item
                        label="详细地址"
                        prop="dz"
                      >
                        <el-input
                          v-model="form.dz"
                          :rows="2" type="textarea"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
              </el-form>
            </div>
          </el-col>
          <el-col
            :span="10"
            class="viewer-project"
          >
            <Viewer2d
              :viewerId="viewerProjectId"
              @onCreateViewer2d="createViewer2d"
            />
          </el-col>
        </el-row>
      </div>
      <div class="footer-button-land">
        <el-button
          :loading="buttonLoading"
          type="primary" @click="submitForm" plain
        >
          <el-icon>
            <CircleCheckFilled/>
          </el-icon>
          提交项目信息
        </el-button>
        <el-button
          @click="cancel"
          plain
        >
          <el-icon>
            <CircleCloseFilled/>
          </el-icon>
          取 消
        </el-button>
      </div>
    </div>

    <!--  项目模板数据导入对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="导入项目范围数据"
      width="500"
      :before-close="handleDialogClose"
    >
      <el-upload
        ref="projectUploadRef"
        class="upload-demo"
        drag
        multiple
        accept=".zip"
        :action="uploadModelActionUrl"
        :headers="headers"
        :before-upload="beforeUploadProject"
        :on-success="handleProjectSuccess"
        :on-error="handleProjectError"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          拖动文件 或者<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            请上传 Shapefile 【.zip】 压缩文件 <span class="upload-tip">（坐标系：CGCS2000）</span>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="submitProjectModel"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ReserveLandManagement">
/**
 * CHN：储备土地管理
 */

import { getRegionTreeList } from "@/api/gis/layerTree.js";
import { deleteReserveLand, reserveLandlist } from "@/api/patrol/reserveProject.js"
import { addLand, getLandDetail, updateLand } from "@/api/patrol/tempMaintenance.js";
import { getGhdldm } from "@/api/system/dict/data.js";
import UseViewer2d from "@/components/GISTools/Viewer/UseViewer2d.js";
import Viewer2d from "@/components/GISTools/Viewer/Viewer2d.vue";
import { getToken } from "@/utils/auth.js"
import { ElMessage } from "element-plus"
import { addGeoJSON2Map } from "@/utils/OpenLayers/olLayer.js"

const { proxy } = getCurrentInstance();

const relLandList = ref([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const total = ref(0);

const queryFormRef = ref();
const relLandFormRef = ref();

const land = ref({});

const showDetail = ref(false);

// 上传储备土地Shape数据对话框参数
const projectUploadRef = ref()
const dialogVisible = ref(false)

const headers = {
  Authorization: "Bearer " + getToken(),
  clientid: import.meta.env.VITE_APP_CLIENT_ID
}
// 批量上传征拆项目范围数据接口
const uploadModelActionUrl = import.meta.env.VITE_APP_BASE_API + "/patrol/land/importShape"

const projectForm = ref({})
const showMap = ref(false)
const viewerProjectId = ref("viewerProjectId")
const viewer2d = ref(null)


const dialog = reactive({
  visible: false,
  title: ""
});

const initFormData = {
  id: undefined,
  xzqdm: undefined,
  xzqmc: undefined,
  dkbh: undefined,
  tbbh: undefined,
  dkmc: undefined,
  ghyt: undefined,
  dkmj: undefined,
  dz: undefined,
  tdzt: undefined,
  dkbsm: undefined,
  bz: undefined
};
const data = reactive({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    xzqdm: undefined,
    xzqmc: undefined,
    dkbh: undefined,
    tbbh: undefined,
    dkmc: undefined,
    ghyt: undefined,
    dkmj: undefined,
    dz: undefined,
    tdzt: undefined,
    smgeometry: undefined,
    bz: undefined,
    params: {}
  },
  rules: {
    xzqdm: [{ required: true, message: "行政区代码不能为空", trigger: "blur" }],
    dkbh: [{ required: true, message: "地块编号不能为空", trigger: "blur" }],
    dkmc: [{ required: true, message: "地块名称不能为空", trigger: "blur" }],
    ghyt: [{ required: true, message: "规划用途不能为空", trigger: "blur" }],
    dkmj: [{ required: true, message: "地块面积不能为空", trigger: "blur" }],
    dz: [{ required: true, message: "地不能为空", trigger: "blur" }],
    tdzt: [{ required: true, message: "土地状态不能为空", trigger: "blur" }]
  }
});

const { queryParams, form, rules } = toRefs(data);

const ghdldm = ref([]);
getGhdldm().then((res) => {
  ghdldm.value = res.data;
});

/** 查询拆迁地块信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await reserveLandlist(queryParams.value);
  relLandList.value = res.rows.map(item=>{
    // 转换面积单位：平方米->公顷
    item.dkmj = ((item.dkmj) * 1e-4).toFixed(4);
    return item;
  });
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  showDetail.value = false;
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  land.value = {};
  relLandFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 选中数据 */
const handleSelectionChange = (value) => {
  const findNodeById = (selection) => {
    let stack = [...region.value];
    while (stack.length) {
      const node = stack.pop();
      if (node.areaCode === selection) {
        form.value.xzqmc = node.name; // 更新label值
        return; // 找到后退出函数
      }
      if (node.children) node.children.forEach(child => stack.push(child));
    }
  };
  findNodeById(value);
};

//标题变量
const ghTitle = ref('新增收储地块信息')

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  showDetail.value = true;
  ghTitle.value = '新增收储地块信息'
};

/** 修改按钮操作 */
const handleUpdate = async (row) => {
  reset();
  ghTitle.value = '修改收储地块信息'
  const _id = row?.id || ids.value[0];
  const response = await getLandDetail(_id);

  const transformData = {}
  Object.keys(response.data).forEach(key=>{
    if(key === "dkmj"){
      // 转换面积单位：平方米->公顷
      transformData[key] = ((response.data[key]) * 1e-4).toFixed(4);
      return
    }
    transformData[key] = response.data[key]
  })
  Object.assign(form.value, transformData);
  showDetail.value = true;
};

/** 提交按钮 */
const submitForm = () => {
  relLandFormRef.value?.validate(async (valid) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateLand(form.value).finally(
          () => (buttonLoading.value = false)
        );
      } else {
        await addLand(form.value).finally(
          () => (buttonLoading.value = false)
        );
      }
      proxy?.$modal.msgSuccess("操作成功");
      showDetail.value = false;
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal
    .confirm('是否确认删除收储地块信息编号为"' + _ids + '"的数据项？')
    .finally(() => (loading.value = false));
  await deleteReserveLand(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    "patrol/relLand/export",
    {
      ...queryParams.value
    },
    `relLand_${new Date().getTime()}.xlsx`
  );
};

const props = {
  value: 'areaCode',
  children: 'children',
  label: 'name'
}
const region = ref([]);
const regionTree = async () => {
  const tree = await getRegionTreeList();
  region.value = tree.data;
}

const createFilter = async (queryString) => {
  return await reserveLandlist({
    dkbh: queryString,
    pageNum: 1,
    pageSize: 10
  });
}

const isAddGeometry = computed(()=>{
  return form.value && viewer2d.value
})

watch(isAddGeometry,(value)=>{
  if(value){
    const geoJson = JSON.parse(form.value.smgeometry)
    addGeoJSON2Map(geoJson, undefined, viewer2d.value)
    // viewer2d.value.getView().setZoom(15)
  }
})

/**
 * 创建2d视图
 * @param viewer2dId
 * @param mapInitStore
 * @param mapViewStore
 */
const createViewer2d = async (viewer2dId,mapInitStore,mapViewStore)=>{
  const useViewer2d = new UseViewer2d(viewer2dId,mapInitStore,mapViewStore)
  await useViewer2d.createViewer2d()
  viewer2d.value = useViewer2d.map
}
/**
 * 项目Shape数据批量导入
 */
const handleImport = ()=>{
  dialogVisible.value = true
}


const beforeUploadProject = (file)=>{
  return true;
}

const handleProjectSuccess = (response,uploadFile)=>{
  projectUploadRef.value.clearFiles()
  ElMessage.success("项目数据上传城功")
  getList()
}

const handleProjectError = ()=>{
  projectUploadRef.value.clearFiles()
  ElMessage.error("项目数据上传失败")
}

const submitProjectModel = ()=>{
  dialogVisible.value = false
}
// 关闭导入对话框
const handleDialogClose = ()=>{
  projectUploadRef.value.clearFiles()
  dialogVisible.value = false
}
onMounted(() => {
  getList();
  regionTree();
});
</script>
<style scoped lang="scss">
.main-content {
  padding: 10px;
}

.content {
  border: 1px solid rgb(233, 233, 233);
  border-radius: 4px;
}

.content-land {
  display: flex;
}
.add-content-land {
  padding: 0px 10px;
}

.map-content-land {
  margin-top: 70px;
}

.result-wrap {
  margin-top: 10px;
}
.new {
  margin-top: 10px;
}

.add-header-title-land {
  padding: 10px;
  display: flex;
  justify-content: space-between;
  height: 50px;
  background-color: rgb(222, 239, 255);
  box-sizing: border-box;
  border-bottom: 1px solid rgb(233, 233, 233);
  font-weight: 700;
  font-size: 14px;
  line-height: 28px;
}

.add-title-return {
  display: flex;
  align-content: center;
  color: rgb(32, 119, 255);
  cursor: pointer;
  font-weight: normal;
  &:hover{
    cursor: pointer;
    font-size: 16px;
    transform: scale(1.15);
    transition: all ease-in .25s;
  }
}

.back {
  height: 18px;
  width: 18px;
  margin-top: 5px;
}

.backlist {
  padding-left: 6px;
  font-size: 14px;
}
.content-title-land {
  font-weight: bold;
  display: flex;
  align-items: center;

  p {
    margin-left: 8px;
  }
}

.footer-button-land {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  border-top: 1px solid #e0e0e0;
  padding: 20px 0;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  button {
    height: 40px;
  }
}

.laberone {
  height: 60px;
  width: 100%;
  display: flex;
}

.titleone {
  height: 100%;
  width: 90px;
  text-align: center;
}

.information-rev {
  margin-top: 4px;

  img {
    width: 30px;
    height: auto;
    transform: rotate(-1deg);
  }
}
.tab-border-map {
  border: 1px solid rgb(173, 211, 246);
  padding: 10px;
  margin-right: 10px;
  border-radius: 4px;
}
.tab-active {
  color: #0f7dff;
  font-weight: bold;
}

.tab-content-map {
  height: 550px;
}

.attachment {
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  width: 470px !important;
}

.related_accessories {
  height: 8px;
  background-color: rgba(33, 120, 255, 1);
  border-radius: 4px 4px 0 0;
}

.related {
  border-bottom: 1px solid rgb(230, 230, 230);
  height: 40px;
  line-height: 40px;
  align-content: center;
  padding-left: 10px;
  font-size: small;
  font-weight: bold;
}

.upload-tip{
  color:red;
}
.viewer-project{
  margin-top: 70px;
  border: 1px solid rgb(233, 233, 233);
}

.query-form{
  /**防止输入框出现清除按钮时输入框产生宽度变化**/
  :deep(.el-input--suffix) {
    // 固定宽度
    width: 200px !important;
  }
}
@media(max-width: 1000px){
  .query-form{
    :deep(.el-form-item) {
      margin-bottom: 18px !important;
    }
  }
}
@media(min-width: 1000px){
  .query-form{
    :deep(.el-form-item) {
      margin-bottom: 0 !important;
    }
  }
}
</style>
