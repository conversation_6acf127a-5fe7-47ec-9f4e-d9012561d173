/**
 * @name: UseViewer3d
 * @description：3d视图管理
 * @author: zyc
 * @time: 2025-05-12
 **/
import eventBus from "@/utils/eventBus.js"
import { flyToPoint, setSceneEffect } from "@/utils/Cesium/CesiumTool.js"
import { addTdtImageLayer, addTDTTerrainLayer, addWMTSLayer } from "@/utils/Cesium/CesiumLayer.js"

export default class UseViewer3d {
  /**
   * 创建3d视图
   * @param viewerId {String}：地图挂载元素id选择器|地图挂载元素
   * @param mapInitStore {Store}：地图初始化信息
   * @param mapViewStore {Store}：地图视图信息
   */
  constructor(viewerId,mapInitStore,mapViewStore) {
    console.log("现在开始了，从这里加载！！！：")
    this.viewerId = viewerId;
    this.mapInitStore = mapInitStore;
    this.mapViewStore = mapViewStore;

    this.viewer3d = null
  }
  async createViewer3d() {
    try{
      window._viewer3d = null;
      const viewer3d = new Cesium.Viewer(this.viewerId, {
        selectionIndicator: false,
        timeline: false,
        baseLayerPicker: false,
        shadows: true,
        infoBox: false,
        geocoder: false,
        skyBox: false, // 关闭天空盒会一同关闭太阳，场景会变暗
        navigation: true // 初始化导航控件
      });
      this.viewer3d = viewer3d
      const scene = viewer3d.scene
      this.mapViewStore.viewer3d = viewer3d

      // 去除supermap SuperMap3D 超图logo--Cesium
      viewer3d._cesiumWidget._creditContainer.style.display = "none";

      const mapInfo = await this.mapInitStore.getMapInfo()
      const center = mapInfo.center
      flyToPoint(viewer3d,{ lng: center[0],lat: center[1],height: 30000 })

      // 禁用双击事件
      viewer3d.trackedEntity = undefined;
      viewer3d.screenSpaceEventHandler.removeInputAction(
        Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK
      );

      // 设置是否贴地飞行，默认为flase,设置为true时始终在地形之上飞行，不会钻进地形内部。
      viewer3d.camera.flyClampToGround = true
      // 渲染分辨率缩放因子
      viewer3d.resolutionScale = window.devicePixelRatio;

      /**===============================================添加影像底图开始==========================================**/
      const wmtsOption = {
        url: mapInfo.url,
        layerName: mapInfo.layerName
      }

      // addIserverLayer(viewer3d,wmtsOption)

      if(import.meta.env.DEV){
        // 开发环境：只在当前环境添加互联网地图
        addTDTTerrainLayer(viewer3d)
        addTdtImageLayer(viewer3d)
      }
      if(import.meta.env.PROD){
        addWMTSLayer(viewer3d,wmtsOption)
      }
      /**================================================添加影像底图结束=========================================**/
      /**================================================添加地形设置开始=========================================**/
      scene.undergroundMode = true;
      // scene.globe.globeAlpha = 0.75; // 设置地形透明度
      scene.screenSpaceCameraController.minimumZoomDistance = -1000;
      scene.globe.depthTestAgainstTerrain = true; // 开启地形深度测试
      /**================================================添加地形设置结束=========================================**/

      /**================================================设置场景参数开始=========================================**/
      setSceneEffect(viewer3d)
      /**===============================================设置场景参数结束==========================================**/
      return viewer3d
    }catch (err){
      throw new Error("Cesium Viewer created failed，" + err)
    }finally {
      eventBus.emit('showMapLoading',false)
    }
  }
}
