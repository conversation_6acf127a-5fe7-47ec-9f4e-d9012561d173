import request from "@/utils/request";

/**
 * 查询拆迁巡查任务管理列表
 * @param query
 * @returns {*}
 */

export const listRelPatrolTask = (query) => {
  return request({
    url: "/patrol/relPatrolTask/list",
    method: "get",
    params: query
  });
};

/**
 * 查询拆迁巡查任务管理详细
 * @param id
 */
export const getRelPatrolTask = (id) => {
  return request({
    url: "/patrol/relPatrolTask/" + id,
    method: "get"
  });
};

/**
 * 新增拆迁巡查任务管理
 * @param data
 */
export const addRelPatrolTask = (data) => {
  return request({
    url: "/patrol/relPatrolTask",
    method: "post",
    data: data
  });
};

/**
 * 修改拆迁巡查任务管理
 * @param data
 */
export const updateRelPatrolTask = (data) => {
  return request({
    url: "/patrol/relPatrolTask",
    method: "put",
    data: data
  });
};

/**
 * 删除拆迁巡查任务管理
 * @param id
 */
export const delRelPatrolTask = (id) => {
  return request({
    url: "/patrol/relPatrolTask/" + id,
    method: "delete"
  });
};

/**
 * 获取巡查记录信息列表
 */
export const listRelPatrolRecord = (query)=>{
  return request({
    url: "/patrol/relPatrolRecord/list",
    method: "get",
    params: query
  })
}

/**
 * 查询征拆巡查记录详细信息
 */

export const getRelPatrolRecord = (id) => {
  return request({
    url: "/patrol/relPatrolRecord/" + id,
    method: "get"
  })
}


/**
 * 新增征拆巡查记录
 */
export const addRelPatrolRecord = (data)=>{
  return request({
    url: "/patrol/relPatrolRecord",
    method: "post",
    data: data
  })
}

/**
 * 更新征拆巡查记录信息
 */
export const updateRelPatrolRecord = (data)=>{
  return request({
    urL: "/patrol/relPatrolRecord",
    method: "put",
    data: data
  })
}



