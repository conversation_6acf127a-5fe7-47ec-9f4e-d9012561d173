<template>
  <div class="main-content">
    <div class="content">
      <div class="add-header-title">
        <div class="add-title">{{ `【${headerTitle}】项目巡查信息详情` }}</div>
        <div
          class="add-title-return"
          @click="closeDetail"
        >
          <img
            src="@/assets/images/img-return.png"
            class="back"
          >
          <div class="backlist">返回列表</div>
        </div>
      </div>
      <div class="add-content-rev">
        <el-row :gutter="10">
          <el-col
            :span="14"
            class="contentlist"
          >
            <div class="laberone">
              <div
                style="height: 100%; width: 120px; text-align: center"
                @click="handleTabSelect(0)"
              >
                <div
                  :class="showContent === 0 ? 'tab-active' : ''"
                  style="margin-top: 16px; cursor: pointer; font-size: 16px"
                >
                  巡查信息
                </div>
                <div
                  class="information-rev"
                  v-if="showContent === 0"
                >
                  <img src="@/assets/images/tabs.png">
                </div>
              </div>
              <div
                class="titleone"
                @click="handleTabSelect(1)"
              >
                <div
                  :class="showContent === 1 ? 'tab-active' : ''"
                  style="margin-top: 16px; cursor: pointer; font-size: 16px"
                >
                  基本信息
                </div>
                <div
                  class="information-rev"
                  v-if="showContent === 1"
                >
                  <img src="@/assets/images/tabs.png">
                </div>
              </div>
              <div
                class="titleone"
                @click="handleTabSelect(2)"
              >
                <div
                  :class="showContent === 2 ? 'tab-active' : ''"
                  style="margin-top: 16px; cursor: pointer; font-size: 16px"
                >
                  地图查看
                </div>
                <div
                  class="information-rev"
                  v-if="showContent === 2"
                >
                  <img src="@/assets/images/tabs.png">
                </div>
              </div>
            </div>
            <div
              v-if="showContent === 0"
              class="tab-content"
            >
              <el-row :span="24">
                <el-col>
                  <div>
                    <div class="patrol">
                      <div class="content-project-rev">
                        <img src="@/assets/images/left.png">
                        <p>巡查信息</p>
                      </div>
                    </div>
                    <el-descriptions
                      class="margin-top"
                      :column="1"
                      size="default"
                      border
                    >
                      <el-descriptions-item>
                        <template #label>
                          <div class="cell-item">
                            巡查人员
                          </div>
                        </template>
                        {{taskForm.xcry}}
                      </el-descriptions-item>
                      <el-descriptions-item>
                        <template #label>
                          <div class="cell-item">
                            情况说明
                          </div>
                        </template>
                        {{taskForm.qtqksm }}
                      </el-descriptions-item>
                      <el-descriptions-item>
                        <template #label>
                          <div class="cell-item">
                            是否改变用途
                          </div>
                        </template>
                        <el-switch
                          v-model="taskForm.sfgbyt"
                          inline-prompt
                          active-text="是"
                          inactive-text="否"
                          disabled
                        />
                      </el-descriptions-item>
                      <el-descriptions-item>
                        <template #label>
                          <div class="cell-item">
                            是否永久性建筑物
                          </div>
                        </template>
                        <el-switch
                          v-model="taskForm.sfjsyjxjz"
                          inline-prompt
                          active-text="是"
                          inactive-text="否"
                          disabled
                        />
                      </el-descriptions-item>
                    </el-descriptions>
                  </div>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col>
                  <div>
                    <div class="patrol xx">
                      <div class="content-project-rev">
                        <img src="@/assets/images/left.png">
                        <p>巡查照片</p>
                      </div>
                    </div>
                    <div
                      class="patrol-task-preview"
                      v-if="patrolTaskImageList"
                    >
                      <el-image
                        style="width: 100px; height: 100px"
                        :src="patrolTaskImageUrl"
                        :zoom-rate="1.2"
                        :max-scale="7"
                        :min-scale="0.2"
                        :preview-src-list="patrolTaskImageList"
                        show-progress
                        :initial-index="8"
                        fit="cover"
                      />
                    </div>
                    <div v-else>
                      <el-empty description="还没有上传照片"/>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
            <div
              v-if="showContent === 1"
              class="tab-content"
            >
              <!--项目基本信息-->
              <div>
                <div class="content-project-rev">
                  <img src="@/assets/images/left.png">
                  <p>项目信息</p>
                </div>
                <el-row :span="24">
                  <el-col>
                    <el-descriptions
                      class="margin-top"
                      :column="1"
                      size="default"
                      border
                    >
                      <el-descriptions-item>
                        <template #label>
                          <div class="cell-item">
                            项目名称
                          </div>
                        </template>
                        {{taskForm.xmmc }}
                      </el-descriptions-item>
                      <el-descriptions-item>
                        <template #label>
                          <div class="cell-item">
                            行政区域
                          </div>
                        </template>
                        {{taskForm.xzqmc }}
                      </el-descriptions-item>
                      <el-descriptions-item>
                        <template #label>
                          <div class="cell-item">
                            发布时间
                          </div>
                        </template>
                        {{taskForm.releaseTime}}
                      </el-descriptions-item>
                      <el-descriptions-item>
                        <template #label>
                          <div class="cell-item">
                            巡查状态
                          </div>
                        </template>
                        {{taskForm.rwzt}}
                      </el-descriptions-item>
                      <el-descriptions-item>
                        <template #label>
                          <div class="cell-item">
                            巡查时间
                          </div>
                        </template>
                        {{taskForm.xcsj}}
                      </el-descriptions-item>
                    </el-descriptions>
                  </el-col>
                </el-row>
              </div>
            </div>
            <div
              v-if="showContent === 2"
              class="tab-content"
            >
              <Viewer2d
                class="viewer-project"
                v-if="showMap"
                :viewerId="viewerProjectId"
                @onCreateViewer2d="createViewer2d"
              />
            </div>
          </el-col>
          <el-col :span="10">
            <div class="attachment">
              <div>
                <div class="related_accessories"/>
                <div class="related">相关附件</div>
                <div
                  class="attachment-content"
                  v-loading="isFileLoading"
                  element-loading-text="正在上传..."
                  :element-loading-spinner="svg"
                  element-loading-svg-view-box="-10, -10, 50, 50"
                  element-loading-background="rgba(122, 122, 122, 0.25)"
                >
                  <ul>
                    <li
                      v-for="(item,index) in dictList"
                      :key="index"
                      class="attachment-item"
                    >
                      <div class="attachment-item-header">
                        <span
                          class="arrow-icon"
                          @click="getFiles(item,false)"
                        >
                          <el-icon v-if="item.isArrowUp"><ArrowDownBold /></el-icon>
                          <el-icon v-else><ArrowUpBold /></el-icon>
                        </span>
                        <span>{{ item.dictLabel }}</span>
                      </div>
                      <div
                        v-if="item.isArrowUp"
                        class="attachment-item-content"
                      >
                        <ul>
                          <li
                            class="file-item clearfix"
                            v-for="(file,i) in allFiles[item.dictValue]" :key="i"
                          >
                            <span v-if="file.wjmc.length<20">{{file.wjmc}}</span>
                            <el-popover
                              v-else
                              placement="top-start"
                              width="500"
                              trigger="hover"
                              :content="file.wjmc"
                            >
                              <template #reference>
                                <span>{{file.wjmc.substring(0,20)+"···"}}</span>
                              </template>
                            </el-popover>
                            <span class="action-btn">
                              <el-button
                                type="primary"
                                plain
                                @click="previewFile(file)"
                              >预览</el-button>
                              <el-button
                                type="success"
                                plain
                                @click="downloadFile(file)"
                              >下载</el-button>
                            </span>
                          </li>
                        </ul>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup name="PatrolTaskReviewDetail">
import { useRoute, useRouter } from "vue-router"
import { getRelPatrolRecord } from "@/api/relocationPatrol/patrolTaskReview.js"
import { getAttachments, getRelProject } from "@/api/relocationPatrol/relocationLand.js"
import { fetchAndDownload, filePreview } from "@/utils/index.js"
import { getDicts } from "@/api/system/dict/data.js"
import Viewer2d from "@/components/GISTools/Viewer/Viewer2d.vue"
import { addGeoJSON2Map } from "@/utils/OpenLayers/olLayer.js"
import UseViewer2d from "@/components/GISTools/Viewer/UseViewer2d.js"
import { getRelPatrolTask } from "@/api/relocationPatrol/patrolTask.js"

const router = useRouter()
const route = useRoute()

const showContent = ref(0);
// 标题变量
const headerTitle = ref("巡查信息详情");

const showMap = ref(false)
const viewerProjectId = ref("viewerProjectId")
const viewer2d = ref(null)
const geoJSONObj = ref(null)

const dictList = ref()
const svg = `
        <path class="path" d="
          M 30 15
          L 28 17
          M 25.61 25.61
          A 15 15, 0, 0, 1, 15 30
          A 15 15, 0, 1, 1, 27.99 7.5
          L 15 15
        "style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
      `
const isFileLoading = ref(false)
const allFiles = reactive({
  province: [],
  approve: [],
  other: []
})

// 巡查照片地址
const patrolTaskImageUrl = ref("")
const patrolTaskImageList = ref([])

const initFormData = {
  xcsj: undefined,
  tjsj: undefined,
  xcry: undefined,
  sfgbyt: undefined,
  sfjsyjxjz: undefined,
  qtqksm: undefined,
  xcjlxh: undefined,
  shzt: undefined,
  shsm: undefined,
  shsj: undefined,
  shry: undefined,
  bz: undefined
};

const data = reactive({
  taskForm: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    xcsj: undefined,
    tjsj: undefined,
    xcry: undefined,
    sfgbyt: undefined,
    sfjsyjxjz: undefined,
    qtqksm: undefined,
    xcjlxh: undefined,
    shzt: undefined,
    shsm: undefined,
    shsj: undefined,
    shry: undefined,
    bz: undefined
  }
});

const { taskForm } = toRefs(data);


const isAddGeometry = computed(()=>{
  return geoJSONObj.value && viewer2d.value
})

watch(isAddGeometry,(value)=>{
  if(value){
    const geoJson = JSON.parse(geoJSONObj.value)
    addGeoJSON2Map(geoJson, undefined, viewer2d.value)
    // viewer2d.value.getView().setZoom(15)
  }
})

const handleTabSelect = (index) => {
  showContent.value = index;
};

/**
 * 获取巡查信息数据
 */
const taskId = route.query.id
const getRelPatrolTaskById = async (id) => {
  const response = await getRelPatrolRecord(id)
  taskForm.value = response.data
  headerTitle.value = response.data.xmmc
  taskForm.value.sfjsyjxjz = Boolean(taskForm.value.sfjsyjxjz)
  taskForm.value.sfgbyt = Boolean(taskForm.value.sfgbyt)
  patrolTaskImageList.value = taskForm.value.images.map(image=>{
    return image.url
  })
  patrolTaskImageUrl.value = patrolTaskImageList.value[0]
  getDictData()

  await getPatrolTaskById(response.data.rwId)
}

/**
 * 根据id查找巡查任务
 */
const getPatrolTaskById = async (id) => {
  const response = await getRelPatrolTask(id)
  await getRelocationProject(response.data.projectId)
}

/**
 * 根据项目id获取征拆项目数据
 */
const getRelocationProject = async (projectId)=>{
  const result = await getRelProject(projectId)
  taskForm.value.xmmc = result.data.xmmc
  geoJSONObj.value = result.data.smgeometry
  showMap.value = true
}


const getDictData = ()=>{
  // console.log("巡查和任务：",taskForm.value)
  getDicts('tdcb_file_type').then((res) => {
    dictList.value = res.data.map(item=>{
      item.wjflId = item.dictCode
      item.zbId = taskForm.value.id
      item.isArrowUp = false
      getFiles(item,true)
      return item
    })
  })
}

/**
 * 巡查照片下载
 * @param index
 */
const downloadPatrolTaskImage = (index) => {
  const url = patrolTaskImageList.value[index]
  const suffix = url.slice(url.lastIndexOf('.'))
  const filename = Date.now() + suffix

  fetch(url)
    .then((response) => response.blob())
    .then((blob) => {
      const blobUrl = URL.createObjectURL(new Blob([blob]))
      const link = document.createElement('a')
      link.href = blobUrl
      link.download = filename
      document.body.appendChild(link)
      link.click()
      URL.revokeObjectURL(blobUrl)
      link.remove()
    })
}

/**
 * 获取附件数据
 */
const getFiles = (item,isArrowUp)=>{
  // isFileLoading.value = true
  item.isArrowUp = !item.isArrowUp
  if(isArrowUp){
    item.isArrowUp = true
  }
  item.createTime = new Date(item.createTime)
  getAttachments(item).then(res=>{
    allFiles[item.dictValue] = res.rows
    isFileLoading.value = false
  })
}

/**
 * 文件预览
 */
const previewFile = (file)=>{
  filePreview(file.wjlj,"_blank")
}

/**
 * 文件下载
 */
const downloadFile = (file)=>{
  fetchAndDownload(file.wjlj, file.wjmc);
}


/** 返回操作 */
const closeDetail = ()=>{
  router.push({ path: "/relocationPatrol/patrolTaskReview" })
}

/**
 * 创建2d视图
 * @param viewer2dId
 * @param mapInitStore
 * @param mapViewStore
 */
const createViewer2d = async (viewer2dId,mapInitStore,mapViewStore)=>{
  const useViewer2d = new UseViewer2d(viewer2dId,mapInitStore,mapViewStore)
  await useViewer2d.createViewer2d()
  viewer2d.value = useViewer2d.map
}

onBeforeMount(()=>{
  getRelPatrolTaskById(taskId)
})
</script>
<style lang="scss" scoped>
@import "@/styles/variables.module.scss";

.main-content {
  padding: 10px;
}

.result-wrap {
  margin-top: 10px;
}

.add-header-title {
  padding: 10px;
  display: flex;
  justify-content: space-between;
  height: 50px;
  background-color: rgb(222, 239, 255);
  box-sizing: border-box;
  border-bottom: 1px solid rgb(233, 233, 233);
  font-weight: 700;
  font-size: 14px;
  line-height: 28px;
}

.add-title-return {
  display: flex;
  align-content: center;
  color: rgb(32, 119, 255);
  cursor: pointer;
  font-weight: normal;
  &:hover{
    cursor: pointer;
    font-size: 16px;
    transform: scale(1.15);
    transition: all ease-in .25s;
  }
}

.add-content-rev {
  padding: 0px 10px;
}

.content-project-rev {
  display: flex;
  align-items: center;

  p {
    color: #333333;
    font-weight: bold;
    margin-left: 8px;
  }
}

.tab-active {
  color: #0f7dff;
  font-weight: bold;
}

.back {
  height: 18px;
  width: 18px;
  margin-top: 5px;
}

.backlist {
  padding-left: 6px;
  font-size: 14px;
}
.el-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.content {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #dadada;
}

.contentlist {
  padding: 10px;

  .laberone {
    height: 60px;
    width: 100%;
    display: flex;
  }

  .titleone {
    height: 100%;
    width: 120px;
    text-align: center;
  }

  .information-rev {
    margin-top: 4px;
    width: 100%;
    img {
      width: 30px;
      height: auto;
      transform: rotate(-1deg);
    }
  }

  .tab-content {
    border: 1px solid rgb(173, 211, 246);
    padding: 20px;
    border-radius: 4px;
  }

  .four {
    background-color: white;
    z-index: 999;
  }

  .patrol {
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid rgb(230, 230, 230);
  }

  .xcxx {
    text-align: center;
    line-height: 51px;
  }

  .xx {
    margin-top: 15px;
  }

  .xcxx span {
    font-size: 12px;
    color: rgb(140, 140, 140);
  }

  .personnel {
    display: flex;
    height: 40px;
    text-align: center;
    line-height: 40px;
  }

  .xcry {
    width: 120px;
    font-size: 13px;
    border: 1px solid #e9e9e9;
    font-weight: bold;
    background-color: #fafafa;
  }

  .xcry-none {
    width: 120px;
    font-size: 13px;
    border: 1px solid #e9e9e9;
    font-weight: bold;
    background-color: #fafafa;
    border-top: none !important;
  }

  .xcrymz {
    width: 180px;
    font-size: 14px;
    border: 1px solid #e9e9e9;
    border-left: none;
    color: rgb(85, 85, 85);
  }

  .xcrymz-none {
    width: 180px;
    font-size: 14px;
    border: 1px solid #e9e9e9;
    border-left: none;
    color: rgb(85, 85, 85);
    border-top: none !important;

    :deep(.el-input__wrapper) {
      box-shadow: none;
    }
  }
}

.attachment {
  margin-top: 70px;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
}
.attachment-content {
  min-height: 300px;
  max-height: 350px;
  overflow: auto;
}

.upload-icon{
  display: inline;
  margin-left: 5px;
  color: rgba(68, 68, 68, 0.7411764706);
  .el-icon:hover{
    cursor: pointer;
    color: #0d84ff;
    transform: scale(1.5);
    margin-left: 5px;
    transition: all ease-in .25s;
  }
}
.attachment-item{
  font-weight: bold;
  margin: 5px 0;
  color: #444444bd;
  .arrow-icon:hover{
    cursor: pointer;
  }
}
.attachment-item-content{
  font-weight: normal;
  .file-item{
    padding-bottom: 2.5px;
    margin: 5px 0;
    border-bottom: 1px solid #eee;
  }
}
.action-btn{
  float: right;
  margin-right: 5px;
}
.download-btn{
  margin-bottom: 10px;
}
.related_accessories {
  height: 8px;
  background-color: rgba(33, 120, 255, 1);
  border-radius: 4px 4px 0 0;
}
.related_accessories {
  height: 8px;
  background-color: rgba(33, 120, 255, 1);
  border-radius: 4px 4px 0 0;
}

.related {
  border-bottom: 1px solid rgb(230, 230, 230);
  height: 40px;
  line-height: 40px;
  align-content: center;
  padding-left: 10px;
  font-size: small;
  font-weight: bold;
}
.patrol-info{
  width: 100%;
}

.add-content-wrap {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  border-top: 1px solid #e0e0e0;
  padding: 20px 0;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.viewer-project{
  height: 500px;
  border: 1px solid rgb(233, 233, 233);
}

:deep(.el-descriptions__label) {
  width: 35%;
}
</style>
