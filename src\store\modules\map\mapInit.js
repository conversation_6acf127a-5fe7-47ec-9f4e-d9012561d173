/**
 * @name: mapInit
 * @description: 地图初始化参数
 * @author: zyc
 * @time: 2025-03-20
 **/
import { getConfigKey } from "@/api/system/config.js";
import OlSRS from "@/utils/OpenLayers/olSRS.js";

const useMapInitStore = defineStore("mapInit", {
  state: () => ({
    // 地图初始化信息配置
    mapInfo: {
      projection: undefined, // 投影坐标
      center: [], // 中心点
      zoom: 15, // 默认缩放级别
      crs: 4490, // EPSG code
      height: 8000, // 三维地球相机高度
      serviceType: "geoserver", // 地图服务类型
      url: "", // 地图服务地址
      layerName: "", // 图层名称
      gridsetName: "" // geoserver 切片网格级名称
    }
  }),
  actions: {
    getMapInfo: () => {
      return new Promise((resolve, reject) => {
        const mapInfo = {};
        getConfigKey("sys.mapInfo").then((response) => {
          const value = JSON.parse(response.data);
          mapInfo.projection = OlSRS[value.crs];
          mapInfo.height = Number(value.height);
          mapInfo.zoom = Number(value.zoom);
          mapInfo.crs = Number(value.crs);
          mapInfo.center = mapInfo.crs === 4490 ? value.centerDegrees : value.centerMeters;
          mapInfo.serviceType = value.serviceType;
          mapInfo.url = value.url;
          mapInfo.layerName = value.layerName;
          resolve(mapInfo);
        });
      });
    }
  }
});
export default useMapInitStore;
