<template>
  <div>
    <div class="side-bar">
      <div class="side-tool">
        <button
          v-for="(option, index) in navOptions"
          :key="index"
          class="side-bar-item"
          :class="{ active: option.isActive }"
          @click="toogleBar(option)"
        >
          <svg-icon :icon-class="option.iconClass" />
          <div>{{ option.name }}</div>
        </button>
      </div>
      <div class="side-tool">
        <button
          class="zoom-bar-item"
          title="清除" @click="clearAllResult"
        >
          <svg-icon icon-class="clear2" />
        </button>
        <button
          class="zoom-bar-item"
          title="切换地图" @click="switchView"
        >
          <svg-icon icon-class="2D" />
        </button>
        <button
          class="zoom-bar-item"
          title="放大" @click="zoomIn"
        >
          <svg-icon icon-class="zoomin" />
        </button>
        <button
          class="zoom-bar-item"
          title="缩放到起始位置"
          @click="resetPosition"
        >
          <svg-icon icon-class="homepage" />
        </button>
        <button
          class="zoom-bar-item"
          title="缩小" @click="zoomOut"
        >
          <svg-icon icon-class="zoomout" />
        </button>
      </div>
    </div>
    <div
      v-if="isActive"
      class="side-content" :class="{ active: isActive }"
    >
      <el-collapse
        ref="elCollapse"
        v-model="activeNames"
        accordion
        @change="handleChange"
      >
        <el-collapse-item
          v-for="(item, index) in optionsList"
          :key="index"
          :title="item.name"
          :name="index"
        >
          <template v-slot:title>
            <div class="tool-item">
              <svg-icon :icon-class="item.iconClass" />
              <span>{{ item.name }}</span>
            </div>
          </template>
          <component
            :is="item.componentId"
            v-if="!whiteList.includes(item.componentId) && activeIndex === index"
            ref="toolComponent"
            :header-info="{ iconClass: item.iconClass, toolTitle: item.name }"
          />
        </el-collapse-item>
      </el-collapse>
      <div
        class="side-flod"
        @click="sideFlod"
      />
    </div>
    <component
      :is="whiteComponentId"
      v-if="whichShow"
      ref="analyseComponent"
      :header-info="headerInfo"
      @closeCompare="whichShow = false"
      @closePanel="whichShow = false"
    />
  </div>
</template>

<script setup name="SideBar">
import useMapViewStore from "@/store/modules/map/mapView.js";
import {
  setLayerSelectStatus,
  flyToPoint,
  flyToS3MLayerCenter
} from "@/utils/Cesium/CesiumTool.js";
import Measure from "@/components/GISTools/3d/Measure/MeasureCesium";
import Location from "@/components/GISTools/3d/Location/Location";
import RainAndSnow from "@/components/GISTools/3d/RainAndSnow/RainAndSnow.vue";
import ScenneChange from "@/components/GISTools/3d/SceneChange/ScenneChange.vue";
import FlyRoute from "@/components/GISTools/3d/FlyRoute/FlyRoute.vue";
import ScreenCompare from "@/components/GISTools/3d/ScreenCompare/ScreenCompare.vue";
import Model from "@/components/GISTools/3d/Model/Model.vue";
import SceneEffect from "@/components/GISTools/3d/SceneEffect/SceneEffect.vue";
import WaterAnalysis from "@/components/GISTools/3d/Analysis/WaterAnalysis.vue";
import SightLineAnalysis from "@/components/GISTools/3d/Analysis/SightLineAnalysis.vue";
import ViewshedAnalysis from "@/components/GISTools/3d/Analysis/ViewshedAnalysis.vue";
import SunLightAnalysis from "@/components/GISTools/3d/Analysis/SunLightAnalysis.vue";
import SkyLineAnalysis from "@/components/GISTools/3d/Analysis/SkyLineAnalysis.vue";
import TerrainExcavationAnalysis from "@/components/GISTools/3d/Analysis/TerrainExcavationAnalysis.vue";
import OpennessAnalysis from "@/components/GISTools/3d/Analysis/OpennessAnalysis.vue";
import LimitHeightAnalysis from "@/components/GISTools/3d/Analysis/LimitHeightAnalysis.vue";
import TerrainSlopeAnalysis from "@/components/GISTools/3d/Analysis/TerrainSlopeAnalysis.vue";

const { proxy } = getCurrentInstance();

// 不需要展开的组件componentId
const whiteList = ref([ScreenCompare]);
const whichShow = ref(false);
const whiteComponentId = ref("");
// 导航配置
const navOptions = reactive([
  // {
  //   name: "底图",
  //   isActive: false,
  //   iconClass: "basemap",
  //   funcName: "showGallery"
  // },
  {
    name: "工具",
    isActive: false,
    iconClass: "tool",
    funcName: "showTool"
  },
  {
    name: "分析",
    isActive: false,
    iconClass: "analysis",
    funcName: "showAnalyse"
  }
]);

// 工具配置
const toolOptions = reactive([
  {
    name: "地面量算",
    isActive: false,
    iconClass: "measure",
    componentId: shallowRef(Measure)
  },
  {
    name: "坐标定位",
    isActive: false,
    iconClass: "locate",
    componentId: shallowRef(Location)
  },
  {
    name: "雨雪天气",
    isActive: false,
    iconClass: "scene",
    componentId: shallowRef(RainAndSnow)
  },
  {
    name: "场景切换",
    isActive: false,
    iconClass: "scene",
    componentId: shallowRef(ScenneChange)
  },
  {
    name: "飞行路径",
    isActive: false,
    iconClass: "fly-route",
    componentId: shallowRef(FlyRoute)
  },
  {
    name: "分屏对比",
    isActive: false,
    iconClass: "scene-swipe",
    componentId: shallowRef(ScreenCompare)
  },
  {
    name: "模型工具",
    isActive: false,
    iconClass: "3D-model",
    componentId: shallowRef(Model)
  },
  {
    name: "场景特效",
    isActive: false,
    iconClass: "scene-effect",
    componentId: shallowRef(SceneEffect)
  }
]);

// 分析配置
const analyseOptions = reactive([
  {
    name: "水淹分析",
    isActive: false,
    iconClass: "water-analyse",
    componentId: shallowRef(WaterAnalysis)
  },
  {
    name: "通视分析",
    isActive: false,
    iconClass: "sightline",
    componentId: shallowRef(SightLineAnalysis)
  },
  {
    name: "可视分析",
    isActive: false,
    iconClass: "viewshed",
    componentId: shallowRef(ViewshedAnalysis)
  },
  {
    name: "日照分析",
    isActive: false,
    iconClass: "sun-analyse",
    componentId: shallowRef(SunLightAnalysis)
  },
  // {
  //   name: "剖面分析",
  //   isActive: false,
  //   iconClass: "profile-analyse",
  //   componentId: "ProfileAnalyse"
  // },
  {
    name: "天际线分析",
    isActive: false,
    iconClass: "skyLine",
    componentId: shallowRef(SkyLineAnalysis)
  },
  {
    name: "挖方分析",
    isActive: false,
    iconClass: "excavate",
    componentId: shallowRef(TerrainExcavationAnalysis)
  },
  {
    name: "开敞度分析",
    isActive: false,
    iconClass: "opennessAnalyse",
    componentId: shallowRef(OpennessAnalysis)
  },
  {
    name: "限高分析",
    isActive: false,
    iconClass: "limit_height",
    componentId: shallowRef(LimitHeightAnalysis)
  },
  {
    name: "坡度分析",
    isActive: false,
    iconClass: "slope",
    componentId: shallowRef(TerrainSlopeAnalysis)
  }
]);

const componentId = ref(undefined);
const activeNames = ref("");
const activeIndex = ref(-1);
const optionsList = ref([]);
let headerInfo = reactive({});
// 计算属性
const viewer3d = computed(() => useMapViewStore().viewer3d);
const isActive = computed(() => {
  return navOptions.some((item) => item.isActive);
});

const sideFlod = () => {
  navOptions.map((item) => (item.isActive = false));
  setLayerSelectStatus(viewer3d.value, true);
};

const toogleBar = (option) => {
  navOptions.map((item) => (item.isActive = false));
  option.isActive = true;
  componentId.value = option.funcName;
  setLayerSelectStatus(viewer3d.value, false);
  switch (option.funcName) {
    case "showTool":
      optionsList.value = toolOptions;
      break;
    case "showAnalyse":
      optionsList.value = analyseOptions;
  }
  activeNames.value = "";
};

const handleChange = (index) => {
  activeIndex.value = index;
  if (index) {
    if (whiteList.value.includes(optionsList.value[index].componentId)) {
      activeNames.value = undefined;
      headerInfo = {
        iconClass: optionsList.value[index].iconClass,
        toolTitle: optionsList.value[index].name
      };
      whiteComponentId.value = optionsList.value[index].componentId;
      whichShow.value = true;
    } else {
      headerInfo = {
        iconClass: optionsList.value[index].iconClass,
        toolTitle: optionsList.value[index].name
      };
    }
  }
};
/**
 * 清除结果
 */
const clearAllResult = () => {
  // 移除所有图元
  viewer3d.value.entities.removeAll();
  // 清除结果
  const components = proxy.$refs.analyseComponent || proxy.$refs.toolComponent;

  if (components) {
    const targetComp = components[0];
    if (targetComp && typeof targetComp.clearResult === "function") {
      targetComp.clearResult();
    }
  }
};

/**
 * 切换视图
 */
function switchView() {
  useMapViewStore().switchMap("2D");
}

/**
 * 放大
 */
function zoomIn() {
  if (viewer3d.value) {
    const viewer3d = useMapViewStore().viewer3d;
    const amount = Math.ceil(viewer3d.camera.positionCartographic.height / 3);
    viewer3d.camera.zoomIn(amount);
  }
}
/**
 * 缩小
 */
function zoomOut() {
  if (viewer3d.value) {
    const amount = Math.ceil(
      viewer3d.value.camera.positionCartographic.height / 3
    );
    viewer3d.value.camera.zoomOut(amount);
  }
}
/**
 * 复位
 */
function resetPosition() {
  if (viewer3d.value) {
    const s3mLayer = viewer3d.value.scene.layers.find("fengceng") ||
      viewer3d.value.scene.layers.find("fenghu");
    if (s3mLayer) {
      flyToS3MLayerCenter(viewer3d.value, s3mLayer);
    } else {
      flyToPoint(viewer3d.value);
    }
  }
}
</script>

<style scoped lang="scss">
@import "@/styles/variables.module.scss";
$sideBarWeight: 50px;
$sideContentWeight: 325px;
.side-bar {
  width: $sideBarWeight;
  height: calc(100vh - #{$topBarHeight});
  position: absolute;
  right: 0;
  background-color: rgba(0, 19, 46, 0.6784313725);
  border-left: 1px solid #3b3b3b;
  color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  .side-tool {
    display: flex;
    flex-direction: column;
    justify-content: center;
    .side-bar-item {
      padding: 10px 0px;
      width: $sideBarWeight;
      .svg-icon {
        width: 18px;
        height: 18px;
        margin-bottom: 5px;
      }
    }
    .side-bar-item.active {
      width: 100%;
      background: linear-gradient(to left, #093765, #075198);
      div {
        text-shadow: 0 0 3px #ffffff21, 0 0 2px #ffffff2e, 0 0 10px #ffffff21,
          0 0 10px #409eff, 0 0 10px #409eff, 0 0 6px #00cffa, 0 0 10px #409eff,
          0 0 5px #409eff;
      }
    }
  }
}
.zoom-bar-item {
  position: relative;
  width: $sideBarWeight;
  height: 50px;
  transition: all 0.25s; /* 定义过渡效果 */
  color: #ffffff;
  cursor: pointer;
  background: #00132eb8;
  border: none; /* 移除原有边框 */
  border-bottom: 1px solid rgba(204, 204, 204, 0.3803921569);
  /* 添加渐变边框 */
  // &::before {
  //   content: '';
  //   position: absolute;
  //   top: 0;
  //   left: 0;
  //   right: 0;
  //   bottom: 0;
  //   border: 1px solid transparent;
  //   background: linear-gradient(135deg, #195EC9, #0080FD);
  //   -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
  //   -webkit-mask-composite: xor;
  //   mask-composite: exclude;
  //   pointer-events: none;
  //   border-radius: inherit;
  // }

  &:hover {
    transition-delay: 0.25s;
    background-color: rgb(6 122 242 / 88%);
  }
  .svg-icon {
    width: 1.25em;
    height: 1.25em;
  }
}
.side-content {
  width: 0;
  z-index: 999;
  position: absolute;
  right: $sideBarWeight;
  background-color: rgba(0, 19, 46, 0.6784313725);
  height: calc(100vh - #{$topBarHeight});
  .side-flod {
    background: url("@/assets/images/map/shousuo3.png");
    background-size: cover;
    width: 25px;
    height: 100px;
    position: absolute;
    left: -30px;
    top: 40%;
  }
}
.side-content.active {
  width: $sideContentWeight;
}
.tool-item {
  font-size: 14px;
  padding: 0 10px;
  &:hover {
    cursor: pointer;
    color: #00d2ff;
    transition-delay: 0.25s;
  }
  svg {
    width: 1.25em;
    height: 1.25em;
  }
  span {
    margin-left: 10px;
  }
}
:deep(.el-collapse) {
  border: 0px;
}
:deep(.el-collapse-item) {
  margin-bottom: 10px;
}
:deep(.el-collapse-item__wrap) {
  background-color: #00338299;
  border-bottom: 1px solid #0080fd6e;
}
:deep(.el-collapse-item__header) {
  // background: #033f83cc;
  background-color: #ffffff00;
  background-size: 100% 100%;
  background-image: url(/src/assets/images/yy_03.png);
  color: #fff;
  border: 0px;
}
:deep(.el-collapse-item__header.is-active) {
  background-size: cover;
  margin-left: -4px;
  padding-left: 8px;
  text-shadow: 0 0 3px #ffffff21, 0 0 2px #ffffff2e, 0 0 10px #ffffff21,
    0 0 10px #409eff, 0 0 10px #409eff, 0 0 6px #00cffa, 0 0 10px #409eff,
    0 0 5px #409eff;
}
:deep(.el-collapse-item__arrow) {
  display: none;
}
:deep(.el-collapse-item__content) {
  padding-bottom: 0;
}
.analysis {
  background: #00cffa;
}
</style>
