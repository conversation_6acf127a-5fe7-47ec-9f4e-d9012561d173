define(["./when-b60132fc","./Rectangle-dee65d21","./arrayFill-4513d7ad","./buildModuleUrl-9085faaa","./Cartographic-3309dd0d","./ComponentDatatype-c140a87d","./Check-7b2a090c","./GeometryAttribute-c65394ac","./GeometryAttributes-252e9929","./GeometryOffsetAttribute-fbeb6f1a","./IndexDatatype-8a5eead4","./Math-119be1a3","./PolygonPipeline-d83979ed","./FeatureDetection-806b12f0","./RectangleGeometryLibrary-d0377774","./Event-16a2dfbf","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Cartesian2-db21342c","./Cartesian4-3ca25aab","./earcut-2.2.1-20c8012f","./EllipsoidRhumbLine-30b5229b"],(function(e,t,i,a,r,n,o,l,u,s,d,c,p,f,g,h,y,b,m,_,v,E){"use strict";var A=new a.BoundingSphere,G=new a.BoundingSphere,R=new r.Cartesian3,P=new t.Rectangle;function C(e,t){var i=e._ellipsoid,a=t.height,r=t.width,o=t.northCap,s=t.southCap,c=a,p=2,h=0,y=4;o&&(p-=1,c-=1,h+=1,y-=2),s&&(p-=1,c-=1,h+=1,y-=2),h+=p*r+2*c-y;var b,m=new Float64Array(3*h),_=0,v=0,E=R;if(o)g.RectangleGeometryLibrary.computePosition(t,i,!1,v,0,E),m[_++]=E.x,m[_++]=E.y,m[_++]=E.z;else for(b=0;b<r;b++)g.RectangleGeometryLibrary.computePosition(t,i,!1,v,b,E),m[_++]=E.x,m[_++]=E.y,m[_++]=E.z;for(b=r-1,v=1;v<a;v++)g.RectangleGeometryLibrary.computePosition(t,i,!1,v,b,E),m[_++]=E.x,m[_++]=E.y,m[_++]=E.z;if(v=a-1,!s)for(b=r-2;b>=0;b--)g.RectangleGeometryLibrary.computePosition(t,i,!1,v,b,E),m[_++]=E.x,m[_++]=E.y,m[_++]=E.z;for(b=0,v=a-2;v>0;v--)g.RectangleGeometryLibrary.computePosition(t,i,!1,v,b,E),m[_++]=E.x,m[_++]=E.y,m[_++]=E.z;for(var A=m.length/3*2,G=d.IndexDatatype.createTypedArray(m.length/3,A),P=0,C=0;C<m.length/3-1;C++)G[P++]=C,G[P++]=C+1;G[P++]=m.length/3-1,G[P++]=0;var w=new l.Geometry({attributes:new u.GeometryAttributes,primitiveType:f.PrimitiveType.LINES});return w.attributes.position=new l.GeometryAttribute({componentDatatype:n.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:m}),w.indices=G,w}function w(i){var a=(i=e.defaultValue(i,e.defaultValue.EMPTY_OBJECT)).rectangle,r=e.defaultValue(i.granularity,c.CesiumMath.RADIANS_PER_DEGREE),n=e.defaultValue(i.ellipsoid,t.Ellipsoid.WGS84),o=e.defaultValue(i.rotation,0),l=e.defaultValue(i.height,0),u=e.defaultValue(i.extrudedHeight,l);this._rectangle=t.Rectangle.clone(a),this._granularity=r,this._ellipsoid=n,this._surfaceHeight=Math.max(l,u),this._rotation=o,this._extrudedHeight=Math.min(l,u),this._offsetAttribute=i.offsetAttribute,this._workerName="createRectangleOutlineGeometry"}w.packedLength=t.Rectangle.packedLength+t.Ellipsoid.packedLength+5,w.pack=function(i,a,r){return r=e.defaultValue(r,0),t.Rectangle.pack(i._rectangle,a,r),r+=t.Rectangle.packedLength,t.Ellipsoid.pack(i._ellipsoid,a,r),r+=t.Ellipsoid.packedLength,a[r++]=i._granularity,a[r++]=i._surfaceHeight,a[r++]=i._rotation,a[r++]=i._extrudedHeight,a[r]=e.defaultValue(i._offsetAttribute,-1),a};var L=new t.Rectangle,D=t.Ellipsoid.clone(t.Ellipsoid.UNIT_SPHERE),x={rectangle:L,ellipsoid:D,granularity:void 0,height:void 0,rotation:void 0,extrudedHeight:void 0,offsetAttribute:void 0};w.unpack=function(i,a,r){a=e.defaultValue(a,0);var n=t.Rectangle.unpack(i,a,L);a+=t.Rectangle.packedLength;var o=t.Ellipsoid.unpack(i,a,D);a+=t.Ellipsoid.packedLength;var l=i[a++],u=i[a++],s=i[a++],d=i[a++],c=i[a];return e.defined(r)?(r._rectangle=t.Rectangle.clone(n,r._rectangle),r._ellipsoid=t.Ellipsoid.clone(o,r._ellipsoid),r._surfaceHeight=u,r._rotation=s,r._extrudedHeight=d,r._offsetAttribute=-1===c?void 0:c,r):(x.granularity=l,x.height=u,x.rotation=s,x.extrudedHeight=d,x.offsetAttribute=-1===c?void 0:c,new w(x))};var H=new r.Cartographic;return w.createGeometry=function(t){var r,o,u=t._rectangle,h=t._ellipsoid,y=g.RectangleGeometryLibrary.computeOptions(u,t._granularity,t._rotation,0,P,H);if(!c.CesiumMath.equalsEpsilon(u.north,u.south,c.CesiumMath.EPSILON10)&&!c.CesiumMath.equalsEpsilon(u.east,u.west,c.CesiumMath.EPSILON10)){var b,m=t._surfaceHeight,_=t._extrudedHeight;if(!c.CesiumMath.equalsEpsilon(m,_,0,c.CesiumMath.EPSILON2)){if(r=function(e,t){var i=e._surfaceHeight,a=e._extrudedHeight,r=e._ellipsoid,n=a,o=i,l=C(e,t),u=t.height,s=t.width,c=p.PolygonPipeline.scaleToGeodeticHeight(l.attributes.position.values,o,r,!1),f=c.length,g=new Float64Array(2*f);g.set(c);var h=p.PolygonPipeline.scaleToGeodeticHeight(l.attributes.position.values,n,r);g.set(h,f),l.attributes.position.values=g;var y=t.northCap,b=t.southCap,m=4;y&&(m-=1),b&&(m-=1);var _=2*(g.length/3+m),v=d.IndexDatatype.createTypedArray(g.length/3,_);f=g.length/6;for(var E,A=0,G=0;G<f-1;G++)v[A++]=G,v[A++]=G+1,v[A++]=G+f,v[A++]=G+f+1;if(v[A++]=f-1,v[A++]=0,v[A++]=f+f-1,v[A++]=f,v[A++]=0,v[A++]=f,y)E=u-1;else{var R=s-1;v[A++]=R,v[A++]=R+f,E=s+u-2}if(v[A++]=E,v[A++]=E+f,!b){var P=s+E-1;v[A++]=P,v[A]=P+f}return l.indices=v,l}(t,y),e.defined(t._offsetAttribute)){var v=r.attributes.position.values.length/3,E=new Uint8Array(v);t._offsetAttribute===s.GeometryOffsetAttribute.TOP?E=i.arrayFill(E,1,0,v/2):(b=t._offsetAttribute===s.GeometryOffsetAttribute.NONE?0:1,E=i.arrayFill(E,b)),r.attributes.applyOffset=new l.GeometryAttribute({componentDatatype:n.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:E})}var R=a.BoundingSphere.fromRectangle3D(u,h,m,G),w=a.BoundingSphere.fromRectangle3D(u,h,_,A);o=a.BoundingSphere.union(R,w)}else{if((r=C(t,y)).attributes.position.values=p.PolygonPipeline.scaleToGeodeticHeight(r.attributes.position.values,m,h,!1),e.defined(t._offsetAttribute)){var L=r.attributes.position.values.length,D=new Uint8Array(L/3);b=t._offsetAttribute===s.GeometryOffsetAttribute.NONE?0:1,i.arrayFill(D,b),r.attributes.applyOffset=new l.GeometryAttribute({componentDatatype:n.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:D})}o=a.BoundingSphere.fromRectangle3D(u,h,m)}return new l.Geometry({attributes:r.attributes,indices:r.indices,primitiveType:f.PrimitiveType.LINES,boundingSphere:o,offsetAttribute:t._offsetAttribute})}},function(i,a){return e.defined(a)&&(i=w.unpack(i,a)),i._ellipsoid=t.Ellipsoid.clone(i._ellipsoid),i._rectangle=t.Rectangle.clone(i._rectangle),w.createGeometry(i)}}));
