<template>
  <div
    :id="viewerId"
    class="viewer3d-container"
    ref="viewer3dRef"
    v-loading="isLoading"
    element-loading-text="正在加载"
  />
</template>

<script setup name="Viewer3d">
import useMapInitStore from "@/store/modules/map/mapInit.js";
import useMapViewStore from "@/store/modules/map/mapView.js"
const props = defineProps({
  viewerId: {
    type: String,
    required: false,
    default: "defaultViewer3d"
  }
});

const mapInitStore = useMapInitStore();
const mapViewStore = useMapViewStore();


const { viewerId } = toRefs(props);
const viewer3dRef = ref(null)
const emits = defineEmits({
  onCreateViewer3d: (viewerId) => {
    // 事件参数校验
    if (!viewerId){
      throw new Error("地图挂载元素或ID无效，请重新设置！")
    }
    return true
  }
});
const isLoading = ref(false);

onMounted( () => {
  // 抛出地图加载事件
  emits("onCreateViewer3d", viewer3dRef.value,mapInitStore,mapViewStore);
});
</script>

<style scoped lang="scss">
.viewer3d-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #ffffff;
}
</style>
