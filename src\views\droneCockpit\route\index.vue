<template>
  <div class="container">
    <div v-if="!showDetail" class="project-list">
      <transition name="fade">
        <div v-show="showSearch" class="search-container">
          <el-card shadow="hover" class="search-card">
            <el-form ref="queryFormRef" :model="queryParams" :inline="true">
              <el-form-item label="航线名称" prop="name" class="serchtop">
                <el-input v-model="queryParams.name" placeholder="请输入航线名称" clearable @keyup.enter="handleQuery"/>
              </el-form-item>
              <el-form-item class="serchtop">
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </div>
      </transition>

      <el-card shadow="never" class="result-card">
        <el-table v-loading="loading" :data="reserveProjectList" @selection-change="handleSelectionChange" style="width: 100%" stripe>
          <el-table-column label="航线名称" align="center" prop="name" min-width="180"/>
          <el-table-column label="设备类型中文名称" align="center" prop="deviceModelName" width="160"/>
          <el-table-column label="航线类型" align="center" prop="templateTypes" width="160"/>
          <el-table-column label="文件大小" align="center" prop="size" width="180">
            <template #default="{ row }">
              {{ formatFileSize(row.size) }}
            </template>
          </el-table-column>
          <el-table-column label="更新时间" align="center" prop="updateTime" min-width="200"/>
          <el-table-column label="操作" align="center" width="100" fixed="right">
            <template #default="scope">
              <el-button plain type="primary" size="small" @click="viewDetail(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
            class="pagination-container"
        />
      </el-card>
    </div>

    <div v-else class="project-detail">
      <div class="add-header-title">
        <div class="add-title">{{ hxheaderTitle }}</div>
          <div class="add-title-return" @click="cancel">
            <img src="@/assets/images/img-return.png" class="back" />
            <div class="backlist">返回列表</div>
          </div>
        </div>
      <el-card  class="detail-section" style="margin-top: 10px">
        <div class="section-header">
          <div class="content-title-1">
                <img src="@/assets/images/left.png" />
                <p>航线信息</p>
              </div>
        </div>
        <div class="section-content-wrapper">
          <!-- 左侧内容区 -->
          <div class="section-content-left">
            <el-row :gutter="20" class="mcheng">
              <el-col :span="6"><div class="grid-content">{{routeDetail.name}}</div></el-col>
              <el-col :span="6"><div class="grid-content">{{routeDetail.deviceModelName}}</div></el-col>
              <el-col :span="6"><div class="grid-content">{{StatusUtils.toFixed2(routeDetail.distance)}}</div></el-col>
              <el-col :span="6"><div class="grid-content">{{routeDetail.waylinePointNums}}</div></el-col>
            </el-row>
            <el-row :gutter="20" class="hxgutter">
              <el-col :span="6"><div class="grid-content">航线名称</div></el-col>
              <el-col :span="6"><div class="grid-content">设备类型中文名称</div></el-col>
              <el-col :span="6"><div class="grid-content">航线长度</div></el-col>
              <el-col :span="6"><div class="grid-content">航点个数</div></el-col>
            </el-row>
            <el-divider />
            <el-row :gutter="20" class="mcheng">
              <el-col :span="6"><div class="grid-content">{{routeDetail?.templateTypes[0]}}</div></el-col>
              <el-col :span="6"><div class="grid-content">{{routeDetail.updateTime}}</div></el-col>
              <el-col :span="6"><div class="grid-content">{{routeDetail.payloadInformation[0].lensType }}</div></el-col>
            </el-row>
            <el-row :gutter="20" class="hxgutter">
              <el-col :span="6"><div class="grid-content">航线类型</div></el-col>
              <el-col :span="6"><div class="grid-content">创建时间</div></el-col>
              <el-col :span="6"><div class="grid-content">镜头类型</div></el-col>
            </el-row>
            <el-divider />
            <el-button link type="primary" @click="downloadRoute(routeDetail.downloadUrl)" class="back-button">
              下载航线
            </el-button>
          </div>
        </div>
      </el-card>
      <el-card class="section-bottom" >
        <el-table :data="tableData" style="width: 100%">
          <el-table-column prop="flightTaskName" label="任务名称" align="center"></el-table-column>
          <el-table-column prop="waylineName" label="航线名称" align="center"></el-table-column>
          <el-table-column prop="startTime" label="开始时间" align="center"></el-table-column>
          <el-table-column prop="endTime" label="结束时间" align="center"></el-table-column>
          <el-table-column label="坐标点数量" align="center">
            <template #default="scope">
              {{ scope.row.trajectory.coordinates.length }}
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" align="center"></el-table-column>
          <el-table-column prop="updateTime" label="更新时间" align="center"></el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup name="ReserveProject">
import { InfoFilled, Collection, MapLocation, Aim } from '@element-plus/icons-vue';
import {wayLineId, wayLineList, wayList} from "@/api/uav/flightHub2/way_line.js";
import {StatusUtils} from  "@/constants/flightTask.js"
const { proxy } = getCurrentInstance();
const showDetail = ref(false);

// 数据表格
const tableDataCQ = ref([]);
const tableDataDK = ref([]);
const reserveProjectList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const routeDetail = ref({});
const total = ref(0);
const tableData = ref([]);

const queryFormRef = ref();
const reserveProjectFormRef = ref();

const initFormData = {
  id: undefined,
  name: undefined,
  bz: undefined,
  rightBos: undefined,
  landBos: undefined,
};
//动态详情页表头
const hxheaderTitle = computed(() => {
  if (!routeDetail.value.name) return "航线信息";
  return `${routeDetail.value.name}航线信息`;
});

const data = reactive({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: undefined,
    params: {},
  },
  rules: {
    id: [{ required: true, message: "$comment不能为空", trigger: "blur" }],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询储备项目列表 */
const getList = async () => {
  loading.value = true;
  await wayList(queryParams.value)
  const res = await wayLineList(queryParams.value);
  reserveProjectList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};
// Format file size from bytes to format
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
/** 取消按钮 */
const cancel = () => {
  reset();
  showDetail.value = false;
};
/** 下载航线 */
const downloadRoute = (url) => {
      const a = document.createElement('a');
      a.href = url;
      a.rel = 'noreferrer'; // 避免 Referer 被检查
      a.download = url.split('/').pop().split('?')[0] || 'route.kmz'; // 自动提取文件名，如果没有则默认 'route.kmz'
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    };

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  reserveProjectFormRef.value?.resetFields();
  tableDataCQ.value = [];
  tableDataDK.value = [];
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 修改按钮操作 */
const viewDetail = async (row) => {
  reset();
   const _id = row?.id || ids.value[0];
  const res = await wayLineId(_id);
  Object.assign(form.value, res.data);
  routeDetail.value = res.data.detail
  tableData.value = res.data.task;
  console.log("routeDetail.value" + res.data)
  showDetail.value = true;
};

onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.container {
  padding: 20px;
  background-color: #f5f7fa;
}

.search-container {
  margin-bottom: 20px;

  .search-card {
    border-radius: 8px;

    :deep(.el-card__body) {
      padding: 20px;
    }
  }
}

.result-card {
  border-radius: 8px;

  :deep(.el-card__body) {
    padding: 0;
  }
}

.pagination-container {
  padding: 16px;
  text-align: right;
}

.project-detail {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;

  .detail-title {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
    margin: 0;
  }

  .back-button {
    border-radius: 4px;
  }
}


.detail-section {
  border-radius: 8px;
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  align-items: center;
  padding-bottom: 16px;
  margin-bottom: 16px;
}

.section-header .el-icon {
  margin-right: 8px;
  color: #409EFF;
}

.section-header span {
  font-size: 15px;
  font-weight: 500;
}

.section-content-wrapper {
  display: flex;
  gap: 20px;
}

.section-content-left {
  flex: 1;
  text-align: center;
  min-width: 0; /* 防止内容溢出 */
}

.section-content-right {
  flex: 0 0 500px; /* 固定宽度 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.route-image {
  max-width: 100%;
  max-height: 400px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.grid-content {
  padding: 1px 0;
  font-size: 16px;
}

.el-row {
  margin-bottom: 10px;
}

.back-button {
  margin-top: 10px;
  float: left !important;
}

/* 响应式设计 */
@media (max-width: 992px) {
  .section-content-wrapper {
    flex-direction: column;
  }

  .section-content-right {
    flex: 1;
    margin-top: 20px;
  }
}
.serchtop {
  margin-bottom: 0 !important;
}
.add-header-title {
  padding: 10px;
  display: flex;
  justify-content: space-between;
  height: 50px;
  font-weight: bold;
  background-color: rgb(222, 239, 255);
  box-sizing: border-box;
  border-bottom: 1px solid rgb(233, 233, 233);
  font-weight: 700;
  font-size: 16px;
  line-height: 28px;
}

.add-title-return {
  display: flex;
  align-content: center;
  color: rgb(32, 119, 255);
  cursor: pointer;
  font-weight: normal;
}

.back {
  height: 18px;
  width: 18px;
  margin-top: 5px;
}

.backlist {
  padding-left: 6px;
  font-size: 14px;
}
.content-title-1 {
  font-weight: bold;
  display: flex;
  align-items: center;
  p {
    margin-left: 8px;
  }
}
.hxgutter{
  color: #848484;
  font-size: 14px;
}
.mcheng{
  color: #333333;
  font-weight: bold;
  font-size: 16px;
}

</style>
