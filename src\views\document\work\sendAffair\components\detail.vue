<template>
  <div>
    <div class="header">
      <el-page-header @back="goBack">
        <template #content>
          <span class="text-large font-600 mr-3"> 批转收文 </span>
        </template>
      </el-page-header>
      <div class="btn-submit">
        <el-button v-if="!readOnly" type="primary" v-loading="showLoding" @click="selectUsers('')">批转</el-button>
        <el-button v-if="!readOnly && rejectNodeState(nodeId)" type="danger"
          @click="selectUsers('reject')">驳回</el-button>
        <!-- <el-button type="warning" @click="gotoHistory('0')">查看办理过程</el-button> -->
        <el-button type="success" @click="gotoHistory('1')">查看批转记录</el-button>
        <el-button @click="handleExport">打印</el-button>
      </div>
    </div>
    <el-row :gutter="20" class="container">
      <el-col :span="18" class="card" v-loading="showLoding">
        <SelectUserTransfer v-if="userDialog" @back="handleUserTransfer" :msg="form.docAudit?.auditIdea"
          :taskId="taskId" :nodeId="nodeId" :processInstanceId="processInstanceId" :rejectFlag="rejectFlag"
          @saveData="handleSelectUsers">
        </SelectUserTransfer>
        <div class="form">
          <div class="form-content">
            <h1 style="text-align: center">昆明市土地矿产储备中心发文审签表</h1>
            <el-form ref="receiptFormRef" :model="form" :rules="rules" label-width="80px">
              <div class="flx-justify-between">
                <el-form-item label="案卷号" prop="num">
                  <el-input v-model="form.num" :disabled="tabulation" placeholder="请输入案卷号" />
                </el-form-item>
                <el-form-item label="文号" prop="docNum">
                  <el-input :disabled="tabulation" v-model="form.docNum" placeholder="请输入文号" />
                </el-form-item>
              </div>
              <table>
                <tr>
                  <td colspan="4">
                    <el-form-item label="发文标题" prop="title" label-width="150px" style="width: 100%">
                      <el-input :rows="2" type="textarea" v-model="form.title" placeholder="请输入标题"
                        :disabled="formDisabled" />
                    </el-form-item>
                  </td>
                </tr>
                <tr>
                  <td>
                    <el-form-item label="发文类型" prop="type" label-width="150px">
                      <el-select v-model="form.type" placeholder="发文类型" clearable :disabled="formDisabled">
                        <el-option v-for="dict in dicts.doc_send_type" :key="dict.dictValue" :label="dict.dictLabel"
                          :value="dict.dictValue" />
                      </el-select>
                    </el-form-item>
                  </td>
                  <td>
                    <el-form-item label="紧急程度" prop="urgentDegree" label-width="150px">
                      <el-select v-model="form.urgentDegree" placeholder="急 缓" clearable :disabled="formDisabled">
                        <el-option v-for="dict in dicts.doc_urgency" :key="dict.dictValue" :label="dict.dictLabel"
                          :value="dict.dictValue" />
                      </el-select>
                    </el-form-item>
                  </td>
                  <td>
                    <el-form-item label="文件密级" prop="secretLevel" label-width="150px">
                      <el-select v-model="form.secretLevel" placeholder="密 级" clearable :disabled="formDisabled">
                        <el-option v-for="dict in dicts.doc_secret" :key="dict.dictValue" :label="dict.dictLabel"
                          :value="dict.dictValue" />
                      </el-select>
                    </el-form-item>
                  </td>
                </tr>
                <tr>
                  <td colspan="1">
                    <el-form-item label="是否有上行文" prop="isWriting" label-width="150px">
                      <el-select v-model="form.isWriting" placeholder="是否有上行文" clearable :disabled="formDisabled">
                        <el-option label="否" value="0" />
                        <el-option label="是" value="1" />
                      </el-select>
                    </el-form-item>
                  </td>
                  <td colspan="3">
                    <el-form-item label="政府信息公开属性" prop="infoAttribute" label-width="150px">
                      <el-select v-model="form.infoAttribute" placeholder="请选择政府信息公开属性" clearable
                        :disabled="formDisabled">
                        <el-option v-for="dict in dicts.doc_attribute" :key="dict.dictValue" :label="dict.dictLabel"
                          :value="dict.dictValue" />
                      </el-select>
                    </el-form-item>
                  </td>
                </tr>
                <tr>
                  <td colspan="4">
                    <el-form-item label="主送" prop="report" label-width="150px">
                      <el-input v-model="form.report" placeholder="请输入主送" :disabled="formDisabled" />
                    </el-form-item>
                  </td>
                </tr>
                <tr>
                  <td colspan="4">
                    <el-form-item label="抄送" prop="transcribe" label-width="150px">
                      <el-input v-model="form.transcribe" placeholder="请输入主送" :disabled="formDisabled" />
                    </el-form-item>
                  </td>
                </tr>
                <tr>
                  <td colspan="4">
                    <el-form-item label="备注" prop="remark" label-width="150px">
                      <el-input type="textarea" v-model="form.remark" placeholder="请输入备注" :disabled="formDisabled" />
                    </el-form-item>
                  </td>
                </tr>
                <tr>
                  <td colspan="4">
                    <el-form-item label="关联标题" :prop="form.isWriting === '1' ? 'linkTitle' : ''" label-width="150px">
                      <div class="flx-center" style="width: 100%">
                        <el-input v-model="form.linkTitle" disabled placeholder="请输入关联标题" style="flex: 1" />
                        <el-button class="ml5" type="primary" @click="showSelectRecipt = true"
                          :disabled="formDisabled">关联收文</el-button>
                        <component v-if="showSelectRecipt" :is="selectRecipt" @close="handleClose"
                          @selectReciptData="handleData" />
                      </div>
                    </el-form-item>
                  </td>
                </tr>
                <tr>
                  <td colspan="4">
                    <el-form-item label="关联文号" prop="linkNum" label-width="150px" style="width: 100%">
                      <el-input v-model="form.linkNum" disabled placeholder="请输入关联文号" />
                    </el-form-item>
                  </td>
                </tr>
                <tr v-for="(item, index) in tmpJson" :key="index">
                  <td v-if="item.docData === '1'" colspan="4">
                    <el-form-item v-if="item.nodeId === nodeId && !readOnly" :label="item.nodeName" label-width="150px">
                      <el-input type="textarea" v-model="form.docAudit.auditIdea" placeholder="请输入意见" />
                      <el-form-item>
                        <el-form-item label="签字">
                          <!-- <div class="sign-box" v-if="form.docAudit.auditSign">
                            {{ form.docAudit.auditSign }}
                          </div> -->
                          <img v-if="form.docAudit.auditSign" style="width: 100px;height: 40px;"
                            :src="perviewUrl + form.docAudit.auditSign">
                          <div class="sign-box" v-else-if="form.docAudit.name">
                            {{ form.docAudit.name }}
                          </div>
                          <div class="sign-box" v-else @click="docSign">
                            点击签字
                          </div>
                        </el-form-item>
                        <el-form-item label="日期">
                          <el-date-picker :disabled="!(item.nodeId === nodeId)" clearable
                            v-model="form.docAudit.auditTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss"
                            placeholder="请选择日期">
                          </el-date-picker>
                        </el-form-item>
                      </el-form-item>
                    </el-form-item>
                    <el-form-item v-else :label="item.nodeName" label-width="150px">
                      <el-input :disabled="true" type="textarea" v-model="item.data[0].auditIdea" placeholder="请输入意见" />
                      <el-form-item>
                        <el-form-item label="签字">
                          <!-- <div class="sign-box" style="background-color: #f5f7fa" v-if="item.data[0].auditSign">
                            {{ item.data[0].auditSign }}
                          </div> -->
                          <img style="width: 100px;height: 40px;" v-if="item.data[0].auditSign"
                            :src="perviewUrl + item.data[0].auditSign">
                          <div class="sign-box" style="background-color: #f5f7fa" v-else>
                            {{ item.data[0].name }}
                          </div>
                        </el-form-item>
                        <el-form-item label="日期">
                          <el-date-picker :disabled="true" clearable v-model="item.data[0].auditTime" type="datetime"
                            value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择日期">
                          </el-date-picker>
                        </el-form-item>
                      </el-form-item>
                    </el-form-item>
                  </td>
                  <td v-if="item.docData === '2'" colspan="4">
                    <div class="el-form-item__label" style="width: 150px; border: 0px">
                      {{ item.nodeName }}
                    </div>
                    <el-table :data="item.data" border style="width: 100%" height="200">
                      <el-table-column prop="auditIdea" label="意见" />
                      <el-table-column label="签字" width="180">
                        <template #default="scope">
                          <img style="width: 100px;height: 40px;" v-if="scope.row.auditSign"
                            :src="perviewUrl + scope.row.auditSign">
                          <div v-else>{{
                            scope.row.name
                          }}
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column prop="auditTime" label="日期" width="180" />
                    </el-table>
                    <div class="audit-btn flx-justify-between">
                      <el-input v-if="item.nodeId === nodeId && !readOnly" v-model="auditIdea" placeholder="请输入意见" />
                      <el-input v-else disabled placeholder="请输入意见" />
                      <el-button @click="docSignList(item)" :disabled="!(item.nodeId === nodeId) && !readOnly"
                        type="primary" plain>保存意见</el-button>
                      <el-button @click="docSignRevoke(item)" :disabled="!(item.nodeId === nodeId) && !readOnly"
                        type="info" plain>撤销意见</el-button>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td style="text-align: center">拟稿</td>
                  <td style="text-align: center">核稿</td>
                  <td style="text-align: center">校对</td>
                </tr>
                <tr>
                  <td colspan="3">
                    <el-form-item label="拟稿处室" prop="draftDept" label-width="150px" style="width: 100%">
                      <el-input v-model="form.draftDept" type="textarea" disabled />
                    </el-form-item>
                  </td>
                </tr>
                <tr>
                  <td>
                    <el-form-item label="拟稿人" prop="draftUser" label-width="150px" style="width: 100%">
                      <el-input v-model="form.draftUser" disabled type="textarea" />
                    </el-form-item>
                  </td>
                  <td>
                    <el-form-item label="核稿人" prop="reviewUser" label-width="150px" style="width: 100%">
                      <el-input v-model="form.reviewUser" disabled type="textarea" />
                    </el-form-item>
                  </td>
                  <td>
                    <el-form-item label="校对人" prop="checkUser" label-width="150px" style="width: 100%">
                      <el-input v-model="form.checkUser" disabled type="textarea" />
                    </el-form-item>
                  </td>
                </tr>
                <tr>
                  <td>
                    <el-form-item label="拟稿日期" prop="draftDate" label-width="150px" style="width: 100%">
                      <el-input v-model="form.draftDate" disabled type="textarea" />
                    </el-form-item>
                  </td>
                  <td>
                    <el-form-item label="核稿日期" prop="reviewDate" label-width="150px" style="width: 100%">
                      <el-input v-model="form.reviewDate" disabled type="textarea" />
                    </el-form-item>
                  </td>
                  <td>
                    <el-form-item label="校对日期" prop="checkDate" label-width="150px" style="width: 100%">
                      <el-input v-model="form.checkDate" disabled type="textarea" />
                    </el-form-item>
                  </td>
                </tr>
                <tr v-for="(item, index) in tmpJson1" :key="index">
                  <td v-if="item.docData === '1'" colspan="4">
                    <el-form-item v-if="item.nodeId === nodeId && !readOnly" :label="item.nodeName" label-width="150px">
                      <el-input type="textarea" v-model="form.docAudit.auditIdea" placeholder="请输入意见" />
                      <el-form-item>
                        <el-form-item label="签字">
                          <img v-if="form.docAudit.auditSign" style="width: 100px;height: 40px;"
                            :src="perviewUrl + form.docAudit.auditSign">
                          <div class="sign-box" v-else-if="form.docAudit.name">
                            {{ form.docAudit.name }}
                          </div>
                          <div class="sign-box" v-else @click="docSign">
                            点击签字
                          </div>
                        </el-form-item>
                        <el-form-item label="日期">
                          <el-date-picker :disabled="!(item.nodeId === nodeId)" clearable
                            v-model="form.docAudit.auditTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss"
                            placeholder="请选择日期">
                          </el-date-picker>
                        </el-form-item>
                      </el-form-item>
                    </el-form-item>
                    <el-form-item v-else :label="item.nodeName" label-width="150px">
                      <el-input :disabled="true" type="textarea" v-model="item.data[0].auditIdea" placeholder="请输入意见" />
                      <el-form-item>
                        <el-form-item label="签字">
                          <img style="width: 100px;height: 40px;" v-if="item.data[0].auditSign"
                            :src="perviewUrl + item.data[0].auditSign">
                          <div class="sign-box" style="background-color: #f5f7fa" v-else>
                            {{ item.data[0].name }}
                          </div>
                        </el-form-item>
                        <el-form-item label="日期">
                          <el-date-picker :disabled="true" clearable v-model="item.data[0].auditTime" type="datetime"
                            value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择日期">
                          </el-date-picker>
                        </el-form-item>
                      </el-form-item>
                    </el-form-item>
                  </td>
                  <td v-if="item.docData === '2'" colspan="4">
                    <div class="el-form-item__label" style="width: 150px; border: 0px">
                      {{ item.nodeName }}
                    </div>
                    <el-table :data="item.data" border style="width: 100%" height="200">
                      <el-table-column prop="auditIdea" label="意见" />
                      <el-table-column label="签字" width="180">
                        <template #default="scope">
                          <img style="width: 100px;height: 40px;" v-if="scope.row.auditSign"
                            :src="perviewUrl + scope.row.auditSign">
                          <div v-else>{{
                            scope.row.name
                          }}
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column prop="auditTime" label="日期" width="180" />
                    </el-table>
                    <div class="audit-btn flx-justify-between">
                      <el-input v-if="item.nodeId === nodeId && !readOnly" v-model="auditIdea" placeholder="请输入意见" />
                      <el-input v-else disabled placeholder="请输入意见" />
                      <el-button @click="docSignList(item)" :disabled="!(item.nodeId === nodeId && !readOnly)"
                        type="primary" plain>保存意见</el-button>
                      <el-button @click="docSignRevoke(item)" :disabled="!(item.nodeId === nodeId && !readOnly)"
                        type="info" plain>撤销意见</el-button>
                    </div>
                  </td>
                </tr>
              </table>
            </el-form>
          </div>
        </div>
      </el-col>
      <el-col :span="6" v-loading="showLoding">
        <div class="file-content card">
          <FileTree v-if="fileTypeList.length > 0" :key="formKey" :data="fileTypeList" :upload="!formDisabled"
            @onSuccess="onUploadSuccess" @onRemove="onUploadRemove" />
        </div>
      </el-col>
    </el-row>
    <History :activeName="historyType" :businessId="businessId" @back="goBackHistory" v-if="showHistory" />
  </div>
</template>

<script setup>
import FileTree from "@/components/FileTree";
import { onMounted, ref } from "vue";
import { getDicts } from "@/api/system/dict/data";
import { addReceipt, getDetail, updateReceipt } from "@/api/document/receipt";
import { getSendDetail, updateSend } from "@/api/document/send";
import {
  startWorkFlow,
  completeTask,
  signTask,
  backProcess,
} from "@/api/document/common";
import SelectUserTransfer from "@/components/SelectUserTransfer/index.vue";
import useUserStore from "@/store/modules/user";
import { parseTime } from "@/utils/ruoyi";
import History from "@/components/History/index.vue";
import { rejectNodeState } from "@/utils/rejectNodeMap.js";
import { exportWordForId } from "@/utils/common.js";


const showHistory = ref(false);
const historyType = ref("0");
const goBackHistory = () => {
  showHistory.value = false;
};
const gotoHistory = (type) => {
  showHistory.value = true;
  historyType.value = type;
};

const emit = defineEmits();
const props = defineProps({
  businessId: {
    type: String,
  },
  taskId: {
    type: String,
  },
  nodeId: {
    type: String,
  },
  processInstanceId: {
    type: String,
  },
  readOnly: {
    type: Boolean,
    default: () => false
  }
});

const { proxy } = getCurrentInstance();

const formKey = ref(new Date().getTime());
const fileTypeList = ref([]);
//表单修改开关
const formDisabled = ref(true);
const tabulation = ref(true)
const showLoding = ref(false);

//需要请求的字典类型
const dictsList = [
  "doc_secret",
  "doc_release",
  "doc_urgency",
  "doc_attribute",
  "doc_send_type",
  "doc_send_file_type",
];
const dicts = ref({});
dictsList.map((item) => (dicts.value[item] = []));

const receiptFormRef = ref(null);
const initFormData = {
  num: undefined,
  category: undefined,
  secretLevel: undefined,
  urgentDegree: undefined,
  releaseMethod: undefined,
  unitName: undefined,
  title: undefined,
  receiptTime: undefined,
  readType: undefined,
  affixList: [],
};
const data = reactive({
  form: { ...initFormData },
  rules: {
    title: [{ required: true, message: "发文标题不能为空", trigger: "blur" }],
    secretLevel: [{ required: true, message: "密级不能为空", trigger: "blur" }],
    isWriting: [
      { required: true, message: "是否有上行文不能为空", trigger: "blur" },
    ],
    report: [{ required: true, message: "主送不能为空", trigger: "blur" }],
    linkTitle: [
      {
        required: true,
        message: "存在上行文时关联标题不能为空",
        trigger: "blur",
      },
    ],
  },
});
const { form, rules } = toRefs(data);
const tmpJson = ref([]);
const tmpJson1 = ref([]);
const auditIdea = ref("");
const perviewUrl = import.meta.env.VITE_APP_FILE_SERVICE_URL

//print
const handleExport = () => {
  console.log('type,id', props.businessId);
  exportWordForId('昆明市土储矿产储备中心发文审签表', '3', props.businessId)
}

const handleUserTransfer = () => {
  userDialog.value = false
  rejectFlag.value = false
}

const userDialog = ref(false);
function goBack () {
  emit("back");
}
//上传成功
function onUploadSuccess (fileList) {
  form.value.affixList.push(fileList);
  console.log('新增附件', form.value.affixList);
}
//删除文件
function onUploadRemove (file) {
  form.value.affixList = form.value.affixList.filter(
    (item) => item.name !== file.response.data.url
  );
}
function docSign () {
  let userInfo = useUserStore().user;
  if (userInfo.signUrl)
  {
    form.value.docAudit.auditSign = userInfo.signUrl;
  } else
  {
    form.value.docAudit.name = userInfo.nickName;
  }
  let date = parseTime(new Date().getTime());
  form.value.docAudit.auditTime = date;
}
//多人签字
function docSignList (item) {
  let userInfo = useUserStore().user;
  if (userInfo.signUrl)
  {
    form.value.docAudit.auditSign = userInfo.signUrl;
  } else
  {
    form.value.docAudit.name = userInfo.nickName;
  }
  form.value.docAudit.auditIdea = auditIdea.value;
  let date = parseTime(new Date().getTime());

  let ArrIndex = item.data.findIndex((it) => it.del);
  if (ArrIndex !== -1)
  {
    item.data[ArrIndex] = {
      auditSign: userInfo.signUrl,
      name: userInfo.nickName,
      auditIdea: auditIdea.value,
      auditTime: date,
      del: true, //删除标志
    };
  } else
  {
    form.value.docAudit.auditTime = date;
    item.data.push({
      auditSign: userInfo.signUrl,
      name: userInfo.nickName,
      auditIdea: auditIdea.value,
      auditTime: date,
      del: true, //删除标志
    });
  }
}
//撤销签字
function docSignRevoke (item) {
  form.value.docAudit.auditSign = "";
  form.value.docAudit.name = "";
  form.value.docAudit.auditIdea = "";
  form.value.docAudit.auditTime = "";
  item.data = item.data.filter((it) => !it.del);
}

const rejectFlag = ref(false);
//调起选择框
function selectUsers (type) {
  let flag = true;
  tmpJson.value.map((item) => {
    if (proxy.nodeId === item.nodeId)
    {
      if (!form.value.docAudit.auditTime)
      {
        proxy.$modal.msgError("请在下方完成" + item.nodeName + "并签字");
        flag = false;
      }
    }
  });
  tmpJson1.value.map((item) => {
    if (proxy.nodeId === item.nodeId)
    {
      if (!form.value.docAudit.auditTime)
      {
        proxy.$modal.msgError("请在下方完成" + item.nodeName + "并签字");
        flag = false;
      }
    }
  });
  if (!flag) return;
  if (type === "reject") rejectFlag.value = true;
  userDialog.value = true;
}
//批转回调
function handleSelectUsers (result) {
  receiptFormRef.value.validate((valid) => {
    if (valid)
    {
      showLoding.value = true;
      if (form.value.docAudit)
      {
        form.value.docAudit.auditIdea = result.message;
      }
      let userInfo = useUserStore().user;
      console.log("result", form.value);

      //修改收文
      updateSend(form.value).then(() => {
        let data = {
          taskId: props.taskId,
          messageType: ["1"],
          message: JSON.stringify({
            msg: result.message,
            sign: userInfo.signUrl ? userInfo.signUrl : "",
            name: userInfo.signUrl ? "" : userInfo.nickName,
          }),
          variables: { entity: form.value, ...result.variables },
        };
        if (rejectFlag.value)
        {
          let params = {
            taskId: data.taskId,
            messageType: data.messageType,
            message: data.message,
            targetActivityId: result.variables
          }
          backProcess(params).then((res) => {
            showLoding.value = false;
            proxy.$modal.msgSuccess("驳回成功");
            goBack();
          });
        } else
        {
          // 办理任务
          completeTask(data).then((res) => {
            showLoding.value = false;
            proxy.$modal.msgSuccess("批转成功");
            goBack();
          });
        }

      });
    }
  });
}
function onSubmit () {
  receiptFormRef.value.validate((valid) => {
    if (valid)
    {
      addReceipt(form.value).then((res) => {
        let data = {
          businessKey: res.data.id,
          tableName: "doc_receipt",
          variables: {
            entity: res.data,
          },
        };
        startWorkFlow(data).then(() => {
          proxy.$modal.msgSuccess("提交成功");
          receiptFormRef.value.resetFields();
          formKey.value = new Date().getTime();
        });
      });
    }
  });
}
onMounted(() => {
  if (!props.readOnly)
  {
    // 签收任务
    signTask(props.taskId)
  }
  dictsList.map((item) => {
    getDicts(item).then((res) => {
      dicts.value[item] = res.data;
      if (dicts.value.doc_send_file_type.length > 0)
      {
        let listF = dicts.value.doc_send_file_type.map(item => {
          let obj = {
            dictLabel: item.dictLabel,
            fileList: []
          }
          return obj
        })

        getSendDetail(props.businessId).then((res) => {
          form.value = res.data;
          form.value.affixList = []
          if (form.value.affixes)
          {
            form.value.affixes.map((item) => {
              item.dictLabel = item.typeName;
              item.fileList = item.affixs;
            });
            fileTypeList.value = listF.map(itemA => {
              const itemB = res.data.affixes.find(itemB => itemB.typeName === itemA.dictLabel);
              if (itemB)
              {
                itemA.fileList = itemB.affixs;
              }
              return itemA;
            });
          }
          tmpJson.value = form.value.audits;
          tmpJson.value.map((item) => {
            if (proxy.nodeId === item.nodeId)
            {
              form.value.docAudit = {
                businessId: props.businessId,
                nodeName: item.nodeName,
                nodeId: item.nodeId,
                userId: useUserStore().id,
                name: "",
                auditIdea: "",
                auditTime: "",
                auditSign: "",
              };
            }
          });
          let index = form.value.audits.findIndex(
            (item) => item.nodeName === "办公室审核意见"
          );
          tmpJson1.value = tmpJson.value.slice(index + 1);
          tmpJson.value = tmpJson.value.slice(0, index + 1);
          if (proxy.nodeId === "Activity_0wxa7xu" && props.readOnly)
          {
            formDisabled.value = false;
          }
          if (proxy.nodeId === "Activity_02rjxpi")
          {
            tabulation.value = false
            formDisabled.value = false;
          }
        });

      }
    });
  });



  //   getTmpJson({ type: "3" }).then((res) => {
  //     tmpJson.value = res.data;
  //   });
});
</script>

<style scoped lang="scss">
@import "@/styles/variables.module.scss";;

.container {
  padding: 10px 20px;
  background-color: #e9eef3;
  height: calc($contentHeight - 50px);
  overflow-x: hidden;
  overflow-y: scroll;
}

.form {
  display: flex;
  justify-content: center;
  background: #dce2f1;
}

.form-content {
  width: 770px;
}

table {
  width: 100%;
  border: 1px solid #e2e2e2;
  border-collapse: collapse;

  :deep(.el-form-item) {
    margin-bottom: 0;
  }

  :deep(.el-form-item__label) {
    justify-content: center;
    border-right: 1px solid #e2e2e2;
    margin-right: 1px;
    height: auto;
    padding: 10px 10px;
  }

  :deep(.el-form-item__error) {
    position: static;
  }

  td {
    border: 1px solid #e2e2e2;
    background-color: rgb(246, 246, 246);
  }
}

.header {
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  box-shadow: 0 0 12px rgba(0, 0, 0, 0.05);
  border-bottom: 1px solid #e2e2e2;
  background-color: #fff;
}

.btn-submit {
  display: flex;
  justify-content: flex-end;
}

.audit-btn {
  padding: 5px 5px;
}

.sign-box {
  background-color: #fff;
  height: 100%;
  width: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #606266;
  font-family: inherit;
  font-size: inherit;
  cursor: pointer;
  border-right: 1px solid #e2e2e2;
}
</style>
