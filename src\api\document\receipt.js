import request from '@/utils/request'

// 查询待办箱
export function lisTodo(query) {
    return request({
        url: '/document/receipt/backlog',
        method: 'get',
        params: query
    })
}
// 新增收文
export function addReceipt(data) {
    return request({
        url: '/document/receipt',
        method: 'post',
        data: data
    })
}
// 修改收文
export function updateReceipt(data) {
    return request({
        url: '/document/receipt/updateDraftByBo',
        method: 'PUT',
        data: data
    })
}
//获取收文详细信息
export function getDetail(id) {
    return request({
        url: '/document/receipt/' + id,
        method: 'get',
    })
}

/** 
 * 获取最近两个月未发文的收文文章
 * @returns
 */
export function getLatest() {
    return request.get(`/document/receipt/latest/unsend`);
  }

/** 
 * 获取未发文的收文文档分页
  * @param {string} num 案卷号
  * @param {string} unitName 来文单位
  * @param {string} title 来文标题
  * @param {string} docNum 来文文号
  * @param {string} queryStartDate 查询开始日期
  * @param {string} queryEndDate 查询结束日期
  * @param {string} pageSize 分页大小
  * @param {string} pageNum 当前页数
  * @returns
 */
export function getUnSendPageQuery(query) {
    return request({
        url: '/document/receipt/latest/unsend/page',
        method: 'get',
        params: query
    })
}