/**
 * 获取当前月份的第一天，格式为 "YYYY-MM-DD HH:mm:ss"
 */
export const getMonthFirstDayWithTime = () => {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = '01'; // 每月第一天

    // 固定时间为 00:00:00
    const hours = '00';
    const minutes = '00';
    const seconds = '00';

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

/**
 * 获取当前月份的最后一天，格式为 "YYYY-MM-DD HH:mm:ss"
 */
export const getMonthLastDayWithTime = () => {
    const today = new Date();
    const year = today.getFullYear();
    const month = today.getMonth(); // 注意：月份从 0 开始

    // 创建下个月的第一天，再减去1毫秒，得到本月最后一天
    const lastDayOfMonth = new Date(year, month + 1, 0);

    const formattedYear = lastDayOfMonth.getFullYear();
    const formattedMonth = String(lastDayOfMonth.getMonth() + 1).padStart(2, '0');
    const formattedDay = String(lastDayOfMonth.getDate()).padStart(2, '0');

    const hours = '00';
    const minutes = '00';
    const seconds = '00';

    return `${formattedYear}-${formattedMonth}-${formattedDay} ${hours}:${minutes}:${seconds}`;
};

export const  formatDate = (dateString) => {
    const date = new Date(dateString);

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始
    const day = String(date.getDate()).padStart(2, '0');

    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}
