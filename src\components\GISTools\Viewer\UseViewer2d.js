import { Map, View } from "ol"
import { defaults } from "ol/control.js"
import { addLayer, addTDTCvaLayer, addTDTImageLayer } from "@/utils/OpenLayers/olLayer.js"
import eventBus from "@/utils/eventBus.js"

/**
 * @name: UseViewer2d
 * @description：2d视图管理
 * @author: zyc
 * @time: 2025-05-12
 **/
export default class UseViewer2d {
  /**
   * 创建2d视图
   * @param viewerId {String}：地图挂载元素id选择器|地图挂载元素
   * @param mapInitStore {Store}：地图初始化信息
   * @param mapViewStore {Store}：地图视图信息
   */
  constructor(viewerId,mapInitStore,mapViewStore){
    this.viewerId = viewerId
    this.mapInitStore = mapInitStore;
    this.mapViewStore = mapViewStore

    this.map = null
  }
  async createViewer2d(){
    eventBus.emit('showMapLoading',true)
    try{
      const mapInfo = await this.mapInitStore.getMapInfo()
      const olMap = new Map({
        target: this.viewerId,
        view: new View({
          center: mapInfo.center,
          zoom: mapInfo.zoom,
          maxZoom: 18,
          minZoom: 0,
          projection: mapInfo.projection // 定义坐标系
        }),
        controls: defaults({
          zoom: false,
          rotate: false,
          attribution: false
        })
      })

      /**###############################################修改地图变量开始##########################################*/
      this.map = olMap
      // 定义全局变量
      window._map = olMap
      this.mapViewStore.map = olMap
      this.mapViewStore.viewer2d = olMap.getView()
      // 修改地图初始化信息
      this.mapInitStore.mapInfo = mapInfo
      /**###############################################修改地图变量结束##########################################*/

      /**###############################################添加底图开始##########################################*/
      const wmtsOptions = {
        url: mapInfo.url,
        layerName: mapInfo.layerName,
        isBasemap: true,
        gridsetName: "EPSG:4490_" + mapInfo.layerName,
        layerId: 111111111111,
        zIndex: -1
      }
      if(import.meta.env.DEV){
        // 开发环境：添加互联网地图
        // addTDTImageLayer(olMap)
        // addTDTCvaLayer(olMap)
        addLayer(mapInfo.serviceType,wmtsOptions)
      }
      if(import.meta.env.PROD){
        // 生产环境：添加互内网服务
        addLayer(mapInfo.serviceType,wmtsOptions)
      }
      /**###############################################添加底图结束##########################################*/
      eventBus.emit('showMapLoading',false)
    }catch (err){
      throw new Error("Map create failed，" + err)
    }finally {
      eventBus.emit('showMapLoading',false)
    }
  }
}
