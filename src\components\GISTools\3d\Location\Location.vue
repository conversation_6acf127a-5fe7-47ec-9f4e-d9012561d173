<template>
  <div v-if="isShow" class="tool-wrap">
    <div class="tool-content">
      <div class="tool-main">
        <div class="tool-tabs">
          <span
            :class="{ 'tab-active': isGCS }"
            @click="showGCSCoords"
          >地理坐标</span>
          <span
            :class="{ 'tab-active': isPlan }"
            @click="showPlanCoords"
          >平面坐标</span>
        </div>
        <div v-show="isGCS" class="tool-tab-content">
          <el-form ref="formRef" :model="dataForm" label-width="80px">
            <el-form-item label="经度">
              <el-input v-model="dataForm.longitude" />
            </el-form-item>
            <el-form-item label="纬度">
              <el-input v-model="dataForm.latitude" />
            </el-form-item>
            <el-form-item label="高程">
              <el-input v-model="dataForm.height" />
            </el-form-item>
          </el-form>
        </div>
        <div v-show="isPlan" class="tool-tab-content">
          <el-form ref="form" :model="dataForm" label-width="80px">
            <el-form-item label="X 坐标">
              <el-input v-model="dataForm.x" />
            </el-form-item>
            <el-form-item label="Y 坐标">
              <el-input v-model="dataForm.y" />
            </el-form-item>
            <el-form-item label="高程">
              <el-input v-model="dataForm.height" />
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="tool-bottom">
        <div class="tool-btn" @click="getCoordsByClick">
          <span>拾取坐标</span>
        </div>
        <div class="tool-btn" @click="getCoordsByCenter">
          <svg-icon icon-class="locate" />
          <span>地图定位</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="js" name="Location">
import useMapViewStore from "@/store/modules/map/mapView.js"
import { setCursor } from "@/utils/Cesium/CesiumTool";
const store = useMapViewStore()

defineProps({
  isShow: {
    type: Boolean,
    default: true
  },
  headerInfo: {
    type: Object
  }
})
const dataForm = reactive({
  x: 0,
  y: 0,
  height: 0,
  latitude: 24.665586,
  longitude: 102.508035
})

const isGCS = ref(true)
const isPlan = ref(false)

const viewer3d = computed(()=>useMapViewStore().viewer3d)

const getCoordsByClick = ()=> {
  setCursor(viewer3d.value, "crosshair");
  const handler = new Cesium.ScreenSpaceEventHandler(
    viewer3d.value.canvas
  );
  handler.setInputAction((evt) => {
    let worldPosition = viewer3d.value.scene.pickPosition(evt.position)
    if (!worldPosition) {
      worldPosition = Cesium.Cartesian3.fromDegrees(0, 0, 0);
    }
    const cartographic = Cesium.Cartographic.fromCartesian(worldPosition);

    const data = coordsTransform(cartographic, worldPosition)
    Object.assign(dataForm,reactive(data))
    addPoint();

    handler.destroy();
    setCursor(viewer3d.value, "pointer");
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
}

const showGCSCoords = ()=> {
  isGCS.value = true;
  isPlan.value = false;
}

const showPlanCoords = ()=> {
  isPlan.value = true;
  isGCS.value = false;
}


/**
 * 获取视图中心点
 * @returns {undefined|{latitude: number, longitude: number, height: number}}
 */
const getCenterPoint = (viewer3d)=>{
  const center = viewer3d.camera.getPickRay(
    new Cesium.Cartesian2(
      viewer3d.canvas.clientWidth / 2,
      viewer3d.canvas.clientHeight / 2
    )
  );
  let data = {}
  if (Cesium.defined(center)) {
    // 将Cartesian3坐标转换为经纬度高度
    const cartographic = viewer3d.scene.globe.ellipsoid.cartesianToCartographic(center.origin);
    data = coordsTransform(cartographic, center.origin);
    Object.assign(dataForm,reactive(data))
  }
}

const coordsTransform = (cartographic, cartesian3)=> {
  const longitude = Cesium.Math.toDegrees(cartographic.longitude);
  const latitude = Cesium.Math.toDegrees(cartographic.latitude);
  const height = viewer3d.value.scene.globe.getHeight(cartographic);
  const lon = longitude.toFixed(6);
  const lat = latitude.toFixed(6);
  const h = height.toFixed(6);
  const x = cartesian3.x.toFixed(6);
  const y = cartesian3.y.toFixed(6);
  return {
    x: Number(x),
    y: Number(y),
    longitude: Number(lon),
    latitude: Number(lat),
    height: Number(h)
  };
}
const addPoint = ()=>{
  const pointEntity = new Cesium.Entity({
    name: "绘制点",
    show: true,
    position: Cesium.Cartesian3.fromDegrees(
      +dataForm.longitude,
      +dataForm.latitude,
      +dataForm.height
    ),
    point: new Cesium.PointGraphics({
      color: Cesium.Color.AQUA,
      pixelSize: 15,
      outlineColor: Cesium.Color.WHITE,
      outlineWidth: 2.5
    })
  });
  viewer3d.value.entities.add(pointEntity);
  // Object.assign(pointEntity,pointEntity)
}

const getCoordsByCenter = ()=>{
  if (dataForm.latitude) {
    // 跳转到目标点
    // viewer3d.value.camera.flyTo(pointEntity);
    // viewer3d.value.zoomTo(pointEntity.value);

    viewer3d.value.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(
        dataForm.longitude, dataForm.latitude, dataForm.height + 5000
      ),
      orientation: {
        // 弧度单位
        heading: Cesium.Math.toRadians(0),
        pitch: Cesium.Math.toRadians(-90),
        roll: 0.0
      }
    })
  }
}
onMounted(()=>{
  getCenterPoint(viewer3d.value);
})
</script>

<style scoped lang="scss">
.tool-wrap {
  color: #fff;
  background-image: url("@/assets/images/map/tool.png");
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-size: cover;
  box-shadow: 0 0 8px 0 #057595;
}

.title {
  padding: 12px;
  font-size: 16px;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;

  &:hover {
    cursor: all-scroll;
  }

  .title-name {
    float: left;

    span {
      margin-left: 10px;
    }

    .svg-icon {
      color: #30fdff;
    }
  }

  .title-close {
    float: right;
    color: #ccc;

    &:hover {
      cursor: pointer;
      filter: brightness(150%);
      transition-delay: 0.25s;
    }
  }
}

.tool-content {
  padding: 10px;
  width: 100%;
  font-size: 16px;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;

  .tool-bottom {
    display: flex;
    justify-content: space-between;
    justify-items: center;
  }

  .tool-btn {
    margin-top: 20px;
    padding: 10px;
    width: 48%;
    background-color: #0f7dff;
    border-color: #fff;
    text-align: center;
    border-radius: 4px;
    color: #ffffff;

    &:hover {
      cursor: pointer;
      color: #fff;
      filter: brightness(110%) opacity(100%);
      transition: all 0.5s ease-in;
      background: linear-gradient(to bottom right, #00baff, #0f7dff);
    }

    svg {
      margin-right: 10px;
    }
  }
}

.tool-tabs {
  display: flex;
  justify-items: center;
  justify-content: space-between;
  background-color: #4f7287;

  span {
    display: inline-block;
    width: 50%;
    padding: 10px 5px;
    text-align: center;
    color: #fff;

    &:hover {
      cursor: pointer;
      background-color: #00baff;
      filter: brightness(110%);
      color: #fff;
      transition: background-color 0.25s;
    }
  }
}

.tab-active {
  color: #fff;
  background: linear-gradient(to bottom right, #00baff, #0f7dff);
}

.tool-tab-content {
  padding-top: 20px;
}

:deep(.el-form-item__label) {
  width: 60px !important;
  color: #b5b5b5;
}
</style>
