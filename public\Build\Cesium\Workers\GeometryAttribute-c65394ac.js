define(["exports","./Cartesian2-db21342c","./Cartographic-3309dd0d","./Check-7b2a090c","./when-b60132fc","./FeatureDetection-806b12f0","./Math-119be1a3","./Rectangle-dee65d21","./Cartesian4-3ca25aab","./buildModuleUrl-9085faaa","./RuntimeError-4a5c8994"],(function(e,t,n,a,r,i,s,o,u,l,d){"use strict";var c=Object.freeze({NONE:0,TRIANGLES:1,LINES:2,POLYLINES:3});function f(e,t,n,a){this[0]=r.defaultValue(e,0),this[1]=r.defaultValue(n,0),this[2]=r.defaultValue(t,0),this[3]=r.defaultValue(a,0)}f.packedLength=4,f.pack=function(e,t,n){return n=r.defaultValue(n,0),t[n++]=e[0],t[n++]=e[1],t[n++]=e[2],t[n++]=e[3],t},f.unpack=function(e,t,n){return t=r.defaultValue(t,0),r.defined(n)||(n=new f),n[0]=e[t++],n[1]=e[t++],n[2]=e[t++],n[3]=e[t++],n},f.clone=function(e,t){if(r.defined(e))return r.defined(t)?(t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t):new f(e[0],e[2],e[1],e[3])},f.fromArray=function(e,t,n){return t=r.defaultValue(t,0),r.defined(n)||(n=new f),n[0]=e[t],n[1]=e[t+1],n[2]=e[t+2],n[3]=e[t+3],n},f.fromColumnMajorArray=function(e,t){return f.clone(e,t)},f.fromRowMajorArray=function(e,t){return r.defined(t)?(t[0]=e[0],t[1]=e[2],t[2]=e[1],t[3]=e[3],t):new f(e[0],e[1],e[2],e[3])},f.fromScale=function(e,t){return r.defined(t)?(t[0]=e.x,t[1]=0,t[2]=0,t[3]=e.y,t):new f(e.x,0,0,e.y)},f.fromUniformScale=function(e,t){return r.defined(t)?(t[0]=e,t[1]=0,t[2]=0,t[3]=e,t):new f(e,0,0,e)},f.fromRotation=function(e,t){var n=Math.cos(e),a=Math.sin(e);return r.defined(t)?(t[0]=n,t[1]=a,t[2]=-a,t[3]=n,t):new f(n,-a,a,n)},f.toArray=function(e,t){return r.defined(t)?(t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t):[e[0],e[1],e[2],e[3]]},f.getElementIndex=function(e,t){return 2*e+t},f.getColumn=function(e,t,n){var a=2*t,r=e[a],i=e[a+1];return n.x=r,n.y=i,n},f.setColumn=function(e,t,n,a){var r=2*t;return(a=f.clone(e,a))[r]=n.x,a[r+1]=n.y,a},f.getRow=function(e,t,n){var a=e[t],r=e[t+2];return n.x=a,n.y=r,n},f.setRow=function(e,t,n,a){return(a=f.clone(e,a))[t]=n.x,a[t+2]=n.y,a};var h=new t.Cartesian2;f.getScale=function(e,n){return n.x=t.Cartesian2.magnitude(t.Cartesian2.fromElements(e[0],e[1],h)),n.y=t.Cartesian2.magnitude(t.Cartesian2.fromElements(e[2],e[3],h)),n};var m=new t.Cartesian2;function p(e,t,n,a){this.x=r.defaultValue(e,0),this.y=r.defaultValue(t,0),this.z=r.defaultValue(n,0),this.w=r.defaultValue(a,0)}f.getMaximumScale=function(e){return f.getScale(e,m),t.Cartesian2.maximumComponent(m)},f.multiply=function(e,t,n){var a=e[0]*t[0]+e[2]*t[1],r=e[0]*t[2]+e[2]*t[3],i=e[1]*t[0]+e[3]*t[1],s=e[1]*t[2]+e[3]*t[3];return n[0]=a,n[1]=i,n[2]=r,n[3]=s,n},f.add=function(e,t,n){return n[0]=e[0]+t[0],n[1]=e[1]+t[1],n[2]=e[2]+t[2],n[3]=e[3]+t[3],n},f.subtract=function(e,t,n){return n[0]=e[0]-t[0],n[1]=e[1]-t[1],n[2]=e[2]-t[2],n[3]=e[3]-t[3],n},f.multiplyByVector=function(e,t,n){var a=e[0]*t.x+e[2]*t.y,r=e[1]*t.x+e[3]*t.y;return n.x=a,n.y=r,n},f.multiplyByScalar=function(e,t,n){return n[0]=e[0]*t,n[1]=e[1]*t,n[2]=e[2]*t,n[3]=e[3]*t,n},f.multiplyByScale=function(e,t,n){return n[0]=e[0]*t.x,n[1]=e[1]*t.x,n[2]=e[2]*t.y,n[3]=e[3]*t.y,n},f.negate=function(e,t){return t[0]=-e[0],t[1]=-e[1],t[2]=-e[2],t[3]=-e[3],t},f.transpose=function(e,t){var n=e[0],a=e[2],r=e[1],i=e[3];return t[0]=n,t[1]=a,t[2]=r,t[3]=i,t},f.abs=function(e,t){return t[0]=Math.abs(e[0]),t[1]=Math.abs(e[1]),t[2]=Math.abs(e[2]),t[3]=Math.abs(e[3]),t},f.equals=function(e,t){return e===t||r.defined(e)&&r.defined(t)&&e[0]===t[0]&&e[1]===t[1]&&e[2]===t[2]&&e[3]===t[3]},f.equalsArray=function(e,t,n){return e[0]===t[n]&&e[1]===t[n+1]&&e[2]===t[n+2]&&e[3]===t[n+3]},f.equalsEpsilon=function(e,t,n){return e===t||r.defined(e)&&r.defined(t)&&Math.abs(e[0]-t[0])<=n&&Math.abs(e[1]-t[1])<=n&&Math.abs(e[2]-t[2])<=n&&Math.abs(e[3]-t[3])<=n},f.IDENTITY=Object.freeze(new f(1,0,0,1)),f.ZERO=Object.freeze(new f(0,0,0,0)),f.COLUMN0ROW0=0,f.COLUMN0ROW1=1,f.COLUMN1ROW0=2,f.COLUMN1ROW1=3,Object.defineProperties(f.prototype,{length:{get:function(){return f.packedLength}}}),f.prototype.clone=function(e){return f.clone(this,e)},f.prototype.equals=function(e){return f.equals(this,e)},f.prototype.equalsEpsilon=function(e,t){return f.equalsEpsilon(this,e,t)},f.prototype.toString=function(){return"("+this[0]+", "+this[2]+")\n("+this[1]+", "+this[3]+")"};var y=new n.Cartesian3;p.fromAxisAngle=function(e,t,a){var i=t/2,s=Math.sin(i),o=(y=n.Cartesian3.normalize(e,y)).x*s,u=y.y*s,l=y.z*s,d=Math.cos(i);return r.defined(a)?(a.x=o,a.y=u,a.z=l,a.w=d,a):new p(o,u,l,d)};var w=[1,2,0],C=new Array(3);p.fromRotationMatrix=function(e,t){var n,a,s,o,u,l=e[i.Matrix3.COLUMN0ROW0],d=e[i.Matrix3.COLUMN1ROW1],c=e[i.Matrix3.COLUMN2ROW2],f=l+d+c;if(f>0)u=.5*(n=Math.sqrt(f+1)),n=.5/n,a=(e[i.Matrix3.COLUMN1ROW2]-e[i.Matrix3.COLUMN2ROW1])*n,s=(e[i.Matrix3.COLUMN2ROW0]-e[i.Matrix3.COLUMN0ROW2])*n,o=(e[i.Matrix3.COLUMN0ROW1]-e[i.Matrix3.COLUMN1ROW0])*n;else{var h=0;d>l&&(h=1),c>l&&c>d&&(h=2);var m=w[h],y=w[m];n=Math.sqrt(e[i.Matrix3.getElementIndex(h,h)]-e[i.Matrix3.getElementIndex(m,m)]-e[i.Matrix3.getElementIndex(y,y)]+1);var x=C;x[h]=.5*n,n=.5/n,u=(e[i.Matrix3.getElementIndex(y,m)]-e[i.Matrix3.getElementIndex(m,y)])*n,x[m]=(e[i.Matrix3.getElementIndex(m,h)]+e[i.Matrix3.getElementIndex(h,m)])*n,x[y]=(e[i.Matrix3.getElementIndex(y,h)]+e[i.Matrix3.getElementIndex(h,y)])*n,a=-x[0],s=-x[1],o=-x[2]}return r.defined(t)?(t.x=a,t.y=s,t.z=o,t.w=u,t):new p(a,s,o,u)};var x=new p,M=new p,_=new p,E=new p;p.fromHeadingPitchRoll=function(e,t){return E=p.fromAxisAngle(n.Cartesian3.UNIT_X,e.roll,x),_=p.fromAxisAngle(n.Cartesian3.UNIT_Y,-e.pitch,t),t=p.multiply(_,E,_),M=p.fromAxisAngle(n.Cartesian3.UNIT_Z,-e.heading,x),p.multiply(M,t,t)};var O=new n.Cartesian3,v=new n.Cartesian3,S=new p,T=new p,g=new p;p.packedLength=4,p.pack=function(e,t,n){return n=r.defaultValue(n,0),t[n++]=e.x,t[n++]=e.y,t[n++]=e.z,t[n]=e.w,t},p.unpack=function(e,t,n){return t=r.defaultValue(t,0),r.defined(n)||(n=new p),n.x=e[t],n.y=e[t+1],n.z=e[t+2],n.w=e[t+3],n},p.packedInterpolationLength=3,p.convertPackedArrayForInterpolation=function(e,t,n,a){p.unpack(e,4*n,g),p.conjugate(g,g);for(var r=0,i=n-t+1;r<i;r++){var s=3*r;p.unpack(e,4*(t+r),S),p.multiply(S,g,S),S.w<0&&p.negate(S,S),p.computeAxis(S,O);var o=p.computeAngle(S);a[s]=O.x*o,a[s+1]=O.y*o,a[s+2]=O.z*o}},p.unpackInterpolationResult=function(e,t,a,i,s){r.defined(s)||(s=new p),n.Cartesian3.fromArray(e,0,v);var o=n.Cartesian3.magnitude(v);return p.unpack(t,4*i,T),0===o?p.clone(p.IDENTITY,S):p.fromAxisAngle(v,o,S),p.multiply(S,T,s)},p.clone=function(e,t){if(r.defined(e))return r.defined(t)?(t.x=e.x,t.y=e.y,t.z=e.z,t.w=e.w,t):new p(e.x,e.y,e.z,e.w)},p.conjugate=function(e,t){return t.x=-e.x,t.y=-e.y,t.z=-e.z,t.w=e.w,t},p.magnitudeSquared=function(e){return e.x*e.x+e.y*e.y+e.z*e.z+e.w*e.w},p.magnitude=function(e){return Math.sqrt(p.magnitudeSquared(e))},p.normalize=function(e,t){var n=1/p.magnitude(e),a=e.x*n,r=e.y*n,i=e.z*n,s=e.w*n;return t.x=a,t.y=r,t.z=i,t.w=s,t},p.inverse=function(e,t){var n=p.magnitudeSquared(e);return t=p.conjugate(e,t),p.multiplyByScalar(t,1/n,t)},p.add=function(e,t,n){return n.x=e.x+t.x,n.y=e.y+t.y,n.z=e.z+t.z,n.w=e.w+t.w,n},p.subtract=function(e,t,n){return n.x=e.x-t.x,n.y=e.y-t.y,n.z=e.z-t.z,n.w=e.w-t.w,n},p.negate=function(e,t){return t.x=-e.x,t.y=-e.y,t.z=-e.z,t.w=-e.w,t},p.dot=function(e,t){return e.x*t.x+e.y*t.y+e.z*t.z+e.w*t.w},p.multiply=function(e,t,n){var a=e.x,r=e.y,i=e.z,s=e.w,o=t.x,u=t.y,l=t.z,d=t.w,c=s*o+a*d+r*l-i*u,f=s*u-a*l+r*d+i*o,h=s*l+a*u-r*o+i*d,m=s*d-a*o-r*u-i*l;return n.x=c,n.y=f,n.z=h,n.w=m,n},p.multiplyByVec=function(e,t,a){var r=new n.Cartesian3,i=new n.Cartesian3,s=new n.Cartesian3(e.x,e.y,e.z);r=n.Cartesian3.cross(s,t,r),i=n.Cartesian3.cross(s,r,i);var o=new n.Cartesian3;o=n.Cartesian3.multiplyByScalar(r,2*e.w,o);var u=new n.Cartesian3;return u=n.Cartesian3.multiplyByScalar(r,2,u),a=n.Cartesian3.add(t,o,a),a=n.Cartesian3.add(a,u,a)},p.multiplyByScalar=function(e,t,n){return n.x=e.x*t,n.y=e.y*t,n.z=e.z*t,n.w=e.w*t,n},p.divideByScalar=function(e,t,n){return n.x=e.x/t,n.y=e.y/t,n.z=e.z/t,n.w=e.w/t,n},p.computeAxis=function(e,t){var n=e.w;if(Math.abs(n-1)<s.CesiumMath.EPSILON6)return t.x=t.y=t.z=0,t;var a=1/Math.sqrt(1-n*n);return t.x=e.x*a,t.y=e.y*a,t.z=e.z*a,t},p.computeAngle=function(e){return Math.abs(e.w-1)<s.CesiumMath.EPSILON6?0:2*Math.acos(e.w)};var D=new p;p.lerp=function(e,t,n,a){return D=p.multiplyByScalar(t,n,D),a=p.multiplyByScalar(e,1-n,a),p.add(D,a,a)};var P=new p,N=new p,I=new p;p.slerp=function(e,t,n,a){var r=p.dot(e,t),i=t;if(r<0&&(r=-r,i=P=p.negate(t,P)),1-r<s.CesiumMath.EPSILON6)return p.lerp(e,i,n,a);var o=Math.acos(r);return N=p.multiplyByScalar(e,Math.sin((1-n)*o),N),I=p.multiplyByScalar(i,Math.sin(n*o),I),a=p.add(N,I,a),p.multiplyByScalar(a,1/Math.sin(o),a)},p.log=function(e,t){var a=s.CesiumMath.acosClamped(e.w),r=0;return 0!==a&&(r=a/Math.sin(a)),n.Cartesian3.multiplyByScalar(e,r,t)},p.exp=function(e,t){var a=n.Cartesian3.magnitude(e),r=0;return 0!==a&&(r=Math.sin(a)/a),t.x=e.x*r,t.y=e.y*r,t.z=e.z*r,t.w=Math.cos(a),t};var R=new n.Cartesian3,A=new n.Cartesian3,U=new p,b=new p;p.computeInnerQuadrangle=function(e,t,a,r){var i=p.conjugate(t,U);p.multiply(i,a,b);var s=p.log(b,R);p.multiply(i,e,b);var o=p.log(b,A);return n.Cartesian3.add(s,o,s),n.Cartesian3.multiplyByScalar(s,.25,s),n.Cartesian3.negate(s,s),p.exp(s,U),p.multiply(t,U,r)},p.squad=function(e,t,n,a,r,i){var s=p.slerp(e,t,r,U),o=p.slerp(n,a,r,b);return p.slerp(s,o,2*r*(1-r),i)};for(var z=new p,F=1.9011074535173003,V=i.FeatureDetection.supportsTypedArrays()?new Float32Array(8):[],W=i.FeatureDetection.supportsTypedArrays()?new Float32Array(8):[],q=i.FeatureDetection.supportsTypedArrays()?new Float32Array(8):[],L=i.FeatureDetection.supportsTypedArrays()?new Float32Array(8):[],k=0;k<7;++k){var Y=k+1,B=2*Y+1;V[k]=1/(Y*B),W[k]=Y/B}function j(e,t,n){for(var a,r,i=0,s=e.length-1;i<=s;)if((r=n(e[a=~~((i+s)/2)],t))<0)i=a+1;else{if(!(r>0))return a;s=a-1}return~(s+1)}function G(e,t,n,a,r){this.xPoleWander=e,this.yPoleWander=t,this.xPoleOffset=n,this.yPoleOffset=a,this.ut1MinusUtc=r}
/**
    @license
    sprintf.js from the php.js project - https://github.com/kvz/phpjs
    Directly from https://github.com/kvz/phpjs/blob/master/functions/strings/sprintf.js

    php.js is copyright 2012 Kevin van Zonneveld.

    Portions copyright Brett Zamir (http://brett-zamir.me), Kevin van Zonneveld
    (http://kevin.vanzonneveld.net), Onno Marsman, Theriault, Michael White
    (http://getsprink.com), Waldo Malqui Silva, Paulo Freitas, Jack, Jonas
    Raoni Soares Silva (http://www.jsfromhell.com), Philip Peterson, Legaev
    Andrey, Ates Goral (http://magnetiq.com), Alex, Ratheous, Martijn Wieringa,
    Rafa? Kukawski (http://blog.kukawski.pl), lmeyrick
    (https://sourceforge.net/projects/bcmath-js/), Nate, Philippe Baumann,
    Enrique Gonzalez, Webtoolkit.info (http://www.webtoolkit.info/), Carlos R.
    L. Rodrigues (http://www.jsfromhell.com), Ash Searle
    (http://hexmen.com/blog/), Jani Hartikainen, travc, Ole Vrijenhoek,
    Erkekjetter, Michael Grier, Rafa? Kukawski (http://kukawski.pl), Johnny
    Mast (http://www.phpvrouwen.nl), T.Wild, d3x,
    http://stackoverflow.com/questions/57803/how-to-convert-decimal-to-hex-in-javascript,
    Rafa? Kukawski (http://blog.kukawski.pl/), stag019, pilus, WebDevHobo
    (http://webdevhobo.blogspot.com/), marrtins, GeekFG
    (http://geekfg.blogspot.com), Andrea Giammarchi
    (http://webreflection.blogspot.com), Arpad Ray (mailto:<EMAIL>),
    gorthaur, Paul Smith, Tim de Koning (http://www.kingsquare.nl), Joris, Oleg
    Eremeev, Steve Hilder, majak, gettimeofday, KELAN, Josh Fraser
    (http://onlineaspect.com/2007/06/08/auto-detect-a-time-zone-with-javascript/),
    Marc Palau, Martin
    (http://www.erlenwiese.de/), Breaking Par Consulting Inc
    (http://www.breakingpar.com/bkp/home.nsf/0/87256B280015193F87256CFB006C45F7),
    Chris, Mirek Slugen, saulius, Alfonso Jimenez
    (http://www.alfonsojimenez.com), Diplom@t (http://difane.com/), felix,
    Mailfaker (http://www.weedem.fr/), Tyler Akins (http://rumkin.com), Caio
    Ariede (http://caioariede.com), Robin, Kankrelune
    (http://www.webfaktory.info/), Karol Kowalski, Imgen Tata
    (http://www.myipdf.com/), mdsjack (http://www.mdsjack.bo.it), Dreamer,
    Felix Geisendoerfer (http://www.debuggable.com/felix), Lars Fischer, AJ,
    David, Aman Gupta, Michael White, Public Domain
    (http://www.json.org/json2.js), Steven Levithan
    (http://blog.stevenlevithan.com), Sakimori, Pellentesque Malesuada,
    Thunder.m, Dj (http://phpjs.org/functions/htmlentities:425#comment_134018),
    Steve Clay, David James, Francois, class_exists, nobbler, T. Wild, Itsacon
    (http://www.itsacon.net/), date, Ole Vrijenhoek (http://www.nervous.nl/),
    Fox, Raphael (Ao RUDLER), Marco, noname, Mateusz "loonquawl" Zalega, Frank
    Forte, Arno, ger, mktime, john (http://www.jd-tech.net), Nick Kolosov
    (http://sammy.ru), marc andreu, Scott Cariss, Douglas Crockford
    (http://javascript.crockford.com), madipta, Slawomir Kaniecki,
    ReverseSyntax, Nathan, Alex Wilson, kenneth, Bayron Guevara, Adam Wallner
    (http://web2.bitbaro.hu/), paulo kuong, jmweb, Lincoln Ramsay, djmix,
    Pyerre, Jon Hohle, Thiago Mata (http://thiagomata.blog.com), lmeyrick
    (https://sourceforge.net/projects/bcmath-js/this.), Linuxworld, duncan,
    Gilbert, Sanjoy Roy, Shingo, sankai, Oskar Larsson H?gfeldt
    (http://oskar-lh.name/), Denny Wardhana, 0m3r, Everlasto, Subhasis Deb,
    josh, jd, Pier Paolo Ramon (http://www.mastersoup.com/), P, merabi, Soren
    Hansen, Eugene Bulkin (http://doubleaw.com/), Der Simon
    (http://innerdom.sourceforge.net/), echo is bad, Ozh, XoraX
    (http://www.xorax.info), EdorFaus, JB, J A R, Marc Jansen, Francesco, LH,
    Stoyan Kyosev (http://www.svest.org/), nord_ua, omid
    (http://phpjs.org/functions/380:380#comment_137122), Brad Touesnard, MeEtc
    (http://yass.meetcweb.com), Peter-Paul Koch
    (http://www.quirksmode.org/js/beat.html), Olivier Louvignes
    (http://mg-crea.com/), T0bsn, Tim Wiel, Bryan Elliott, Jalal Berrami,
    Martin, JT, David Randall, Thomas Beaucourt (http://www.webapp.fr), taith,
    vlado houba, Pierre-Luc Paour, Kristof Coomans (SCK-CEN Belgian Nucleair
    Research Centre), Martin Pool, Kirk Strobeck, Rick Waldron, Brant Messenger
    (http://www.brantmessenger.com/), Devan Penner-Woelk, Saulo Vallory, Wagner
    B. Soares, Artur Tchernychev, Valentina De Rosa, Jason Wong
    (http://carrot.org/), Christoph, Daniel Esteban, strftime, Mick@el, rezna,
    Simon Willison (http://simonwillison.net), Anton Ongson, Gabriel Paderni,
    Marco van Oort, penutbutterjelly, Philipp Lenssen, Bjorn Roesbeke
    (http://www.bjornroesbeke.be/), Bug?, Eric Nagel, Tomasz Wesolowski,
    Evertjan Garretsen, Bobby Drake, Blues (http://tech.bluesmoon.info/), Luke
    Godfrey, Pul, uestla, Alan C, Ulrich, Rafal Kukawski, Yves Sucaet,
    sowberry, Norman "zEh" Fuchs, hitwork, Zahlii, johnrembo, Nick Callen,
    Steven Levithan (stevenlevithan.com), ejsanders, Scott Baker, Brian Tafoya
    (http://www.premasolutions.com/), Philippe Jausions
    (http://pear.php.net/user/jausions), Aidan Lister
    (http://aidanlister.com/), Rob, e-mike, HKM, ChaosNo1, metjay, strcasecmp,
    strcmp, Taras Bogach, jpfle, Alexander Ermolaev
    (http://snippets.dzone.com/user/AlexanderErmolaev), DxGx, kilops, Orlando,
    dptr1988, Le Torbi, James (http://www.james-bell.co.uk/), Pedro Tainha
    (http://www.pedrotainha.com), James, Arnout Kazemier
    (http://www.3rd-Eden.com), Chris McMacken, gabriel paderni, Yannoo,
    FGFEmperor, baris ozdil, Tod Gentille, Greg Frazier, jakes, 3D-GRAF, Allan
    Jensen (http://www.winternet.no), Howard Yeend, Benjamin Lupton, davook,
    daniel airton wermann (http://wermann.com.br), Atli T¨®r, Maximusya, Ryan
    W Tenney (http://ryan.10e.us), Alexander M Beedie, fearphage
    (http://http/my.opera.com/fearphage/), Nathan Sepulveda, Victor, Matteo,
    Billy, stensi, Cord, Manish, T.J. Leahy, Riddler
    (http://www.frontierwebdev.com/), Rafa? Kukawski, FremyCompany, Matt
    Bradley, Tim de Koning, Luis Salazar (http://www.freaky-media.com/), Diogo
    Resende, Rival, Andrej Pavlovic, Garagoth, Le Torbi
    (http://www.letorbi.de/), Dino, Josep Sanz (http://www.ws3.es/), rem,
    Russell Walker (http://www.nbill.co.uk/), Jamie Beck
    (http://www.terabit.ca/), setcookie, Michael, YUI Library:
    http://developer.yahoo.com/yui/docs/YAHOO.util.DateLocale.html, Blues at
    http://hacks.bluesmoon.info/strftime/strftime.js, Ben
    (http://benblume.co.uk/), DtTvB
    (http://dt.in.th/2008-09-16.string-length-in-bytes.html), Andreas, William,
    meo, incidence, Cagri Ekin, Amirouche, Amir Habibi
    (http://www.residence-mixte.com/), Luke Smith (http://lucassmith.name),
    Kheang Hok Chin (http://www.distantia.ca/), Jay Klehr, Lorenzo Pisani,
    Tony, Yen-Wei Liu, Greenseed, mk.keck, Leslie Hoare, dude, booeyOH, Ben
    Bryan

    Licensed under the MIT (MIT-LICENSE.txt) license.

    Permission is hereby granted, free of charge, to any person obtaining a
    copy of this software and associated documentation files (the
    "Software"), to deal in the Software without restriction, including
    without limitation the rights to use, copy, modify, merge, publish,
    distribute, sublicense, and/or sell copies of the Software, and to
    permit persons to whom the Software is furnished to do so, subject to
    the following conditions:

    The above copyright notice and this permission notice shall be included
    in all copies or substantial portions of the Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
    OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
    MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
    IN NO EVENT SHALL KEVIN VAN ZONNEVELD BE LIABLE FOR ANY CLAIM, DAMAGES
    OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE,
    ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
    OTHER DEALINGS IN THE SOFTWARE.
    */function Z(){var e=/%%|%(\d+\$)?([-+\'#0 ]*)(\*\d+\$|\*|\d+)?(\.(\*\d+\$|\*|\d+))?([scboxXuideEfFgG])/g,t=arguments,n=0,a=t[n++],r=function(e,t,n,a){n||(n=" ");var r=e.length>=t?"":Array(1+t-e.length>>>0).join(n);return a?e+r:r+e},i=function(e,t,n,a,i,s){var o=a-e.length;return o>0&&(e=n||!i?r(e,a,s,n):e.slice(0,t.length)+r("",o,"0",!0)+e.slice(t.length)),e},s=function(e,t,n,a,s,o,u){var l=e>>>0;return e=(n=n&&l&&{2:"0b",8:"0",16:"0x"}[t]||"")+r(l.toString(t),o||0,"0",!1),i(e,n,a,s,u)},o=function(e,t,n,a,r,s){return null!=a&&(e=e.slice(0,a)),i(e,"",t,n,r,s)},u=function(e,a,u,l,d,c,f){var h,m,p,y,w;if("%%"==e)return"%";for(var C=!1,x="",M=!1,_=!1,E=" ",O=u.length,v=0;u&&v<O;v++)switch(u.charAt(v)){case" ":x=" ";break;case"+":x="+";break;case"-":C=!0;break;case"'":E=u.charAt(v+1);break;case"0":M=!0;break;case"#":_=!0}if((l=l?"*"==l?+t[n++]:"*"==l.charAt(0)?+t[l.slice(1,-1)]:+l:0)<0&&(l=-l,C=!0),!isFinite(l))throw new Error("sprintf: (minimum-)width must be finite");switch(c=c?"*"==c?+t[n++]:"*"==c.charAt(0)?+t[c.slice(1,-1)]:+c:"fFeE".indexOf(f)>-1?6:"d"==f?0:void 0,w=a?t[a.slice(0,-1)]:t[n++],f){case"s":return o(String(w),C,l,c,M,E);case"c":return o(String.fromCharCode(+w),C,l,c,M);case"b":return s(w,2,_,C,l,c,M);case"o":return s(w,8,_,C,l,c,M);case"x":return s(w,16,_,C,l,c,M);case"X":return s(w,16,_,C,l,c,M).toUpperCase();case"u":return s(w,10,_,C,l,c,M);case"i":case"d":return h=+w||0,w=(m=(h=Math.round(h-h%1))<0?"-":x)+r(String(Math.abs(h)),c,"0",!1),i(w,m,C,l,M);case"e":case"E":case"f":case"F":case"g":case"G":return m=(h=+w)<0?"-":x,p=["toExponential","toFixed","toPrecision"]["efg".indexOf(f.toLowerCase())],y=["toString","toUpperCase"]["eEfFgG".indexOf(f)%2],w=m+Math.abs(h)[p](c),i(w,m,C,l,M)[y]();default:return e}};return a.replace(e,u)}function X(e,t,n,a,r,i,s,o){this.year=e,this.month=t,this.day=n,this.hour=a,this.minute=r,this.second=i,this.millisecond=s,this.isLeapSecond=o}function J(e){return e%4==0&&e%100!=0||e%400==0}function H(e,t){this.julianDate=e,this.offset=t}V[7]=F/136,W[7]=8*F/17,p.fastSlerp=function(e,t,n,a){var r,i=p.dot(e,t);i>=0?r=1:(r=-1,i=-i);for(var s=i-1,o=1-n,u=n*n,l=o*o,d=7;d>=0;--d)q[d]=(V[d]*u-W[d])*s,L[d]=(V[d]*l-W[d])*s;var c=r*n*(1+q[0]*(1+q[1]*(1+q[2]*(1+q[3]*(1+q[4]*(1+q[5]*(1+q[6]*(1+q[7])))))))),f=o*(1+L[0]*(1+L[1]*(1+L[2]*(1+L[3]*(1+L[4]*(1+L[5]*(1+L[6]*(1+L[7])))))))),h=p.multiplyByScalar(e,f,z);return p.multiplyByScalar(t,c,a),p.add(h,a,a)},p.fastSquad=function(e,t,n,a,r,i){var s=p.fastSlerp(e,t,r,U),o=p.fastSlerp(n,a,r,b);return p.fastSlerp(s,o,2*r*(1-r),i)},p.equals=function(e,t){return e===t||r.defined(e)&&r.defined(t)&&e.x===t.x&&e.y===t.y&&e.z===t.z&&e.w===t.w},p.equalsEpsilon=function(e,t,n){return e===t||r.defined(e)&&r.defined(t)&&Math.abs(e.x-t.x)<=n&&Math.abs(e.y-t.y)<=n&&Math.abs(e.z-t.z)<=n&&Math.abs(e.w-t.w)<=n},p.ZERO=Object.freeze(new p(0,0,0,0)),p.IDENTITY=Object.freeze(new p(0,0,0,1)),p.prototype.clone=function(e){return p.clone(this,e)},p.prototype.equals=function(e){return p.equals(this,e)},p.prototype.equalsEpsilon=function(e,t){return p.equalsEpsilon(this,e,t)},p.prototype.toString=function(){return"("+this.x+", "+this.y+", "+this.z+", "+this.w+")"};var $=Object.freeze({SECONDS_PER_MILLISECOND:.001,SECONDS_PER_MINUTE:60,MINUTES_PER_HOUR:60,HOURS_PER_DAY:24,SECONDS_PER_HOUR:3600,MINUTES_PER_DAY:1440,SECONDS_PER_DAY:86400,DAYS_PER_JULIAN_CENTURY:36525,PICOSECOND:1e-9,MODIFIED_JULIAN_DATE_DIFFERENCE:2400000.5}),Q=Object.freeze({UTC:0,TAI:1}),K=new X,ee=[31,28,31,30,31,30,31,31,30,31,30,31];function te(e,t){return ye.compare(e.julianDate,t.julianDate)}var ne=new H;function ae(e){ne.julianDate=e;var t=ye.leapSeconds,n=j(t,ne,te);n<0&&(n=~n),n>=t.length&&(n=t.length-1);var a=t[n].offset;n>0&&(ye.secondsDifference(t[n].julianDate,e)>a&&(a=t[--n].offset));ye.addSeconds(e,a,e)}function re(e,t){ne.julianDate=e;var n=ye.leapSeconds,a=j(n,ne,te);if(a<0&&(a=~a),0===a)return ye.addSeconds(e,-n[0].offset,t);if(a>=n.length)return ye.addSeconds(e,-n[a-1].offset,t);var r=ye.secondsDifference(n[a].julianDate,e);return 0===r?ye.addSeconds(e,-n[a].offset,t):r<=1?void 0:ye.addSeconds(e,-n[--a].offset,t)}function ie(e,t,n){var a=t/$.SECONDS_PER_DAY|0;return e+=a,(t-=$.SECONDS_PER_DAY*a)<0&&(e--,t+=$.SECONDS_PER_DAY),n.dayNumber=e,n.secondsOfDay=t,n}function se(e,t,n,a,r,i,s){var o=(t-14)/12|0,u=e+4800+o,l=(1461*u/4|0)+(367*(t-2-12*o)/12|0)-(3*((u+100)/100|0)/4|0)+n-32075;(a-=12)<0&&(a+=24);var d=i+(a*$.SECONDS_PER_HOUR+r*$.SECONDS_PER_MINUTE+s*$.SECONDS_PER_MILLISECOND);return d>=43200&&(l-=1),[l,d]}var oe=/^(\d{4})$/,ue=/^(\d{4})-(\d{2})$/,le=/^(\d{4})-?(\d{3})$/,de=/^(\d{4})-?W(\d{2})-?(\d{1})?$/,ce=/^(\d{4})-?(\d{2})-?(\d{2})$/,fe=/([Z+\-])?(\d{2})?:?(\d{2})?$/,he=/^(\d{2})(\.\d+)?/.source+fe.source,me=/^(\d{2}):?(\d{2})(\.\d+)?/.source+fe.source,pe=/^(\d{2}):?(\d{2}):?(\d{2})(\.\d+)?/.source+fe.source;function ye(e,t,n){this.dayNumber=void 0,this.secondsOfDay=void 0,e=r.defaultValue(e,0),t=r.defaultValue(t,0),n=r.defaultValue(n,Q.UTC);var a=0|e;ie(a,t+=(e-a)*$.SECONDS_PER_DAY,this),n===Q.UTC&&ae(this)}ye.fromGregorianDate=function(e,t){var n=se(e.year,e.month,e.day,e.hour,e.minute,e.second,e.millisecond);return r.defined(t)?(ie(n[0],n[1],t),ae(t),t):new ye(n[0],n[1],Q.UTC)},ye.fromDate=function(e,t){var n=se(e.getUTCFullYear(),e.getUTCMonth()+1,e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds());return r.defined(t)?(ie(n[0],n[1],t),ae(t),t):new ye(n[0],n[1],Q.UTC)},ye.fromIso8601=function(e,t){var n,a,i,s,o=(e=e.replace(",",".")).split("T"),u=1,l=1,d=0,c=0,f=0,h=0,m=o[0],p=o[1];if(null!==(o=m.match(ce)))n=+o[1],u=+o[2],l=+o[3];else if(null!==(o=m.match(ue)))n=+o[1],u=+o[2];else if(null!==(o=m.match(oe)))n=+o[1];else{var y;if(null!==(o=m.match(le)))n=+o[1],y=+o[2],i=J(n);else if(null!==(o=m.match(de)))n=+o[1],y=7*+o[2]+(+o[3]||0)-new Date(Date.UTC(n,0,4)).getUTCDay()-3;(a=new Date(Date.UTC(n,0,1))).setUTCDate(y),u=a.getUTCMonth()+1,l=a.getUTCDate()}if(i=J(n),r.defined(p)){null!==(o=p.match(pe))?(d=+o[1],c=+o[2],f=+o[3],h=1e3*+(o[4]||0),s=5):null!==(o=p.match(me))?(d=+o[1],c=+o[2],f=60*+(o[3]||0),s=4):null!==(o=p.match(he))&&(d=+o[1],c=60*+(o[2]||0),s=3);var w=o[s],C=+o[s+1],x=+(o[s+2]||0);switch(w){case"+":d-=C,c-=x;break;case"-":d+=C,c+=x;break;case"Z":break;default:c+=new Date(Date.UTC(n,u-1,l,d,c)).getTimezoneOffset()}}var M=60===f;for(M&&f--;c>=60;)c-=60,d++;for(;d>=24;)d-=24,l++;for(a=i&&2===u?29:ee[u-1];l>a;)l-=a,++u>12&&(u-=12,n++),a=i&&2===u?29:ee[u-1];for(;c<0;)c+=60,d--;for(;d<0;)d+=24,l--;for(;l<1;)--u<1&&(u+=12,n--),l+=a=i&&2===u?29:ee[u-1];var _=se(n,u,l,d,c,f,h);return r.defined(t)?(ie(_[0],_[1],t),ae(t)):t=new ye(_[0],_[1],Q.UTC),M&&ye.addSeconds(t,1,t),t},ye.now=function(e){return ye.fromDate(new Date,e)};var we=new ye(0,0,Q.TAI);function Ce(e){if(e=r.defaultValue(e,r.defaultValue.EMPTY_OBJECT),this._dates=void 0,this._samples=void 0,this._dateColumn=-1,this._xPoleWanderRadiansColumn=-1,this._yPoleWanderRadiansColumn=-1,this._ut1MinusUtcSecondsColumn=-1,this._xCelestialPoleOffsetRadiansColumn=-1,this._yCelestialPoleOffsetRadiansColumn=-1,this._taiMinusUtcSecondsColumn=-1,this._columnCount=0,this._lastIndex=-1,this._downloadPromise=void 0,this._dataError=void 0,this._addNewLeapSeconds=r.defaultValue(e.addNewLeapSeconds,!0),r.defined(e.data))Me(this,e.data);else if(r.defined(e.url)){var t=l.Resource.createIfNeeded(e.url),n=this;this._downloadPromise=r.when(t.fetchJson(),(function(e){Me(n,e)}),(function(){n._dataError="An error occurred while retrieving the EOP data from the URL "+t.url+"."}))}else Me(this,{columnNames:["dateIso8601","modifiedJulianDateUtc","xPoleWanderRadians","yPoleWanderRadians","ut1MinusUtcSeconds","lengthOfDayCorrectionSeconds","xCelestialPoleOffsetRadians","yCelestialPoleOffsetRadians","taiMinusUtcSeconds"],samples:[]})}function xe(e,t){return ye.compare(e.julianDate,t)}function Me(e,t){if(r.defined(t.columnNames))if(r.defined(t.samples)){var n=t.columnNames.indexOf("modifiedJulianDateUtc"),a=t.columnNames.indexOf("xPoleWanderRadians"),i=t.columnNames.indexOf("yPoleWanderRadians"),s=t.columnNames.indexOf("ut1MinusUtcSeconds"),o=t.columnNames.indexOf("xCelestialPoleOffsetRadians"),u=t.columnNames.indexOf("yCelestialPoleOffsetRadians"),l=t.columnNames.indexOf("taiMinusUtcSeconds");if(n<0||a<0||i<0||s<0||o<0||u<0||l<0)e._dataError="Error in loaded EOP data: The columnNames property must include modifiedJulianDateUtc, xPoleWanderRadians, yPoleWanderRadians, ut1MinusUtcSeconds, xCelestialPoleOffsetRadians, yCelestialPoleOffsetRadians, and taiMinusUtcSeconds columns";else{var d,c=e._samples=t.samples,f=e._dates=[];e._dateColumn=n,e._xPoleWanderRadiansColumn=a,e._yPoleWanderRadiansColumn=i,e._ut1MinusUtcSecondsColumn=s,e._xCelestialPoleOffsetRadiansColumn=o,e._yCelestialPoleOffsetRadiansColumn=u,e._taiMinusUtcSecondsColumn=l,e._columnCount=t.columnNames.length,e._lastIndex=void 0;for(var h=e._addNewLeapSeconds,m=0,p=c.length;m<p;m+=e._columnCount){var y=c[m+n],w=c[m+l],C=new ye(y+$.MODIFIED_JULIAN_DATE_DIFFERENCE,w,Q.TAI);if(f.push(C),h){if(w!==d&&r.defined(d)){var x=ye.leapSeconds,M=j(x,C,xe);if(M<0){var _=new H(C,w);x.splice(~M,0,_)}}d=w}}}}else e._dataError="Error in loaded EOP data: The samples property is required.";else e._dataError="Error in loaded EOP data: The columnNames property is required."}function _e(e,t,n,a,r){var i=n*a;r.xPoleWander=t[i+e._xPoleWanderRadiansColumn],r.yPoleWander=t[i+e._yPoleWanderRadiansColumn],r.xPoleOffset=t[i+e._xCelestialPoleOffsetRadiansColumn],r.yPoleOffset=t[i+e._yCelestialPoleOffsetRadiansColumn],r.ut1MinusUtc=t[i+e._ut1MinusUtcSecondsColumn]}function Ee(e,t,n){return t+e*(n-t)}function Oe(e,t,n,a,r,i,s){var o=e._columnCount;if(i>t.length-1)return s.xPoleWander=0,s.yPoleWander=0,s.xPoleOffset=0,s.yPoleOffset=0,s.ut1MinusUtc=0,s;var u=t[r],l=t[i];if(u.equals(l)||a.equals(u))return _e(e,n,r,o,s),s;if(a.equals(l))return _e(e,n,i,o,s),s;var d=ye.secondsDifference(a,u)/ye.secondsDifference(l,u),c=r*o,f=i*o,h=n[c+e._ut1MinusUtcSecondsColumn],m=n[f+e._ut1MinusUtcSecondsColumn],p=m-h;if(p>.5||p<-.5){var y=n[c+e._taiMinusUtcSecondsColumn],w=n[f+e._taiMinusUtcSecondsColumn];y!==w&&(l.equals(a)?h=m:m-=w-y)}return s.xPoleWander=Ee(d,n[c+e._xPoleWanderRadiansColumn],n[f+e._xPoleWanderRadiansColumn]),s.yPoleWander=Ee(d,n[c+e._yPoleWanderRadiansColumn],n[f+e._yPoleWanderRadiansColumn]),s.xPoleOffset=Ee(d,n[c+e._xCelestialPoleOffsetRadiansColumn],n[f+e._xCelestialPoleOffsetRadiansColumn]),s.yPoleOffset=Ee(d,n[c+e._yCelestialPoleOffsetRadiansColumn],n[f+e._yCelestialPoleOffsetRadiansColumn]),s.ut1MinusUtc=Ee(d,h,m),s}function ve(e,t,n){this.heading=r.defaultValue(e,0),this.pitch=r.defaultValue(t,0),this.roll=r.defaultValue(n,0)}function Se(e,t,n){this.x=e,this.y=t,this.s=n}function Te(e){e=r.defaultValue(e,r.defaultValue.EMPTY_OBJECT),this._xysFileUrlTemplate=l.Resource.createIfNeeded(e.xysFileUrlTemplate),this._interpolationOrder=r.defaultValue(e.interpolationOrder,9),this._sampleZeroJulianEphemerisDate=r.defaultValue(e.sampleZeroJulianEphemerisDate,2442396.5),this._sampleZeroDateTT=new ye(this._sampleZeroJulianEphemerisDate,0,Q.TAI),this._stepSizeDays=r.defaultValue(e.stepSizeDays,1),this._samplesPerXysFile=r.defaultValue(e.samplesPerXysFile,1e3),this._totalSamples=r.defaultValue(e.totalSamples,27426),this._samples=new Array(3*this._totalSamples),this._chunkDownloadsInProgress=[];for(var t=this._interpolationOrder,n=this._denominators=new Array(t+1),a=this._xTable=new Array(t+1),i=Math.pow(this._stepSizeDays,t),s=0;s<=t;++s){n[s]=i,a[s]=s*this._stepSizeDays;for(var o=0;o<=t;++o)o!==s&&(n[s]*=s-o);n[s]=1/n[s]}this._work=new Array(t+1),this._coef=new Array(t+1)}ye.toGregorianDate=function(e,t){var n=!1,a=re(e,we);r.defined(a)||(ye.addSeconds(e,-1,we),a=re(we,we),n=!0);var i=a.dayNumber,s=a.secondsOfDay;s>=43200&&(i+=1);var o=i+68569|0,u=4*o/146097|0,l=4e3*((o=o-((146097*u+3)/4|0)|0)+1)/1461001|0,d=80*(o=o-(1461*l/4|0)+31|0)/2447|0,c=o-(2447*d/80|0)|0,f=d+2-12*(o=d/11|0)|0,h=100*(u-49)+l+o|0,m=s/$.SECONDS_PER_HOUR|0,p=s-m*$.SECONDS_PER_HOUR,y=p/$.SECONDS_PER_MINUTE|0,w=0|(p-=y*$.SECONDS_PER_MINUTE),C=(p-w)/$.SECONDS_PER_MILLISECOND;return(m+=12)>23&&(m-=24),n&&(w+=1),r.defined(t)?(t.year=h,t.month=f,t.day=c,t.hour=m,t.minute=y,t.second=w,t.millisecond=C,t.isLeapSecond=n,t):new X(h,f,c,m,y,w,C,n)},ye.toDate=function(e){var t=ye.toGregorianDate(e,K),n=t.second;return t.isLeapSecond&&(n-=1),new Date(Date.UTC(t.year,t.month-1,t.day,t.hour,t.minute,n,t.millisecond))},ye.toIso8601=function(e,t){var n=ye.toGregorianDate(e,K),a=n.year,i=n.month,s=n.day,o=n.hour,u=n.minute,l=n.second,d=n.millisecond;return 1e4===a&&1===i&&1===s&&0===o&&0===u&&0===l&&0===d&&(a=9999,i=12,s=31,o=24),r.defined(t)||0===d?r.defined(t)&&0!==t?Z("%04d-%02d-%02dT%02d:%02d:%02d.%sZ",a,i,s,o,u,l,(.01*d).toFixed(t).replace(".","").slice(0,t)):Z("%04d-%02d-%02dT%02d:%02d:%02dZ",a,i,s,o,u,l):Z("%04d-%02d-%02dT%02d:%02d:%02d.%sZ",a,i,s,o,u,l,(.01*d).toString().replace(".",""))},ye.clone=function(e,t){if(r.defined(e))return r.defined(t)?(t.dayNumber=e.dayNumber,t.secondsOfDay=e.secondsOfDay,t):new ye(e.dayNumber,e.secondsOfDay,Q.TAI)},ye.compare=function(e,t){var n=e.dayNumber-t.dayNumber;return 0!==n?n:e.secondsOfDay-t.secondsOfDay},ye.equals=function(e,t){return e===t||r.defined(e)&&r.defined(t)&&e.dayNumber===t.dayNumber&&e.secondsOfDay===t.secondsOfDay},ye.equalsEpsilon=function(e,t,n){return e===t||r.defined(e)&&r.defined(t)&&Math.abs(ye.secondsDifference(e,t))<=n},ye.totalDays=function(e){return e.dayNumber+e.secondsOfDay/$.SECONDS_PER_DAY},ye.secondsDifference=function(e,t){return(e.dayNumber-t.dayNumber)*$.SECONDS_PER_DAY+(e.secondsOfDay-t.secondsOfDay)},ye.daysDifference=function(e,t){return e.dayNumber-t.dayNumber+(e.secondsOfDay-t.secondsOfDay)/$.SECONDS_PER_DAY},ye.computeTaiMinusUtc=function(e){ne.julianDate=e;var t=ye.leapSeconds,n=j(t,ne,te);return n<0&&(n=~n,--n<0&&(n=0)),t[n].offset},ye.addSeconds=function(e,t,n){return ie(e.dayNumber,e.secondsOfDay+t,n)},ye.addMinutes=function(e,t,n){var a=e.secondsOfDay+t*$.SECONDS_PER_MINUTE;return ie(e.dayNumber,a,n)},ye.addHours=function(e,t,n){var a=e.secondsOfDay+t*$.SECONDS_PER_HOUR;return ie(e.dayNumber,a,n)},ye.addDays=function(e,t,n){return ie(e.dayNumber+t,e.secondsOfDay,n)},ye.lessThan=function(e,t){return ye.compare(e,t)<0},ye.lessThanOrEquals=function(e,t){return ye.compare(e,t)<=0},ye.greaterThan=function(e,t){return ye.compare(e,t)>0},ye.greaterThanOrEquals=function(e,t){return ye.compare(e,t)>=0},ye.prototype.clone=function(e){return ye.clone(this,e)},ye.prototype.equals=function(e){return ye.equals(this,e)},ye.prototype.equalsEpsilon=function(e,t){return ye.equalsEpsilon(this,e,t)},ye.prototype.toString=function(){return ye.toIso8601(this)},ye.leapSeconds=[new H(new ye(2441317,43210,Q.TAI),10),new H(new ye(2441499,43211,Q.TAI),11),new H(new ye(2441683,43212,Q.TAI),12),new H(new ye(2442048,43213,Q.TAI),13),new H(new ye(2442413,43214,Q.TAI),14),new H(new ye(2442778,43215,Q.TAI),15),new H(new ye(2443144,43216,Q.TAI),16),new H(new ye(2443509,43217,Q.TAI),17),new H(new ye(2443874,43218,Q.TAI),18),new H(new ye(2444239,43219,Q.TAI),19),new H(new ye(2444786,43220,Q.TAI),20),new H(new ye(2445151,43221,Q.TAI),21),new H(new ye(2445516,43222,Q.TAI),22),new H(new ye(2446247,43223,Q.TAI),23),new H(new ye(2447161,43224,Q.TAI),24),new H(new ye(2447892,43225,Q.TAI),25),new H(new ye(2448257,43226,Q.TAI),26),new H(new ye(2448804,43227,Q.TAI),27),new H(new ye(2449169,43228,Q.TAI),28),new H(new ye(2449534,43229,Q.TAI),29),new H(new ye(2450083,43230,Q.TAI),30),new H(new ye(2450630,43231,Q.TAI),31),new H(new ye(2451179,43232,Q.TAI),32),new H(new ye(2453736,43233,Q.TAI),33),new H(new ye(2454832,43234,Q.TAI),34),new H(new ye(2456109,43235,Q.TAI),35),new H(new ye(2457204,43236,Q.TAI),36),new H(new ye(2457754,43237,Q.TAI),37)],Ce.NONE=Object.freeze({getPromiseToLoad:function(){return r.when()},compute:function(e,t){return r.defined(t)?(t.xPoleWander=0,t.yPoleWander=0,t.xPoleOffset=0,t.yPoleOffset=0,t.ut1MinusUtc=0):t=new G(0,0,0,0,0),t}}),Ce.prototype.getPromiseToLoad=function(){return r.when(this._downloadPromise)},Ce.prototype.compute=function(e,t){if(r.defined(this._samples)){if(r.defined(t)||(t=new G(0,0,0,0,0)),0===this._samples.length)return t.xPoleWander=0,t.yPoleWander=0,t.xPoleOffset=0,t.yPoleOffset=0,t.ut1MinusUtc=0,t;var n=this._dates,a=this._lastIndex,i=0,s=0;if(r.defined(a)){var o=n[a],u=n[a+1],l=ye.lessThanOrEquals(o,e),c=!r.defined(u),f=c||ye.greaterThanOrEquals(u,e);if(l&&f)return i=a,!c&&u.equals(e)&&++i,s=i+1,Oe(this,n,this._samples,e,i,s,t),t}var h=j(n,e,ye.compare,this._dateColumn);return h>=0?(h<n.length-1&&n[h+1].equals(e)&&++h,i=h,s=h):(i=(s=~h)-1)<0&&(i=0),this._lastIndex=i,Oe(this,n,this._samples,e,i,s,t),t}if(r.defined(this._dataError))throw new d.RuntimeError(this._dataError)},ve.fromQuaternion=function(e,t){r.defined(t)||(t=new ve);var n=2*(e.w*e.y-e.z*e.x),a=1-2*(e.x*e.x+e.y*e.y),i=2*(e.w*e.x+e.y*e.z),o=1-2*(e.y*e.y+e.z*e.z),u=2*(e.w*e.z+e.x*e.y);return t.heading=-Math.atan2(u,o),t.roll=Math.atan2(i,a),t.pitch=-s.CesiumMath.asinClamped(n),t},ve.fromDegrees=function(e,t,n,a){return r.defined(a)||(a=new ve),a.heading=e*s.CesiumMath.RADIANS_PER_DEGREE,a.pitch=t*s.CesiumMath.RADIANS_PER_DEGREE,a.roll=n*s.CesiumMath.RADIANS_PER_DEGREE,a},ve.clone=function(e,t){if(r.defined(e))return r.defined(t)?(t.heading=e.heading,t.pitch=e.pitch,t.roll=e.roll,t):new ve(e.heading,e.pitch,e.roll)},ve.equals=function(e,t){return e===t||r.defined(e)&&r.defined(t)&&e.heading===t.heading&&e.pitch===t.pitch&&e.roll===t.roll},ve.equalsEpsilon=function(e,t,n,a){return e===t||r.defined(e)&&r.defined(t)&&s.CesiumMath.equalsEpsilon(e.heading,t.heading,n,a)&&s.CesiumMath.equalsEpsilon(e.pitch,t.pitch,n,a)&&s.CesiumMath.equalsEpsilon(e.roll,t.roll,n,a)},ve.prototype.clone=function(e){return ve.clone(this,e)},ve.prototype.equals=function(e){return ve.equals(this,e)},ve.prototype.equalsEpsilon=function(e,t,n){return ve.equalsEpsilon(this,e,t,n)},ve.prototype.toString=function(){return"("+this.heading+", "+this.pitch+", "+this.roll+")"};var ge=new ye(0,0,Q.TAI);function De(e,t,n){var a=ge;return a.dayNumber=t,a.secondsOfDay=n,ye.daysDifference(a,e._sampleZeroDateTT)}function Pe(e,t){if(e._chunkDownloadsInProgress[t])return e._chunkDownloadsInProgress[t];var n,a=r.when.defer();e._chunkDownloadsInProgress[t]=a;var i=e._xysFileUrlTemplate;return n=r.defined(i)?i.getDerivedResource({templateValues:{0:t}}):new l.Resource({url:l.buildModuleUrl("Assets/IAU2006_XYS/IAU2006_XYS_"+t+".json")}),r.when(n.fetchJson(),(function(n){e._chunkDownloadsInProgress[t]=!1;for(var r=e._samples,i=n.samples,s=t*e._samplesPerXysFile*3,o=0,u=i.length;o<u;++o)r[s+o]=i[o];a.resolve()})),a.promise}Te.prototype.preload=function(e,t,n,a){var i=De(this,e,t),s=De(this,n,a),o=i/this._stepSizeDays-this._interpolationOrder/2|0;o<0&&(o=0);var u=s/this._stepSizeDays-this._interpolationOrder/2|0+this._interpolationOrder;u>=this._totalSamples&&(u=this._totalSamples-1);for(var l=o/this._samplesPerXysFile|0,d=u/this._samplesPerXysFile|0,c=[],f=l;f<=d;++f)c.push(Pe(this,f));return r.when.all(c)},Te.prototype.computeXysRadians=function(e,t,n){var a=De(this,e,t);if(!(a<0)){var i=a/this._stepSizeDays|0;if(!(i>=this._totalSamples)){var s=this._interpolationOrder,o=i-(s/2|0);o<0&&(o=0);var u=o+s;u>=this._totalSamples&&(o=(u=this._totalSamples-1)-s)<0&&(o=0);var l=!1,d=this._samples;if(r.defined(d[3*o])||(Pe(this,o/this._samplesPerXysFile|0),l=!0),r.defined(d[3*u])||(Pe(this,u/this._samplesPerXysFile|0),l=!0),!l){r.defined(n)?(n.x=0,n.y=0,n.s=0):n=new Se(0,0,0);var c,f,h=a-o*this._stepSizeDays,m=this._work,p=this._denominators,y=this._coef,w=this._xTable;for(c=0;c<=s;++c)m[c]=h-w[c];for(c=0;c<=s;++c){for(y[c]=1,f=0;f<=s;++f)f!==c&&(y[c]*=m[f]);y[c]*=p[c];var C=3*(o+c);n.x+=y[c]*d[C++],n.y+=y[c]*d[C++],n.s+=y[c]*d[C]}return n}}}};var Ne={},Ie={up:{south:"east",north:"west",west:"south",east:"north"},down:{south:"west",north:"east",west:"north",east:"south"},south:{up:"west",down:"east",west:"down",east:"up"},north:{up:"east",down:"west",west:"up",east:"down"},west:{up:"north",down:"south",north:"down",south:"up"},east:{up:"south",down:"north",north:"up",south:"down"}},Re={north:[-1,0,0],east:[0,1,0],up:[0,0,1],south:[1,0,0],west:[0,-1,0],down:[0,0,-1]},Ae={},Ue={east:new n.Cartesian3,north:new n.Cartesian3,up:new n.Cartesian3,west:new n.Cartesian3,south:new n.Cartesian3,down:new n.Cartesian3},be=new n.Cartesian3,ze=new n.Cartesian3,Fe=new n.Cartesian3;Ne.localFrameToFixedFrameGenerator=function(e,t){if(!Ie.hasOwnProperty(e)||!Ie[e].hasOwnProperty(t))throw new a.DeveloperError("firstAxis and secondAxis must be east, north, up, west, south or down.");var u,l=Ie[e][t],d=e+t;return r.defined(Ae[d])?u=Ae[d]:(u=function(a,u,d){if(r.defined(d)||(d=new i.Matrix4),n.Cartesian3.equalsEpsilon(a,n.Cartesian3.ZERO,s.CesiumMath.EPSILON14))n.Cartesian3.unpack(Re[e],0,be),n.Cartesian3.unpack(Re[t],0,ze),n.Cartesian3.unpack(Re[l],0,Fe);else if(s.CesiumMath.equalsEpsilon(a.x,0,s.CesiumMath.EPSILON14)&&s.CesiumMath.equalsEpsilon(a.y,0,s.CesiumMath.EPSILON14)){var c=s.CesiumMath.sign(a.z);n.Cartesian3.unpack(Re[e],0,be),"east"!==e&&"west"!==e&&n.Cartesian3.multiplyByScalar(be,c,be),n.Cartesian3.unpack(Re[t],0,ze),"east"!==t&&"west"!==t&&n.Cartesian3.multiplyByScalar(ze,c,ze),n.Cartesian3.unpack(Re[l],0,Fe),"east"!==l&&"west"!==l&&n.Cartesian3.multiplyByScalar(Fe,c,Fe)}else{(u=r.defaultValue(u,o.Ellipsoid.WGS84)).geodeticSurfaceNormal(a,Ue.up);var f=Ue.up,h=Ue.east;h.x=-a.y,h.y=a.x,h.z=0,n.Cartesian3.normalize(h,Ue.east),n.Cartesian3.cross(f,h,Ue.north),n.Cartesian3.multiplyByScalar(Ue.up,-1,Ue.down),n.Cartesian3.multiplyByScalar(Ue.east,-1,Ue.west),n.Cartesian3.multiplyByScalar(Ue.north,-1,Ue.south),be=Ue[e],ze=Ue[t],Fe=Ue[l]}return d[0]=be.x,d[1]=be.y,d[2]=be.z,d[3]=0,d[4]=ze.x,d[5]=ze.y,d[6]=ze.z,d[7]=0,d[8]=Fe.x,d[9]=Fe.y,d[10]=Fe.z,d[11]=0,d[12]=a.x,d[13]=a.y,d[14]=a.z,d[15]=1,d},Ae[d]=u),u},Ne.eastNorthUpToFixedFrame=Ne.localFrameToFixedFrameGenerator("east","north"),Ne.northEastDownToFixedFrame=Ne.localFrameToFixedFrameGenerator("north","east"),Ne.northUpEastToFixedFrame=Ne.localFrameToFixedFrameGenerator("north","up"),Ne.northWestUpToFixedFrame=Ne.localFrameToFixedFrameGenerator("north","west");var Ve=new p,We=new n.Cartesian3(1,1,1),qe=new i.Matrix4;Ne.headingPitchRollToFixedFrame=function(e,t,a,s,o){s=r.defaultValue(s,Ne.eastNorthUpToFixedFrame);var u=p.fromHeadingPitchRoll(t,Ve),l=i.Matrix4.fromTranslationQuaternionRotationScale(n.Cartesian3.ZERO,u,We,qe);return o=s(e,a,o),i.Matrix4.multiply(o,l,o)};var Le=new i.Matrix4,ke=new i.Matrix3;Ne.headingPitchRollQuaternion=function(e,t,n,a,r){var s=Ne.headingPitchRollToFixedFrame(e,t,n,a,Le),o=i.Matrix4.getMatrix3(s,ke);return p.fromRotationMatrix(o,r)};var Ye=new n.Cartesian3(1,1,1),Be=new n.Cartesian3,je=new i.Matrix4,Ge=new i.Matrix4,Ze=new i.Matrix3,Xe=new p;Ne.fixedFrameToHeadingPitchRoll=function(e,t,a,s){t=r.defaultValue(t,o.Ellipsoid.WGS84),a=r.defaultValue(a,Ne.eastNorthUpToFixedFrame),r.defined(s)||(s=new ve);var u=i.Matrix4.getTranslation(e,Be);if(n.Cartesian3.equals(u,n.Cartesian3.ZERO))return s.heading=0,s.pitch=0,s.roll=0,s;var l=i.Matrix4.inverseTransformation(a(u,t,je),je),d=i.Matrix4.setScale(e,Ye,Ge);d=i.Matrix4.setTranslation(d,n.Cartesian3.ZERO,d),l=i.Matrix4.multiply(l,d,l);var c=p.fromRotationMatrix(i.Matrix4.getMatrix3(l,Ze),Xe);return c=p.normalize(c,c),ve.fromQuaternion(c,s)};var Je=s.CesiumMath.TWO_PI/86400,He=new ye;Ne.computeTemeToPseudoFixedMatrix=function(e,t){var n,a=(He=ye.addSeconds(e,-ye.computeTaiMinusUtc(e),He)).dayNumber,o=He.secondsOfDay,u=a-2451545,l=(24110.54841+(n=o>=43200?(u+.5)/$.DAYS_PER_JULIAN_CENTURY:(u-.5)/$.DAYS_PER_JULIAN_CENTURY)*(8640184.812866+n*(.093104+-62e-7*n)))*Je%s.CesiumMath.TWO_PI+(72921158553e-15+11772758384668e-32*(a-2451545.5))*((o+.5*$.SECONDS_PER_DAY)%$.SECONDS_PER_DAY),d=Math.cos(l),c=Math.sin(l);return r.defined(t)?(t[0]=d,t[1]=-c,t[2]=0,t[3]=c,t[4]=d,t[5]=0,t[6]=0,t[7]=0,t[8]=1,t):new i.Matrix3(d,c,0,-c,d,0,0,0,1)},Ne.iau2006XysData=new Te,Ne.earthOrientationParameters=Ce.NONE;var $e=32.184;Ne.preloadIcrfFixed=function(e){var t=e.start.dayNumber,n=e.start.secondsOfDay+$e,a=e.stop.dayNumber,i=e.stop.secondsOfDay+$e,s=Ne.iau2006XysData.preload(t,n,a,i),o=Ne.earthOrientationParameters.getPromiseToLoad();return r.when.all([s,o])},Ne.computeIcrfToFixedMatrix=function(e,t){r.defined(t)||(t=new i.Matrix3);var n=Ne.computeFixedToIcrfMatrix(e,t);if(r.defined(n))return i.Matrix3.transpose(n,t)};var Qe=new Se(0,0,0),Ke=new G(0,0,0,0,0,0),et=new i.Matrix3,tt=new i.Matrix3;Ne.computeFixedToIcrfMatrix=function(e,t){r.defined(t)||(t=new i.Matrix3);var n=Ne.earthOrientationParameters.compute(e,Ke);if(r.defined(n)){var a=e.dayNumber,o=e.secondsOfDay+$e,u=Ne.iau2006XysData.computeXysRadians(a,o,Qe);if(r.defined(u)){var l=u.x+n.xPoleOffset,d=u.y+n.yPoleOffset,c=1/(1+Math.sqrt(1-l*l-d*d)),f=et;f[0]=1-c*l*l,f[3]=-c*l*d,f[6]=l,f[1]=-c*l*d,f[4]=1-c*d*d,f[7]=d,f[2]=-l,f[5]=-d,f[8]=1-c*(l*l+d*d);var h=i.Matrix3.fromRotationZ(-u.s,tt),m=i.Matrix3.multiply(f,h,et),p=e.dayNumber-2451545,y=(e.secondsOfDay-ye.computeTaiMinusUtc(e)+n.ut1MinusUtc)/$.SECONDS_PER_DAY,w=.779057273264+y+.00273781191135448*(p+y);w=w%1*s.CesiumMath.TWO_PI;var C=i.Matrix3.fromRotationZ(w,tt),x=i.Matrix3.multiply(m,C,et),M=Math.cos(n.xPoleWander),_=Math.cos(n.yPoleWander),E=Math.sin(n.xPoleWander),O=Math.sin(n.yPoleWander),v=a-2451545+o/$.SECONDS_PER_DAY,S=-47e-6*(v/=36525)*s.CesiumMath.RADIANS_PER_DEGREE/3600,T=Math.cos(S),g=Math.sin(S),D=tt;return D[0]=M*T,D[1]=M*g,D[2]=E,D[3]=-_*g+O*E*T,D[4]=_*T+O*E*g,D[5]=-O*M,D[6]=-O*g-_*E*T,D[7]=O*T-_*E*g,D[8]=_*M,i.Matrix3.multiply(x,D,t)}}};var nt=new u.Cartesian4;Ne.pointToWindowCoordinates=function(e,t,n,a){return(a=Ne.pointToGLWindowCoordinates(e,t,n,a)).y=2*t[5]-a.y,a},Ne.pointToGLWindowCoordinates=function(e,n,a,s){r.defined(s)||(s=new t.Cartesian2);var o=nt;return i.Matrix4.multiplyByVector(e,u.Cartesian4.fromElements(a.x,a.y,a.z,1,o),o),u.Cartesian4.multiplyByScalar(o,1/o.w,o),i.Matrix4.multiplyByVector(n,o,o),t.Cartesian2.fromCartesian4(o,s)};var at=new n.Cartesian3,rt=new n.Cartesian3,it=new n.Cartesian3;Ne.rotationMatrixFromPositionVelocity=function(e,t,a,u){var l=r.defaultValue(a,o.Ellipsoid.WGS84).geodeticSurfaceNormal(e,at),d=n.Cartesian3.cross(t,l,rt);n.Cartesian3.equalsEpsilon(d,n.Cartesian3.ZERO,s.CesiumMath.EPSILON6)&&(d=n.Cartesian3.clone(n.Cartesian3.UNIT_X,d));var c=n.Cartesian3.cross(d,t,it);return n.Cartesian3.normalize(c,c),n.Cartesian3.cross(t,c,d),n.Cartesian3.negate(d,d),n.Cartesian3.normalize(d,d),r.defined(u)||(u=new i.Matrix3),u[0]=t.x,u[1]=t.y,u[2]=t.z,u[3]=d.x,u[4]=d.y,u[5]=d.z,u[6]=c.x,u[7]=c.y,u[8]=c.z,u};var st=new i.Matrix4(0,0,1,0,1,0,0,0,0,1,0,0,0,0,0,1),ot=new n.Cartographic,ut=new n.Cartesian3,lt=new n.Cartesian3,dt=new i.Matrix3,ct=new i.Matrix4,ft=new i.Matrix4;function ht(e){e=r.defaultValue(e,r.defaultValue.EMPTY_OBJECT),this.attributes=e.attributes,this.indices=e.indices,this.primitiveType=r.defaultValue(e.primitiveType,i.PrimitiveType.TRIANGLES),this.boundingSphere=e.boundingSphere,this.geometryType=r.defaultValue(e.geometryType,c.NONE),this.boundingSphereCV=e.boundingSphereCV,this.offsetAttribute=e.offsetAttribute}Ne.basisTo2D=function(e,t,a){var r=i.Matrix4.getTranslation(t,lt),s=e.ellipsoid,o=s.cartesianToCartographic(r,ot),u=e.project(o,ut);n.Cartesian3.fromElements(u.z,u.x,u.y,u);var l=Ne.eastNorthUpToFixedFrame(r,s,ct),d=i.Matrix4.inverseTransformation(l,ft),c=i.Matrix4.getMatrix3(t,dt),f=i.Matrix4.multiplyByMatrix3(d,c,a);return i.Matrix4.multiply(st,f,a),i.Matrix4.setTranslation(a,u,a),a},Ne.wgs84To2DModelMatrix=function(e,t,a){var r=e.ellipsoid,s=Ne.eastNorthUpToFixedFrame(t,r,ct),o=i.Matrix4.inverseTransformation(s,ft),u=r.cartesianToCartographic(t,ot),l=e.project(u,ut);n.Cartesian3.fromElements(l.z,l.x,l.y,l);var d=i.Matrix4.fromTranslation(l,ct);return i.Matrix4.multiply(st,o,a),i.Matrix4.multiply(d,a,a),a},Ne.buildUp=function(e,t){var a=t.clone(),r=e.clone();r=n.Cartesian3.normalize(r,r),Math.abs(n.Cartesian3.dot(r,a))>=1&&(r=Math.abs(n.Cartesian3.dot(a,n.Cartesian3.UNIT_Y))<1?n.Cartesian3.clone(n.Cartesian3.UNIT_Y,r):n.Cartesian3.clone(n.Cartesian3.UNIT_Z,r));var i=new n.Cartesian3;return n.Cartesian3.cross(r,a,i),i=n.Cartesian3.normalize(i,i),n.Cartesian3.cross(a,i,r),r=n.Cartesian3.normalize(r,r)},Ne.getHeading=function(e,t){var n;return n=s.CesiumMath.equalsEpsilon(Math.abs(e.z),1,s.CesiumMath.EPSILON3)?Math.atan2(t.y,t.x)-s.CesiumMath.PI_OVER_TWO:Math.atan2(e.y,e.x)-s.CesiumMath.PI_OVER_TWO,s.CesiumMath.TWO_PI-s.CesiumMath.zeroToTwoPi(n)},Ne.convertToColumbusCartesian=function(e){var t=new l.GeographicProjection,a=t.ellipsoid,r=new n.Cartesian3,i=new n.Cartographic;return a.cartesianToCartographic(e,i),t.project(i,r),n.Cartesian3.fromElements(r.z,r.x,r.y)},Ne.convertTo3DCartesian=function(e){var t=new l.GeographicProjection,a=t.ellipsoid,r=new n.Cartesian3,i=new n.Cartographic;return r=n.Cartesian3.fromElements(e.y,e.z,e.x),t.unproject(r,i),a.cartographicToCartesian(i,r)},ht.computeNumberOfVertices=function(e){var t=-1;for(var n in e.attributes)if(e.attributes.hasOwnProperty(n)&&r.defined(e.attributes[n])&&r.defined(e.attributes[n].values)){var a=e.attributes[n];if(a.isInstanceAttribute)continue;t=a.values.length/a.componentsPerAttribute}return t};var mt=new n.Cartographic,pt=new n.Cartesian3,yt=new i.Matrix4,wt=[new n.Cartographic,new n.Cartographic,new n.Cartographic],Ct=[new t.Cartesian2,new t.Cartesian2,new t.Cartesian2],xt=[new t.Cartesian2,new t.Cartesian2,new t.Cartesian2],Mt=new n.Cartesian3,_t=new p,Et=new i.Matrix4,Ot=new f;ht._textureCoordinateRotationPoints=function(e,a,r,s){var u,l=o.Rectangle.center(s,mt),d=n.Cartographic.toCartesian(l,r,pt),c=Ne.eastNorthUpToFixedFrame(d,r,yt),h=i.Matrix4.inverse(c,yt),m=Ct,y=wt;y[0].longitude=s.west,y[0].latitude=s.south,y[1].longitude=s.west,y[1].latitude=s.north,y[2].longitude=s.east,y[2].latitude=s.south;var w=Mt;for(u=0;u<3;u++)n.Cartographic.toCartesian(y[u],r,w),w=i.Matrix4.multiplyByPointAsVector(h,w,w),m[u].x=w.x,m[u].y=w.y;var C=p.fromAxisAngle(n.Cartesian3.UNIT_Z,-a,_t),x=i.Matrix3.fromQuaternion(C,Et),M=e.length,_=Number.POSITIVE_INFINITY,E=Number.POSITIVE_INFINITY,O=Number.NEGATIVE_INFINITY,v=Number.NEGATIVE_INFINITY;for(u=0;u<M;u++)w=i.Matrix4.multiplyByPointAsVector(h,e[u],w),w=i.Matrix3.multiplyByVector(x,w,w),_=Math.min(_,w.x),E=Math.min(E,w.y),O=Math.max(O,w.x),v=Math.max(v,w.y);var S=f.fromRotation(a,Ot),T=xt;T[0].x=_,T[0].y=E,T[1].x=_,T[1].y=v,T[2].x=O,T[2].y=E;var g=m[0],D=m[2].x-g.x,P=m[1].y-g.y;for(u=0;u<3;u++){var N=T[u];f.multiplyByVector(S,N,N),N.x=(N.x-g.x)/D,N.y=(N.y-g.y)/P}var I=T[0],R=T[1],A=T[2],U=new Array(6);return t.Cartesian2.pack(I,U),t.Cartesian2.pack(R,U,2),t.Cartesian2.pack(A,U,4),U},e.Geometry=ht,e.GeometryAttribute=function(e){e=r.defaultValue(e,r.defaultValue.EMPTY_OBJECT),this.componentDatatype=e.componentDatatype,this.componentsPerAttribute=e.componentsPerAttribute,this.normalize=r.defaultValue(e.normalize,!1),this.values=e.values},e.GeometryType=c,e.Matrix2=f,e.Quaternion=p,e.Transforms=Ne}));
