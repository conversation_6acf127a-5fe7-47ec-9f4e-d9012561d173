<template>
  <div class="main-content">
    <div class="statistic-tab">
      <header class="project-header">项目数量统计</header>
      <div class="project-charts">
        <div
          class="count-charts"
          ref="countChartRef"
        />
      </div>
    </div>
    <div class="statistic-tab">
      <header class="project-header">项目面积统计</header>
      <div class="project-charts">
        <div
          class="count-charts"
          ref="areaChartRef"
        />
      </div>
    </div>
  </div>
</template>

<script setup name="RelStatistic">
/**
 * CHN：储备土地统计数据
 */
import * as echarts from 'echarts';
import { getReserveLandStatistics } from "@/api/patrol/reserveProject.js"

const countChartRef = ref("countChartRef")
const areaChartRef = ref("areaChartRef")
const projectCountData = ref({})
const projectAreaData = ref({})
/**
 * 生成项目数量统计图
 */
getReserveLandStatistics().then((res) => {
  // console.log("统计数据：",res)
  const years = res.data.map(item => item.nf);
  const counts = res.data.map(item => item.num);
  const area = res.data.map(item => (item.dkzmj * 1e-4).toFixed(4));
  projectCountData.value = {
    years,counts
  }
  projectAreaData.value = {
    years,area
  }
})

const isGenerateCharts = computed(()=>{
  return countChartRef.value && projectCountData.value
})

watch(isGenerateCharts,(value)=>{
  if(value){
    generateCountCharts()
    generateAreaCharts()
  }
})

const generateCountCharts = () => {
  const myChart = echarts.init(countChartRef.value);
  // console.log("projectCountData：",projectCountData.value)

  const option = {
    xAxis: {
      type: 'category',
      name: '年份',
      data: projectCountData.value.years
    },
    yAxis: {
      type: 'value',
      name: '项目数量（个）'
    },
    tooltip: {
      trigger: 'axis',
      formatter: '{b}<br/>{c} 个' // 提示框也可以加单位
    },
    series: [
      {
        data: projectCountData.value.counts,
        type: 'bar',
        barWidth: 40, // 关键：直接设置柱条的固定宽度（单位px）
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(180, 180, 180, 0.2)'
        }
      }
    ]
  };

  option && myChart.setOption(option);
}

const generateAreaCharts = () => {
  const myChart = echarts.init(areaChartRef.value);
  console.log("projectCountData：",projectAreaData.value)

  const option = {
    xAxis: {
      type: 'category',
      name: '年份',
      data: projectAreaData.value.years
    },
    yAxis: {
      type: 'value',
      name: '项目面积（公顷）'
    },
    tooltip: {
      trigger: 'axis',
      formatter: '{b}<br/>{c} 公顷' // 提示框也可以加单位
    },
    series: [
      {
        data: projectAreaData.value.area,
        type: 'bar',
        barWidth: 40, // 关键：直接设置柱条的固定宽度（单位px）
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(180, 180, 180, 0.2)'
        }
      }
    ]
  };
  option && myChart.setOption(option);
}


onMounted(()=>{
  // generateCountCharts()
})
</script>

<style scoped lang="scss">
.main-content{
  padding: 10px;
}
.project-header{
  padding: 10px;
  background-color: #f5f7fa;
  color: #909399;
  font-weight: bold;
}
.project-charts{
  width: 100%;
  height: 100%;
}
.count-charts{
  width: 100%;
  height: 350px;
}
</style>
