<template>
  <div class="body">
    <div
      class="mb5"
      v-if="props.indexType !== 'index'"
    >
      <el-button
        type="primary"
        size="mini"
        @click="submit"
        v-show="props.type !== 'view'"
      >保存</el-button>
      <el-button
        type="primary"
        size="small" @click="handleClose"
      >关闭</el-button>
    </div>
    <div class="card backBox">
      <div
        style="
          font-size: 30px;
          text-align: center;
          font-weight: 600;
          width: 100%;
        "
      >
        政策法规
      </div>
      <div class="flx-center mt50">
        <div style="width: 65%">
          <el-form
            :model="form"
            label-width="auto"
          >
            <table style="width: 100%">
              <tr>
                <td>
                  <el-form-item
                    label="名称"
                    prop="title"
                  >
                    <el-input
                      v-model="form.title"
                      :rows="1"
                      type="textarea"
                      placeholder="请输入政策法规名称"
                      autosize
                      :disabled="pageView"
                    />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item
                    label="所属类别"
                    prop="title"
                  >
                    <el-select
                      v-model="form.flag"
                      placeholder="选择政策还是法规"
                      style="width: 100%"
                      :disabled="pageView"
                    >
                      <el-option
                        label="政策"
                        value="0"
                      />
                      <el-option
                        label="法规"
                        value="1"
                      />
                    </el-select>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td>
                  <el-form-item
                    label="文号"
                    prop="docNum"
                  >
                    <el-input
                      v-model="form.docNum"
                      placeholder="请输入政策法规文号"
                      :disabled="pageView"
                    />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item
                    label="发布时间"
                    prop="sendTime"
                  >
                    <el-date-picker
                      v-model="form.sendTime"
                      type="date"
                      style="width: 100%"
                      placeholder="请选择发布时间"
                      value-format="YYYY-MM-DD"
                      :disabled="pageView"
                    />
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td>
                  <el-form-item label="发布机构">
                    <el-input
                      v-model="form.mechanism"
                      placeholder="请输入发布机构"
                      :disabled="pageView"
                    />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="适用区域">
                    <el-input
                      v-model="form.applicableArea"
                      placeholder="请输入适用区域"
                      :disabled="pageView"
                    />
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td>
                  <el-form-item label="业务类型">
                    <el-input
                      v-model="form.type"
                      placeholder="请输入业务类型"
                      :disabled="pageView"
                    />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="效力级别">
                    <el-input
                      v-model="form.level"
                      placeholder="请输入效力级别"
                      :disabled="pageView"
                    />
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td>
                  <el-form-item label="废止记录">
                    <el-input
                      v-model="form.abolish"
                      placeholder="请输入废止记录"
                      :disabled="pageView"
                    />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="时效状态">
                    <el-input
                      v-model="form.agingState"
                      placeholder="请输入时效状态"
                      :disabled="pageView"
                    />
                  </el-form-item>
                </td>
              </tr>
            </table>

            <el-form-item label="       ">
              <table class="styled-table">
                <tr>
                  <td/>
                  <td>材料项</td>
                  <td>类别</td>
                  <td>必须</td>
                  <td>数量</td>
                  <td>文件</td>
                </tr>
                <tr>
                  <td>1</td>
                  <td>附件</td>
                  <td>附件材料</td>
                  <td>否</td>
                  <td>1</td>
                  <td>
                    <FileUpload
                      v-show="props.type !== 'view'"
                      fileSize="10"
                      :fileType="['doc', 'docx', 'pdf']"
                      :modelValue="form.affixList"
                      @update:modelValue="handleFileList"
                    />
                    <div v-show="props.type === 'view'">
                      <div
                        v-for="obj in form.affixList"
                        :key="obj.id"
                        class="flx-center"
                      >
                        <div
                          class="cursor-pointer"
                          style="color: #409efc"
                          @click="onPreview(obj)"
                        >
                          {{ obj.name }}
                        </div>
                        <div class="ml5">
                          <el-tag
                            class="cursor-pointer"
                            type="primary"
                            @click="onPreview(obj)"
                            size="small"
                          >
                            <el-icon class="mr5"> <View /></el-icon>阅览
                          </el-tag>
                          <el-tag
                            class="ml5 cursor-pointer"
                            type="success"
                            @click="downloadFile(obj)"
                            size="small"
                          >
                            <el-icon class="mr5"> <Download /></el-icon>下载
                          </el-tag>
                        </div>
                      </div>
                    </div>
                  </td>
                </tr>
              </table>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref } from "vue";
import { addStudy, getStudyById, editStudy } from "@/api/study/study.js"; //addDocumentPolicy
import { addDocumentPolicy, updataDocumentPolicy, getPolicyForId } from "@/api/content/policy.js";
import { ElMessage } from "element-plus";
import { base64EncodeUnicode } from "@/utils/base64.js";
import { downloadFileFromUrl } from "@/utils/common.js";

const emit = defineEmits(["close"]);

const props = defineProps({
  id: String,
  type: String,
  indexType: String
});

const form = ref({
  affixList: [],
  title: "",
  docNum: "",
  sendTime: "",
  mechanism: "",
  applicableArea: "",
  level: "",
  abolish: "",
  agingState: "",
  flag: ''
});

// 处理文件选择
const handleFileList = (msg) => {
  form.value.affixList = msg.map((item) => {
    console.log("item", item);
    let obj = {
      name: item.name,
      url: item.url
    };
    return obj;
  });
};

const handleClose = () => {
  emit("close", false);
};

const submit = () => {
  if (props.type === "edit") {
    updataDocumentPolicy(form.value).then((res) => {
      if (res.code === 200) {
        ElMessage({
          message: "修改完成",
          type: "success"
        });
        handleClose();
      }
    });
  } else {
    addDocumentPolicy(form.value).then((res) => {
      if (res.code === 200) {
        ElMessage({
          message: "新增完成",
          type: "success"
        });
        handleClose();
      }
    });
  }
};

// 根据id获取详情
const getDetail = () => {
  getPolicyForId(props.id).then((res) => {
    if (res.code === 200) {
      form.value = res.data;
      form.value.affixList = form.value.affixes[0].affixs;
    }
  });
};

// 阅览
function onPreview(obj) {
  const url = import.meta.env.VITE_APP_FILE_SERVICE_URL + obj.url;
  window.open(
    `${import.meta.env.VITE_APP_FILE_PERVIEW_URL}?url=${base64EncodeUnicode(
      url
    )}`
  );
}
// 下载
function downloadFile(obj) {
  const url = import.meta.env.VITE_APP_FILE_SERVICE_URL + obj.url;
  downloadFileFromUrl(url, obj.name);
}

const pageView = ref(false)
onMounted(() => {
  if (props.type === "view" || props.type === "edit") {
    getDetail();
  }
  if(props.type === "view"){
    pageView.value = true
  }
});
</script>

<style scoped lang="scss">
@import "@/styles/variables.module.scss";
.body {
  width: 100%;
  height: $contentHeight;
  background-color: #e9eef3;
  overflow: hidden;
  padding: 5px;
  .card {
    height: calc($contentHeight - 42px);
    overflow: scroll;
    background: #dce2f1;
  }
  .styled-table {
    width: 100%;
    border-collapse: collapse;
    border-radius: 8px;
  }
  .styled-table td {
    border: 1px solid #e9eef3;
    text-align: center;
    font-size: 14px;
    font-weight: 700;
    color: rgb(96, 98, 102);
    flex-shrink: 1;
  }
}
</style>
