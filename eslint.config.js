import globals from "globals"
import pluginJs from "@eslint/js"
import pluginVue from "eslint-plugin-vue"
import { readFile } from "node:fs/promises"
// import prettier from "eslint-plugin-prettier"

/**
 * https://blog.csdn.net/sayUonly/article/details/123482912
 * 自动导入的配置
 */
const autoImportFile = new URL(".eslintrc-auto-import.json", import.meta.url)
const autoImportGlobals = JSON.parse(await readFile(autoImportFile, "utf8"))

/** @type {import('eslint').Linter.Config[]} */
export default [
  {
    /**
     * 不需要.eslintignore文件 而是在这里配置
     */
    ignores: [
      "*.sh",
      "node_modules",
      "*.md",
      "*.woff",
      "*.ttf",
      ".vscode",
      ".idea",
      "dist",
      "/public",
      "/docs",
      ".husky",
      ".local",
      "/bin",
      ".eslintrc.cjs",
      "prettier.config.js",
      "src/assets",
      "tailwind.config.js"
    ]
  },
  { files: ["**/*.{js,mjs,cjs,ts,vue}"] },
  {
    languageOptions: {
      globals: globals.browser
    }
  },
  pluginJs.configs.recommended,
  ...pluginVue.configs["flat/essential"],
  {
    files: ["**/*.vue"],
    languageOptions: {
      parserOptions: {}
    }
  },
  {
    languageOptions: {
      globals: {
        // 自动导入的配置 undef
        ...autoImportGlobals.globals,
        DialogOption: "readonly",
        LayoutSetting: "readonly"
      }
    },
    // plugins: { prettier },
    rules: {
      // vue
      "vue/multi-word-component-names": "off",
      "vue/valid-define-props": "off",
      "vue/no-v-model-argument": "off",
      "prefer-rest-params": "off",
      // vue 自闭合标签
      "vue/html-self-closing": ["error", {
        "html": {
          "void": "never",
          "normal": "always",
          "component": "always"
        },
        "svg": "always",
        "math": "always"
      }],
      // 属性缩进
      // 'vue/html-indent': ['error', 2, {
      //   attribute: 1,            // 属性相对于标签的缩进级别
      //   baseIndent: 1,           // 文件基础缩进级别
      //   closeBracket: {          // 闭合括号的缩进
      //     startTag: 0,           // 开始标签闭合括号缩进
      //     endTag: 1,             // 结束标签闭合括号缩进
      //     selfClosingTag: 1      // 自闭合标签闭合括号缩进
      //   },
      //   alignAttributesVertically: true,  // 是否垂直对齐属性
      //   ignores: []              // 忽略的元素列表
      // }],
      // 控制属性换行行为
      'vue/first-attribute-linebreak': ['error', {
        singleline: 'ignore', // 单行情况下不强制换行
        multiline: 'below' // 多行时第一个属性在标签下方
      }],
      // vue 属性行数
      "vue/max-attributes-per-line": ["error", {
        "singleline": {
          "max": 1
        },
        "multiline": {
          "max": 3
        }
      }],
      // Vue 属性缩进
      "vue/html-indent": ["error", 2, {
        "attribute": 1,
        "baseIndent": 1,
        "closeBracket": 0,
        "alignAttributesVertically": true,
        "ignores": []
      }],
      // 自闭合标签换行
      "vue/html-closing-bracket-newline": [
        "error",
        {
          "singleline": "never",
          "multiline": "always",
          "selfClosingTag": {
            "singleline": "never",
            "multiline": "always"
          }
        }
      ],
      // prettier
      // "prettier/prettier": "error",
      // 禁止分号 (强制无分号风格)
      // "semi": ["error", "never"],
      // 对象/数组最后一个元素禁止逗号
      "comma-dangle": ["error", "never"],
      // "no-debugger": import.meta.env.MODE === "production" ? 2 : 0
      "no-debugger": 2,
      // JavaScript 缩进规则
      'indent': ['error', 2, {
        'SwitchCase': 1, // switch case 缩进
        'VariableDeclarator': 1, // 变量声明缩进
        'outerIIFEBody': 1, // 立即执行函数缩进
        'MemberExpression': 1, // 成员表达式缩进
        'FunctionDeclaration': {
          'parameters': 1, // 函数参数缩进
          'body': 1 // 函数体缩进
        },
        'FunctionExpression': {
          'parameters': 1,
          'body': 1
        },
        'CallExpression': {
          'arguments': 1 // 函数调用参数缩进
        },
        'ArrayExpression': 1, // 数组元素缩进
        'ObjectExpression': 1, // 对象属性缩进
        'ImportDeclaration': 1 // import 语句缩进
      }],
      // 等号周围的空格
      'operator-linebreak': ['error', 'after', {
        'overrides': {
          '=': 'none' // 等号前后不需要换行
        }
      }],
      // 对象字面量属性空格
      'key-spacing': ['error', {
        'beforeColon': false, // 键名与冒号间无空格
        'afterColon': true // 冒号与值间有空格
      }],
      // 变量声明时等号周围的空格
      'space-infix-ops': ['error', { 'int32Hint': false }],
      // 变量声明后的空格
      'init-declarations': 'off',
      // 多个变量声明时的空格
      'one-var': ['error', {
        'var': 'never', // var 声明不允许逗号分隔
        'let': 'never', // let 声明不允许逗号分隔
        'const': 'never' // const 声明不允许逗号分隔
      }],
      // 变量声明时的分号空格
      'semi-spacing': ['error', {
        'before': false, // 分号前无空格
        'after': true // 分号后有空格
      }],
      // 删除语句末尾多余空格
      'no-trailing-spaces': ['error', { skipBlankLines: false, ignoreComments: false }],
      // 禁止行内多余空格
      'no-multi-spaces': ['error', { ignoreEOLComments: false }],
      // 控制对象大括号 {} 内的空格，大括号内总是有空格
      'object-curly-spacing': ['error', 'always']
    }
  }
]
