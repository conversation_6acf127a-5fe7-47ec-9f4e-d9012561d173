import request from "@/utils/request";

/**
 * 查询巡查记录列表
 * @param query
 * @returns {*}
 */

export const listPatrolRecord = (query) => {
  return request({
    url: "/patrol/patrolRecord/list",
    method: "get",
    params: query,
  });
};

/**
 * 查询巡查记录详细
 * @param id
 */
export const getPatrolRecord = (id) => {
  return request({
    url: "/patrol/patrolRecord/" + id,
    method: "get",
  });
};

/**
 * 新增巡查记录
 * @param data
 */
export const addPatrolRecord = (data) => {
  return request({
    url: "/patrol/patrolRecord",
    method: "post",
    data: data,
  });
};

/**
 * 修改巡查记录
 * @param data
 */
export const updatePatrolRecord = (data) => {
  return request({
    url: "/patrol/patrolRecord",
    method: "put",
    data: data,
  });
};

/**
 * 删除巡查记录
 * @param id
 */
export const delPatrolRecord = (id) => {
  return request({
    url: "/patrol/patrolRecord/" + id,
    method: "delete",
  });
};
