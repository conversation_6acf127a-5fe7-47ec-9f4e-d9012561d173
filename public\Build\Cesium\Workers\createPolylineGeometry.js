define(["./when-b60132fc","./Rectangle-dee65d21","./ArcType-29cf2197","./arrayRemoveDuplicates-d2f048c5","./buildModuleUrl-9085faaa","./Cartographic-3309dd0d","./Color-2a095a27","./ComponentDatatype-c140a87d","./Check-7b2a090c","./GeometryAttribute-c65394ac","./GeometryAttributes-252e9929","./IndexDatatype-8a5eead4","./Math-119be1a3","./PolylinePipeline-a5200218","./FeatureDetection-806b12f0","./VertexFormat-6446fca0","./Event-16a2dfbf","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Cartesian2-db21342c","./Cartesian4-3ca25aab","./EllipsoidGeodesic-139a7db9","./EllipsoidRhumbLine-30b5229b","./IntersectionTests-0d6905a3","./Plane-a3d8b3d2"],(function(e,t,a,r,o,i,n,l,s,p,d,c,u,y,m,f,h,v,C,_,A,g,b,E,w){"use strict";var P=[];function T(e,t,a,r,o){var i,l=P;l.length=o;var s=a.red,p=a.green,d=a.blue,c=a.alpha,u=r.red,y=r.green,m=r.blue,f=r.alpha;if(n.Color.equals(a,r)){for(i=0;i<o;i++)l[i]=n.Color.clone(a);return l}var h=(u-s)/o,v=(y-p)/o,C=(m-d)/o,_=(f-c)/o;for(i=0;i<o;i++)l[i]=new n.Color(s+i*h,p+i*v,d+i*C,c+i*_);return l}function x(r){var l,s,p=(r=e.defaultValue(r,e.defaultValue.EMPTY_OBJECT)).positions,d=r.colors,c=e.defaultValue(r.width,1),y=e.defaultValue(r.hMax,-1),m=e.defaultValue(r.colorsPerVertex,!1);this._positions=p,this._colors=d,this._width=c,this._hMax=y,this._colorsPerVertex=m,this._dist=r.dist,this._period=r.period,this._vertexFormat=f.VertexFormat.clone(e.defaultValue(r.vertexFormat,f.VertexFormat.DEFAULT)),this._followSurface=e.defaultValue(r.followSurface,!0),e.defined(r.followSurface)&&(l="PolylineGeometry.followSurface",s="PolylineGeometry.followSurface is deprecated and will be removed in Cesium 1.55. Use PolylineGeometry.arcType instead.",o.oneTimeWarning(l,s),r.arcType=r.followSurface?a.ArcType.GEODESIC:a.ArcType.NONE),this._arcType=e.defaultValue(r.arcType,a.ArcType.GEODESIC),this._followSurface=this._arcType!==a.ArcType.NONE,this._granularity=e.defaultValue(r.granularity,u.CesiumMath.RADIANS_PER_DEGREE),this._ellipsoid=t.Ellipsoid.clone(e.defaultValue(r.ellipsoid,t.Ellipsoid.WGS84)),this._workerName="createPolylineGeometry";var h=1+p.length*i.Cartesian3.packedLength;h+=e.defined(d)?1+d.length*n.Color.packedLength:1,this.packedLength=h+t.Ellipsoid.packedLength+f.VertexFormat.packedLength+4+2}x.pack=function(a,r,o){var l;o=e.defaultValue(o,0);var s=a._positions,p=s.length;for(r[o++]=p,l=0;l<p;++l,o+=i.Cartesian3.packedLength)i.Cartesian3.pack(s[l],r,o);var d=a._colors;for(p=e.defined(d)?d.length:0,r[o++]=p,l=0;l<p;++l,o+=n.Color.packedLength)n.Color.pack(d[l],r,o);return t.Ellipsoid.pack(a._ellipsoid,r,o),o+=t.Ellipsoid.packedLength,f.VertexFormat.pack(a._vertexFormat,r,o),o+=f.VertexFormat.packedLength,r[o++]=a._width,r[o++]=a._colorsPerVertex?1:0,r[o++]=a._arcType,r[o++]=a._granularity,r[o++]=a._hMax,r[o++]=a._dist,r[o]=a._period,r};var D=t.Ellipsoid.clone(t.Ellipsoid.UNIT_SPHERE),k=new f.VertexFormat,G={positions:void 0,colors:void 0,ellipsoid:D,vertexFormat:k,width:void 0,colorsPerVertex:void 0,arcType:void 0,granularity:void 0};x.unpack=function(a,r,o){var l;r=e.defaultValue(r,0);var s=a[r++],p=new Array(s);for(l=0;l<s;++l,r+=i.Cartesian3.packedLength)p[l]=i.Cartesian3.unpack(a,r);var d=(s=a[r++])>0?new Array(s):void 0;for(l=0;l<s;++l,r+=n.Color.packedLength)d[l]=n.Color.unpack(a,r);var c=t.Ellipsoid.unpack(a,r,D);r+=t.Ellipsoid.packedLength;var u=f.VertexFormat.unpack(a,r,k);r+=f.VertexFormat.packedLength;var y=a[r++],m=1===a[r++],h=a[r++],v=a[r++],C=a[r++],_=1==a[r++],A=a[r];return e.defined(o)?(o._positions=p,o._colors=d,o._ellipsoid=t.Ellipsoid.clone(c,o._ellipsoid),o._vertexFormat=f.VertexFormat.clone(u,o._vertexFormat),o._width=y,o._colorsPerVertex=m,o._arcType=h,o._granularity=v,o._hMax=C,o._dist=_,o._period=A,o):(G.positions=p,G.colors=d,G.width=y,G.colorsPerVertex=m,G.arcType=h,G.granularity=v,G.hMax=C,G.dist=_,G.period=A,new x(G))};var V=new i.Cartesian3,F=new i.Cartesian3,L=new i.Cartesian3,S=new i.Cartesian3;return x.createGeometry=function(t){var s,f,h,v=t._width,C=t._hMax,_=t._vertexFormat,A=t._colors,g=t._colorsPerVertex,b=t._arcType,E=t._granularity,w=t._ellipsoid,x=t._dist,D=t._period,k=r.arrayRemoveDuplicates(t._positions,i.Cartesian3.equalsEpsilon),G=k.length;if(!(G<2||v<=0)){if(b===a.ArcType.GEODESIC||b===a.ArcType.RHUMB){var O,M;b===a.ArcType.GEODESIC?(O=u.CesiumMath.chordLength(E,w.maximumRadius),M=y.PolylinePipeline.numberOfPoints):(O=E,M=y.PolylinePipeline.numberOfPointsRhumbLine);var R=y.PolylinePipeline.extractHeights(k,w);if(e.defined(A)){var I=1;for(s=0;s<G-1;++s)I+=M(k[s],k[s+1],O);var B=new Array(I),N=0;for(s=0;s<G-1;++s){var U=k[s],W=k[s+1],H=A[s],Y=M(U,W,O);if(g&&s<I){var q=T(0,0,H,A[s+1],Y),z=q.length;for(f=0;f<z;++f)B[N++]=q[f]}else for(f=0;f<Y;++f)B[N++]=n.Color.clone(H)}B[N]=n.Color.clone(A[A.length-1]),A=B,P.length=0}k=b===a.ArcType.GEODESIC?y.PolylinePipeline.generateCartesianArc({positions:k,minDistance:O,ellipsoid:w,height:R,hMax:C}):y.PolylinePipeline.generateCartesianRhumbArc({positions:k,granularity:O,ellipsoid:w,height:R})}var J,j=4*(G=k.length)-4,K=new Float64Array(3*j),Q=new Float64Array(3*j),X=new Float64Array(3*j),Z=new Float32Array(2*j),$=_.st?new Float32Array(2*j):void 0,ee=e.defined(A)?new Uint8Array(4*j):void 0,te=x?new Float32Array(3*j):void 0,ae=0,re=0,oe=0,ie=0,ne=0,le=0;for(f=0;f<G;++f){var se,pe;0===f?(J=V,i.Cartesian3.subtract(k[0],k[1],J),i.Cartesian3.add(k[0],J,J)):J=k[f-1],i.Cartesian3.clone(J,L),i.Cartesian3.clone(k[f],F),f===G-1?(J=V,i.Cartesian3.subtract(k[G-1],k[G-2],J),i.Cartesian3.add(k[G-1],J,J)):J=k[f+1],i.Cartesian3.clone(J,S),e.defined(ee)&&(se=0===f||g?A[f]:A[f-1],f!==G-1&&(pe=A[f]));var de=f===G-1?2:4;for(h=0===f?2:0;h<de;++h){i.Cartesian3.pack(F,K,ae),i.Cartesian3.pack(L,Q,ae),i.Cartesian3.pack(S,X,ae),ae+=3;var ce=h-2<0?-1:1,ue=h%2*2-1,ye=ue*f/G;if(Z[re++]=C>0?ye:ue,Z[re++]=ce*v,_.st&&($[oe++]=f/(G-1),$[oe++]=Math.max(Z[re-2],0)),e.defined(ee)){var me=h<2?se:pe;ee[ie++]=n.Color.floatToByte(me.red),ee[ie++]=n.Color.floatToByte(me.green),ee[ie++]=n.Color.floatToByte(me.blue),ee[ie++]=n.Color.floatToByte(me.alpha)}x&&(te[3*ne]=le,ne++)}le+=i.Cartesian3.distance(J,k[f])}if(x){var fe=le,he=Math.random()*(D>0?D:fe);for(f=0;f<j;f++)te[3*f+1]=fe,te[3*f+2]=he}var ve=new d.GeometryAttributes;ve.position=new p.GeometryAttribute({componentDatatype:l.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:K}),ve.prevPosition=new p.GeometryAttribute({componentDatatype:l.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:Q}),ve.nextPosition=new p.GeometryAttribute({componentDatatype:l.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:X}),ve.expandAndWidth=new p.GeometryAttribute({componentDatatype:l.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:Z}),_.st&&(ve.st=new p.GeometryAttribute({componentDatatype:l.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:$})),e.defined(ee)&&(ve.color=new p.GeometryAttribute({componentDatatype:l.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:4,values:ee,normalize:!0})),x&&(ve.dist=new p.GeometryAttribute({componentDatatype:l.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:te}));var Ce=c.IndexDatatype.createTypedArray(j,6*G-6),_e=0,Ae=0,ge=G-1;for(f=0;f<ge;++f)Ce[Ae++]=_e,Ce[Ae++]=_e+2,Ce[Ae++]=_e+1,Ce[Ae++]=_e+1,Ce[Ae++]=_e+2,Ce[Ae++]=_e+3,_e+=4;return new p.Geometry({attributes:ve,indices:Ce,primitiveType:m.PrimitiveType.TRIANGLES,boundingSphere:o.BoundingSphere.fromPoints(k),geometryType:p.GeometryType.POLYLINES})}},function(a,r){return e.defined(r)&&(a=x.unpack(a,r)),a._ellipsoid=t.Ellipsoid.clone(a._ellipsoid),x.createGeometry(a)}}));
