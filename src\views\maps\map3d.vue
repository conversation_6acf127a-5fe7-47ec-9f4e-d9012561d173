<template>
  <div class="map-container-3d">
    <Viewer3d
      ref="viewer3d"
      :viewerId="viewer3dId"
      @onCreateViewer3d="createViewer3d"
    />
    <Resource
      class="resource"
      v-if="isShowResource"
      :has-floor="false"
      :viewer3d="viewer3dProp"
      :isOnlyAddTerrainLayer="true"
    />
    <SideBar />
  </div>
</template>

<script setup name="Map3d">
// import Resource from "@/components/GISTools/3d/Resource/Resource";
// import SideBar from "@/components/GISTools/3d/ToolBar/SideBar";
import UseViewer3d from "@/components/GISTools/Viewer/UseViewer3d.js"
import Viewer3d from "@/components/GISTools/Viewer/Viewer3d.vue"

// 使用异步方式加载组件
const Resource = defineAsyncComponent(() =>
  import('@/components/GISTools/3d/Resource/Resource')
)
const SideBar = defineAsyncComponent(() =>
  import('@/components/GISTools/3d/ToolBar/SideBar')
)

/**
 * 响应数据
 */
const isShowResource = ref(false);
const viewer3dId = ref("viewer3d")
const viewer3dProp = reactive({});

/**
 * 监听数据，监听视图对象值：当viewer3d值为真时，显示资源组件
 * 目前使用父组件传递属性不成功，只能使用provide,inject进行传递
 */
watch(()=>viewer3dProp.viewer3d,(newValue,oldValue) => {
  if (Object.keys(newValue).length) {
    isShowResource.value = true
  }
})
provide('viewer3d', viewer3dProp);

const createViewer3d = async (viewerId,mapInitStore,mapViewStore)=>{
  const useViewer3d = new UseViewer3d(viewerId,mapInitStore,mapViewStore)
  await useViewer3d.createViewer3d()
  const viewer3d = useViewer3d.viewer3d
  Object.assign(viewer3dProp,{ viewer3d })
}
onMounted(() => {

});
</script>

<style scoped lang="scss">
@import "@/styles/map2d.scss";
@import "@/styles/variables.module.scss";
.map-container-3d {
  width: 100vw;
  height: $resourceContentHeight;
}
.resource {
  position: absolute;
  z-index: 999;
  background: rgba(0, 19, 46, 0.67843137254902)!important;
  height: $resourceContentHeight;
  color: $mapMenuText;
}
</style>
