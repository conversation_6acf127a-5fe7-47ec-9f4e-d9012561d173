import request from "@/utils/request";

// 分页查询对应项目的航线文件列表
export const wayLineList = (query) => {
    return request({
        url: "/uav/flightHub2/way_line/page",
        method: "get",
        params: query
    });
};

// 查询航线文件详情，包含下载路径
export const wayLineId = (id) => {
    return request({
        url: "/uav/flightHub2/way_line/"+ id,
        method: "get",
    });
};

// 获取当前系统当前年每月的飞行任务统计
export const monthOfYear = () => {
    return request({
        url: "/uav/flightHub2/way_line/statistics/month_of_year",
        method: "get",
    });
};

// 获取所有设备的物模型信息
export const deviceModel = () => {
    return request({
        url: "uav/flightHub2/device/model",
        method: "get",
    });
};

// 获取指定sn号的镜头信息
export const deviceSnCameral = (sn) => {
    return request({
        url: "/uav/flightHub2/device/"+sn+"/cameral",
        method: "get",
    });
};

// 获取指定sn号的镜头信息
export const deviceSn = (sn) => {
    return request({
        url: "/uav/flightHub2/device/"+sn,
        method: "get",
    });
};

// 查询已同步的航线轨迹详情
export const wayList = () => {
    return request({
        url: "/uav/flightHub2/way_line/list",
        method: "get",
    });
};
