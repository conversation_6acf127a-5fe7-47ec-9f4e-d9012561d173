<template>
  <div
    v-if="isShow"
    class="tool-wrap"
  >
    <!-- <Header :header-info="headerInfo" @closePanel="closePanel" /> -->
    <div class="scene-container">
      <div
        v-for="(opt, index) in sceneOptions"
        :key="index"
        class="scene-item"
        :class="toogleIndex === index ? 'is-active' : ''"
        @click="toogleScene(opt, index)"
      >
        <svg-icon :icon-class="opt.iconClass" />
        <span class="scene-name">{{ opt.name }}</span>
      </div>
    </div>
  </div>
</template>

<script setup name="RainAndSnow">
import useMapViewStore from "@/store/modules/map/mapView.js"

const store = useMapViewStore()
defineProps({
  isShow: {
    type: Boolean,
    default: true
  },
  headerInfo: {
    type: Object,
    default: ()=>{}
  }
})

const toolTitle = ref("场景切换")
const toogleIndex = ref("")
const sceneOptions = ref([
  {
    name: "雨",
    iconClass: "rain",
    funcName: "openSceneOfRain"
  },
  {
    name: "雪",
    iconClass: "snow",
    funcName: "openSceneOfSnow"
  }
])
const isRain = ref(false)
const isSnow = ref(false)

const viewer3d = computed(()=>useMapViewStore().viewer3d);

watch(isRain, (newVal, oldVal) => {
  if (newVal) {
    isSnow.value = false;
  }
})

watch(isSnow, (newValue, oldValue) => {
  if (newValue) {
    isRain.value = false;
  }
})

const emits = defineEmits(['closePanel'])

/**
 * 头部事件
 */
const closePanel = ()=> {
  emits("closePanel");
}


/**
 * 场景切换
 */
const toogleScene = (opt, index)=> {
  toogleIndex.value === index ?
    (toogleIndex.value = "") :
    (toogleIndex.value = index)
  opt.funcName === "openSceneOfRain" ? openSceneOfRain() : openSceneOfSnow()
}
/**
 * 场景-雨
 */
const openSceneOfRain = ()=> {
  isRain.value = !isRain.value;
  const scene = viewer3d.value.scene;
  scene.postProcessStages.snow.enabled = false;
  scene.postProcessStages.rain.enabled = false
  // 开启雨景
  scene.postProcessStages.rain.enabled = isRain.value;
  scene.postProcessStages.rain.uniforms.angle = 6;
  scene.postProcessStages.rain.uniforms.speed = 6;
  if (isRain.value) {
    scene.postProcessStages.rain.uniforms.angle = 6;
    scene.postProcessStages.rain.uniforms.speed = 6;
    for (let i = 0; i < scene.layers.layerQueue.length; i++) {
      const layer = scene.layers.layerQueue[i];
      // layer.setPBRMaterialFromJSON("./materialJSON/rain.json");
      // 实现雨水渐增的效果
      const tv = setInterval(() => {
        if (layer.rainEffect !== undefined) {
          layer.rainEffect.wetnessFactor += 0.005;
        }
        if (
          layer.rainEffect !== undefined &&
          layer.rainEffect.wetnessFactor - 0.65 > 0
        ) {
          clearInterval(tv);
        }
      }, 40);
    }
  } else {
    removeScene(scene);
  }

  /**添加闪电效果，目前无法使用**/
  // const LightningPostStage = new SuperMap3D.PostProcessStage({
  //   fragmentShader: LightningShader, // 导入的闪电shader
  //   uniforms: {
  //     mix_factor: 0.5,//混合系数0-1之间的数
  //     fall_interval: 0.7//0-1之间的数
  //   }
  // })
  // 添加到场景后处理中
  // scene.postProcessStages.add(LightningPostStage);
}

/**
 * 场景-雪
 */
const openSceneOfSnow = ()=> {
  isSnow.value = !isSnow.value;
  const scene = viewer3d.value.scene;
  scene.postProcessStages.snow.enabled = false;
  scene.postProcessStages.rain.enabled = false
  // 开启雪天
  scene.postProcessStages.snow.enabled = isSnow.value;
  if (isSnow.value) {
    scene.postProcessStages.snow.uniforms.density = 10;
    scene.postProcessStages.snow.uniforms.angle = 0;
    scene.postProcessStages.snow.uniforms.speed = 3;
    for (let i = 0; i < scene.layers.layerQueue.length; i++) {
      const layer = scene.layers.layerQueue[i];
      // layer.setPBRMaterialFromJSON("./materialJSON/snow.json","");
      const timeValue = setInterval(() => {
        if (
          layer._PBRMaterialParams.pbrMetallicRoughness.snowEffect !== undefined
        ) {
          layer._PBRMaterialParams.pbrMetallicRoughness.snowEffect.snow_coverage += 0.0006;
        }
        if (
          layer._PBRMaterialParams.pbrMetallicRoughness.snowEffect !== undefined &&
          layer._PBRMaterialParams.pbrMetallicRoughness.snowEffect
            .snow_coverage - 1 > 0
        ) {
          clearInterval(timeValue);
        }
      }, 30);
    }
  } else {
    removeScene(scene);
  }
}
/**
 * 移除场景效果
 * @param scene：场景对象
 */
const removeScene = (scene)=> {
  for (let i = 0; i < scene.layers.layerQueue.length; i++) {
    const layer = scene.layers.layerQueue[i];
    layer.removePBRMaterial();
  }
}

/**
 * 恢复场景
 */
const resetScene = ()=>{
  viewer3d.value.scene.postProcessStages.rain.enabled = false;
  viewer3d.value.scene.postProcessStages.snow.enabled = false;
}

onBeforeUnmount(()=>{
  resetScene()
  removeScene(viewer3d.value.scene)
})
</script>

<style scoped lang="scss">
.tool-wrap {
  color: #fff;
  background-image: url("@/assets/images/map/tool.png");
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-size: cover;
  box-shadow: 0 0 8px 0 #057595;
}
.scene-container {
  display: flex;
  padding: 10px;
  flex-wrap: wrap;
  justify-content: space-around;
  .scene-item {
    cursor: pointer;
    .svg-icon {
      width: 1.25em;
      height: 1.25em;
    }
  }
  .scene-item.is-active {
    text-shadow: 0 0 3px #ffffff21, 0 0 2px #ffffff2e, 0 0 10px #ffffff21, 0 0 10px #409eff, 0 0 10px #409eff, 0 0 6px #00cffa, 0 0 10px #409eff, 0 0 5px #409eff;
  }
}
:deep(.el-button--primary) {
  margin: 10px;
}
.scene-name {
  margin-left: 5px;
}
</style>
