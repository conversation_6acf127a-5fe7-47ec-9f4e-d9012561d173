import { ElMessage } from 'element-plus'

/**
 * 收文—办公室分件 Activity_1sfnhah
 *  headFlag  0/1  牵头部门(负责人)
 *  coFlag  0/1 协办部门(负责人)
 *  leaderFlag 0/1 领导阅签
 */
export function officeDivision(data) {
    if (data.leaderList && Array.isArray(data.leaderList) && data.leaderList.length > 0) {
        data.leaderFlag = '1'
    } else {
        data.leaderFlag = '0'
    }
    
    if (data.ManagerOfCoDeptList && Array.isArray(data.ManagerOfCoDeptList) && data.ManagerOfCoDeptList.length > 0) {
        data.coFlag = '1'
    } else {
        data.coFlag = '0'
    }
    
    if (data.headOfLeadDept) {
        data.headFlag = '1'
    } else {
        data.headFlag = '0'
    }

    if (!data.leader2 && (!data.ManagerOfCoDeptList || data.ManagerOfCoDeptList.length <= 0) && !data.headOfLeadDept) {
        ElMessage.error('请至少选择一位下一步执行人员')
        return
    }

    return data
}

/**
 * 收文—牵头部门(负责人) Activity_0by1yt6
 *  needCoHost && coFlag 是否需要协办
 */
export function leadingDepartment(data) {
    if (data.ManagerOfCoDeptList && Array.isArray(data.ManagerOfCoDeptList)) {
        data.needCoHost = "1"
        data.coFlag = '0'
    } else {
        data.needCoHost = "0"
    }

    if (!data.handlerOfLeadDeptList) {
        ElMessage.error('请选择经办人.')
        return
    }
    
    return data
}