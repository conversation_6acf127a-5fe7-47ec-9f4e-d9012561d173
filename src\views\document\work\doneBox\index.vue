<template>
    <div class="box">
        <div v-if="showObj.table" class="container">
            <div class="card table-search mb10">
                <el-form :model="queryParams" ref="queryRef" :inline="true">
                    <el-form-item label="类型" prop="type">
                        <el-select v-model="queryParams.type" placeholder="类型" clearable style="width: 200px">
                            <el-option v-for="dict in dicts.doc_type" :key="dict.dictValue" :label="dict.dictLabel"
                                :value="dict.dictValue" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="公文标题" prop="title">
                        <el-input v-model="queryParams.title" placeholder="请输入公文标题" clearable style="width: 200px"
                            @keyup.enter="handleQuery" />
                    </el-form-item>
                    <el-form-item label="案卷号" prop="number">
                        <el-input v-model="queryParams.number" placeholder="请输入案卷号" clearable style="width: 200px"
                            @keyup.enter="handleQuery" />
                    </el-form-item>
                    <el-form-item label="来文类别" prop="receiveType">
                        <el-select v-model="queryParams.receiveType" placeholder="来文类别" clearable style="width: 200px">
                            <el-option v-for="dict in dicts.doc_category" :key="dict.dictValue" :label="dict.dictLabel"
                                :value="dict.dictValue" />
                        </el-select>
                    </el-form-item>
                    <!-- <el-form-item label="来文单位" prop="unitName">
                        <el-select v-model="queryParams.unitName" placeholder="来文单位" clearable style="width: 200px">
                            <el-option v-for="dict in dicts.doc_unit_name" :key="dict.dictValue" :label="dict.dictLabel"
                                :value="dict.dictValue" />
                        </el-select>
                    </el-form-item> -->
                    <!-- <el-form-item label="密 级" prop="secretLevel">
                        <el-select v-model="queryParams.secretLevel" placeholder="密 级" clearable style="width: 200px">
                            <el-option v-for="dict in dicts.doc_secret" :key="dict.dictValue" :label="dict.dictLabel"
                                :value="dict.dictValue" />
                        </el-select>
                    </el-form-item> -->
                    <!-- <el-form-item label="发布方式" prop="releaseMethod">
                        <el-select v-model="queryParams.releaseMethod" placeholder="发布方式" clearable
                            style="width: 200px">
                            <el-option v-for="dict in dicts.doc_release" :key="dict.dictValue" :label="dict.dictLabel"
                                :value="dict.dictValue" />
                        </el-select>
                    </el-form-item> -->
                    <!-- <el-form-item label="急 缓" prop="urgent">
                        <el-select v-model="queryParams.urgent" placeholder="急 缓" clearable style="width: 200px">
                            <el-option v-for="dict in dicts.doc_urgency" :key="dict.dictValue" :label="dict.dictLabel"
                                :value="dict.dictValue" />
                        </el-select>
                    </el-form-item> -->
                    <el-form-item>
                        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-form>
            </div>

            <div class="card">
                <el-table border v-loading="loading" :data="receiptList" style="width: 100%">
                    <el-table-column prop="typeName" label="类型" width="100px" />
                    <el-table-column prop="number" label="案卷号" />
                    <el-table-column prop="urgent" label="急 缓" width="80px">
                        <template #default="scope">
                            <div v-for="(item, index) in dicts.doc_urgency" :key="index">
                                <el-tag v-if="scope.row.urgent === item.dictValue" :type="item.listClass">{{
                                    item.dictLabel }}</el-tag>
                            </div>
                        </template>
                    </el-table-column>
                    <!-- <el-table-column prop="stageStatusName" label="状态" /> -->
                    <el-table-column prop="currentStage" label="当前阶段" />
                    <el-table-column prop="title" label="公文标题" width="400px" />
                    <el-table-column prop="receivingTime" label="日期" />
                    <el-table-column prop="receiveUnit" label="来文单位" />

                    <el-table-column prop="receiveTypeName" label="来文类别" />

                    <el-table-column fixed="right" label="操作">
                        <template #default="scope">
                            <el-button type="primary" @click="gotoDetail(scope.row)" link icon="View">查看</el-button>
                            <el-button type="primary" link icon="View" @click="gotoHistory(scope.row)">办理过程</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
                    v-model:limit="queryParams.pageSize" @pagination="getList" />
            </div>
        </div>
        <sendDetail :readOnly="true" :processInstanceId="componentsData.formDetail.processInstanceId"
            :nodeId="componentsData.formDetail.nodeId" :businessId="componentsData.formDetail.businessId"
            :taskId="componentsData.formDetail.taskId" @back="goBack('showForm')" v-if="showObj.showForm === '3'" />
        <meetDetail :readOnly="true" :processInstanceId="componentsData.formDetail.processInstanceId"
            :nodeId="componentsData.formDetail.nodeId" :businessId="componentsData.formDetail.businessId"
            :taskId="componentsData.formDetail.taskId" @back="goBack('showForm')" v-if="showObj.showForm === '5'" />
        <readDetail :readOnly="true" :processInstanceId="componentsData.formDetail.processInstanceId"
            :nodeId="componentsData.formDetail.nodeId" :businessId="componentsData.formDetail.businessId"
            :taskId="componentsData.formDetail.taskId" @back="goBack('showForm')" v-if="showObj.showForm === '2'" />
        <receiptDetail :readOnly="true" :processInstanceId="componentsData.formDetail.processInstanceId"
            :nodeId="componentsData.formDetail.nodeId" :businessId="componentsData.formDetail.businessId"
            :taskId="componentsData.formDetail.taskId" @back="goBack('showForm')" v-if="showObj.showForm === '1'" />
        <History activeName="0" :businessId="componentsData.history.businessId" @back="goBack('showHistory')"
            v-if="showObj.showHistory" />
    </div>
</template>

<script setup>
import { getDonebox } from '@/api/document/common'
import { getDicts } from '@/api/system/dict/data';
// import formDetail from './components/detail.vue'
import sendDetail from '@/views/document/work/sendAffair/components/detail.vue'
import meetDetail from '@/views/document/work/workMeeting/components/detail.vue'
import readDetail from '@/views/document/work/workRead/components/detail.vue'
import receiptDetail from '@/views/document/work/workReceipt/components/detail.vue'
import History from "@/components/History/index.vue";


import useUserStore from '@/store/modules/user'
import { computed } from 'vue';

const { proxy } = getCurrentInstance();

//需要请求的字典类型
const dictsList = ['doc_category', 'doc_unit_name', 'doc_secret', 'doc_release', 'doc_urgency', 'doc_type']
const dicts = ref({})
dictsList.map(item => dicts.value[item] = [])

const showObj = reactive({
    table: true,
    showForm: false,
    showHistory: false
})
const componentsData = reactive({
    formDetail: {
        businessId: '',
        processInstanceId: '',
        taskId: '',
        nodeId: ''
    },
    history: {
        businessId: '',
    }
})
const goBack = (e) => {
    showObj[e] = false
    showObj.table = true
    queryParams.value.pageNum = 1;
    getList()
}

const data = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        type: undefined,
        typeName: undefined,
        title: undefined,
        number: undefined,//案卷号
        receivingTime: undefined,//收文日期
        receiveUnit: undefined,//来文单位
        receiveType: undefined,//来文类别
        urgent: undefined,//	急缓
    },
});
const total = ref(0);
const { queryParams } = toRefs(data);

const receiptList = ref([]);
const nodeList = ref([]);
const activeNames = ref(0);
const loading = ref(true);

function getList() {
    loading.value = true;
    getDonebox(queryParams.value).then(response => {
        receiptList.value = response.rows;
        total.value = response.total;
        loading.value = false;
    });
}
/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

function gotoDetail(row) {
    showObj.table = false
    showObj.showForm = row.type
    componentsData.formDetail.businessId = row.businessId
    componentsData.formDetail.processInstanceId = row.processInstanceId
    componentsData.formDetail.taskId = row.taskId
    componentsData.formDetail.nodeId = row.nodeId
}
function gotoHistory(row) {
    showObj.showHistory = true
    componentsData.history.businessId = row.businessId
}


getList()
onMounted(() => {
    dictsList.map(item => {
        getDicts(item).then(res => {
            dicts.value[item] = res.data
        })
    })
})
</script>
<style scoped lang="scss">
@import "@/styles/variables.module.scss";;

.box {
    height: $contentHeight;
    background-color: #E9EEF3;
    overflow-y: scroll;
    overflow-x: hidden;
}

.container {
    padding: 10px 20px;
}

.search-content {
    background-color: #fff;
    padding: 10px 10px;
    border-radius: 10px;
}
</style>
