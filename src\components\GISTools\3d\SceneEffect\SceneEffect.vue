<template>
  <div v-if="isShow" class="content-wrap">
    <div class="tab-container">
      <el-button type="primary" @click="startSceneEffect">
        <span class="sight-btn">扫描特效</span>
      </el-button>
      <el-button type="primary" @click="rotateSceneEffect">
        <span class="sight-btn">旋转特效</span>
      </el-button>
      <el-button type="primary" @click="clearResult">
        <svg-icon icon-class="clear" />
        <span class="sight-btn">清除</span>
      </el-button>
    </div>
  </div>
</template>

<script setup name="SceneEffect">
import useMapViewStore from "@/store/modules/map/mapView.js"
import { setLayerSelectStatus, setSceneEffect } from "@/utils/Cesium/CesiumTool.js"


defineProps({
  isShow: {
    type: Boolean,
    default: true
  },
  headerInfo: {
    type: Object
  }
})

const position = ref(undefined)


const viewer3d = computed(()=>useMapViewStore().viewer3d)

const emits = defineEmits(['closePanel'])

const closePanel = ()=>{
  emits("closePanel")
}

/**
 * 开启场景特效
 */
const startSceneEffect = ()=>{
  const scene = viewer3d.value.scene
  position.value = Cesium.Cartesian3.fromDegrees(102.840745, 24.859662, 1928.049976)
  // 开启环状扫描效果
  scene.scanEffect.show = true
  // 圆环状扫描模式
  scene.scanEffect.mode = Cesium.ScanEffectMode.CIRCLE
  scene.scanEffect.centerPostion = position.value
  // 获取或设置扫描线的运行周期，单位：秒。
  scene.scanEffect.period = 1
  // 获取或设置线状扫描线的宽度，单位：米。
  scene.scanEffect.lineWidth = 50
  // scene.scanEffect.color = SuperMap3D.Color.CORNFLOWERBLUE
  scene.scanEffect.color = Cesium.Color.DODGERBLUE
}

/**
 * 旋转特效
 */
const rotateSceneEffect = ()=>{
  const scene = viewer3d.value.scene
  // 设置场景参数
  scene.sun.brightness = 0.0
  scene.shadowMap.darkness = 0
  scene.sun.show = false
  scene.skyAtmosphere.show = false
  scene.skyAtmosphere.brightnessShift = -0.5; // 减少大气光的强度
  scene.skyAtmosphere.saturationShift = -5.0; // 减少大气光的饱和度

  // 设置图层亮度
  const layers = scene.layers._layers._array;
  for (let i = 0; i < layers.length; i++) {
    if (layers[i].brightness >= 0.5) {
      layers[i].brightness -= 0.1;
    }
  }

  const position = Cesium.Cartesian3.fromDegrees(102.839122, 24.859401, 1926.886042)
  const flyCirclePos = Cesium.Cartesian3.fromDegrees(102.840957, 24.859585, 1928.180724)
  // 关闭环状扫描效果
  scene.scanEffect.show = false
  // 设置循环
  viewer3d.value.camera.flyCircle(flyCirclePos)
  viewer3d.value.camera.flyCircleLoop = true
  // 开启特效
  scene.scanEffect.show = true;
  // 开启线状扫描效果
  scene.scanEffect.mode = Cesium.ScanEffectMode.LINE;

  // 特效参数设置
  scene.scanEffect.centerPostion = position;
  scene.scanEffect._speed = 500;
  scene.scanEffect._lineWidth = 200;
  scene.scanEffect._period = 2000;
  scene.scanEffect.color = Cesium.Color.WHITE;
}
/**
 * 清除结果
 */
const clearResult = ()=> {
  // 关闭环状扫描效果
  viewer3d.value.scene.scanEffect.show = false
  // 停止旋转
  viewer3d.value.camera.stopFlyCircle()
  // 恢复场景设置
  setSceneEffect(viewer3d.value)
}

onBeforeUnmount(()=>{
  clearResult();
  setLayerSelectStatus(viewer3d.value,true)
})
</script>

<style scoped lang="scss">
.content-wrap {
  background: #193b51;
  color: #fff;
  box-shadow: 0 0 8px 0 #057595;
}

:deep(.el-form-item__label) {
  width: 60px !important;
  color: #b5b5b5;
}
.tab-container {
  display: flex;
  padding: 10px;
  flex-direction: column;
  column-count: 2;
}
.tool-btn {
  margin-top: 20px;
  padding: 10px;
  width: 48%;
  background-color: #00cffa;
  border-color: #fff;
  text-align: center;
  border-radius: 4px;
  color: #ffffff;
  &:hover {
    cursor: pointer;
    color: #fff;
    filter: brightness(110%) opacity(100%);
    transition: all 0.5s ease-in;
    background: linear-gradient(to bottom right, #00baff, #00cffa);
  }
  svg {
    margin-right: 10px;
  }
}
.sight-btn {
  margin-left: 5px;
}
.el-button--primary {
  margin: 10px;
}
</style>
