# 页面标题
VITE_APP_TITLE = "储备项目与地块巡查管理系统"

# 生产环境配置
VITE_APP_ENV = 'production'
# .env.development
VITE_APP_MODE = 'production'

# 生产环境公共API前缀
VITE_APP_BASE_API = '/land-prod-api'

# 服务器地址
VITE_APP_BASE_URL = "*************:8080"

# 生产环境应用访问路径，例如使用前缀 /admin/
VITE_APP_CONTEXT_PATH = '/land/'

# 是否在打包时开启压缩，支持 gzip 和 brotli
VITE_BUILD_COMPRESS = gzip

# 客户端id
VITE_APP_CLIENT_ID = 'e5cd7e4891bf95d1d19206ce24a7b32e'

# 文件服务器
# VITE_APP_FILE_SERVICE_URL = 'http://*************:19000/test'

# 文件预览
VITE_APP_FILE_PERVIEW_URL = 'http://*************:8012/onlinePreview?url='
# 在线编辑服务器
VITE_APP_OFFICE_URL = 'http://************:8088/'

# 天地图token
VITE_APP_TDT_TOKEN = "fa7ec9766b2c00747e3dd60ab3d05892"

# ISERVER 服务地址
VITE_APP_ISERVER_URL = 'http://***********:8090/'

# GEOSERVER 服务地址
VITE_APP_GEOSERVER_URL = "http://*************:8000/geoserver/gwc/service/wmts"


