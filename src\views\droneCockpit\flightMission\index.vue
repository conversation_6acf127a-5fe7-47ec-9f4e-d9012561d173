<template>
  <div class="flight-task-container">
    <!-- 默认视图 - 任务列表 -->
    <div
      v-if="showDetail"
      class="task-list-view"
    >
      <!-- 搜索区域 -->
      <transition name="el-fade-in">
        <el-card
          v-show="showSearch"
          class="search-card"
        >
          <el-form
            ref="queryFormRef"
            :model="queryParams"
            :inline="true"
            label-width="100px"
          >
            <el-row
              :gutter="20"
              class="five-column-row"
            >
              <!-- Task Name -->
              <el-col :span="4">
                <el-form-item
                  label="任务名称"
                  prop="name"
                  class="form-item-compact serach"
                >
                  <el-input
                    class="form-control"
                    v-model="queryParams.name"
                    placeholder="请输入任务名称"
                    clearable
                    @keyup.enter="handleQuery"
                  />
                </el-form-item>
              </el-col>

              <!-- Status -->
              <el-col :span="5">
                <el-form-item
                  label="任务状态"
                  prop="status"
                  class="form-item-compact serach"
                >
                  <el-select
                    class="form-control"
                    v-model="queryParams.status"
                    placeholder="请选择任务状态"
                    clearable
                  >
                    <el-option
                      v-for="item in statusList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>

              <!-- Date Range -->
              <el-col :span="10">
                <el-form-item
                  label="查询时间范围"
                  class="date-range-item serach"
                >
                  <div class="date-range-container">
                    <el-date-picker
                      v-model="queryParams.searchStartTime"
                      type="datetime"
                      placeholder="开始时间"
                      value-format="YYYY-MM-DD HH:mm:ss"
                      class="date-picker"
                    />
                    <span
                      class="date-separator"
                      style="margin-left: 10px; margin-right: 10px"
                    >至</span>
                    <el-date-picker
                      v-model="queryParams.searchEndTime"
                      type="datetime"
                      placeholder="结束时间"
                      value-format="YYYY-MM-DD HH:mm:ss"
                      class="date-picker"
                    />
                  </div>
                </el-form-item>
              </el-col>

              <!-- Actions 列：使用 offset 占据剩余空间 -->
              <el-col :span="5">
                <el-form-item class="form-actions">
                  <el-button
                    type="primary"
                    icon="Search" @click="handleQuery"
                  >搜索</el-button>
                  <el-button
                    icon="Refresh"
                    @click="resetQuery"
                  >重置</el-button>
                  <el-button
                    icon="Refresh"
                    @click="getList"
                  >刷新</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </transition>
      <!-- 结果表格 -->
      <el-card class="result-card">
        <el-table
          v-loading="loading"
          :data="flightHub2EndpointList"
          style="width: 100%"
          row-key="uuid"
        >
          <el-table-column
            label="任务名称"
            prop="name"
            align="center"
            min-width="200"
          />
          <el-table-column
            label="状态"
            prop="status" align="center"
          >
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)">
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="设备SN"
            prop="sn"
            align="center"
            width="250"
          />
          <el-table-column
            label="断点续飞状态"
            prop="resumableStatus"
            align="center"
          />
          <el-table-column
            label="飞完的航点数量"
            prop="currentWaypointIndex"
            align="center"
            width="150"
          />
          <el-table-column
            label="开始时间"
            prop="beginAt"
            align="center"
            width="240"
          >
            <template #default="{ row }">
              {{ formatDate(row.beginAt) }}
            </template>
          </el-table-column>
          <el-table-column
            label="完成时间"
            prop="completedAt"
            align="center"
            width="240"
          >
            <template #default="{ row }">
              {{ formatDate(row.completedAt) }}
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            fixed="right" align="center"
          >
            <template #default="{ row }">
              <el-button
                link
                type="primary" @click="viewDetail(row)"
              >
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>

    <!-- 任务详情视图 -->
    <div
      v-else-if="!showDetail"
      class="task-detail-view"
    >
      <!-- <div class="detail-header">
        <h2>任务详情</h2>
        <el-button  @click="showDetail = true">返回列表</el-button>
      </div> -->
      <div class="add-header-title">
        <div class="add-title">{{ RwmcTitle }}</div>
        <div
          class="add-title-return"
          @click="showDetail = true"
        >
          <img
            src="@/assets/images/img-return.png"
            class="back"
          >
          <div class="backlist">返回列表</div>
        </div>
      </div>
      <div class="task-information">
        <el-card class="info-card">
          <div class="section-header">
            <div class="content-title-1">
              <img src="@/assets/images/left.png" >
              <p>基本信息</p>
            </div>
          </div>
          <el-descriptions
            title=""
            :column="1" border
          >
            <el-descriptions-item label="任务名称">{{
              taskDetail.name
            }}</el-descriptions-item>
            <el-descriptions-item label="任务类型">{{
              taskDetail.taskType
            }}</el-descriptions-item>
            <el-descriptions-item label="设备SN">{{
              taskDetail.sn
            }}</el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="getStatusTagType(taskDetail.status)">
                {{ getStatusLabel(taskDetail.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="开始时间">{{
              formatDate(taskDetail.beginAt)
            }}</el-descriptions-item>
            <el-descriptions-item label="结束时间">{{
              formatDate(taskDetail.endAt)
            }}</el-descriptions-item>
            <el-descriptions-item label="任务重复模式">{{
              taskDetail.repeatType
            }}</el-descriptions-item>
            <el-descriptions-item label="自动断点续飞">{{
              taskDetail.resumableStatus
            }}</el-descriptions-item>
            <el-descriptions-item label="返航模式">{{
              taskDetail.rthMode
            }}</el-descriptions-item>
            <el-descriptions-item label="丢失信号后无人机动作">{{
              taskDetail.outOfControlActionInFlight
            }}</el-descriptions-item>
            <el-descriptions-item label="每月第几周">{{
              taskDetail.weekOfMonth
            }}</el-descriptions-item>

            <el-descriptions-item label="飞行器SN">{{
              locusDetail?.track?.droneSn
            }}</el-descriptions-item>
            <el-descriptions-item label="飞行时长">{{
              locusDetail?.track?.flightDuration
            }}</el-descriptions-item>
            <el-descriptions-item label="飞行距离">{{
              locusDetail?.track?.flightDistance
            }}</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <el-card class="track-card">
          <div class="section-header">
            <div class="content-title-1">
              <img src="@/assets/images/left.png" >
              <p>轨迹点</p>
            </div>
          </div>
          <el-descriptions
            title=""
            :column="2" border
          />
          <!-- 添加容器包裹Viewer3d并限制尺寸 -->
          <div class="viewer-container">
            <Viewer3d
              ref="viewer3d"
              @onCreateViewer3d="createViewer3d"
            />
          </div>
        </el-card>
      </div>
      <el-card class="detail-card">
        <div class="section-header">
          <div class="content-title-1">
            <img src="@/assets/images/left.png" >
            <p>媒体文件列表</p>
          </div>
        </div>
        <el-descriptions
          title=""
          :column="2" border
        />
        <el-table
          :data="mediaList"
          v-loading="loading"
          v-infinite-scroll="handleLoadMore"
          :infinite-scroll-disabled="disabled"
          :infinite-scroll-distance="100"
          :infinite-scroll-immediate="false"
          :infinite-scroll-delay="200"
          class="media-table"
        >
          <el-table-column
            label="文件名"
            align="center" prop="name"
          />
          <el-table-column
            label="类型"
            align="center"
            prop="fileType"
            width="180"
          />
          <el-table-column
            label="大小"
            align="center" width="180"
          >
            <template #default="{ row }">
              {{ formatFileSize(row.size) }}
            </template>
          </el-table-column>
          <el-table-column
            label="预览"
            align="center" width="180"
          >
            <template #default="{ row }">
              <!-- 图片预览 -->
              <el-image
                v-if="row.fileType === 'image'"
                style="width: 40px; height: 40px"
                :src="row.previewUrl"
                @click="handleImagePreview(row.originalUrl)"
                :preview-teleported="true"
                referrerpolicy="no-referrer"
              >
                <template #error>
                  <div class="image-error">
                    <el-icon><Picture /></el-icon>
                  </div>
                </template>
              </el-image>
              <div v-if="row.fileType === 'video'">
                <el-image
                  style="width: 40px; height: 40px"
                  :src="row.previewUrl"
                  hide-on-click-modal
                  :preview-teleported="true"
                  referrerpolicy="no-referrer"
                  @click="handleVideoPreview(row.originalUrl)"
                >
                  <template #error>
                    <div class="image-error">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="创建时间"
            align="center" width="280"
          >
            <template #default="{ row }">
              {{ formatDate(row.createAt) }}
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="100"
            fixed="right"
          >
            <template #default="scope">
              <el-tooltip
                content="下载"
                placement="top"
              >
                <!-- 使用 <a> 标签包裹按钮，并设置 download 属性 -->
                <a
                  referrerpolicy="no-referrer"
                  :href="scope.row.originalUrl"
                  download
                  target="_blank"
                  style="text-decoration: none; color: inherit"
                >
                  <el-button
                    link
                    type="primary" icon="Download"
                  />
                </a>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, toRaw } from "vue";
import { Picture, Document, Back } from "@element-plus/icons-vue";
import {
  flightTaskListAll,
  getFlightTask,
  getFlightTaskMedia,
  getFlightTaskTrack
} from "@/api/uav/flightHub2/flight_task";
import { STATUS_LIST, TASK_TYPE_LIST } from "@/constants/flightTask.js";
import UseViewer3d from "@/components/GISTools/Viewer/UseViewer3d.js";
import useMapViewStore from "@/store/modules/map/mapView.js";
import Viewer3d from "@/components/GISTools/Viewer/Viewer3d.vue"
import {
  formatDate,
  getMonthFirstDayWithTime,
  getMonthLastDayWithTime
} from "@/constants/flightUtils.js";
// 用户信息
const store = useMapViewStore();
// 状态管理
const showDetail = ref(true);
const loading = ref(false);
const showSearch = ref(true);
const total = ref(0);
const flightHub2EndpointList = ref([]);
const mediaList = ref([]);
const taskDetail = ref({});
const locusDetail = ref({});
const loadingMore = ref(false);
const mediaPageNum = ref(1);
const mediaPageSize = 10;
const mediaTotal = ref(0);
// 计算属性：判断是否还有更多数据可以加载
// 当 mediaList 当前长度 < 服务器返回的总数时，表示还有更多数据
const hasMore = computed(() => mediaList.value.length < mediaTotal.value);

// 计算属性：判断是否应该禁用无限滚动加载
// 满足以下任一条件时禁用：
// 1. 主加载中(loading.value)
// 2. 正在加载更多(loadingMore.value)
// 3. 没有更多数据可加载(!hasMore.value)
const disabled = computed(
  () => loading.value || loadingMore.value || !hasMore.value
);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  sn: "",
  name: "",
  status: "",
  taskType: "",
  searchStartTime: "2025-04-1 00:00:00",
  searchEndTime: "2025-04-30 00:00:00"
});

//详情页动态表头
const RwmcTitle = computed(() => {
  if (!taskDetail.value.name) return "任务详情";
  return `${taskDetail.value.name}任务详情 `;
});
// 常量
const statusList = ref(STATUS_LIST);

// 状态管理
const viewer3d = reactive({}); // 兼容原有代码

// 状态映射表（可单独抽成 config.js 复用）
const STATUS_MAP = {
  成功: 'success',
  执行中: 'primary',
  终止: 'danger',
  超时: 'danger',
  启动失败: 'danger',
  挂起: 'warning',
  暂停: 'warning',
  待开始: 'info'
};

/**
 * 获取状态标签类型（用于 el-tag type 属性）
 * @param {string} [status=''] - 状态中文名
 * @returns {string} - Element Plus 支持的 tag 类型
 */
const getStatusTagType = (status = '') => {
  return STATUS_MAP[status] || 'info'; // 默认使用 'info' 更安全
};

// 添加特殊标记点
const addSpecialPoint = (viewer, point, text, color) => {
  viewer.entities.add({
    id: `track-${text}`,
    position: Cesium.Cartesian3.fromDegrees(
      point.longitude,
      point.latitude,
      point.height
    ),
    point: {
      pixelSize: 15,
      color: color,
      outlineColor: Cesium.Color.WHITE,
      outlineWidth: 3
    },
    label: {
      text: text,
      font: "14px sans-serif",
      fillColor: color,
      style: Cesium.LabelStyle.FILL_AND_OUTLINE,
      outlineWidth: 2,
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      pixelOffset: new Cesium.Cartesian2(0, -15)
    }
  });
};

// 定位到轨迹范围
const zoomToTrack = (viewer, positions) => {
  const boundingSphere = Cesium.BoundingSphere.fromPoints(positions);
  boundingSphere.radius *= 1.2; // 添加20%边距

  viewer.camera.flyToBoundingSphere(boundingSphere, {
    duration: 2,
    offset: new Cesium.HeadingPitchRange(
      0,
      Cesium.Math.toRadians(-30),
      boundingSphere.radius * 2
    )
  });
};

/**
 * 响应数据
 */
const isShowResource = ref(false);
const viewer3dProp = reactive({});

/**
 * 监听数据，监听视图对象值：当viewer3d值为真时，显示资源组件
 * 目前使用父组件传递属性不成功，只能使用provide,inject进行传递
 */
watch(()=>viewer3dProp.viewer3d,(newValue,oldValue) => {
  if (Object.keys(newValue).length) {
    isShowResource.value = true
  }
})
provide("viewer3d", viewer3dProp);

const createViewer3d = async (viewerId, mapInitStore, mapViewStore) => {
  console.log("rowUuid", toRaw(rowUuid.value));
  const trackRes = await getFlightTaskTrack(toRaw(rowUuid.value));
  locusDetail.value = trackRes.data;

  const useViewer3d = new UseViewer3d(viewerId, mapInitStore, mapViewStore);
  await useViewer3d.createViewer3d();
  const viewer3d = useViewer3d.viewer3d;
  const points = toRaw(locusDetail.value).track.points;

  showTrackData(viewer3d, points);
  Object.assign(viewer3dProp, { viewer3d });
};

// 显示轨迹数据
const showTrackData = (viewer, points) => {
  if (!viewer || !points?.length) return;
  // 转换坐标
  const positions = points.map((p) =>
    Cesium.Cartesian3.fromDegrees(p.longitude, p.latitude, p.height)
  );

  // 添加轨迹线
  viewer.entities.add({
    id: "track-line",
    polyline: {
      positions: positions,
      width: 5,
      material: new Cesium.PolylineGlowMaterialProperty({
        glowPower: 0.2,
        color: Cesium.Color.CYAN
      }),
      clampToGround: false
    }
  });

  // 添加轨迹点标记
  points.forEach((point, index) => {
    if (index % 1 === 0) {
      viewer.entities.add({
        id: `track-point-${index}`,
        position: positions[index],
        point: {
          pixelSize: 10,
          color: Cesium.Color.RED,
          outlineColor: Cesium.Color.WHITE,
          outlineWidth: 2
        },
        label: {
          text: `${point.timestamp.split(" ")[1]}`,
          font: "12px sans-serif",
          fillColor: Cesium.Color.WHITE,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          outlineWidth: 2,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          pixelOffset: new Cesium.Cartesian2(0, -10)
        }
      });
    }
  });

  // 添加起终点标记
  addSpecialPoint(viewer, points[0], "起点", Cesium.Color.GREEN);
  addSpecialPoint(viewer, points[points.length - 1], "终点", Cesium.Color.RED);

  // 定位到轨迹
  zoomToTrack(viewer, positions);
};
const rowUuid = ref("");

// 查看详情
const viewDetail = async (row) => {
  rowUuid.value = row.uuid;
  try {
    loading.value = true;
    showDetail.value = false;
    const detailRes = await getFlightTask(row.uuid)
    taskDetail.value = detailRes.data;

    const mediaRes = await getFlightTaskMedia(row.uuid)
    mediaList.value = mediaRes.rows.filter(item =>
      item.fileType === 'video' || item.fileType === 'image'
    );
    mediaTotal.value = mediaRes.total;
    loading.value = false;
  } catch (e) {
    console.log(e);
  }
};

// 格式化文件大小（字节转为可读格式）
const formatFileSize = (bytes) => {
  if (bytes === 0) return "0 B"; // 处理0字节的情况
  const k = 1024; // 进制基数
  const sizes = ["B", "KB", "MB", "GB"]; // 单位数组
  // 计算对数确定单位级别（取整）
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  // 转换数值并保留2位小数，拼接单位
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

// 无限滚动加载更多处理函数
const handleLoadMore = async () => {
  // 如果正在加载或已无更多数据，则终止
  if (disabled.value) return;

  try {
    loadingMore.value = true; // 标记加载状态
    mediaPageNum.value++; // 页码自增

    // 请求下一页媒体数据
    const res = await getFlightTaskMedia(taskDetail.value.uuid, {
      pageNum: mediaPageNum.value,
      pageSize: mediaPageSize
    });

    // 合并新旧数据（数组解构拼接）
    mediaList.value = [...mediaList.value, ...res.rows];
    mediaTotal.value = res.total; // 更新总数
  } finally {
    loadingMore.value = false; // 无论成功失败都解除加载状态
  }
};

// 获取状态显示文本
const getStatusLabel = (status) => {
  const item = statusList.value.find((item) => item.value === status);
  return item?.label || status;
};

// 获取任务列表
const getList = async () => {
  loading.value = true;
  try {
    const res = await flightTaskListAll(queryParams);
    flightHub2EndpointList.value = res.rows;
    total.value = res.total;
  } finally {
    loading.value = false;
  }
};

// 获取媒体列表
const handleVideoPreview = (previewUrl) => {
  // 关键修复：必须将replace()的结果赋值给变量
  const playableUrl = previewUrl
    .replace("response-content-disposition=attachment", "")
    .replace(/;filename.*?(?=&|$)/, "");
  console.log("playableUrl", playableUrl);
  window.open(playableUrl, "_blank", "noopener,noreferrer");
};

// 获取图片安全预览地址
const handleImagePreview = (previewUrl) => {
  // 关键修复：必须将replace()的结果赋值给变量
  const playableUrl = previewUrl
    .replace("response-content-disposition=attachment", "")
    .replace(/;filename.*?(?=&|$)/, "");
  window.open(playableUrl, "_blank", "noopener,noreferrer");
};

// 搜索
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

// 重置
const resetQuery = () => {
  queryParams.pageNum = 1;
  queryParams.sn = "";
  queryParams.name = "";
  queryParams.status = "";
  queryParams.taskType = "";
  queryParams.searchStartTime = "2025-04-01 00:00:00";
  queryParams.searchEndTime = "2025-04-30 00:00:00";
  getList();
};

onMounted(() => {
  queryParams.searchStartTime = getMonthFirstDayWithTime();
  queryParams.searchEndTime = getMonthLastDayWithTime();
  getList();
});
</script>

<style scoped>
.form-control {
  width: 100%;
  min-width: 180px;
  max-width: 200px;
}
.serchtop {
  margin-bottom: 0 !important;
}
/* Or for responsive full width */
.form-item-compact :deep(.el-form-item__content) {
  width: 100%;
}

.form-item-compact :deep(.el-input),
.form-item-compact :deep(.el-select),
.form-item-compact :deep(.el-input__wrapper) {
  width: 100%;
}

.search-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: var(--el-box-shadow-light);
}

/* Form item styling */
.form-item-compact :deep(.el-form-item__content) {
  width: 100%;
}

.form-item-compact :deep(.el-input),
.form-item-compact :deep(.el-select) {
  width: 100%;
}

/* Date range specific styling */
.date-range-item :deep(.el-form-item__content) {
  display: flex;
  width: 100%;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .form-actions {
    justify-content: flex-start;
  }
}

/* Button actions */
.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-left: 0;
}

.form-actions .el-button {
  margin-left: 8px;
}
.result-card {
  margin-top: 10px;
}
.flight-task-container {
  padding: 20px;
  overflow-y: auto;
  height: 85vh;
}

/* Task Detail View Container */
.task-detail-view {
  display: flex;
  flex-direction: column;
  padding: 16px;
  background: #ffffff;
}

.detail-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0;
}

.detail-header .el-button {
  padding: 8px 16px;
}

.detail-card :deep(.el-card__header) {
  padding: 16px 20px;
  /* background-color: var(--el-fill-color-light); */
  border-bottom: 1px solid var(--el-border-color-light);
}

.detail-card :deep(.el-card__body) {
  padding: 20px;
}

/* Descriptions Styles */
.detail-card :deep(.el-descriptions) {
  margin-top: 10px;
}

.detail-card :deep(.el-descriptions__title) {
  font-size: 18px;
  font-weight: 500;

  color: var(--el-text-color-primary);
}

.detail-card :deep(.el-descriptions__label) {
  width: 180px;
  font-weight: 500;
  color: var(--el-text-color-secondary);
}

.detail-card :deep(.el-descriptions__content) {
  color: var(--el-text-color-primary);
}

.viewer-container {
  /* 关键样式设置 */
  width: 100%;
  height: 550px; /* 给地图指定高度，小于卡片高度 */
  position: relative; /* 确保Cesium能正确渲染 */
  overflow: hidden; /* 防止内容溢出 */
}

/* 如果需要响应式高度 */
@media (max-width: 768px) {
  .viewer-container {
    height: 300px;
  }
}

.media-table :deep(.el-table__header) th {
  background-color: var(--el-fill-color-light);
  font-weight: 600;
}

.media-table :deep(.el-table__row) {
  transition: background-color 0.2s ease;
}

.media-table :deep(.el-table__row:hover) {
  background-color: var(--el-color-primary-light-9);
}

.image-error {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: var(--el-fill-color-light);
  color: var(--el-color-danger);
  border-radius: 4px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .task-detail-view {
    padding: 12px;
  }

  .detail-card :deep(.el-descriptions) {
    :deep(.el-descriptions-item) {
      display: block;
      width: 100%;
    }

    :deep(.el-descriptions__label) {
      width: 100%;
      margin-bottom: 4px;
    }
  }
}

.add-header-title {
  padding: 10px;
  display: flex;
  justify-content: space-between;
  height: 50px;
  font-weight: bold;
  background-color: rgb(222, 239, 255);
  box-sizing: border-box;
  border-bottom: 1px solid rgb(233, 233, 233);
  font-weight: 700;
  font-size: 16px;
  line-height: 28px;
}

.add-title-return {
  display: flex;
  align-content: center;
  color: rgb(32, 119, 255);
  cursor: pointer;
  font-weight: normal;
}

.back {
  height: 18px;
  width: 18px;
  margin-top: 5px;
}

.backlist {
  padding-left: 6px;
  font-size: 14px;
}
.task-information {
  display: flex;
  gap: 20px; /* 卡片间距 */
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
  box-shadow: var(--el-box-shadow-light);
  border-top: none;
}
.info-card,
.track-card {
  flex: 1; /* 平均分配宽度 */
  min-width: 0; /* 防止内容溢出 */
  box-shadow: none;
  border: none;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .task-information {
    flex-direction: column;
  }

  .info-card,
  .track-card {
    width: 100%;
  }
}
.section-header {
  display: flex;
  align-items: center;
}
.content-title-1 {
  font-weight: bold;
  display: flex;
  align-items: center;
  p {
    margin-left: 8px;
  }
}
:deep(.serach) {
  margin-right: 1px;
}
</style>
