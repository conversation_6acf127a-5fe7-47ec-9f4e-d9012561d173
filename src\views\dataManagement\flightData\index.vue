<template>
  <div class="container">
    <div
      v-if="!showDetail"
      class="project-list"
    >
      <transition name="fade">
        <div  class="search-container">
          <el-card
            shadow="hover"
            class="search-card"
          >
            <el-form
              ref="queryFormRef"
              :model="queryParams" :inline="true"
            >
              <el-form-item
                label="无人机sn"
                prop="sn" class="serchtop"
              >
                <el-input
                  v-model="queryParams.sn"
                  placeholder="请输入项目名称" clearable @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item
                label="任务名称"
                prop="taskName" class="serchtop"
              >
                <el-input
                  v-model="queryParams.taskName"
                  placeholder="请输入经度" clearable @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item class="serchtop">
                <el-button
                  type="primary"
                  icon="Search" @click="handleQuery"
                >搜索</el-button>
                <el-button
                  icon="Refresh"
                  @click="resetQuery"
                >重置</el-button>
                <el-button
                  icon="Upload"
                  @click="handleExport"
                >导出</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </div>
      </transition>

      <el-card
        shadow="never"
        class="result-card"
      >
        <el-table
          v-loading="loading"
          :data="reserveProjectList"  style="width: 100%" stripe
        >
          <el-table-column
            label="执行设备名称"
            align="center" prop="snName" min-width="200"
          />
          <el-table-column
            label="任务名称"
            align="center" prop="taskName" width="200"
          />
          <el-table-column
            label="无人机sn"
            align="center" prop="sn" width="160"
          />
          <el-table-column
            label="任务类型"
            align="center" prop="taskType" width="120"
          />
          <el-table-column
            label="任务状态"
            align="center" prop="taskStatus" width="120"
          />
          <el-table-column
            label="开始时间"
            align="center" prop="beginAt" width="220"
          />
          <el-table-column
            label="结束时间"
            align="center" prop="endAt" min-width="200"
          />
          <el-table-column
            label="任务产生的媒体数量"
            align="center" prop="expectedFileCount" min-width="200"
          />
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
          class="pagination-container"
        />
      </el-card>
    </div>

    <div
      v-else
      class="project-detail"
    >
      <div class="detail-header">
        <div class="add-header-title">
          <div
            class="add-title"
            style="margin-right:1200px"
          >{{ FXheaderTitle }}</div>
          <div
            class="add-title-return"
            @click="cancel"
          >
            <img
              src="@/assets/images/img-return.png"
              class="back"
            >
            <div class="backlist">返回列表</div>
          </div>
        </div>
      </div>
      <div class="button-style">
        <el-button
          plain
          type="success"
          @click="DataModelingClick"
          class="action-button"
        >
          数据建模
        </el-button>
        <el-button
          plain
          type="primary" @click="cancel" class="action-button"
        >
          拉取飞行数据
        </el-button>
        <el-button
          plain
          type="warning" @click="cancel" class="action-button"
        >
          下载飞行数据
        </el-button>
      </div>

      <el-card
        shadow="never"
        class="onetable"
      >
        <el-descriptions
          :column="2"
          border class="nn"
        >
          <el-descriptions-item label="项目名称">{{
            projectInfo.name
          }}</el-descriptions-item>
          <el-descriptions-item label="任务名称">{{
            projectInfo.createdAt
          }}</el-descriptions-item>
          <el-descriptions-item label="无人机型号">{{
            projectInfo.longitude
          }}</el-descriptions-item>
          <el-descriptions-item label="执飞时间">{{
            projectInfo.updatedAt
          }}</el-descriptions-item>
          <el-descriptions-item label="飞行状态">{{
            projectInfo.latitude
          }}</el-descriptions-item>
          <el-descriptions-item label="航线">
            <el-button
              link
              type="primary" @click="cancel" class="back-button"
            >
              航电航线
            </el-button>
          </el-descriptions-item>

          <el-descriptions-item label="航线长度">{{
            projectInfo.longitude
          }}</el-descriptions-item>
          <el-descriptions-item label="预计执行时间">{{
            projectInfo.updatedAt
          }}</el-descriptions-item>
          <el-descriptions-item label="航点">{{
            projectInfo.latitude
          }}</el-descriptions-item>
          <el-descriptions-item label="照片">{{
            projectInfo.description
          }}</el-descriptions-item>

          <el-descriptions-item label="媒体文件类型">{{
            projectInfo.longitude
          }}</el-descriptions-item>
          <el-descriptions-item label="媒体文件的后缀">{{
            projectInfo.updatedAt
          }}</el-descriptions-item>
          <el-descriptions-item label="媒体文件大小">{{
            projectInfo.latitude
          }}</el-descriptions-item>
          <el-descriptions-item label="预览图片">
            <el-button
              link
              type="primary" @click="cancel" class="back-button"
            >
              查看
            </el-button>
            <el-button
              link
              type="primary" @click="cancel" class="back-button"
            >
              下载
            </el-button>
          </el-descriptions-item>

          <el-descriptions-item label="媒体文件创建时间">{{
            projectInfo.latitude
          }}</el-descriptions-item>
          <el-descriptions-item label="媒体文件更新时间">{{
            projectInfo.description
          }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
    </div>

    <el-dialog
      title="数据建模"
      v-model="dialog.visible" width="680px" append-to-body
      class="advanced-config-dialog"
    >
      <el-form
        ref="flightHub2EndpointFormRef"
        :model="form" :rules="rules" label-width="120px"
        label-position="top"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <h3
            class="section-title"
            style="color: #409eff;"
          >数据建模</h3>
          <el-row :gutter="20">
            <el-col :span="12" >
              <el-form-item
                label="重建名称"
                prop="endpointName" class="table-name"
              >
                <el-input
                  v-model="form.endpointName"
                  placeholder="请输入接入点名称" clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="重建照片"
                prop="endpointUrl"  class="table-name"
              >
                <el-input
                  v-model="form.endpointUrl"
                  placeholder="请输入接入点URL" clearable
                >
                  <template #append>
                    <el-button
                      icon="Plus"
                      @click="testConnection"
                    />
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 重建设置 -->
        <div class="form-section">
          <h3 class="section-title">重建设置</h3>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item
                label="重建类型"
                prop="reconstructionType"  class="table-name"
              >
                <el-radio-group v-model="form.reconstructionType">
                  <el-radio-button label="3d">三维重建</el-radio-button>
                  <el-radio-button label="2d">二维重建</el-radio-button>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="重建场景"
                prop="sceneType"  class="table-name"
              >
                <el-select
                  v-model="form.sceneType"
                  placeholder="请选择场景类型" style="width: 100%"
                >
                  <el-option
                    v-for="item in sceneOptions"
                    :key="item.value" :label="item.label" :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item
            label="分辨率和速度"
            prop="quality"  class="table-name"
          >
            <div
              class="slider-container"
              style="width: 100%"
            >
              <el-slider
                v-model="form.quality"
                range :marks="{ 0: '低', 50: '', 100: '高' }" :step="10"
              />
            </div>
          </el-form-item>
        </div>

        <!-- 输出设置 -->
        <el-form-item
          label="Mesh模型"
          prop="meshFormat"  class="table-name"
        >
          <div
            class="format-options"
            width="100px"
          >
            <el-switch v-model="form.enableMesh" />
            <el-radio-group
              v-model="form.meshFormat"
              :disabled="!form.enableMesh" class="radio-group"
            >
              <el-radio-button label="b3dm">B3DM</el-radio-button>
              <el-radio-button label="osgb">OSGB</el-radio-button>
              <el-radio-button label="ply">PLY</el-radio-button>
              <el-radio-button label="obj">OBJ</el-radio-button>
            </el-radio-group>
          </div>
        </el-form-item>

        <el-form-item
          label="点云数据"
          prop="pointCloudFormat"  class="table-name"
        >
          <div class="format-options">
            <el-switch v-model="form.enablePointCloud" />
            <el-radio-group
              v-model="form.pointCloudFormat"
              :disabled="!form.enablePointCloud" class="radio-group"
            >
              <el-radio-button label="ply">PLY</el-radio-button>
              <el-radio-button label="las">LAS</el-radio-button>
              <el-radio-button label="pnts">PNTS</el-radio-button>
            </el-radio-group>
          </div>
        </el-form-item>

        <el-form-item
          label="贴近摄影粗模"
          prop="roughModel"  class="table-name"
        >
          <div class="format-options">
            <el-switch v-model="form.enableRoughModel" />
            <el-radio-group
              v-model="form.roughModelFormat"
              :disabled="!form.enableRoughModel" class="radio-group"
            >
              <el-radio-button label="ply">PLY粗模</el-radio-button>
            </el-radio-group>
          </div>
        </el-form-item>

        <!-- 高级设置 -->
        <div
          class="form-section"
          style="padding-bottom: 0;margin-bottom: 0;"
        >
          <h3 class="section-title">高级设置</h3>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item
                label="坐标系"
                prop="coordinateSystem"  class="table-name"
              >
                <el-select
                  v-model="form.coordinateSystem"
                  placeholder="请选择坐标系" style="width: 100%"
                >
                  <el-option
                    v-for="item in coordinateOptions"
                    :key="item.value" :label="item.label" :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="模型简化至"
                prop="simplification" class="table-name"
              >
                <el-input-number
                  style="width: 100%"
                  v-model="form.simplification"
                  :min="0"
                  :max="100"
                  :step="1"
                  :precision="0"
                  controls-position="right"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>

      <template #footer>
        <div class="dialog-footer" >
          <el-button @click="cancel">取消</el-button>
          <el-button
            type="primary"
            @click="submitForm" :loading="buttonLoading" icon="el-icon-check"
          >
            确认配置
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ReserveProject">
import { taskDataList } from "@/api/uav/taskData.js";
const { proxy } = getCurrentInstance();
const showDetail = ref(false);
const dialog = reactive({
  visible: false,
  title: ""
});

const coordinateOptions = ref([
  {
    value: "已知坐标系",
    label: "已知坐标系"
  },
  {
    value: "任意坐标系",
    label: "任意坐标系"
  }
]);
const sceneOptions = ref([
  {
    value: "普通场景",
    label: "普通场景"
  },
  {
    value: "环绕场景",
    label: "环绕场景"
  }
]);
const reserveProjectList = ref([]);
const loading = ref(true);
const total = ref(0);
const buttonLoading = ref(false);
const queryFormRef = ref();
const reserveProjectFormRef = ref();
const initFormData = {
  id: undefined,
  bz: undefined,
  rightBos: undefined,
  landBos: undefined
};
const data = reactive({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    params: {}
  },
  rules: {
    id: [{ required: true, message: "$comment不能为空", trigger: "blur" }]
  }
});

const { queryParams, form, rules } = toRefs(data);

// 项目信息
const projectInfo = ref({
  name: "XXX学校科技楼用地项目",
  createdAt: "2025-04-02 10:20:25",
  updatedAt: "2025-04-02 16:12:20",
  longitude: "116.290998",
  latitude: "39.956041",
  description: "-"
});

/** 查询储备项目列表 */
const getList = async () => {
  loading.value = true;
  const res = await taskDataList(queryParams.value);
  reserveProjectList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

//飞行任务表头
const FXheaderTitle = computed(() => {
  return projectInfo.value.name ?
    `${projectInfo.value.name} - 飞行任务详情` :
    "飞行任务详情";
});

const DataModelingClick = () => {
  dialog.visible = true;
};

/** 提交按钮 */
const submitForm = () => {
  flightHub2EndpointFormRef.value?.validate(async (valid) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateFlightHub2Endpoint(form.value).finally(
          () => (buttonLoading.value = false)
        );
      } else {
        await addFlightHub2Endpoint(form.value).finally(
          () => (buttonLoading.value = false)
        );
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
};

/** 取消按钮 */
const cancel = () => {
  reset();
  showDetail.value = false;
};

/** 重建照片 */
const testConnection = () => {
  reset();
  showDetail.value = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  reserveProjectFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    "uav/taskData/export",
    {
      ...queryParams.value
    },
    `patrolTask_${new Date().getTime()}.xlsx`
  );
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.advanced-config-dialog {
  :deep(.el-dialog__body) {
    max-height: 70vh;
    overflow-y: auto;
    padding: 20px;
  }

  .form-section {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px dashed #ebeef5;

    &:last-child {
      border-bottom: none;
    }
  }

  .section-title {
    color: #409eff;
    font-size: 16px;
    margin: 0 0 16px 0;
    padding-left: 8px;
    border-left: 3px solid #409eff;
  }

  .slider-container {
    padding: 0 20px;

    .slider-labels {
      display: flex;
      justify-content: space-between;
      margin-top: -10px;
      font-size: 12px;
      color: #909399;
    }
  }

  .format-options {
    display: flex;
    align-items: center;
    gap: 15px;

    .el-switch {
      margin-right: 10px;
    }

    .el-checkbox-group,
    .el-radio-group {
      flex: 1;
    }
  }

  .unit {
    margin-left: 8px;
    color: #909399;
  }

  .dialog-footer {
    display: flex;
    justify-content: center;
    padding: 10px 20px 0;
    border-top: 1px solid #ebeef5;
  }
}
.format-options {
  display: flex;
  align-items: center;
  gap: 20px;
}

.radio-group {
  margin-left: 20px;
}

/* 确保单选按钮组不会换行 */
.el-radio-group {
  white-space: nowrap;
}

/* 调整单选按钮间距 */
.el-radio-button {
  margin-right: 20px;
}
.container {
  padding: 20px;
  background-color: #f5f7fa;
}

.search-container {
  margin-bottom: 20px;

  .search-card {
    border-radius: 8px;

    :deep(.el-card__body) {
      padding: 20px;
    }
  }
}

.result-card {
  border-radius: 8px;

  :deep(.el-card__body) {
    padding: 0;
  }
}

.pagination-container {
  padding: 16px;
  text-align: right;
}

.detail-header {
  display: flex;
  background-color: rgb(222, 239, 255);
  justify-content: space-between;
  align-items: center;

  .detail-title {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
    margin: 0;
  }

  .back-button {
    border-radius: 4px;
  }
}

.detail-section {
  margin-bottom: 20px;
  border-radius: 8px;

  :deep(.el-card__body) {
    padding: 20px;
  }
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;

  .el-icon {
    margin-right: 8px;
    color: #409eff;
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

:deep(.el-descriptions) {
  margin-top: 16px;
}

:deep(.el-table) {
  margin-top: 16px;

  .el-button {
    padding: 5px 12px;
  }
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: #fafafa;
}

:deep(.el-table th.el-table__cell) {
  background-color: #f5f7fa;
}
.serchtop {
  margin-bottom: 0 !important;
}
.add-header-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  height: 50px;
  background-color: rgb(222, 239, 255);
  border-bottom: 1px solid rgb(233, 233, 233);
}

.add-title {
  font-weight: 700;
  font-size: 16px;
  line-height: 28px;
}

.add-title-return {
  display: flex;
  align-items: center;
  color: rgb(32, 119, 255);
  cursor: pointer;
  font-weight: normal;
}

.back {
  height: 18px;
  width: 18px;
  margin-right: 6px;
}

.backlist {
  font-size: 14px;
}
.project-detail {
  background: #ffffff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
.onetable {
  border: none;
  box-shadow:none
}
.action-button {
  margin-right: 10px;
}
.button-style{
  margin: 20px;
}
.nn{
  margin-top: -16px;
}
.table-name{
  display: flex;
  align-items: center !important;
  :deep(.el-form-item__label) {
    line-height: 30px !important;
    margin-bottom: 0 !important;
  }
}
.advanced-config-dialog {
  :deep(.el-dialog .el-dialog__header)  {
    background-color: #0747a8; // 使用与容器相同的背景色
    border-radius: 8px;
  }
}

</style>
