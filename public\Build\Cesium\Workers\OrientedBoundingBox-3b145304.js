define(["exports","./buildModuleUrl-9085faaa","./Cartesian2-db21342c","./Cartographic-3309dd0d","./Cartesian4-3ca25aab","./Check-7b2a090c","./when-b60132fc","./Rectangle-dee65d21","./EllipsoidTangentPlane-1dfa0a87","./Math-119be1a3","./FeatureDetection-806b12f0","./Plane-a3d8b3d2","./PolygonPipeline-d83979ed"],(function(a,t,e,n,r,i,s,o,C,d,u,c,l){"use strict";var h=[];function m(a,t){this.center=n.Cartesian3.clone(s.defaultValue(a,n.Cartesian3.ZERO)),this.halfAxes=u.Matrix3.clone(s.defaultValue(t,u.Matrix3.ZERO)),this.areaDirty=1}h[0]={num:0,des:"inside"},h[1]={num:4,data:[0,4,7,3],des:"left"},h[2]={num:4,data:[1,2,6,5],des:"right"},h[3]={num:0},h[4]={num:4,data:[0,1,5,4],des:"bottom"},h[5]={num:6,data:[0,1,5,4,7,3],des:"bottom, left"},h[6]={num:6,data:[0,1,2,6,5,4],des:"bottom, right"},h[7]={num:0},h[8]={num:4,data:[2,3,7,6],des:"top"},h[9]={num:6,data:[4,7,6,2,3,0],des:"top, left"},h[10]={num:6,data:[2,3,7,6,5,1],des:"top, right"},h[11]={num:0},h[12]={num:0},h[13]={num:0},h[14]={num:0},h[15]={num:0},h[16]={num:4,data:[0,3,2,1],des:"front"},h[17]={num:6,data:[0,4,7,3,2,1],des:"front, left"},h[18]={num:6,data:[0,3,2,6,5,1],des:"front, right"},h[19]={num:0},h[20]={num:6,data:[0,3,2,1,5,4],des:"front, bottom"},h[21]={num:6,data:[2,1,5,4,7,3],des:"front, bottom, left"},h[22]={num:6,data:[0,3,2,6,5,4],des:"front, bottom, right"},h[23]={num:0},h[24]={num:6,data:[0,3,7,6,2,1],des:"front, top"},h[25]={num:6,data:[0,4,7,6,2,1],des:"front, top, left"},h[26]={num:6,data:[0,3,7,6,5,1],des:"front, top, right"},h[27]={num:0},h[28]={num:0},h[29]={num:0},h[30]={num:0},h[31]={num:0},h[32]={num:4,data:[4,5,6,7],des:"back"},h[33]={num:6,data:[4,5,6,7,3,0],des:"back, left"},h[34]={num:6,data:[1,2,6,7,4,5],des:"back, right"},h[35]={num:0},h[36]={num:6,data:[0,1,5,6,7,4],des:"back, bottom"},h[37]={num:6,data:[0,1,5,6,7,3],des:"back, bottom, left"},h[38]={num:6,data:[0,1,2,6,7,4],des:"back, bottom, right"},h[39]={num:0},h[40]={num:6,data:[2,3,7,4,5,6],des:"back, top"},h[41]={num:6,data:[0,4,5,6,2,3],des:"back, top, left"},h[42]={num:6,data:[1,2,3,7,4,5],des:"back, top, right"},m.packedLength=n.Cartesian3.packedLength+u.Matrix3.packedLength,m.pack=function(a,t,e){return e=s.defaultValue(e,0),n.Cartesian3.pack(a.center,t,e),u.Matrix3.pack(a.halfAxes,t,e+n.Cartesian3.packedLength),t},m.unpack=function(a,t,e){return t=s.defaultValue(t,0),s.defined(e)||(e=new m),n.Cartesian3.unpack(a,t,e.center),u.Matrix3.unpack(a,t+n.Cartesian3.packedLength,e.halfAxes),e};var x=new n.Cartesian3,p=new n.Cartesian3,M=new n.Cartesian3,f=new n.Cartesian3,g=new n.Cartesian3,b=new n.Cartesian3,w=new u.Matrix3,y={unitary:new u.Matrix3,diagonal:new u.Matrix3},v=new n.Cartesian3,P=new n.Cartesian3,O=new n.Cartesian3;m.fromPoints=function(a,t){if(s.defined(t)||(t=new m),!s.defined(a)||0===a.length)return t.halfAxes=u.Matrix3.ZERO,t.center=n.Cartesian3.ZERO,t;var e,r=a.length,i=n.Cartesian3.clone(a[0],x);for(e=1;e<r;e++)n.Cartesian3.add(i,a[e],i);var o=1/r;n.Cartesian3.multiplyByScalar(i,o,i);var C,d=0,c=0,l=0,h=0,v=0,P=0;for(e=0;e<r;e++)d+=(C=n.Cartesian3.subtract(a[e],i,p)).x*C.x,c+=C.x*C.y,l+=C.x*C.z,h+=C.y*C.y,v+=C.y*C.z,P+=C.z*C.z;d*=o,c*=o,l*=o,h*=o,v*=o,P*=o;var O=w;O[0]=d,O[1]=c,O[2]=l,O[3]=c,O[4]=h,O[5]=v,O[6]=l,O[7]=v,O[8]=P;var z=u.Matrix3.computeEigenDecomposition(O,y),A=u.Matrix3.clone(z.unitary,t.halfAxes),N=u.Matrix3.getColumn(A,0,f),R=u.Matrix3.getColumn(A,1,g),T=u.Matrix3.getColumn(A,2,b),I=-Number.MAX_VALUE,E=-Number.MAX_VALUE,B=-Number.MAX_VALUE,L=Number.MAX_VALUE,k=Number.MAX_VALUE,U=Number.MAX_VALUE;for(e=0;e<r;e++)C=a[e],I=Math.max(n.Cartesian3.dot(N,C),I),E=Math.max(n.Cartesian3.dot(R,C),E),B=Math.max(n.Cartesian3.dot(T,C),B),L=Math.min(n.Cartesian3.dot(N,C),L),k=Math.min(n.Cartesian3.dot(R,C),k),U=Math.min(n.Cartesian3.dot(T,C),U);N=n.Cartesian3.multiplyByScalar(N,.5*(L+I),N),R=n.Cartesian3.multiplyByScalar(R,.5*(k+E),R),T=n.Cartesian3.multiplyByScalar(T,.5*(U+B),T);var V=n.Cartesian3.add(N,R,t.center);n.Cartesian3.add(V,T,V);var S=M;return S.x=I-L,S.y=E-k,S.z=B-U,n.Cartesian3.multiplyByScalar(S,.5,S),u.Matrix3.multiplyByScale(t.halfAxes,S,t.halfAxes),t};var z=new n.Cartesian3,A=new n.Cartesian3;function N(a,t,e,r,i,o,C,d,c,l,h){s.defined(h)||(h=new m);var x=h.halfAxes;u.Matrix3.setColumn(x,0,t,x),u.Matrix3.setColumn(x,1,e,x),u.Matrix3.setColumn(x,2,r,x);var p=z;p.x=(i+o)/2,p.y=(C+d)/2,p.z=(c+l)/2;var M=A;M.x=(o-i)/2,M.y=(d-C)/2,M.z=(l-c)/2;var f=h.center;return p=u.Matrix3.multiplyByVector(x,p,p),n.Cartesian3.add(a,p,f),u.Matrix3.multiplyByScale(x,M,x),h}var R=new n.Cartographic,T=new n.Cartesian3,I=new n.Cartographic,E=new n.Cartographic,B=new n.Cartographic,L=new n.Cartographic,k=new n.Cartographic,U=new n.Cartesian3,V=new n.Cartesian3,S=new n.Cartesian3,D=new n.Cartesian3,_=new n.Cartesian3,X=new e.Cartesian2,W=new e.Cartesian2,q=new e.Cartesian2,j=new e.Cartesian2,Z=new e.Cartesian2,F=new n.Cartesian3,G=new n.Cartesian3,Y=new n.Cartesian3,H=new n.Cartesian3,J=new e.Cartesian2,K=new n.Cartesian3,Q=new n.Cartesian3,$=new n.Cartesian3,aa=new c.Plane(n.Cartesian3.UNIT_X,0);m.fromRectangle=function(a,t,e,r,i){var u,l,h,m,x,p,M;if(t=s.defaultValue(t,0),e=s.defaultValue(e,0),r=s.defaultValue(r,o.Ellipsoid.WGS84),a.width<=d.CesiumMath.PI){var f=o.Rectangle.center(a,R),g=r.cartographicToCartesian(f,T),b=new C.EllipsoidTangentPlane(g,r);M=b.plane;var w=f.longitude,y=a.south<0&&a.north>0?0:f.latitude,v=n.Cartographic.fromRadians(w,a.north,e,I),P=n.Cartographic.fromRadians(a.west,a.north,e,E),O=n.Cartographic.fromRadians(a.west,y,e,B),z=n.Cartographic.fromRadians(a.west,a.south,e,L),A=n.Cartographic.fromRadians(w,a.south,e,k),ta=r.cartographicToCartesian(v,U),ea=r.cartographicToCartesian(P,V),na=r.cartographicToCartesian(O,S),ra=r.cartographicToCartesian(z,D),ia=r.cartographicToCartesian(A,_),sa=b.projectPointToNearestOnPlane(ta,X),oa=b.projectPointToNearestOnPlane(ea,W),Ca=b.projectPointToNearestOnPlane(na,q),da=b.projectPointToNearestOnPlane(ra,j),ua=b.projectPointToNearestOnPlane(ia,Z);return l=-(u=Math.min(oa.x,Ca.x,da.x)),m=Math.max(oa.y,sa.y),h=Math.min(da.y,ua.y),P.height=z.height=t,ea=r.cartographicToCartesian(P,V),ra=r.cartographicToCartesian(z,D),x=Math.min(c.Plane.getPointDistance(M,ea),c.Plane.getPointDistance(M,ra)),p=e,N(b.origin,b.xAxis,b.yAxis,b.zAxis,u,l,h,m,x,p,i)}var ca=a.south>0,la=a.north<0,ha=ca?a.south:la?a.north:0,ma=o.Rectangle.center(a,R).longitude,xa=n.Cartesian3.fromRadians(ma,ha,e,r,F);xa.z=0;var pa=Math.abs(xa.x)<d.CesiumMath.EPSILON10&&Math.abs(xa.y)<d.CesiumMath.EPSILON10?n.Cartesian3.UNIT_X:n.Cartesian3.normalize(xa,G),Ma=n.Cartesian3.UNIT_Z,fa=n.Cartesian3.cross(pa,Ma,Y);M=c.Plane.fromPointNormal(xa,pa,aa);var ga=n.Cartesian3.fromRadians(ma+d.CesiumMath.PI_OVER_TWO,ha,e,r,H);u=-(l=n.Cartesian3.dot(c.Plane.projectPointOntoPlane(M,ga,J),fa)),m=n.Cartesian3.fromRadians(0,a.north,la?t:e,r,K).z,h=n.Cartesian3.fromRadians(0,a.south,ca?t:e,r,Q).z;var ba=n.Cartesian3.fromRadians(a.east,ha,e,r,$);return N(xa,fa,Ma,pa,u,l,h,m,x=c.Plane.getPointDistance(M,ba),p=0,i)},m.clone=function(a,t){if(s.defined(a))return s.defined(t)?(n.Cartesian3.clone(a.center,t.center),u.Matrix3.clone(a.halfAxes,t.halfAxes),t.areaDirty=1,t):new m(a.center,a.halfAxes)},m.intersectPlane=function(a,e){var r=a.center,i=e.normal,s=a.halfAxes,o=i.x,C=i.y,d=i.z,c=Math.abs(o*s[u.Matrix3.COLUMN0ROW0]+C*s[u.Matrix3.COLUMN0ROW1]+d*s[u.Matrix3.COLUMN0ROW2])+Math.abs(o*s[u.Matrix3.COLUMN1ROW0]+C*s[u.Matrix3.COLUMN1ROW1]+d*s[u.Matrix3.COLUMN1ROW2])+Math.abs(o*s[u.Matrix3.COLUMN2ROW0]+C*s[u.Matrix3.COLUMN2ROW1]+d*s[u.Matrix3.COLUMN2ROW2]),l=n.Cartesian3.dot(i,r)+e.distance;return l<=-c?t.Intersect.OUTSIDE:l>=c?t.Intersect.INSIDE:t.Intersect.INTERSECTING};var ta=new n.Cartesian3,ea=new n.Cartesian3,na=new n.Cartesian3,ra=new n.Cartesian3,ia=new n.Cartesian3;m.distanceSquaredTo=function(a,t){var e=n.Cartesian3.subtract(t,a.center,z),r=a.halfAxes,i=u.Matrix3.getColumn(r,0,ta),s=u.Matrix3.getColumn(r,1,ea),o=u.Matrix3.getColumn(r,2,na),C=n.Cartesian3.magnitude(i),d=n.Cartesian3.magnitude(s),c=n.Cartesian3.magnitude(o);n.Cartesian3.normalize(i,i),n.Cartesian3.normalize(s,s),n.Cartesian3.normalize(o,o);var l=ra;l.x=n.Cartesian3.dot(e,i),l.y=n.Cartesian3.dot(e,s),l.z=n.Cartesian3.dot(e,o);var h,m=0;return l.x<-C?m+=(h=l.x+C)*h:l.x>C&&(m+=(h=l.x-C)*h),l.y<-d?m+=(h=l.y+d)*h:l.y>d&&(m+=(h=l.y-d)*h),l.z<-c?m+=(h=l.z+c)*h:l.z>c&&(m+=(h=l.z-c)*h),m};var sa=new n.Cartesian3,oa=new n.Cartesian3;m.computePlaneDistances=function(a,e,r,i){s.defined(i)||(i=new t.Interval);var o=Number.POSITIVE_INFINITY,C=Number.NEGATIVE_INFINITY,d=a.center,c=a.halfAxes,l=u.Matrix3.getColumn(c,0,ta),h=u.Matrix3.getColumn(c,1,ea),m=u.Matrix3.getColumn(c,2,na),x=n.Cartesian3.add(l,h,sa);n.Cartesian3.add(x,m,x),n.Cartesian3.add(x,d,x);var p=n.Cartesian3.subtract(x,e,oa),M=n.Cartesian3.dot(r,p);return o=Math.min(M,o),C=Math.max(M,C),n.Cartesian3.add(d,l,x),n.Cartesian3.add(x,h,x),n.Cartesian3.subtract(x,m,x),n.Cartesian3.subtract(x,e,p),M=n.Cartesian3.dot(r,p),o=Math.min(M,o),C=Math.max(M,C),n.Cartesian3.add(d,l,x),n.Cartesian3.subtract(x,h,x),n.Cartesian3.add(x,m,x),n.Cartesian3.subtract(x,e,p),M=n.Cartesian3.dot(r,p),o=Math.min(M,o),C=Math.max(M,C),n.Cartesian3.add(d,l,x),n.Cartesian3.subtract(x,h,x),n.Cartesian3.subtract(x,m,x),n.Cartesian3.subtract(x,e,p),M=n.Cartesian3.dot(r,p),o=Math.min(M,o),C=Math.max(M,C),n.Cartesian3.subtract(d,l,x),n.Cartesian3.add(x,h,x),n.Cartesian3.add(x,m,x),n.Cartesian3.subtract(x,e,p),M=n.Cartesian3.dot(r,p),o=Math.min(M,o),C=Math.max(M,C),n.Cartesian3.subtract(d,l,x),n.Cartesian3.add(x,h,x),n.Cartesian3.subtract(x,m,x),n.Cartesian3.subtract(x,e,p),M=n.Cartesian3.dot(r,p),o=Math.min(M,o),C=Math.max(M,C),n.Cartesian3.subtract(d,l,x),n.Cartesian3.subtract(x,h,x),n.Cartesian3.add(x,m,x),n.Cartesian3.subtract(x,e,p),M=n.Cartesian3.dot(r,p),o=Math.min(M,o),C=Math.max(M,C),n.Cartesian3.subtract(d,l,x),n.Cartesian3.subtract(x,h,x),n.Cartesian3.subtract(x,m,x),n.Cartesian3.subtract(x,e,p),M=n.Cartesian3.dot(r,p),o=Math.min(M,o),C=Math.max(M,C),i.start=o,i.stop=C,i};var Ca=new t.BoundingSphere;m.isOccluded=function(a,e){var n=t.BoundingSphere.fromOrientedBoundingBox(a,Ca);return!e.isBoundingSphereVisible(n)},m.prototype.intersectPlane=function(a){return m.intersectPlane(this,a)},m.prototype.distanceSquaredTo=function(a){return m.distanceSquaredTo(this,a)},m.prototype.computePlaneDistances=function(a,t,e){return m.computePlaneDistances(this,a,t,e)},m.prototype.isOccluded=function(a){return m.isOccluded(this,a)},m.equals=function(a,t){return a===t||s.defined(a)&&s.defined(t)&&n.Cartesian3.equals(a.center,t.center)&&u.Matrix3.equals(a.halfAxes,t.halfAxes)},m.prototype.clone=function(a){return m.clone(this,a)},m.prototype.equals=function(a){return m.equals(this,a)};var da=new r.Cartesian4;m.prototype._updateBBox=function(){if(1==this.areaDirty){var a=u.Matrix3.getColumn(this.halfAxes,0,ta),t=n.Cartesian3.clone(n.Cartesian3.negate(a,v)),e=u.Matrix3.getColumn(this.halfAxes,1,ea),r=n.Cartesian3.clone(n.Cartesian3.negate(e,v)),i=u.Matrix3.getColumn(this.halfAxes,2,na),s=n.Cartesian3.clone(n.Cartesian3.negate(i,v));this.bbox=[],n.Cartesian3.add(this.center,e,v),n.Cartesian3.add(v,s,P),n.Cartesian3.add(P,t,O),this.bbox[0]=new n.Cartesian3(O.x,O.y,O.z),n.Cartesian3.add(P,a,O),this.bbox[1]=new n.Cartesian3(O.x,O.y,O.z),n.Cartesian3.add(v,i,P),n.Cartesian3.add(P,a,O),this.bbox[2]=new n.Cartesian3(O.x,O.y,O.z),n.Cartesian3.add(P,t,O),this.bbox[3]=new n.Cartesian3(O.x,O.y,O.z),n.Cartesian3.add(this.center,r,v),n.Cartesian3.add(v,s,P),n.Cartesian3.add(P,t,O),this.bbox[4]=new n.Cartesian3(O.x,O.y,O.z),n.Cartesian3.add(P,a,O),this.bbox[5]=new n.Cartesian3(O.x,O.y,O.z),n.Cartesian3.add(v,i,P),n.Cartesian3.add(P,a,O),this.bbox[6]=new n.Cartesian3(O.x,O.y,O.z),n.Cartesian3.add(P,t,O),this.bbox[7]=new n.Cartesian3(O.x,O.y,O.z);var o=n.Cartesian3.magnitude(a),C=n.Cartesian3.magnitude(e),d=n.Cartesian3.magnitude(i),c=new n.Cartesian3(-o,-C,-d),l=new n.Cartesian3(o,C,d);if(o*C*d==0)return void(this.areaDirty=-1);n.Cartesian3.normalize(a,a),n.Cartesian3.normalize(e,e),n.Cartesian3.normalize(i,i),this.u=n.Cartesian3.clone(a),this.v=n.Cartesian3.clone(e),this.w=n.Cartesian3.clone(i),this.posMin=c,this.posMaX=l,this.areaDirty=0}};var ua=[];ua.push(new e.Cartesian2),ua.push(new e.Cartesian2),ua.push(new e.Cartesian2),ua.push(new e.Cartesian2),ua.push(new e.Cartesian2),ua.push(new e.Cartesian2);var ca=new n.Cartographic,la=new n.Cartesian3;m.prototype.calculateBoxArea=function(a,t,e,i,o,C,c,m){this._updateBBox();var x=a,p=n.Cartesian3.subtract(x,this.center,ia);if(-1==this.areaDirty){var M=o/i*(N=-1!=C?C:.5*n.Cartesian3.distance(this.posMaX,this.posMin))/e;return d.CesiumMath.PI*M*M}var f=n.Cartesian3.fromElements(n.Cartesian3.dot(p,this.u),n.Cartesian3.dot(p,this.v),n.Cartesian3.dot(p,this.w),sa),g=(f.x<this.posMin.x?1:0)+((f.x>this.posMaX.x?1:0)<<1)+((f.z<this.posMin.z?1:0)<<2)+((f.z>this.posMaX.z?1:0)<<3)+((f.y>this.posMaX.y?1:0)<<4)+((f.y<this.posMin.y?1:0)<<5);if(g>42)return console.log("area calculation is wrong"),-100;var b=h[g];if(0==b.num){M=o/i*(N=-1!=C?C:.5*n.Cartesian3.distance(this.posMaX,this.posMin))/e;return d.CesiumMath.PI*M*M}if(0==b.num)return console.log("area calculation is wrong"),-100;for(var w,y=[],v=c,P=0;P<b.num;P++){var O,z=ua[P],A=this.bbox[b.data[P]];w=!1;var N,R=d.CesiumMath.PI;if(3===t)(O=u.Matrix4.multiplyByVector(v,r.Cartesian4.fromElements(A.x,A.y,A.z,1),da)).z<0&&(w=!0,-1==N&&(R=d.CesiumMath.PI_OVER_FOUR,e=n.Cartesian3.magnitude(p)));else{var T=m,I=T.ellipsoid.cartesianToCartographic(A,ca);s.defined(I)?(T.project(I,la),(O=u.Matrix4.multiplyByVector(v,r.Cartesian4.fromElements(la.z,la.x,la.y,1),da)).z<0&&(w=!0)):w=!0}if(1==w)return R*(M=o/i*(N=-1!=C?C:.5*n.Cartesian3.distance(this.posMaX,this.posMin))/e)*M;z.x=O.x/O.w,z.y=o-O.y/O.w,y.push(z)}return Math.abs(l.PolygonPipeline.computeArea2D(y))},a.OrientedBoundingBox=m}));
