<template>
    <div>
        <div class="header">
            <el-page-header @back="goBack">
                <template #content>
                    <span class="text-large font-600 mr-3"> 批转收文 </span>
                </template>
            </el-page-header>
            <div class="btn-submit">
                <el-button v-if="!readOnly" type="primary" v-loading="showLoding" @click="selectUsers">批转</el-button>
                <el-button v-if="!readOnly" type="danger" @click="selectUsers('reject')">驳回</el-button>
                <!-- <el-button type="warning" @click="gotoHistory('0')">查看办理过程</el-button> -->
                <el-button type="success" @click="gotoHistory('1')">查看批转记录</el-button>
                <el-button @click="handleExport">打印</el-button>
            </div>
        </div>
        <el-row :gutter="20" class="container" v-if="!showSend">
            <el-col :span="18" class="card" v-loading="showLoding">
                <SelectUserTransfer :processInstanceId="processInstanceId" v-if="userDialog" @back="userDialog = false"
                    :msg="form.docAudit?.auditIdea" :taskId="taskId" :nodeId="nodeId" :rejectFlag="rejectFlag"
                    :readType="readType" @saveData="handleSelectUsers">
                </SelectUserTransfer>
                <div class="form">
                    <div class="form-content">
                        <h1 style="text-align: center">昆明市土地矿产储备中心收文处理笺</h1>
                        <el-form ref="receiptFormRef" :model="form" :rules="rules" label-width="80px">
                            <div class="flx-justify-between">
                                <el-form-item label="案卷号" prop="num">
                                    <el-input :disabled="formDisabled" v-model="form.num" placeholder="请输入案卷号" />
                                </el-form-item>
                                <el-form-item label="收文日期" prop="receiptTime">
                                    <el-date-picker :disabled="formDisabled" clearable v-model="form.receiptTime"
                                        type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择收文日期">
                                    </el-date-picker>
                                </el-form-item>
                            </div>
                            <table>
                                <tr>
                                    <td colspan="4">
                                        <h2 style="text-align: center">来文基本情况</h2>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <el-form-item label="来文类别" prop="category" label-width="150px">
                                            <el-select :disabled="formDisabled" v-model="form.category"
                                                placeholder="来文类别" clearable>
                                                <el-option v-for="dict in dicts.doc_category" :key="dict.dictValue"
                                                    :label="dict.dictLabel" :value="dict.dictValue" />
                                            </el-select>
                                        </el-form-item>
                                    </td>
                                    <td>
                                        <el-form-item label="密 级" prop="secretLevel" label-width="150px">
                                            <el-select :disabled="formDisabled" v-model="form.secretLevel"
                                                placeholder="密 级" clearable>
                                                <el-option v-for="dict in dicts.doc_secret" :key="dict.dictValue"
                                                    :label="dict.dictLabel" :value="dict.dictValue" />
                                            </el-select>
                                        </el-form-item>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <el-form-item label="急 缓" prop="urgentDegree" label-width="150px">
                                            <el-select :disabled="formDisabled" v-model="form.urgentDegree"
                                                placeholder="急 缓" clearable>
                                                <el-option v-for="dict in dicts.doc_urgency" :key="dict.dictValue"
                                                    :label="dict.dictLabel" :value="dict.dictValue" />
                                            </el-select>
                                        </el-form-item>
                                    </td>
                                    <td>
                                        <el-form-item label="发布方式" prop="releaseMethod" label-width="150px">
                                            <el-select :disabled="formDisabled" v-model="form.releaseMethod"
                                                placeholder="发布方式" clearable>
                                                <el-option v-for="dict in dicts.doc_release" :key="dict.dictValue"
                                                    :label="dict.dictLabel" :value="dict.dictValue" />
                                            </el-select>
                                        </el-form-item>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="4">
                                        <el-form-item label="阅办类型" prop="readType" label-width="150px">
                                            <el-select :disabled="formDisabled" v-model="form.readType"
                                                placeholder="阅办类型" clearable>
                                                <el-option v-for="dict in readTypeList" :key="dict.dictValue"
                                                    :label="dict.label" :value="dict.value" />
                                            </el-select>
                                        </el-form-item>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="4">
                                        <el-form-item label="来文单位" prop="unitName" label-width="150px">
                                            <el-input :disabled="formDisabled" v-model="form.unitName"
                                                placeholder="请输入来文单位" />
                                        </el-form-item>
                                        <!-- <el-form-item label="来文单位" prop="unitName" label-width="150px">
                                            <el-select :disabled="formDisabled" v-model="form.unitName"
                                                placeholder="来文单位" clearable>
                                                <el-option v-for="dict in dicts.doc_unit_name" :key="dict.dictValue"
                                                    :label="dict.dictLabel" :value="dict.dictValue" />
                                            </el-select>
                                        </el-form-item> -->
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="4">
                                        <el-form-item label="来文标题" prop="title" label-width="150px">
                                            <el-input :disabled="formDisabled" v-model="form.title"
                                                placeholder="请输入来文标题" />
                                        </el-form-item>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="4">
                                        <el-form-item label="收文备注" prop="remark" label-width="150px">
                                            <el-input :disabled="formDisabled" type="textarea" v-model="form.remark"
                                                placeholder="请输入备注" />
                                        </el-form-item>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="4">
                                        <h2 style="text-align: center">来文审批意见</h2>
                                    </td>
                                </tr>
                                <tr v-for="(item, index) in form.audits" :key="index">
                                    <td v-if="item.docData === '1'" colspan="4">
                                        <el-form-item v-if="item.nodeId === nodeId && !readOnly" :label="item.nodeName"
                                            label-width="150px">
                                            <el-input type="textarea" v-model="form.docAudit.auditIdea"
                                                placeholder="请输入意见" />
                                            <el-form-item>
                                                <el-form-item label="签字">
                                                    <!-- <div class="sign-box" v-if="form.docAudit.auditSign">{{
                                                        form.docAudit.auditSign }}
                                                    </div> -->
                                                    <img v-if="form.docAudit.auditSign"
                                                        style="width: 100px;height: 40px;"
                                                        :src="perviewUrl + form.docAudit.auditSign">
                                                    <div class="sign-box" v-else-if="form.docAudit.name">{{
                                                        form.docAudit.name }}</div>
                                                    <div class="sign-box" v-else @click="docSign">点击签字</div>
                                                </el-form-item>
                                                <el-form-item label="日期">
                                                    <el-date-picker :disabled="!(item.nodeId === nodeId)" clearable
                                                        v-model="form.docAudit.auditTime" type="datetime"
                                                        value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择日期">
                                                    </el-date-picker>
                                                </el-form-item>
                                            </el-form-item>
                                        </el-form-item>
                                        <el-form-item v-else :label="item.nodeName" label-width="150px">
                                            <el-input :disabled="true" type="textarea" v-model="item.data[0].auditIdea"
                                                placeholder="请输入意见" />
                                            <el-form-item>
                                                <el-form-item label="签字">
                                                    <!-- <div class="sign-box" style="background-color: #F5F7FA;"
                                                        v-if="item.data[0].auditSign">{{
                                                            perviewUrl + item.data[0].auditSign }}
                                                    </div> -->
                                                    <img style="width: 100px;height: 40px;"
                                                        v-if="item.data[0].auditSign"
                                                        :src="perviewUrl + item.data[0].auditSign">
                                                    <div class="sign-box" style="background-color: #F5F7FA;" v-else>{{
                                                        item.data[0].name }}</div>
                                                </el-form-item>
                                                <el-form-item label="日期">
                                                    <el-date-picker :disabled="true" clearable
                                                        v-model="item.data[0].auditTime" type="datetime"
                                                        value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择日期">
                                                    </el-date-picker>
                                                </el-form-item>
                                            </el-form-item>
                                        </el-form-item>
                                    </td>
                                    <td v-if="item.docData === '2'" colspan="4">
                                        <div class="el-form-item__label" style="width: 200px;border: 0px;">{{
                                            item.nodeName
                                        }}
                                        </div>
                                        <el-button :disabled="nodeId !== 'Activity_1g2spl2'"
                                            v-if="item.nodeId === 'Activity_1g2spl2'" type="primary"
                                            @click="gotoSend">转发文</el-button>

                                        <el-table :data="item.data" border style="width: 100%" height="200">
                                            <el-table-column prop="auditIdea" label="意见" />
                                            <el-table-column label="签字" width="180">
                                                <template #default="scope">
                                                    <img style="width: 100px;height: 40px;" v-if="scope.row.auditSign"
                                                        :src="perviewUrl + scope.row.auditSign">
                                                    <div v-else>{{
                                                        scope.row.name
                                                        }}
                                                    </div>
                                                </template>
                                            </el-table-column>
                                            <el-table-column prop="auditTime" label="日期" width="180" />
                                        </el-table>
                                        <div class="audit-btn flx-justify-between">
                                            <el-input v-if="item.nodeId === nodeId && !readOnly" v-model="auditIdea"
                                                placeholder="请输入意见" />
                                            <el-input v-else disabled placeholder="请输入意见" />
                                            <el-button @click="docSignList(item)"
                                                :disabled="!(item.nodeId === nodeId && !readOnly)" type="primary"
                                                plain>保存意见</el-button>
                                            <el-button @click="docSignRevoke(item)"
                                                :disabled="!(item.nodeId === nodeId && !readOnly)" type="info"
                                                plain>撤销意见</el-button>
                                        </div>
                                    </td>
                                    <td v-if="item.custom === '回文'">
                                        <el-form-item label="回文办理状态" prop="sendStatus" label-width="150px">
                                            <el-select style="width: 250px;" disabled v-model="form.sendStatus"
                                                placeholder="回文办理状态" clearable>
                                                <el-option v-for="dict in dicts.reply_handle_status"
                                                    :key="dict.dictValue" :label="dict.dictLabel"
                                                    :value="dict.dictValue" />
                                            </el-select>
                                        </el-form-item>
                                    </td>
                                    <td v-if="item.custom === '回文'">
                                        <el-form-item label="办理截止时间" prop="sendEndTime" label-width="150px">
                                            <el-date-picker :disabled="formDisabled" clearable
                                                v-model="form.sendEndTime" type="datetime"
                                                value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择日期">
                                            </el-date-picker>
                                        </el-form-item>
                                    </td>
                                    <td v-if="item.custom === '督办'" colspan="4">
                                        <el-form-item label="督办批示件" label-width="150px">
                                            <el-form-item>
                                                <el-form-item label="领导批示件" label-width="100px" prop="instructions">
                                                    <el-select :disabled="formDisabled" style="width: 150px;"
                                                        v-model="form.instructions" placeholder="请选择" clearable>
                                                        <el-option v-for="dict in boList" :key="dict.label"
                                                            :label="dict.label" :value="dict.label" />
                                                    </el-select>
                                                </el-form-item>
                                                <el-form-item style="border-left: 1px solid #e2e2e2;margin-left: 1px;"
                                                    label="批示领导" prop="insLeader" label-width="150px">
                                                    <el-input :disabled="formDisabled" v-model="form.insLeader"
                                                        placeholder="请输入批示领导" />
                                                </el-form-item>
                                            </el-form-item>
                                            <el-form-item style="border-top: 1px solid #e2e2e2;width: 100%;">
                                                <el-form-item label="督办件" label-width="100px" prop="handleDoc">
                                                    <el-select :disabled="formDisabled" style="width: 150px;"
                                                        v-model="form.handleDoc" placeholder="请选择" clearable>
                                                        <el-option v-for="dict in boList" :key="dict.label"
                                                            :label="dict.label" :value="dict.label" />
                                                    </el-select>
                                                </el-form-item>
                                                <el-form-item style="border-left: 1px solid #e2e2e2;margin-left: 1px;"
                                                    label="督办部门" prop="handleDept" label-width="150px">
                                                    <el-input :disabled="formDisabled" v-model="form.handleDept"
                                                        placeholder="请输入督办部门" />
                                                </el-form-item>
                                            </el-form-item>
                                            <el-form-item style="border-top: 1px solid #e2e2e2;width: 100%;">
                                                <el-input type="textarea" :disabled="formDisabled"
                                                    v-model="form.insContent" placeholder="请输入意见" />
                                            </el-form-item>

                                        </el-form-item>
                                    </td>
                                </tr>
                            </table>


                        </el-form>
                    </div>
                </div>

            </el-col>
            <el-col :span="6" v-loading="showLoding">
                <div class="file-content card">
                    <FileTree v-if="fileTypeList.length > 0" :key="formKey" :data="fileTypeList" :upload="!formDisabled"
                        @onSuccess="onUploadSuccess" @onRemove="onUploadRemove" />
                </div>
            </el-col>
        </el-row>
        <sendDorm @back="showSend = false" :linkTitle="sendProps.linkTitle" :linkNum="sendProps.linkNum" v-else />
        <History :activeName="historyType" :businessId="businessId" @back="goBackHistory" v-if="showHistory" />
    </div>
</template>

<script setup>
import FileTree from '@/components/FileTree'
import sendDorm from '../../../send/index.vue'
import { onMounted, ref } from 'vue';
import { getDicts } from '@/api/system/dict/data';
import { getTmpJson, getTaskNodeList } from '@/api/document/common';
import { addReceipt, getDetail, updateReceipt } from '@/api/document/receipt';
import { startWorkFlow, completeTask, signTask, backProcess } from '@/api/document/common';
import SelectUserTransfer from "@/components/SelectUserTransfer/index.vue";
import useUserStore from '@/store/modules/user'
import { parseTime } from '@/utils/ruoyi';
import History from "@/components/History/index.vue";
import { exportWordForId } from "@/utils/common.js";
const showHistory = ref(false)
const historyType = ref('0')
const goBackHistory = () => {
    showHistory.value = false
}
const gotoHistory = (type) => {
    showHistory.value = true
    historyType.value = type
}

const emit = defineEmits()
const props = defineProps({
    businessId: {
        type: String,
    },
    processInstanceId: {
        type: String,
    },
    taskId: {
        type: String,
    },
    nodeId: {
        type: String,
    },
    readOnly: {
        type: Boolean,
        default: () => false
    }
});

//print
const handleExport = ()=>{
  console.log('type,id', props.businessId);
  exportWordForId('昆明市土地矿产储备中心收文处理笺','1', props.businessId)
}

const { proxy } = getCurrentInstance();

const formKey = ref(new Date().getTime())
const fileTypeList = ref([])
//表单修改开关
const formDisabled = ref(true)
const showLoding = ref(false)

const readTypeList = [
    { label: '办理件', value: '1' },
    { label: '传阅件', value: '2' },
]
const readType = ref('')
const boList = [
    { label: '是' },
    { label: '否' },
]
//需要请求的字典类型
const dictsList = ['doc_category', 'doc_unit_name', 'doc_secret', 'doc_release', 'doc_urgency', 'doc_receipt_file_type']
const dicts = ref({})
dictsList.map(item => dicts.value[item] = [])

const receiptFormRef = ref(null)
const initFormData = {
    num: undefined,
    category: undefined,
    secretLevel: undefined,
    urgentDegree: undefined,
    releaseMethod: undefined,
    unitName: undefined,
    title: undefined,
    receiptTime: undefined,
    readType: undefined,
    affixList: [],
}
const data = reactive({
    form: { ...initFormData },
    rules: {
        num: [
            { required: true, message: "案卷号不能为空", trigger: "blur" }
        ],
        category: [
            { required: true, message: "来文类别不能为空", trigger: "blur" }
        ],
        secretLevel: [
            { required: true, message: "密级不能为空", trigger: "blur" }
        ],
        releaseMethod: [
            { required: true, message: "发布方式不能为空", trigger: "blur" }
        ],
        unitName: [
            { required: true, message: "来文单位不能为空", trigger: "blur" }
        ],
        title: [
            { required: true, message: "来文标题不能为空", trigger: "blur" }
        ],
        receiptTime: [
            { required: true, message: "收文日期不能为空", trigger: "blur" }
        ],
        readType: [
            { required: true, message: "阅办类型不能为空", trigger: "change" }
        ],
    }
});
const { form, rules } = toRefs(data);
const tmpJson = ref([])
const auditIdea = ref('')

const perviewUrl = import.meta.env.VITE_APP_FILE_SERVICE_URL

const userDialog = ref(false)
function goBack() {
    emit('back')
}
//上传成功
function onUploadSuccess(fileList) {
    form.value.affixList.push(fileList)
}
//删除文件
function onUploadRemove(file) {
    form.value.affixList = form.value.affixList.filter(item => item.name !== file.response.data.url)
}
function docSign() {
    let userInfo = useUserStore().user
    if (userInfo.signUrl) {
        form.value.docAudit.auditSign = userInfo.signUrl
    } else {
        form.value.docAudit.name = userInfo.nickName
    }
    let date = parseTime(new Date().getTime())
    form.value.docAudit.auditTime = date
}
//多人签字
function docSignList(item) {
    let userInfo = useUserStore().user
    if (userInfo.signUrl) {
        form.value.docAudit.auditSign = userInfo.signUrl
    } else {
        form.value.docAudit.name = userInfo.nickName
    }
    form.value.docAudit.auditIdea = auditIdea.value
    let date = parseTime(new Date().getTime())

    let ArrIndex = item.data.findIndex(it => it.del);
    if (ArrIndex !== -1) {
        item.data[ArrIndex] = {
            auditSign: userInfo.signUrl,
            name: userInfo.nickName,
            auditIdea: auditIdea.value,
            auditTime: date,
            del: true//删除标志
        }
    } else {
        form.value.docAudit.auditTime = date
        item.data.push({
            auditSign: userInfo.signUrl,
            name: userInfo.nickName,
            auditIdea: auditIdea.value,
            auditTime: date,
            del: true//删除标志
        })
    }

}
//撤销签字
function docSignRevoke(item) {
    form.value.docAudit.auditSign = ''
    form.value.docAudit.name = ''
    form.value.docAudit.auditIdea = ''
    form.value.docAudit.auditTime = ''
    item.data = item.data.filter(it => !it.del)
}

const rejectFlag = ref(false)
//调起选择框
function selectUsers(type) {
    let flag = true
    tmpJson.value.map(item => {
        if (proxy.nodeId === item.nodeId) {
            if (!form.value.docAudit.auditTime) {
                proxy.$modal.msgError('请在下方完成' + item.nodeName + '并签字');
                flag = false
            }
        }
    })
    if (!flag) return
    rejectFlag.value = type === 'reject'
    userDialog.value = true
}
//批转回调
function handleSelectUsers(result) {
    console.log('result', result);
    receiptFormRef.value.validate(valid => {
        if (valid) {
            showLoding.value = true
            if (form.value.docAudit) {
                form.value.docAudit.auditIdea = result.message
            }
            let userInfo = useUserStore().user
            // 修改收文
            updateReceipt(form.value).then(() => {
                let data = {
                    taskId: props.taskId,
                    messageType: ['1'],
                    message: JSON.stringify({
                        msg: result.message,
                        sign: userInfo.signUrl ? userInfo.signUrl : '',
                        name: userInfo.signUrl ? '' : userInfo.nickName,
                    }),
                    variables: { entity: form.value, ...result.variables }
                }
                if (rejectFlag.value) {
                    backProcess(data).then(res => {
                        showLoding.value = false
                        proxy.$modal.msgSuccess('驳回成功');
                        goBack()
                    })
                } else {
                    // 办理任务
                    completeTask(data).then(res => {
                        showLoding.value = false
                        proxy.$modal.msgSuccess('批转成功');
                        goBack()
                    })
                }

            })
        }
    })

}
function onSubmit() {
    receiptFormRef.value.validate(valid => {
        if (valid) {
            addReceipt(form.value).then(res => {
                let data = {
                    businessKey: res.data.id,
                    tableName: 'doc_receipt',
                    variables: {
                        entity: res.data
                    }
                }
                startWorkFlow(data).then(() => {
                    proxy.$modal.msgSuccess('提交成功');
                    receiptFormRef.value.resetFields()
                    formKey.value = new Date().getTime()

                })
            })
        }
    })
}
const showSend = ref(false)
const sendProps = ref({
    linkTitle: "",
    linkNum: ""
})
function gotoSend() {
    showSend.value = true
    form.value.isSend = '1'
    updateReceipt(form.value)
    sendProps.value.linkTitle = form.value.title
    sendProps.value.linkNum = form.value.num
}

onMounted(() => {
    if (!props.readOnly) {
        // 签收任务
        signTask(props.taskId)
    }
    getDetail(props.businessId).then(res => {
        form.value = res.data
        if (form.value.affixes) {
            form.value.affixes.map(item => {
                item.dictLabel = item.typeName
                item.fileList = item.affixs
            })
            fileTypeList.value = form.value.affixes
        }
        // tmpJson.value ={ ...form.value.audits }
        tmpJson.value = JSON.parse(JSON.stringify(form.value.audits));
        // console.log('tmpJson.value', tmpJson.value);

        tmpJson.value.map((item, index) => {
            if (proxy.nodeId === item.nodeId) {
                form.value.docAudit = {
                    "businessId": props.businessId,
                    "nodeName": item.nodeName,
                    "nodeId": item.nodeId,
                    "userId": useUserStore().id,
                    "name": "",
                    "auditIdea": "",
                    "auditTime": "",
                    "auditSign": "",
                }
            }
            if (item.nodeId === 'Activity_1g2spl2') {
                form.value.audits.splice(index + 1, 0, { custom: '回文' });
                form.value.audits.splice(index + 2, 0, { custom: '督办' });
            }
        })
        if (proxy.nodeId === 'Activity_0wxa7xu' && !props.readOnly) {
            formDisabled.value = false
            readType.value = form.value.readType
        }
    })
    dictsList.map(item => {
        getDicts(item).then(res => {
            dicts.value[item] = res.data
        })
    })
    getTmpJson({ type: '1' }).then(res => {
        tmpJson.value = res.data
    })
})
</script>

<style scoped lang="scss">
@import "@/styles/variables.module.scss";;

.container {
    padding: 10px 20px;
    background-color: #E9EEF3;
    height: calc($contentHeight - 50px);
    overflow-x: hidden;
    overflow-y: scroll;
}

.form {
    display: flex;
    justify-content: center;
    background: #dce2f1;
}

.form-content {
    width: 770px;
}

table {
    width: 100%;
    border: 1px solid #e2e2e2;
    border-collapse: collapse;

    :deep(.el-form-item) {
        margin-bottom: 0;
    }

    :deep(.el-form-item__label) {
        justify-content: center;
        border-right: 1px solid #e2e2e2;
        margin-right: 1px;
        height: auto;
        padding: 10px 10px;
    }

    :deep(.el-form-item__error) {
        position: static;
    }

    td {
        border: 1px solid #e2e2e2;
        background-color: rgb(246, 246, 246);
    }
}

.header {
    height: 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    box-shadow: 0 0 12px rgba(0, 0, 0, 0.05);
    border-bottom: 1px solid #e2e2e2;
    background-color: #fff;
}

.btn-submit {
    display: flex;
    justify-content: flex-end;
}

.audit-btn {
    padding: 5px 5px;
}

.sign-box {
    background-color: #fff;
    height: 100%;
    width: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #606266;
    font-family: inherit;
    font-size: inherit;
    cursor: pointer;
    border-right: 1px solid #e2e2e2;
}
</style>
