<template>
  <div
    v-if="isShow"
    class="tool-wrap"
  >
    <div class="scene-container">
      <FlyLineItem
        v-for="item in roamingPaths"
        :key="item.id"
        :roaming-option="item"
        class="fly-line-item"
        @onItemClick="onFlyLineItemClick(item)"
      />
    </div>
    <FlyLineControl
      v-if="isShowControl"
      @flyLineProceed="flyLineProceed"
      @flyLinePause="flyLinePause"
      @flyLineStop="flyLineStop"
    />
  </div>
</template>

<script setup name="FlyRoute">
import FlyLineControl from "@/components/GISTools/3d/FlyRoute/FlyLineControl";
import FlyLineItem from "./FlyLineItem";

import useMapViewStore from "@/store/modules/map/mapView.js"

defineProps({
  isShow: {
    type: Boolean,
    default: true
  },
  headerInfo: {
    type: Object
  }
})

const toolTitle = ref("场景切换")
const isShowControl = ref(false)
const flyManager = ref(undefined)
const roamingPaths = ref([
  {
    id: "0",
    name: "漫游路径",
    img: "/static/img/fpf/img_1.png",
    url: "/static/fpf/flyRoute.fpf"
  },
  {
    id: "1",
    name: "漫游路径1",
    img: "/static/img/fpf/img_1.png",
    url: "/static/fpf/fly1.fpf"
  },
  {
    id: "2",
    name: "漫游路径2",
    img: "/static/img/fpf/img_1.png",
    url: "/static/fpf/fly2.fpf"
  },
  {
    id: "3",
    name: "漫游路径3",
    img: "/static/img/fpf/img_1.png",
    url: "/static/fpf/fly3.fpf"
  }
  // {
  //   id: "4",
  //   name: "漫游路径4",
  //   img: "/static/img/fpf/img_1.png",
  //   url: "/static/fpf/fly4.fpf"
  // },
  // {
  //   id: "5",
  //   name: "鸟巢",
  //   img: "/static/img/fpf/img_1.png",
  //   url: "/static/fpf/niaocao.fpf"
  // }
])

const viewer3d = computed(()=>useMapViewStore().viewer3d)

const emits = defineEmits(['closePanel'])


const closePanel = ()=>{
  emits('closePanel')
}

/**
 * 初始化飞行路线
 */
const initFly = (viewer3d, fpfUrl)=> {
  const scene = viewer3d.scene;
  const routes = new Cesium.RouteCollection(viewer3d.entities);
  // 添加fpf飞行文件，fpf由SuperMap iDesktop生成
  routes.fromFile(fpfUrl);
  // 初始化飞行管理
  flyManager.value = new Cesium.FlyManager({
    scene: scene,
    routes: routes
  });
  //注册站点到达事件
  flyManager.value.stopArrived.addEventListener(function(routeStop) {
    routeStop.waitTime = 1; // 在每个站点处停留1s
  });
}


/**
 * 漫游路径事件
 */
const onFlyLineItemClick = (roamingPath)=> {
  isShowControl.value = true;
  initFly(viewer3d.value, roamingPath.url);
}

/**
 * 飞行继续
 */
const flyLineProceed = ()=> {
  flyManager.value && flyManager.value.play();
}
/**
 * 飞行暂停
 */
const flyLinePause = ()=> {
  flyManager.value && flyManager.value.pause();
}

/**
 * 飞行停止
 */
const flyLineStop = ()=> {
  flyManager.value && flyManager.value.stop();
}

/**
 * 清除飞行路径和站点
 */
const clearResult = ()=>{
  if (flyManager.value){
    const currentRoute = flyManager.value.currentRoute;
    currentRoute.isLineVisible = false
    currentRoute.isStopVisible = false
  }
}

onBeforeUnmount(()=>{
  clearResult()
})

</script>

<style scoped lang="scss">
.tool-wrap {
  color: #fff;
  background-image: url("@/assets/images/map/tool.png");
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-size: cover;
  box-shadow: 0 0 8px 0 #057595;
}
.scene-container {
  display: flex;
  padding: 5px;
  flex-wrap: wrap;
  justify-content: space-around;
  .fly-line-item {
    margin: 10px 10px;
  }
}
.fly-line {
  height: 331px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #193b51;
  border-radius: 0 0 8px 8px;

  .el-tabs {
    padding: 0 10px !important;
    height: 100%;
    display: flex;
    flex-direction: column;

    .el-tabs__item {
      color: #d2e6f0;
      padding: 0 0 0 6px;
    }

    .el-tabs__item:first-of-type {
      padding-left: 0;
    }

    .el-tabs__item.is-active {
      color: #00d2ff;
    }

    .el-tabs__active-bar {
      background-color: #00d2ff;
    }

    .el-tabs__nav {
      background-color: #193b51;
    }

    .el-tabs__content {
      width: 100%;
      height: 100%;
      padding-bottom: 10px;

      .el-tab-pane {
        height: 100%;
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;

        .custom-empty {
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          align-items: center;
        }
      }
    }
  }
}

.el-scrollbar {
  flex: 1;
  height: 100%;
  width: 100%;

  .el-scrollbar__wrap {
    overflow-x: hidden;

    .el-scrollbar__view {
      padding: 0 10px;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .fly-line-item {
      width: 100%;
      margin-bottom: 14px;
    }

    .fly-line-item:last-of-type {
      margin-bottom: 0;
    }
  }
}
</style>
