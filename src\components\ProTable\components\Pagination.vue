<template>
  <!-- 分页组件 -->
  <el-pagination
    :background="true"
    :current-page="pageable.pageNum"
    :page-size="pageable.pageSize"
    :page-sizes="[10, 25, 50, 100,500,1000]"
    :total="pageable.total"
    size="default"
    layout="total, sizes, prev, pager, next, jumper"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  ></el-pagination>
</template>

<script setup>
// 定义输入的属性
const props = defineProps({
  pageable: {
    type: Object,
    required: true
  },
  handleSizeChange: {
    type: Function,
    required: true
  },
  handleCurrentChange: {
    type: Function,
    required: true
  }
});
</script>