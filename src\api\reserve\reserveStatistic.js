import request from '@/utils/request'

// 获取储备项目统计数据
export function getReserveProjectStatistic(query) {
  return request({
    url: '/patrol/relProject/resProjectSta',
    method: 'get',
    params: query
  })
}

// 导出储备项目统计数据
export function exportReserveProjectStatistic(query) {
  return request({
    url: '/reserve/statistic/project/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 获取储备项目年度统计数据
export function getReserveProjectYearStatistic(query) {
  return request({
    url: '/reserve/statistic/project/year',
    method: 'get',
    params: query
  })
}

// 获取储备项目月度统计数据
export function getReserveProjectMonthStatistic(query) {
  return request({
    url: '/reserve/statistic/project/month',
    method: 'get',
    params: query
  })
}

// 获取储备项目区域统计数据
export function getReserveProjectRegionStatistic(query) {
  return request({
    url: '/reserve/statistic/project/region',
    method: 'get',
    params: query
  })
}

// 获取储备项目状态统计数据
export function getReserveProjectStatusStatistic(query) {
  return request({
    url: '/reserve/statistic/project/status',
    method: 'get',
    params: query
  })
}
