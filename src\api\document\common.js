import request from '@/utils/request'

// 获取业务分类待办流程节点
export function notifyByType(query) {
    return request({
        url: '/document/notify/notifyByType',
        method: 'get',
        params: query
    })
}
// 查询当前登录人的所有待办箱信息
export function getDocWorkType(query) {
    return request({
        url: '/document/notify/wait/box/all',
        method: 'get',
        params: query
    })
}
// 查询业务审批配置结构json
export function getTmpJson(query) {
    return request({
        url: '/document/auditTmp/tmpJson',
        method: 'get',
        params: query
    })
}
// 启动流程
export function startWorkFlow(data) {
    return request({
        url: '/workflow/task/startWorkFlow',
        method: 'post',
        data: data
    })
}
// 签收任务
export function signTask(taskId) {
    return request({
        url: '/workflow/task/claim/' + taskId,
        method: 'post',
    })
}
// 办理任务
export function completeTask(data) {
    return request({
        url: '/workflow/task/completeTask',
        method: 'post',
        data: data
    })
}
// 驳回
export function backProcess(data) {
    return request({
        url: '/workflow/task/backProcess',
        method: 'post',
        data: data
    })
}
// 获取可驳回得任务节点
export function getTaskNodeList(taskId, processInstanceId) {
    return request({
        url: '/workflow/processInstance/getDismissibleNodeList/' + taskId + '/' + processInstanceId,
        method: 'get',
    })
}

/** 
 * 获取可驳回得任务节点
 * @param {string} processInstanceId 流程实例id
  * @param {string} taskId 
  * @returns
 */
export function getNodeList(processInstanceId, taskId) {
    return request.get(`/workflow/task/getTaskNodeList/${processInstanceId}/${taskId}`);
}

// 获取下游节点详情配置
export function nextUserTask(taskId) {
    return request({
        url: '/workflow/task/nextUserTask/' + taskId,
        method: 'get',
    })
}
// 审批记录
export function getHistoryRecord(data) {
    return request({
        url: '/document/workFlow/forwardRecord',
        method: 'get',
        params: data
    })
}
// 流程图
export function getHistoryImage(taskId) {
    return request({
        url: '/workflow/processInstance/getHistoryImage/' + taskId,
        method: 'get',
    })
}

// 查询发件箱
export function getOutbox(query) {
    return request({
        url: '/document/notify/out/box',
        method: 'get',
        params: query
    })
}
// 查询已办箱
export function getDonebox(query) {
    return request({
        url: '/document/notify/done/box',
        method: 'get',
        params: query
    })
}
// 公文查询（全）
export function getDocLedge(query) {
    return request({
        url: '/document/notify/docLedge/allType',
        method: 'get',
        params: query
    })
}