define(["exports","./ArcType-29cf2197","./arrayRemoveDuplicates-d2f048c5","./Cartesian2-db21342c","./Cartographic-3309dd0d","./ComponentDatatype-c140a87d","./when-b60132fc","./Rectangle-dee65d21","./EllipsoidRhumbLine-30b5229b","./GeometryAttribute-c65394ac","./GeometryAttributes-252e9929","./GeometryPipeline-7a733318","./IndexDatatype-8a5eead4","./Math-119be1a3","./FeatureDetection-806b12f0","./PolygonPipeline-d83979ed"],(function(e,t,r,i,n,a,o,s,u,l,h,c,f,p,d,y){"use strict";function g(){this._array=[],this._offset=0,this._length=0}Object.defineProperties(g.prototype,{length:{get:function(){return this._length}}}),g.prototype.enqueue=function(e){this._array.push(e),this._length++},g.prototype.dequeue=function(){if(0!==this._length){var e=this._array,t=this._offset,r=e[t];return e[t]=void 0,++t>10&&2*t>e.length&&(this._array=e.slice(t),t=0),this._offset=t,this._length--,r}},g.prototype.peek=function(){if(0!==this._length)return this._array[this._offset]},g.prototype.contains=function(e){return-1!==this._array.indexOf(e)},g.prototype.clear=function(){this._array.length=this._offset=this._length=0},g.prototype.sort=function(e){this._offset>0&&(this._array=this._array.slice(this._offset),this._offset=0),this._array.sort(e)};var v={computeHierarchyPackedLength:function(e){for(var t=0,r=[e];r.length>0;){var i=r.pop();if(o.defined(i)){t+=2;var a=i.positions,s=i.holes;if(o.defined(a)&&(t+=a.length*n.Cartesian3.packedLength),o.defined(s))for(var u=s.length,l=0;l<u;++l)r.push(s[l])}}return t},packPolygonHierarchy:function(e,t,r){for(var i=[e];i.length>0;){var a=i.pop();if(o.defined(a)){var s=a.positions,u=a.holes;if(t[r++]=o.defined(s)?s.length:0,t[r++]=o.defined(u)?u.length:0,o.defined(s))for(var l=s.length,h=0;h<l;++h,r+=3)n.Cartesian3.pack(s[h],t,r);if(o.defined(u))for(var c=u.length,f=0;f<c;++f)i.push(u[f])}}return r},unpackPolygonHierarchy:function(e,t){for(var r=e[t++],i=e[t++],a=new Array(r),o=i>0?new Array(i):void 0,s=0;s<r;++s,t+=n.Cartesian3.packedLength)a[s]=n.Cartesian3.unpack(e,t);for(var u=0;u<i;++u)o[u]=v.unpackPolygonHierarchy(e,t),t=o[u].startingIndex,delete o[u].startingIndex;return{positions:a,holes:o,startingIndex:t}}},m=new n.Cartesian3;function C(e,t,r,i){return n.Cartesian3.subtract(t,e,m),n.Cartesian3.multiplyByScalar(m,r/i,m),n.Cartesian3.add(e,m,m),[m.x,m.y,m.z]}v.subdivideLineCount=function(e,t,r){var i=n.Cartesian3.distance(e,t)/r,a=Math.max(0,Math.ceil(p.CesiumMath.log2(i)));return Math.pow(2,a)};var b=new n.Cartographic,w=new n.Cartographic,I=new n.Cartographic,T=new n.Cartesian3;v.subdivideRhumbLineCount=function(e,t,r,i){var n=e.cartesianToCartographic(t,b),a=e.cartesianToCartographic(r,w),o=new u.EllipsoidRhumbLine(n,a,e).surfaceDistance/i,s=Math.max(0,Math.ceil(p.CesiumMath.log2(o)));return Math.pow(2,s)},v.subdivideLine=function(e,t,r,i){var a=v.subdivideLineCount(e,t,r),s=n.Cartesian3.distance(e,t),u=s/a;o.defined(i)||(i=[]);var l=i;l.length=3*a;for(var h=0,c=0;c<a;c++){var f=C(e,t,c*u,s);l[h++]=f[0],l[h++]=f[1],l[h++]=f[2]}return l},v.subdivideRhumbLine=function(e,t,r,i,n){var a=e.cartesianToCartographic(t,b),s=e.cartesianToCartographic(r,w),l=new u.EllipsoidRhumbLine(a,s,e),h=l.surfaceDistance/i,c=Math.max(0,Math.ceil(p.CesiumMath.log2(h))),f=Math.pow(2,c),d=l.surfaceDistance/f;o.defined(n)||(n=[]);var y=n;y.length=3*f;for(var g=0,v=0;v<f;v++){var m=l.interpolateUsingSurfaceDistance(v*d,I),C=e.cartographicToCartesian(m,T);y[g++]=C.x,y[g++]=C.y,y[g++]=C.z}return y};var E=new n.Cartesian3,P=new n.Cartesian3,x=new n.Cartesian3,_=new n.Cartesian3;v.scaleToGeodeticHeightExtruded=function(e,t,r,i,a){i=o.defaultValue(i,s.Ellipsoid.WGS84);var u=E,l=P,h=x,c=_;if(o.defined(e)&&o.defined(e.attributes)&&o.defined(e.attributes.position))for(var f=e.attributes.position.values,p=f.length/2,d=0;d<p;d+=3)n.Cartesian3.fromArray(f,d,h),i.geodeticSurfaceNormal(h,u),c=i.scaleToGeodeticSurface(h,c),l=n.Cartesian3.multiplyByScalar(u,r,l),l=n.Cartesian3.add(c,l,l),f[d+p]=l.x,f[d+1+p]=l.y,f[d+2+p]=l.z,a&&(c=n.Cartesian3.clone(h,c)),l=n.Cartesian3.multiplyByScalar(u,t,l),l=n.Cartesian3.add(c,l,l),f[d]=l.x,f[d+1]=l.y,f[d+2]=l.z;return e},v.polygonOutlinesFromHierarchy=function(e,t,i){var a,s,u,l=[],h=new g;for(h.enqueue(e);0!==h.length;){var c=h.dequeue(),f=c.positions;if(t)for(u=f.length,a=0;a<u;a++)i.scaleToGeodeticSurface(f[a],f[a]);if(!((f=r.arrayRemoveDuplicates(f,n.Cartesian3.equalsEpsilon,!0)).length<3)){var p=c.holes?c.holes.length:0;for(a=0;a<p;a++){var d=c.holes[a],y=d.positions;if(t)for(u=y.length,s=0;s<u;++s)i.scaleToGeodeticSurface(y[s],y[s]);if(!((y=r.arrayRemoveDuplicates(y,n.Cartesian3.equalsEpsilon,!0)).length<3)){l.push(y);var v=0;for(o.defined(d.holes)&&(v=d.holes.length),s=0;s<v;s++)h.enqueue(d.holes[s])}}l.push(f)}}return l};var A=new n.Cartesian3(6378137,6378137,6378137);v.polygonsFromHierarchy=function(e,t,i,a){var s=[],u=[],l=new g;for(l.enqueue(e);0!==l.length;){var h,c,f,d=l.dequeue(),v=d.positions,m=d.holes,C=v.slice();if(i)for(c=v.length,h=0;h<c;h++)a.scaleToGeodeticSurface(v[h],C[h]);if(o.defined(a)&&!n.Cartesian3.equals(a._radii,A)&&(f=p.CesiumMath.EPSILON7),!((v=r.arrayRemoveDuplicates(C,n.Cartesian3.equalsEpsilon,!0,f)).length<3)){var b=t(v);if(o.defined(b)){var w=[],I=y.PolygonPipeline.computeWindingOrder2D(b);I===y.WindingOrder.CLOCKWISE&&(b.reverse(),v=v.slice().reverse());var T,E=v.slice(),P=o.defined(m)?m.length:0,x=[];for(h=0;h<P;h++){var _=m[h],L=_.positions;if(i)for(c=L.length,T=0;T<c;++T)a.scaleToGeodeticSurface(L[T],L[T]);if(!((L=r.arrayRemoveDuplicates(L,n.Cartesian3.equalsEpsilon,!0,p.CesiumMath.EPSILON7)).length<3)){var M=t(L);if(o.defined(M)){(I=y.PolygonPipeline.computeWindingOrder2D(M))===y.WindingOrder.CLOCKWISE&&(M.reverse(),L=L.slice().reverse()),x.push(L),w.push(E.length),E=E.concat(L),b=b.concat(M);var D=0;for(o.defined(_.holes)&&(D=_.holes.length),T=0;T<D;T++)l.enqueue(_.holes[T])}}}s.push({outerRing:v,holes:x}),u.push({positions:E,positions2D:b,holes:w})}}}return{hierarchy:s,polygons:u}};var L=new i.Cartesian2,M=new n.Cartesian3,D=new l.Quaternion,G=new d.Matrix3;v.computeBoundingRectangle=function(e,t,r,i,a){for(var s=l.Quaternion.fromAxisAngle(e,i,D),u=d.Matrix3.fromQuaternion(s,G),h=Number.POSITIVE_INFINITY,c=Number.NEGATIVE_INFINITY,f=Number.POSITIVE_INFINITY,p=Number.NEGATIVE_INFINITY,y=r.length,g=0;g<y;++g){var v=n.Cartesian3.clone(r[g],M);d.Matrix3.multiplyByVector(u,v,v);var m=t(v,L);o.defined(m)&&(h=Math.min(h,m.x),c=Math.max(c,m.x),f=Math.min(f,m.y),p=Math.max(p,m.y))}return a.x=h,a.y=f,a.width=c-h,a.height=p-f,a},v.createGeometryFromPositions=function(e,r,i,n,o,s){var u=y.PolygonPipeline.triangulate(r.positions2D,r.holes);u.length<3&&(u=[0,1,2]);var h=r.positions;if(n){for(var f=h.length,p=new Array(3*f),g=0,v=0;v<f;v++){var m=h[v];p[g++]=m.x,p[g++]=m.y,p[g++]=m.z}var C=new l.Geometry({attributes:{position:new l.GeometryAttribute({componentDatatype:a.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:p})},indices:u,primitiveType:d.PrimitiveType.TRIANGLES});return o.normal?c.GeometryPipeline.computeNormal(C):C}return s===t.ArcType.GEODESIC?y.PolygonPipeline.computeSubdivision(e,h,u,i):s===t.ArcType.RHUMB?y.PolygonPipeline.computeRhumbLineSubdivision(e,h,u,i):void 0};var S=[],N=new n.Cartesian3,R=new n.Cartesian3;v.computeWallGeometry=function(e,r,i,o,s,u){var c,y,g,m,C,b=u?1:0,w=e.length,I=0;if(o)for(y=3*(w-b)*2,c=new Array(2*y),g=0;g<w-b;g++)m=e[g],C=e[(g+1)%w],c[I]=c[I+y]=m.x,c[++I]=c[I+y]=m.y,c[++I]=c[I+y]=m.z,c[++I]=c[I+y]=C.x,c[++I]=c[I+y]=C.y,c[++I]=c[I+y]=C.z,++I;else{var T=p.CesiumMath.chordLength(i,r.maximumRadius),E=0;if(s===t.ArcType.GEODESIC)for(g=0;g<w;g++)E+=v.subdivideLineCount(e[g],e[(g+1)%w],T);else if(s===t.ArcType.RHUMB)for(g=0;g<w;g++)E+=v.subdivideRhumbLineCount(r,e[g],e[(g+1)%w],T);for(y=3*(E+w),c=new Array(2*y),g=0;g<w;g++){var P;m=e[g],C=e[(g+1)%w],s===t.ArcType.GEODESIC?P=v.subdivideLine(m,C,T,S):s===t.ArcType.RHUMB&&(P=v.subdivideRhumbLine(r,m,C,T,S));for(var x=P.length,_=0;_<x;++_,++I)c[I]=P[_],c[I+y]=P[_];c[I]=C.x,c[I+y]=C.x,c[++I]=C.y,c[I+y]=C.y,c[++I]=C.z,c[I+y]=C.z,++I}}w=c.length;var A=f.IndexDatatype.createTypedArray(w/3,w-6*(e.length-b)),L=0;for(w/=6,g=0;g<w;g++){var M=g,D=M+1,G=M+w,O=G+1;m=n.Cartesian3.fromArray(c,3*M,N),C=n.Cartesian3.fromArray(c,3*D,R),n.Cartesian3.equalsEpsilon(m,C,p.CesiumMath.EPSILON10,p.CesiumMath.EPSILON10)||(A[L++]=M,A[L++]=G,A[L++]=D,A[L++]=D,A[L++]=G,A[L++]=O)}return new l.Geometry({attributes:new h.GeometryAttributes({position:new l.GeometryAttribute({componentDatatype:a.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:c})}),indices:A,primitiveType:d.PrimitiveType.TRIANGLES})},e.PolygonGeometryLibrary=v}));
