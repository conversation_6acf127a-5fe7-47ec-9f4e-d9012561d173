define(["./AttributeCompression-0a087f75","./Cartographic-3309dd0d","./Rectangle-dee65d21","./Math-119be1a3","./createTaskProcessorWorker","./Cartesian2-db21342c","./Check-7b2a090c","./when-b60132fc"],(function(a,e,r,t,n,i,o,s){"use strict";var c=32767,u=new e.Cartographic,p=new e.Cartesian3,h=new r.Rectangle,l=new r.Ellipsoid,d={min:void 0,max:void 0};return n((function(n,i){var o=new Uint16Array(n.positions);!function(a){a=new Float64Array(a);var e=0;d.min=a[e++],d.max=a[e++],r.Rectangle.unpack(a,e,h),e+=r.Rectangle.packedLength,r.Ellipsoid.unpack(a,e,l)}(n.packedBuffer);var s=h,f=l,g=d.min,C=d.max,m=o.length/3,b=o.subarray(0,m),w=o.subarray(m,2*m),k=o.subarray(2*m,3*m);a.AttributeCompression.zigZagDeltaDecode(b,w,k);for(var v=new Float64Array(o.length),y=0;y<m;++y){var A=b[y],R=w[y],M=k[y],x=t.CesiumMath.lerp(s.west,s.east,A/c),D=t.CesiumMath.lerp(s.south,s.north,R/c),E=t.CesiumMath.lerp(g,C,M/c),F=e.Cartographic.fromRadians(x,D,E,u),T=f.cartographicToCartesian(F,p);e.Cartesian3.pack(T,v,3*y)}return i.push(v.buffer),{positions:v.buffer}}))}));
