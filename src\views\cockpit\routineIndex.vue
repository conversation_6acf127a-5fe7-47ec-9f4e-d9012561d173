<template>
  <main class="main-content">
    <!-- 左侧区域 -->
    <div class="left-panel">
        <!-- 储备空间 -->
        <div class="storage-info card resizable">
          <div class="card-header">
            <h2>储备空间</h2>
          </div>
          <div class="storage">
            <div class="storage-item">
              <div class="storage-label">
                <span>存储已用</span>
                <span class="value">5.96GB / 10GB</span>
              </div>
              <progress value="5.96" max="10" class="progress-bar danger"></progress>
            </div>
            <div class="storage-item">
              <div class="storage-label">
                <span>本地服务器存储已用</span>
                <span class="value">4.33GB / 15GB</span>
              </div>
              <progress value="4.33" max="15" class="progress-bar warning"></progress>
            </div>
            <div class="storage-item">
              <div class="storage-label">
                <span>建图已用</span>
                <span class="value">1988张 / 3000张</span>
              </div>
              <progress value="1988" max="3000" class="progress-bar success"></progress>
            </div>
            <div class="storage-item">
              <div class="storage-label">
                <span>视频直播已用</span>
                <span class="value">4120分钟 / 5000分钟</span>
              </div>
              <progress value="4120" max="5000" class="progress-bar warning"></progress>
            </div>
          </div>
        </div>
        <!-- 无人机状态信息 -->
        <div class="drone-status card resizable">
          <div class="card-header">
            <h2>无人机状态信息</h2>
          </div>
          <div class="status-grid">
            <div class="status-item">
              <div class="left-image">
                <img src="../../assets/images/drone/status/fjxx1.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value">{{StatusUtils.flightTaskStep(deviceState[1]?.device_state?.flighttask_step_code) }}</span>
                <span class="label">任务状态</span>
              </div>
            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="../../assets/images/drone/status/fjxx2.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value">{{deviceState[0]?.device_state?.battery?.capacity_percent}}%</span>
                <span class="label">飞行器剩余电量</span>
              </div>
            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="../../assets/images/drone/Information/9.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value">{{deviceState[0]?.device_state?.wind_speed}}</span>
                <span class="label">飞机端风速</span>
              </div>
            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="../../assets/images/drone/status/fjxx3.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value">{{ StatusUtils.kbToMb(deviceState[0]?.device_state?.storage?.used) }}/{{ StatusUtils.kbToMb(deviceState[0]?.device_state?.storage?.total) }} MB</span>
                <span class="label">已使用容量/总容量</span>
              </div>
            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="../../assets/images/drone/status/fjxx4.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value">{{StatusUtils.rtkStatus(deviceState[0]?.device_state?.position_state?.is_fixed)}}</span>
                <span class="label">RTK是否收敛</span>
              </div>
            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="../../assets/images/drone/tc4.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value">{{deviceState[0]?.device_state?.vertical_speed}}</span>
                <span class="label">垂直速度</span>
              </div>
            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="../../assets/images/drone/status/fjxx6.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value">{{deviceState[0]?.device_state?.position_state?.rtk_number}}</span>
                <span class="label">RTK搜星数量</span>
              </div>
            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="../../assets/images/drone/tc4.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value">{{deviceState[0]?.device_state?.position_state?.gps_number}}</span>
                <span class="label">GPS搜星数量</span>
              </div>
            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="../../assets/images/drone/tc4.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value">{{deviceState[0]?.device_state?.horizontal_speed}}</span>
                <span class="label">水平速度</span>
              </div>
            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="../../assets/images/drone/tc4.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value">{{deviceState[0]?.device_state?.height}}</span>
                <span class="label">绝对高度</span>
              </div>
            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="../../assets/images/drone/tc4.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value">{{deviceState[0]?.device_state?.elevation}}</span>
                <span class="label">相对起飞点高度</span>
              </div>
            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="../../assets/images/drone/status/fjxx10.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value">{{deviceState[0]?.device_state?.home_distance}}</span>
                <span class="label">距离Home点的距离</span>
              </div>
            </div>
          </div>
        </div>
    </div>

    <!-- 右侧区域 -->
    <div class="right-panel">
      <!-- 飞行统计图表 -->
      <div class="flight-statistics card resizable">
        <div class="card-header">
          <h2>飞行统计</h2>
        </div>
        <div class="chart-container">
          <div class="echarts-container" ref="flightChart"></div>
        </div>
      </div>
      <!-- 无人机机场状态信息 -->
      <div class="airport-status card resizable">
        <div class="card-header">
          <h2>无人机机场状态信息</h2>
        </div>
        <div class="airport-status-grid">
          <div class="status-item">
            <div class="left-image">
              <img src="../../assets/images/drone/Information/1.png" alt="" />
            </div>
            <div class="right-content">
              <span class="value highlight">{{StatusUtils.secondsToHours(deviceState[1]?.device_state?.acc_time)}}h</span>
              <span class="label">累计运行时长</span>
            </div>
          </div>
          <div class="status-item">
            <div class="left-image">
              <img src="../../assets/images/drone/Information/2.png" alt="" />
            </div>
            <div class="right-content">
              <span class="value">{{StatusUtils.timestampToDate(deviceState[1]?.device_state?.activation_time)}}</span>
              <span class="label">激活时间</span>
            </div>

          </div>
          <div class="status-item">
            <div class="left-image">
              <img src="../../assets/images/drone/Information/13.png" alt="" />
            </div>
            <div class="right-content">
              <span class="value">{{deviceState[1]?.device_state?.humidity }}%RH</span>
              <span class="label">舱内湿度</span>
            </div>
          </div>
          <div class="status-item">
            <div class="left-image">
              <img src="../../assets/images/drone/Information/10.png" alt="" />
            </div>
            <div class="right-content">
              <span class="value safe">{{StatusUtils.rainfallStatus(deviceState[1]?.device_state?.rainfall) }}</span>
              <span class="label">阵雨情况</span>
            </div>

          </div>
          <div class="status-item">
            <div class="left-image">
              <img src="../../assets/images/drone/Information/3.png" alt="" />
            </div>
            <div class="right-content">
              <span class="value highlight"> {{deviceState[1]?.device_state?.job_number }}次</span>
              <span class="label">累计作业次数</span>
            </div>
          </div>
          <div class="status-item">
            <div class="left-image">
              <img src="../../assets/images/drone/Information/4.png" alt="" />
            </div>
            <div class="right-content">
              <span class="value"> {{deviceState[1]?.device_state?.network_state?.rate }} KB/s</span>
              <span class="label">网络状态</span>
            </div>
          </div>
          <div class="status-item">
            <div class="left-image">
              <img src="../../assets/images/drone/Information/9.png" alt="" />
            </div>
            <div class="right-content">
              <span class="value safe">{{deviceState[1]?.device_state?.wind_speed }} m/s</span>
              <span class="label">风速</span>
            </div>
          </div>
          <div class="status-item">
            <div class="left-image">
              <img src="../../assets/images/drone/Information/16.png" alt="" />
            </div>
            <div class="right-content">
              <span class="value">{{deviceState[1]?.device_state?.working_current }} mA</span>
              <span class="label">工作电流</span>
            </div>

          </div>
          <div class="status-item">
            <div class="left-image">
              <img src="../../assets/images/drone/Information/7.png" alt="" />
            </div>
            <div class="right-content">
              <span class="value highlight">{{deviceState[1]?.device_state?.drone_charge_state?.capacity_percent }}%</span>
              <span class="label">飞行器电量</span>
            </div>

          </div>
          <div class="status-item">
            <div class="left-image">
              <img src="../../assets/images/drone/Information/8.png" alt="" />
            </div>
            <div class="right-content">
              <span class="value">{{StatusUtils.chargeState(deviceState[1]?.device_state?.drone_charge_state?.state) }}</span>
              <span class="label">飞行器充电状态</span>
            </div>

          </div>
          <div class="status-item">
            <div class="left-image">
              <img src="../../assets/images/drone/Information/15.png" alt="" />
            </div>
            <div class="right-content">
              <span class="value">{{ deviceState[1]?.device_state?.working_voltage }} mV</span>
              <span class="label">工作电压</span>
            </div>
          </div>
          <div class="status-item">
            <div class="left-image">
              <img src="../../assets/images/drone/Information/6.png" alt="" />
            </div>
            <div class="right-content">
              <span class="value">{{ StatusUtils.kbToMb(deviceState[1]?.device_state?.storage?.used) }}/{{ StatusUtils.kbToMb(deviceState[1]?.device_state?.storage?.total) }} MB</span>
              <span class="label">已使用容量/总容量</span>
            </div>
          </div>
          <div class="status-item">
            <div class="left-image">
              <img src="../../assets/images/drone/Information/14.png" alt="" />
            </div>
            <div class="right-content">
              <span class="value">{{StatusUtils.droneInDockStatus(deviceState[1]?.device_state?.drone_in_dock) }}</span>
              <span class="label">是否在仓</span>
            </div>
          </div>
          <div class="status-item">
            <div class="left-image">
              <img src="../../assets/images/drone/Information/11.png" alt="" />
            </div>
            <div class="right-content">
              <span class="value">{{ deviceState[1]?.device_state?.temperature}}°C</span>
              <span class="label">环境温度</span>
            </div>
          </div>
          <div class="status-item">
            <div class="left-image">
              <img src="../../assets/images/drone/Information/12.png" alt="" />
            </div>
            <div class="right-content">
              <span class="value warning">29.6°C</span>
              <span class="label">舱内温度</span>
            </div>
          </div>
          <div class="status-item">
            <div class="left-image">
              <img src="../../assets/images/drone/Information/5.png" alt="" />
            </div>
            <div class="right-content">
              <span class="value safe">{{ deviceState[1]?.device_state?.media_file_detail?.remain_upload }}个</span>
              <span class="label">待上传文件</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'
import { StatusUtils } from '@/constants/flightTask.js'
import { deviceModel, monthOfYear } from "@/api/uav/flightHub2/way_line.js"
const flightChart = ref(null)
let chartData = {
  months: [],
  missions: [],
  data: []
}

const deviceState = ref([])

const initChart = () => {
  if (!flightChart.value) return;

  const existingInstance = echarts.getInstanceByDom(flightChart.value);
  if (existingInstance) {
    existingInstance.dispose();
  }

  const chartInstance = echarts.init(flightChart.value);

  const colorPalette = {
    blueGradient: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: '#36D1DC' },
      { offset: 1, color: '#5B86E5' }
    ]),
    redGradient: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: '#FF6B6B' },
      { offset: 1, color: '#FF3F3F' }
    ]),
    axisLine: '#2D3E50',
    splitLine: 'rgba(45, 62, 80, 0.3)',
    text: '#7D8B9C',
    highlight: '#E0E6ED'
  };

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
        shadowStyle: {
          color: 'rgba(0, 0, 0, 0.1)'
        }
      },
      backgroundColor: 'rgba(25, 35, 50, 0.9)',
      borderColor: '#2D3E50',
      borderWidth: 1,
      padding: [10, 15],
      textStyle: {
        color: '#E0E6ED',
        fontSize: 12
      },
      formatter: params => {
        let tip = `<div style="font-size: 14px; font-weight: bold; margin-bottom: 8px; color: ${params[0].color}">${params[0].axisValue}</div>`;
        params.forEach(item => {
          tip += `
            <div style="display: flex; align-items: center; margin: 6px 0;">
              <div style="
                width: 10px;
                height: 10px;
                border-radius: 2px;
                background: ${item.color};
                margin-right: 8px;
              "></div>
              <span style="flex: 1;">${item.seriesName}</span>
              <strong style="margin-left: 10px; color: ${item.color}">${item.value}${item.seriesIndex === 0 ? '次' : '张'}</strong>
            </div>
          `;
        });
        return tip;
      }
    },
    legend: {
      data: ['执飞任务次数', '飞行采集数据数量'],
      right: 320,
      top: 0,
      textStyle: {
        color: colorPalette.text,
        fontSize: 12,
        fontWeight: 'normal'
      },
      itemWidth: 14,
      itemHeight: 8,
      itemGap: 16,
      itemStyle: {
        borderRadius: 2
      }
    },
    grid: {
      left: '1%',
      right: '1%',
      bottom: '1%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: chartData.months,
      axisLine: {
        lineStyle: {
          color: colorPalette.axisLine,
          width: 1
        }
      },
      axisLabel: {
        color: colorPalette.text,
        fontSize: 11,
        margin: 10,
        interval: 0
      },
      axisTick: {
        alignWithLabel: true,
        lineStyle: {
          color: colorPalette.axisLine
        }
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '任务次数',
        nameTextStyle: {
          color: colorPalette.text,
          fontSize: 12,
          padding: [0, 0, 0, 40]
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: colorPalette.axisLine
          }
        },
        axisLabel: {
          color: colorPalette.text,
          fontSize: 11
        },
        splitLine: {
          lineStyle: {
            color: colorPalette.splitLine,
            type: 'dashed'
          }
        },
        splitNumber: 5
      },
      {
        type: 'value',
        name: '数据量(张)',
        nameTextStyle: {
          color: colorPalette.text,
          fontSize: 12,
          padding: [0, 0, 0, 40]
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: colorPalette.axisLine
          }
        },
        axisLabel: {
          color: colorPalette.text,
          fontSize: 11
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: '执飞任务次数',
        type: 'bar',
        barWidth: '18%',
        barGap: '100%',
        barCategoryGap: '60%',
        data: chartData.missions,
        itemStyle: {
          color: colorPalette.blueGradient,
          borderRadius: [4, 4, 0, 0],
          shadowColor: 'rgba(54, 209, 220, 0.3)',
          shadowBlur: 6,
          shadowOffsetY: 3
        },
        label: {
          show: true,
          position: 'top',
          color: colorPalette.highlight,
          fontSize: 11,
          fontWeight: 'bold',
          formatter: '{c}次',
          distance: 5
        },
        emphasis: {
          itemStyle: {
            shadowColor: 'rgba(54, 209, 220, 0.5)',
            shadowBlur: 10
          }
        },
        animationDelay: function (idx) {
          return idx * 100;
        }
      },
      {
        name: '飞行采集数据数量',
        type: 'bar',
        barWidth: '18%',
        yAxisIndex: 1,
        barGap: '100%',
        barCategoryGap: '10%',
        data: chartData.data,
        itemStyle: {
          color: colorPalette.redGradient,
          borderRadius: [4, 4, 0, 0],
          shadowColor: 'rgba(255, 107, 107, 0.3)',
          shadowBlur: 6,
          shadowOffsetY: 3
        },
        label: {
          show: true,
          position: 'top',
          color: colorPalette.highlight,
          fontSize: 11,
          fontWeight: 'bold',
          formatter: '{c}张',
          distance: 5
        },
        emphasis: {
          itemStyle: {
            shadowColor: 'rgba(255, 107, 107, 0.5)',
            shadowBlur: 10
          }
        },
        animationDelay: function (idx) {
          return idx * 100 + 100;
        }
      }
    ],
    animationDuration: 1200,
    animationEasing: 'elasticOut'
  };

  window.addEventListener('resize', function() {
    chartInstance.resize();
  });

  chartInstance.setOption(option);
  return chartInstance;
};

const getList = async () => {
  const resMonth = await monthOfYear()
  chartData = { months: [], missions: [], data: [] }

  for (let month = 1; month <= 12; month++) {
    const monthKey = month.toString()
    chartData.months.push(monthKey)
    const currentData = resMonth.data[monthKey] || {taskCount: 0, picCount: 0}
    chartData.missions.push(currentData.taskCount)
    chartData.data.push(currentData.picCount)
  }

  initChart()

  const resDevice = await deviceModel()
  deviceState.value = resDevice.data
}

onMounted(async () => {
  await getList()
})

onBeforeUnmount(() => {
  if (flightChart.value) {
    const instance = echarts.getInstanceByDom(flightChart.value)
    instance && instance.dispose()
  }
})
</script>

<style scoped>
.main-content {
  height: 85vh;
  min-height: 85vh;
  max-height: 85vh;
  box-sizing: border-box;
  display: grid;
  grid-template-columns: 1fr 1fr;

  gap: 1%;
  padding: 1%;
}

.left-panel,
.right-panel {
  display: flex;
  flex-direction: column;
  gap: 1%;
  height: 100%;
}

.card {
  background-color: transparent;
  background-image: url("@/assets/images/drone/cgzt_05.png") !important;
  background-size: cover;
  background-repeat: no-repeat;
  padding: 1%;
  border: none;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  align-self: stretch;

}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 8%;
  margin-left: 6%;
}

.storage{
  margin-top: 5%;
  width: 95%;
  margin-left: 2%;
}
.storage-info .storage-item {
  margin-bottom: 20px;
}
.storage-label {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.85);
  margin-bottom: 8px;
}
.progress-bar {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  overflow: hidden;
}
.progress-bar::-webkit-progress-bar {
  background: transparent;
}
.progress-bar.danger::-webkit-progress-value {
  background: linear-gradient(90deg, #ff416c 0%, #ff4b2b 100%);
}
.progress-bar.warning::-webkit-progress-value {
  background: linear-gradient(90deg, #ffb347 0%, #ffcc33 100%);
}
.progress-bar.success::-webkit-progress-value {
  background: linear-gradient(90deg, #00e676 0%, #00c853 100%);
}
.value {
  display: block;
  color: #4facfe;
  margin-bottom: 4px;
}


.drone-status .status-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  padding-top: 4%;
  gap: 15px;
}
.airport-status-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  padding-top: 4%;
  gap: 15px;
}
.status-item {
  background: #00338299;
  border-radius: 10px;
  padding: 1%;
  display: flex;
  align-items: center;
  transition: transform 0.3s;
}
.status-item::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: 10px;
  padding: 2px;
  background: linear-gradient(45deg, #00c6ff, #0072ff);
  -webkit-mask:
      linear-gradient(#fff, #fff) content-box,
      linear-gradient(#fff, #fff);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
}
.status-item:hover {
  transform: translateY(-2px);
}

.left-image img {
  width: 40px;
  margin-right: 14px;
}
.right-content {
  flex: 1;
}
.label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

.chart-container{
  height: 100%;
}
.flight-statistics {
  height: 100%;
}
.echarts-container {
  padding-top: 5%;
  width: 100%;
  height: 300px;
}
</style>
