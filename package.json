{"name": "ldcc-web-front", "version": "3.8.8", "description": "储备土地管理与巡查系统", "author": "若依", "license": "MIT", "type": "module", "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview", "lint": "eslint --ext .js,.vue src/", "lint:fix": "eslint --fix .js,.vue src/"}, "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@onlyoffice/document-editor-vue": "^1.4.0", "@supermap/iclient-ol": "^11.1.1", "@volcengine/rtc": "^4.66.6", "@volcengine/veplayer": "^2.6.2", "@volcengine/veplayer-plugin": "^2.6.2", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "^10.11.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "agora-rtc-sdk-ng": "^4.23.3", "axios": "^1.8.4", "echarts": "5.5.1", "element-plus": "2.7.6", "file-saver": "^2.0.5", "fuse.js": "6.6.2", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "jszip": "^3.10.1", "mqtt": "^5.13.0", "nprogress": "0.2.0", "ol": "^10.4.0", "pinia": "2.1.7", "proj4": "^2.15.0", "quill-blot-formatter": "^1.0.5", "shapefile": "^0.6.6", "sortablejs": "^1.15.3", "uuid": "^10.0.0", "video.js": "^8.22.0", "videojs-contrib-hls": "^5.15.0", "vue": "3.4.31", "vue-cropper": "1.1.1", "vue-router": "4.4.0"}, "devDependencies": {"@eslint/js": "^9.22.0", "@vitejs/plugin-legacy": "^5.4.2", "@vitejs/plugin-vue": "^5.1.2", "eslint": "^9.15.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.31.0", "globals": "^16.0.0", "sass": "1.77.5", "unplugin-auto-import": "^0.17.6", "unplugin-vue-components": "^28.8.0", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "^5.4.0", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-zip-pack": "^1.2.4"}}