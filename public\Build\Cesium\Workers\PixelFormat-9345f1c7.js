define(["exports","./WebGLConstants-4ae0db90"],(function(_,t){"use strict";const n={UNSIGNED_BYTE:t.WebGLConstants.UNSIGNED_BYTE,UNSIGNED_SHORT:t.WebGLConstants.UNSIGNED_SHORT,UNSIGNED_INT:t.WebGLConstants.UNSIGNED_INT,FLOAT:t.WebGLConstants.FLOAT,HALF_FLOAT:t.WebGLConstants.HALF_FLOAT_OES,UNSIGNED_INT_24_8:t.WebGLConstants.UNSIGNED_INT_24_8,UNSIGNED_SHORT_4_4_4_4:t.WebGLConstants.UNSIGNED_SHORT_4_4_4_4,UNSIGNED_SHORT_5_5_5_1:t.WebGLConstants.UNSIGNED_SHORT_5_5_5_1,UNSIGNED_SHORT_5_6_5:t.WebGLConstants.UNSIGNED_SHORT_5_6_5,toWebGLConstant:function(_,e){switch(_){case n.UNSIGNED_BYTE:return t.WebGLConstants.UNSIGNED_BYTE;case n.UNSIGNED_SHORT:return t.WebGLConstants.UNSIGNED_SHORT;case n.UNSIGNED_INT:return t.WebGLConstants.UNSIGNED_INT;case n.FLOAT:return t.WebGLConstants.FLOAT;case n.HALF_FLOAT:return e.webgl2?t.WebGLConstants.HALF_FLOAT:t.WebGLConstants.HALF_FLOAT_OES;case n.UNSIGNED_INT_24_8:return t.WebGLConstants.UNSIGNED_INT_24_8;case n.UNSIGNED_SHORT_4_4_4_4:return t.WebGLConstants.UNSIGNED_SHORT_4_4_4_4;case n.UNSIGNED_SHORT_5_5_5_1:return t.WebGLConstants.UNSIGNED_SHORT_5_5_5_1;case n.UNSIGNED_SHORT_5_6_5:return n.UNSIGNED_SHORT_5_6_5}},isPacked:function(_){return _===n.UNSIGNED_INT_24_8||_===n.UNSIGNED_SHORT_4_4_4_4||_===n.UNSIGNED_SHORT_5_5_5_1||_===n.UNSIGNED_SHORT_5_6_5},sizeInBytes:function(_){switch(_){case n.UNSIGNED_BYTE:return 1;case n.UNSIGNED_SHORT:case n.UNSIGNED_SHORT_4_4_4_4:case n.UNSIGNED_SHORT_5_5_5_1:case n.UNSIGNED_SHORT_5_6_5:case n.HALF_FLOAT:return 2;case n.UNSIGNED_INT:case n.FLOAT:case n.UNSIGNED_INT_24_8:return 4}},validate:function(_){return _===n.UNSIGNED_BYTE||_===n.UNSIGNED_SHORT||_===n.UNSIGNED_INT||_===n.FLOAT||_===n.HALF_FLOAT||_===n.UNSIGNED_INT_24_8||_===n.UNSIGNED_SHORT_4_4_4_4||_===n.UNSIGNED_SHORT_5_5_5_1||_===n.UNSIGNED_SHORT_5_6_5}};var e=Object.freeze(n);const G={DEPTH_COMPONENT:t.WebGLConstants.DEPTH_COMPONENT,DEPTH_STENCIL:t.WebGLConstants.DEPTH_STENCIL,ALPHA:t.WebGLConstants.ALPHA,RGB:t.WebGLConstants.RGB,RGBA:t.WebGLConstants.RGBA,LUMINANCE:t.WebGLConstants.LUMINANCE,LUMINANCE_ALPHA:t.WebGLConstants.LUMINANCE_ALPHA,RGB_DXT1:t.WebGLConstants.COMPRESSED_RGB_S3TC_DXT1_EXT,RGBA_DXT1:t.WebGLConstants.COMPRESSED_RGBA_S3TC_DXT1_EXT,RGBA_DXT3:t.WebGLConstants.COMPRESSED_RGBA_S3TC_DXT3_EXT,RGBA_DXT5:t.WebGLConstants.COMPRESSED_RGBA_S3TC_DXT5_EXT,RGB_PVRTC_4BPPV1:t.WebGLConstants.COMPRESSED_RGB_PVRTC_4BPPV1_IMG,RGB_PVRTC_2BPPV1:t.WebGLConstants.COMPRESSED_RGB_PVRTC_2BPPV1_IMG,RGBA_PVRTC_4BPPV1:t.WebGLConstants.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG,RGBA_PVRTC_2BPPV1:t.WebGLConstants.COMPRESSED_RGBA_PVRTC_2BPPV1_IMG,RGBA_ASTC:t.WebGLConstants.COMPRESSED_RGBA_ASTC_4x4_WEBGL,RGB_ETC1:t.WebGLConstants.COMPRESSED_RGB_ETC1_WEBGL,RGB8_ETC2:t.WebGLConstants.COMPRESSED_RGB8_ETC2,RGBA8_ETC2_EAC:t.WebGLConstants.COMPRESSED_RGBA8_ETC2_EAC,RGBA_BC7:t.WebGLConstants.COMPRESSED_RGBA_BPTC_UNORM,componentsLength:function(_){switch(_){case G.RGB:return 3;case G.RGBA:return 4;case G.LUMINANCE_ALPHA:return 2;case G.ALPHA:case G.LUMINANCE:default:return 1}},validate:function(_){return _===G.DEPTH_COMPONENT||_===G.DEPTH_STENCIL||_===G.ALPHA||_===G.RGB||_===G.RGBA||_===G.LUMINANCE||_===G.LUMINANCE_ALPHA||_===G.RGB_DXT1||_===G.RGBA_DXT1||_===G.RGBA_DXT3||_===G.RGBA_DXT5||_===G.RGB_PVRTC_4BPPV1||_===G.RGB_PVRTC_2BPPV1||_===G.RGBA_PVRTC_4BPPV1||_===G.RGBA_PVRTC_2BPPV1||_===G.RGBA_ASTC||_===G.RGB_ETC1||_===G.RGB8_ETC2||_===G.RGBA8_ETC2_EAC||_===G.RGBA_BC7},isColorFormat:function(_){return _===G.ALPHA||_===G.RGB||_===G.RGBA||_===G.LUMINANCE||_===G.LUMINANCE_ALPHA},isDepthFormat:function(_){return _===G.DEPTH_COMPONENT||_===G.DEPTH_STENCIL},isCompressedFormat:function(_){return _===G.RGB_DXT1||_===G.RGBA_DXT1||_===G.RGBA_DXT3||_===G.RGBA_DXT5||_===G.RGB_PVRTC_4BPPV1||_===G.RGB_PVRTC_2BPPV1||_===G.RGBA_PVRTC_4BPPV1||_===G.RGBA_PVRTC_2BPPV1||_===G.RGBA_ASTC||_===G.RGB_ETC1||_===G.RGB8_ETC2||_===G.RGBA8_ETC2_EAC||_===G.RGBA_BC7},isDXTFormat:function(_){return _===G.RGB_DXT1||_===G.RGBA_DXT1||_===G.RGBA_DXT3||_===G.RGBA_DXT5},isPVRTCFormat:function(_){return _===G.RGB_PVRTC_4BPPV1||_===G.RGB_PVRTC_2BPPV1||_===G.RGBA_PVRTC_4BPPV1||_===G.RGBA_PVRTC_2BPPV1},isASTCFormat:function(_){return _===G.RGBA_ASTC},isETC1Format:function(_){return _===G.RGB_ETC1},isETC2Format:function(_){return _===G.RGB8_ETC2||_===G.RGBA8_ETC2_EAC},isBC7Format:function(_){return _===G.RGBA_BC7},compressedTextureSizeInBytes:function(_,t,n){switch(_){case G.RGB_DXT1:case G.RGBA_DXT1:case G.RGB_ETC1:case G.RGB8_ETC2:return Math.floor((t+3)/4)*Math.floor((n+3)/4)*8;case G.RGBA_DXT3:case G.RGBA_DXT5:case G.RGBA_ASTC:case G.RGBA8_ETC2_EAC:return Math.floor((t+3)/4)*Math.floor((n+3)/4)*16;case G.RGB_PVRTC_4BPPV1:case G.RGBA_PVRTC_4BPPV1:return Math.floor((Math.max(t,8)*Math.max(n,8)*4+7)/8);case G.RGB_PVRTC_2BPPV1:case G.RGBA_PVRTC_2BPPV1:return Math.floor((Math.max(t,16)*Math.max(n,8)*2+7)/8);case G.RGBA_BC7:return Math.ceil(t/4)*Math.ceil(n/4)*16;default:return 0}},textureSizeInBytes:function(_,t,n,s){let T=G.componentsLength(_);return e.isPacked(t)&&(T=1),T*e.sizeInBytes(t)*n*s},alignmentInBytes:function(_,t,n){const e=G.textureSizeInBytes(_,t,n,1)%4;return 0===e?4:2===e?2:1},createTypedArray:function(_,t,n,s){let T;const R=e.sizeInBytes(t);T=R===Uint8Array.BYTES_PER_ELEMENT?Uint8Array:R===Uint16Array.BYTES_PER_ELEMENT?Uint16Array:R===Float32Array.BYTES_PER_ELEMENT&&t===e.FLOAT?Float32Array:Uint32Array;return new T(G.componentsLength(_)*n*s)},flipY:function(_,t,n,e,s){if(1===s)return _;const T=G.createTypedArray(t,n,e,s),R=G.componentsLength(t),E=e*R;for(let t=0;t<s;++t){const n=t*e*R,G=(s-t-1)*e*R;for(let t=0;t<E;++t)T[G+t]=_[n+t]}return T},toInternalFormat:function(_,n,s){if(!s.webgl2)return _;if(_===G.DEPTH_STENCIL)return t.WebGLConstants.DEPTH24_STENCIL8;if(_===G.DEPTH_COMPONENT){if(n===e.UNSIGNED_SHORT)return t.WebGLConstants.DEPTH_COMPONENT16;if(n===e.UNSIGNED_INT)return t.WebGLConstants.DEPTH_COMPONENT24}if(n===e.FLOAT)switch(_){case G.RGBA:return t.WebGLConstants.RGBA32F;case G.RGB:return t.WebGLConstants.RGB32F;case G.RG:return t.WebGLConstants.RG32F;case G.R:return t.WebGLConstants.R32F}if(n===e.HALF_FLOAT)switch(_){case G.RGBA:return t.WebGLConstants.RGBA16F;case G.RGB:return t.WebGLConstants.RGB16F;case G.RG:return t.WebGLConstants.RG16F;case G.R:return t.WebGLConstants.R16F}return _}};var s=Object.freeze(G);_.PixelDatatype=e,_.PixelFormat=s}));
