<template>
  <div class="main-live">
    <div class="content-area">
      <div
        class="left-panel"
        :style="{ width: leftPanelWidth + 'px' }"
      >
        <div class="map-container">
          <div class="header-drawer">
            <el-button
              type="text"
              icon="el-icon-video-play" @click="toggleAirportDrawer"
            >
              <span>{{ drawerOpen ? '隐藏机场' : '显示机场' }}</span>
              <i :class="['el-icon-arrow-down', { 'rotate-180': drawerOpen }]"/>
            </el-button>
          </div>
        </div>
        <div class="viewer-container">
          <div
            class="airport-drawer" :class="{ 'drawer-open': drawerOpen }"
            :style="{ width: leftPanelWidth + 'px', height: drawerOpen ? drawerHeight + 'px' : '0' }"
          >
            <div class="airport-control">
              <!-- 加载动画，初始时显示 -->
              <div
                v-if="isLoading"
                class="loading-animation"
              >
                <div class="spinner"/>
                <span>视频加载中...</span>
              </div>
              <div class="video-container">
                <div
                  v-if="useRTCLive"
                  ref="remotePlayer" id="remote-player" class="airport-video-player"
                  :class="{ 'hidden': isLoading }"
                />
                <div
                  v-else-if="!useRTCLive"
                  ref="hlsVideoContainer"
                >
                  <video
                    id="airport-video"
                    ref="airportVideo" class="video-js vjs-default-skin" muted
                    controls style="width: 100%;height: 100%"
                  >
                    <source
                      v-if="drones.airportConverterId"
                      :src="airportHlsUrl" type="application/x-mpegURL"
                    >
                  </video>
                </div>
              </div>
            </div>
            <div
              class="splitterAirport"
              @mousedown="startAirportDrag"
            />
          </div>
          <Viewer3d
            ref="viewer3d"
            @onCreateViewer3d="createViewer3d"
          />
        </div>
      </div>
      <div
        class="splitter"
        @mousedown="startDrag"
      />
      <div class="right-panel">
        <div class="header-drawer">
          <el-button
            type="text"
            icon="el-icon-video-play" @click="toggleDroneLive" :disabled="!droneIsTakeOff || (useRTCLive ? isDroneInRoom && !canStop : !drones.droneConverterId && !canStart)"
          >
            {{useRTCLive ? (isDroneInRoom ? '关闭无人机直播' : '开始无人机直播') : (drones.droneConverterId ? '关闭无人机直播(HLS)' : '开始无人机直播(HLS)') }}
          </el-button>
          <el-button
            type="text"
            icon="el-icon-video-play" @click="toggleStatusDrawer"
          >无人机状态</el-button>
        </div>
        <!-- 修改状态面板为抽屉式 -->
        <div
          class="status-drawer"
          :class="{ 'drawer-open': statusDrawerOpen }"
        >
          <div class="status-content">
            <div class="header-right-status">
              <!-- 原有状态项保持不变 -->
              <div
                class="header-item"
                v-for="(item, index) in statusItems" :key="index"
              >
                <h2>{{ item.label }}</h2>
                {{ item.value }}
              </div>
            </div>
            <!--            <div class="header-left-operate">-->
            <!--              <el-button type="text" icon="el-icon-video-play" @click="toggleLiveMode">-->
            <!--                切换到{{ useRTCLive ? 'HLS' : 'RTC' }}模式-->
            <!--              </el-button>-->
            <!--            </div>-->
          </div>
        </div>
        <div class="drone-video-container">
          <!-- 视频播放器容器 -->
          <div
            v-if="useRTCLive"
            ref="dronePlayer" id="drone-player" class="video-player"
          />
          <div
            v-else-if="!useRTCLive"
            ref="hlsVideoContainer"
          >
            <video
              id="drone-video"
              class="video-js vjs-default-skin" muted controls
              style="width: 100%;height: 100%"
            >
              <source
                v-if="drones.droneConverterId"
                :src="droneHlsUrl" type="application/x-mpegURL"
              >
            </video>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, provide, reactive, watch, nextTick } from 'vue'
import VERTC, { IRTCEngine, StreamIndex } from '@volcengine/rtc'
import AgoraRTC, { IAgoraRTCClient } from 'agora-rtc-sdk-ng'
import mqtt from 'mqtt'
import videojs from "video.js/dist/video.min"
import 'video.js/dist/video-js.min.css'
import UseViewer3d from "@/components/GISTools/Viewer/UseViewer3d.js"
import Viewer3d from "@/components/GISTools/Viewer/Viewer3d.vue"
import { useDroneStore } from '@/store/modules/drone.js'
import useMapViewStore from "@/store/modules/map/mapView.js"
import { liveBroadcast } from "@/api/uav/liveBroadcast.js"
import { streamConvertersDelete, streamConvertersGet, streamConvertersPost } from "@/api/uav/cloudConnect.js"
import { StatusUtils } from '@/constants/flightTask.js'

// 使用无人机状态存储
const drones = useDroneStore()
// 使用地图视图存储
const store = useMapViewStore()

// 直播模式切换 (RTC/HLS)
const useRTCLive = ref(true)
// 机场抽屉高度
const drawerHeight = ref(300);

// 3D地图相关引用
const viewer3d = ref(null)
// 无人机实体引用
const droneEntity = ref(null)
// 初始位置和飞行路径
let initialPosition
let flightPathPositions = []
let flightPathEntity
// 添加状态抽屉控制变量
const statusDrawerOpen = ref(false);
const toggleStatusDrawer = () => {
  statusDrawerOpen.value = !statusDrawerOpen.value;
};

// 将状态数据转换为数组便于渲染
const statusItems = computed(() => [
  { label: '剩余飞行时间', value: droneInfo.value?.battery?.remain_flight_time + ' / s' },
  { label: '电池剩余电量', value: droneInfo.value?.battery?.capacity_percent + '%' },
  // { label: '返航所需电量', value: droneInfo.value.battery.return_home_power + '%' },
  // { label: '降落所需电量', value: droneInfo.value.battery.landing_power + '%' },
  // { label: '高度限制', value: droneInfo.value.height_limit + ' m' },
  // { label: '绝对高度', value: StatusUtils.toFixed2(droneInfo.value.height) + ' m' },
  { label: '相对起飞高度', value: droneInfo.value.elevation + ' m' },
  { label: '水平速度', value: droneInfo.value.horizontal_speed + ' m/s' },
  { label: '垂直速度', value: droneInfo.value.vertical_speed + ' m/s' },
  { label: '飞行器状态', value: StatusUtils.flightStateCodeToChinese(droneInfo.value.mode_code) }
]);
// 无人机状态信息
const droneInfo = ref({
  battery: {
    capacity_percent: "--", // 电池百分比
    remain_flight_time: "--", // 剩余飞行时间
    return_home_power: "--", // 返航所需电量
    landing_power: "--" // 降落所需电量
  },
  horizontal_speed: "--", // 水平速度
  vertical_speed: "--", // 垂直速度
  mode_code: 14, // 飞行模式代码 (14=未连接)
  height_limit: "--", // 高度限制
  elevation: '--', // 海拔高度
  height: "--" // 相对高度
})

// 视频相关状态
const droneIsTakeOff = ref(false) // 无人机是否起飞
const airportVideo = ref(null) // 机场视频元素引用
const airportHlsUrl = ref('') // 机场HLS流地址
const droneHlsUrl = ref('') // 无人机HLS流地址

// RTC引擎实例
const airportEngine = ref < IRTCEngine | null > (null) // 机场RTC引擎
const droneEngine = ref < IRTCEngine | null > (null) // 无人机RTC引擎
const isAirportInRoom = ref(false) // 机场是否在RTC房间
const isDroneInRoom = ref(false) // 无人机是否在RTC房间
const isUsingAgora = ref(false) // 是否使用Agora SDK
const airportAgoraClient = ref < IAgoraRTCClient | null > (null) // 机场Agora客户端
const droneAgoraClient = ref < IAgoraRTCClient | null > (null) // 无人机Agora客户端
const isLoading = ref(false) // 无人机是否在RTC房间

// 3D视图状态管理
const viewer3dProp = reactive({}) // 3D视图属性
const isShowResource = ref(false) // 是否显示资源

// MQTT相关状态
const mqttClient = ref(null) // MQTT客户端实例
const mqttStatus = ref('disconnected') // MQTT连接状态

// 左侧面板宽度
const leftPanelWidth = ref(600);
// 拖拽状态
const isDragging = ref(false);
// 机场抽屉是否打开
const drawerOpen = ref(false);

// 计算属性 - 是否可以开始直播
const canStart = computed(() => !isDroneInRoom.value && droneIsTakeOff.value);
// 计算属性 - 是否可以停止直播
const canStop = computed(() => isDroneInRoom.value || drones.droneConverterId);

/**
 * 切换机场抽屉状态
 * 打开时自动开始直播，关闭时自动停止直播
 */
const toggleAirportDrawer = () => {
  const wasOpen = drawerOpen.value;
  drawerOpen.value = !wasOpen;

  if (drawerOpen.value && !wasOpen) {
    // 抽屉打开 - 开始直播
    if (useRTCLive.value) {
      joinRTCHandler(false);
    } else {
      joinRoom(false);
    }
  } else if (!drawerOpen.value && wasOpen) {
    // 抽屉关闭 - 停止直播
    if (useRTCLive.value) {
      leaveRTCHandler(false);
    } else {
      leaveRoom(false);
    }
  }
};

/**
 * 切换无人机直播状态
 * 根据当前模式(RTC/HLS)和状态决定开始或停止直播
 */
const toggleDroneLive = () => {
  if (useRTCLive.value) {
    isDroneInRoom.value ? leaveRTCHandler(true) : joinRTCHandler(true)
  } else {
    drones.droneConverterId ? leaveRoom(true) : joinRoom(true)
  }
}

/**
 * 开始机场分割条拖拽
 * @param {MouseEvent} e 鼠标事件
 */
const startAirportDrag = (e) => {
  isDragging.value = true;
  document.addEventListener('mousemove', handleAirportDrag);
  document.addEventListener('mouseup', stopAirportDrag);
  document.body.style.userSelect = 'none'; // 防止拖拽时选中文本
  e.preventDefault();
};

/**
 * 处理机场分割条拖拽
 * @param {MouseEvent} e 鼠标事件
 */
const handleAirportDrag = (e) => {
  if (!isDragging.value) return;

  const container = document.querySelector('.viewer-container');
  if (!container) return;

  const containerRect = container.getBoundingClientRect();
  // 计算新高度
  const newHeight = e.clientY - containerRect.top;

  // 设置最小和最大高度限制
  const minHeight = 200;
  const maxHeight = 1000;

  // 应用高度限制
  if (newHeight > minHeight && newHeight < maxHeight) {
    drawerHeight.value = newHeight;
  }
};

/**
 * 停止机场分割条拖拽
 */
const stopAirportDrag = () => {
  isDragging.value = false;
  document.removeEventListener('mousemove', handleAirportDrag);
  document.removeEventListener('mouseup', stopAirportDrag);
  document.body.style.userSelect = ''; // 恢复文本选择
};

/**
 * 开始主分割条拖拽
 * @param {MouseEvent} e 鼠标事件
 */
const startDrag = (e) => {
  isDragging.value = true;
  document.addEventListener('mousemove', handleDrag);
  document.addEventListener('mouseup', stopDrag);
  e.preventDefault();
};

/**
 * 处理主分割条拖拽
 * @param {MouseEvent} e 鼠标事件
 */
const handleDrag = (e) => {
  if (!isDragging.value) return;

  const container = document.querySelector('.content-area');
  if (!container) return;

  const containerRect = container.getBoundingClientRect();
  const newWidth = e.clientX - containerRect.left;

  // 设置最小和最大宽度限制
  const minWidth = 100;
  const maxWidth = containerRect.width - 300;
  if (newWidth > minWidth && newWidth < maxWidth) {
    leftPanelWidth.value = newWidth;
  }
};

/**
 * 停止主分割条拖拽
 */
const stopDrag = () => {
  isDragging.value = false;
  document.removeEventListener('mousemove', handleDrag);
  document.removeEventListener('mouseup', stopDrag);
};

// 监听3D视图创建
watch(() => viewer3dProp.viewer3d, (newValue) => {
  if (Object.keys(newValue).length) {
    isShowResource.value = true
  }
})

// 提供3D视图给子组件
provide('viewer3d', viewer3dProp)

/**
 * 创建3D视图
 * @param {string} viewerId 视图ID
 * @param {object} mapInitStore 地图初始化存储
 * @param {object} mapViewStore 地图视图存储
 */
const createViewer3d = async (viewerId, mapInitStore, mapViewStore) => {
  const useViewer3d = new UseViewer3d(viewerId, mapInitStore, mapViewStore)
  await useViewer3d.createViewer3d()
  const viewer3d = useViewer3d.viewer3d

  // 初始化位置
  initialPosition = Cesium.Cartesian3.fromDegrees(101.699636959, 21.207881294, 150)
  const managementPosition = Cesium.Cartesian3.fromDegrees(101.699636959, 21.207881294, 10)

  // 添加管委会标记
  viewer3d.entities.add({
    name: '磨憨管委会',
    position: managementPosition,
    point: {
      pixelSize: 15,
      color: Cesium.Color.BLUE,
      outlineColor: Cesium.Color.WHITE,
      outlineWidth: 2,
      heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND
    },
    label: {
      text: '磨憨管委会',
      font: '14px sans-serif',
      fillColor: Cesium.Color.WHITE,
      heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND,
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      pixelOffset: new Cesium.Cartesian2(0, -10)
    }
  })

  // 添加无人机实体
  droneEntity.value = viewer3d.entities.add({
    name: '无人机当前位置',
    position: initialPosition,
    point: {
      pixelSize: 15,
      color: Cesium.Color.RED,
      outlineColor: Cesium.Color.WHITE,
      outlineWidth: 2,
      heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND
    },
    label: {
      text: '无人机',
      font: '14px sans-serif',
      fillColor: Cesium.Color.WHITE,
      heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND,
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      pixelOffset: new Cesium.Cartesian2(0, -30)
    }
  })

  // 添加飞行路径实体
  flightPathEntity = viewer3d.entities.add({
    name: '无人机飞行路径',
    polyline: {
      positions: new Cesium.CallbackProperty(() => flightPathPositions, false),
      width: 3,
      material: new Cesium.PolylineGlowMaterialProperty({
        glowPower: 0.2,
        color: Cesium.Color.YELLOW
      }),
      clampToGround: true
    }
  })

  // 将3D视图赋值给响应式对象
  Object.assign(viewer3dProp, { viewer3d })
}

/**
 * 更新无人机位置
 * @param {number} longitude 经度
 * @param {number} latitude 纬度
 * @param {number} height 高度
 */
const updateDronePosition = (longitude, latitude, height) => {
  const position = Cesium.Cartesian3.fromDegrees(longitude, latitude, 80)
  droneEntity.value.position = position
  flightPathPositions.push(position)

  // 限制飞行路径点数
  if (flightPathPositions.length > 1000) {
    flightPathPositions = flightPathPositions.slice(-500)
  }
}

/**
 * 初始化MQTT连接
 */
const initMqttConnection = async () => {
  try {
    const options = {
      clean: true,
      connectTimeout: 4000,
      clientId: `web_${Math.random().toString(16).substr(2, 8)}`,
      username: 'dji_flighthub_2',
      password: 'Zlzx@123',
      reconnectPeriod: 5000
    }

    const brokerUrl = 'ws://121.43.136.41:8083/mqtt'
    mqttClient.value = mqtt.connect(brokerUrl, options)

    setupMqttEventListeners();

    await new Promise((resolve, reject) => {
      const timeout = setTimeout(() => reject(new Error('连接超时')), 5000)

      mqttClient.value.on('connect', () => {
        clearTimeout(timeout)
        mqttStatus.value = 'connected'
        resolve(true)
      })

      mqttClient.value.on('error', reject)
    })

  } catch (error) {
    console.error('MQTT初始化失败:', error)
    mqttStatus.value = 'error'
  }
}

/**
 * 订阅MQTT主题
 * @param {string} topic 主题名称
 */
const subscribeMqttTopic = (topic) => {
  if (!mqttClient.value || mqttStatus.value !== 'connected') {
    console.warn('MQTT未连接，无法订阅主题')
    return
  }

  mqttClient.value.subscribe(topic, { qos: 1 }, (err) => {
    if (err) {
      console.error('订阅MQTT主题失败:', err)
    } else {
      console.log('订阅MQTT主题成功:', topic)
    }
  })
}

/**
 * 设置MQTT事件监听器
 */
const setupMqttEventListeners = () => {
  if (!mqttClient.value) return

  mqttClient.value.on('reconnect', () => console.log('MQTT正在重新连接...'))
  mqttClient.value.on('close', () => console.log('MQTT连接关闭'))
  mqttClient.value.on('offline', () => console.log('MQTT离线'))

  mqttClient.value.on('message', (topic, message) => {
    lastMessageTime = Date.now(); // 更新最后收到消息的时间

    try {
      droneIsTakeOff.value = true
      const payload = JSON.parse(message.toString())
      console.log('MQTT消息:', payload)
      droneInfo.value = payload.data.host
      const { latitude, longitude, height } = payload.data.host
      if (droneEntity.value && viewer3d.value) {
        updateDronePosition(longitude, latitude, height)
      }
    } catch (err) {
      console.error('MQTT消息处理错误:', err)
    }
  })
}

// 最后收到消息的时间
let lastMessageTime = Date.now();

// 消息超时检查间隔和超时时间
const checkInterval = 10000; // 检查间隔(毫秒)
const messageTimeout = 15000; // 超时时间(毫秒)

// 设置定时器检查MQTT消息超时
setInterval(() => {
  const now = Date.now();
  if (now - lastMessageTime > messageTimeout) {
    console.warn('警告：MQTT长时间未收到消息！');
    // 重置无人机状态
    droneInfo.value = {
      battery: {
        capacity_percent: "--",
        remain_flight_time: "--",
        return_home_power: "--",
        landing_power: "--"
      },
      horizontal_speed: "--",
      vertical_speed: "--",
      mode_code: 14,
      height_limit: "--",
      elevation: '--',
      height: "--"
    }
    // 停止所有直播
    if (useRTCLive.value) {
      if (isDroneInRoom.value) {
        console.log("视频播放器已清理")
        leaveRTCHandler(true); // 停止RTC直播
      }
    } else {
      if (drones.droneConverterId) {
        console.log("视频播放器已清理")
        leaveRoom(true); // 停止HLS直播
      }
    }
    // 重置状态
    droneIsTakeOff.value = false;
    isDroneInRoom.value = false;
  }
}, checkInterval);

/**
 * 初始化视频源
 */
const initVideoSource = () => {
  nextTick(() => {
    // 初始化机场视频
    if (drones.airportConverterId) {
      const videoElement = document.getElementById('airport-video')
      if (!videoElement) return
      const player = videojs(videoElement, {
        html5: {
          hls: {
            overrideNative: true
          }
        },
        fluid: true,
        fill: true,
        controlBar: true,
        bigPlayButton: false
      }, () => {
        player.play().catch(err => {
          console.error('自动播放失败:', err)
        })
      })

      // 添加尺寸变化监听
      const resizeObserver = new ResizeObserver(() => {
        const container = player.el().parentElement
        player.width(container.offsetWidth)
        player.height(container.offsetHeight)
      })
      resizeObserver.observe(player.el().parentElement)
    }

    // 初始化无人机视频
    if (drones.droneConverterId) {
      const videoElement = document.getElementById('drone-video')
      if (!videoElement) return
      const player = videojs(videoElement, {
        html5: {
          hls: {
            overrideNative: true
          }
        },
        fluid: true,
        fill: true,
        controlBar: true,
        bigPlayButton: false
      }, () => {
        player.play().catch(err => {
          console.error('自动播放失败:', err)
        })
      })

      // 添加尺寸变化监听
      const resizeObserver = new ResizeObserver(() => {
        const container = player.el().parentElement
        player.width(container.offsetWidth)
        player.height(container.offsetHeight)
      })
      resizeObserver.observe(player.el().parentElement)
    }
  })
}

/**
 * 加入HLS房间
 * @param {boolean} isDrone 是否是无人机视频
 */
const joinRoom = async (isDrone) => {
  const deviceConfig = isDrone ? {
    sn: "1581F6QAD241800B73B2",
    camera: "80-0-0",
    channel: "1581F6QAD241800B73B2_80-0-0"
  } : {
    sn: "7CTXMCX00B065A",
    camera: "165-0-7",
    channel: "7CTXMCX00B065A_165-0-7"
  }

  try {
    // 尝试创建新的流转换器
    const startStreamResponse = await streamConvertersPost({
      region: "cn",
      converter_name: isDrone ? "drone" : "airport",
      ...deviceConfig,
      video: "normal-0",
      bypass_option: {
        url: `rtmp://121.43.136.41:1935/live/${deviceConfig.channel}`
      }
    })

    if (startStreamResponse?.data.converterId) {
      // 更新转换器ID
      isDrone ?
        drones.updateDroneConverterId(startStreamResponse.data.converterId) :
        drones.updateAirportConverterId(startStreamResponse.data.converterId)
      await streamConvertersGet({ channel: deviceConfig.channel })
    }
  } catch (e) {
    // 如果创建失败，尝试获取现有流
    const getStreamResponse = await streamConvertersGet({ channel: deviceConfig.channel })
    if (getStreamResponse.data.members[0]?.converterId) {
      isDrone ?
        drones.updateDroneConverterId(getStreamResponse.data.members[0].converterId) :
        drones.updateAirportConverterId(getStreamResponse.data.members[0].converterId)
    }
  }

  // 设置HLS流地址
  isDrone ?
    droneHlsUrl.value = "http://121.43.136.41/hls/1581F6QAD241800B73B2_80-0-0/index.m3u8" :
    airportHlsUrl.value = "http://121.43.136.41/hls/7CTXMCX00B065A_165-0-7/index.m3u8"

  // 初始化视频源
  initVideoSource()
}

/**
 * 离开HLS房间
 * @param {boolean} isDrone 是否是无人机视频
 */
const leaveRoom = (isDrone) => {
  if (isDrone) {
    if (drones.droneConverterId) {
      streamConvertersDelete(drones.droneConverterId).then(() => {
        drones.updateDroneConverterId(null)
        droneHlsUrl.value = ""
      })
    }
  } else {
    if (drones.airportConverterId) {
      streamConvertersDelete(drones.airportConverterId).then(() => {
        drones.updateAirportConverterId(null)
        airportHlsUrl.value = ''
      })
    }
  }
}

/**
 * 解析查询字符串
 * @param {string} queryString 查询字符串
 * @returns {object} 解析后的键值对
 */
const parseQueryString = (queryString) => {
  return Object.fromEntries(
    queryString.split('&').map(pair => {
      const [key, value] = pair.split('=')
      return [decodeURIComponent(key), decodeURIComponent(value || '')]
    }))
}

/**
 * 设置火山引擎事件监听器
 * @param {IRTCEngine} engine RTC引擎实例
 * @param {boolean} isDrone 是否是无人机视频
 */
const setupVolcEventListeners = (engine, isDrone) => {
  if (!engine) return

  // 用户发布流事件
  engine.on(VERTC.events.onUserPublishStream, (e) => {
    handleVolcStreamPublish(e.userId, e.mediaType, isDrone)
  })
}

/**
 * 设置Agora事件监听器
 * @param {IAgoraRTCClient} client Agora客户端
 * @param {boolean} isDrone 是否是无人机视频
 */
const setupAgoraEventListeners = (client, isDrone) => {
  if (!client) return

  // 用户离开事件
  client.on("user-left", (user) => {
    console.log(`${isDrone ? '无人机' : '机场'} 用户离开:`, user.uid)
  })

  // 用户发布事件
  client.on("user-published", async (user, mediaType) => {
    setTimeout(async () => {
      if (!client.remoteUsers.some(u => u.uid === user.uid)) {
        return
      }

      try {
        await client.subscribe(user, mediaType)
        if (mediaType === "video" && user.videoTrack) {
          await user.videoTrack.play(isDrone ? "drone-player" : "remote-player")
          console.log("播放成功");

        }
        if (mediaType === "audio" && user.audioTrack) {
          user.audioTrack.play()
        }
      } catch (error) {
        console.error(`${isDrone ? '无人机' : '机场'} 订阅失败:`, error)

      }
    }, 500)
  })
}

/**
 * 处理火山引擎流发布
 * @param {string} userId 用户ID
 * @param {string} mediaType 媒体类型
 * @param {boolean} isDrone 是否是无人机视频
 */
const handleVolcStreamPublish = async (userId, mediaType, isDrone) => {
  try {
    const player = isDrone ?
      document.querySelector('#drone-player') :
      document.querySelector('#remote-player')

    if (!player) {
      console.error('Player element not found')
      return
    }

    await (isDrone ? droneEngine.value : airportEngine.value).setRemoteVideoPlayer(
      StreamIndex.STREAM_INDEX_MAIN,
      { userId, renderDom: player }
    )
    console.log(`${isDrone ? '无人机' : '机场'} 火山引擎初始化成功:`)
  } catch (error) {
    console.error(`${isDrone ? '无人机' : '机场'} 火山引擎视频渲染失败:`, error)
  }
}

/**
 * 初始化火山引擎
 * @param {string} appId 应用ID
 * @param {boolean} isDrone 是否是无人机视频
 * @returns {Promise<boolean>} 是否初始化成功
 */
const initVolcEngine = async (appId, isDrone) => {
  try {
    const engine = isDrone ? droneEngine : airportEngine
    engine.value = VERTC.createEngine(appId)
    setupVolcEventListeners(engine.value, isDrone)
    return true
  } catch (error) {
    console.error(`${isDrone ? '无人机' : '机场'} 火山引擎初始化失败:`, error)
    return false
  }
}

/**
 * 初始化Agora引擎
 * @param {boolean} isDrone 是否是无人机视频
 * @returns {Promise<boolean>} 是否初始化成功
 */
const initAgoraEngine = async (isDrone) => {
  try {
    const client = isDrone ? droneAgoraClient : airportAgoraClient
    client.value = AgoraRTC.createClient({ mode: "live", codec: "vp8" })
    setupAgoraEventListeners(client.value, isDrone)
    return true
  } catch (error) {
    console.error(`${isDrone ? '无人机' : '机场'} Agora初始化失败:`, error)
    return false
  }
}

/**
 * 加入RTC房间
 * @param {boolean} isDrone 是否是无人机视频
 */
const joinRTCHandler = async (isDrone) => {
  try {
    const res = await liveBroadcast({
      camera_index: isDrone ? '80-0-0' : '165-0-7',
      quality_type: 'adaptive',
      sn: isDrone ? '1581F6QAD241800B73B2' : '7CTXMCX00B065A',
      video_expire: 7200
    })

    const parsedParams = parseQueryString(res.data.url)

    if (res.data.urlType === 'volc') {
      // 火山引擎RTC
      if (!(await initVolcEngine(parsedParams.app_id, isDrone))) return

      await (isDrone ? droneEngine.value : airportEngine.value).joinRoom(
        parsedParams.token,
        parsedParams.room_id,
        { userId: parsedParams.user_id },
        { isAutoPublish: false, isAutoSubscribeAudio: true, isAutoSubscribeVideo: true }
      )
    }
    else if (res.data.urlType === 'agora') {
      // Agora RTC
      isUsingAgora.value = true
      if (!(await initAgoraEngine(isDrone))) return

      await (isDrone ? droneAgoraClient.value : airportAgoraClient.value).setClientRole("host")
      await (isDrone ? droneAgoraClient.value : airportAgoraClient.value).join(
        parsedParams.app_id,
        parsedParams.channel,
        parsedParams.token,
        parseInt(parsedParams.uid, 10)
      )
    }

    // 更新房间状态
    isDrone ? isDroneInRoom.value = true : isAirportInRoom.value = true
  } catch (error) {
    console.error(`${isDrone ? '无人机' : '机场'} 加入房间失败:`, error)
  }
}

/**
 * 离开RTC房间
 * @param {boolean} isDrone 是否是无人机视频
 */
const leaveRTCHandler = async (isDrone) => {
  try {
    if (isUsingAgora.value) {
      // 离开Agora房间
      if (isDrone && droneAgoraClient.value) {
        await droneAgoraClient.value.leave()
      } else if (!isDrone && airportAgoraClient.value) {
        await airportAgoraClient.value.leave()
      }
    } else {
      // 离开火山引擎房间
      if (isDrone && droneEngine.value) {
        await droneEngine.value.leaveRoom()
      } else if (!isDrone && airportEngine.value) {
        await airportEngine.value.leaveRoom()
      }
    }

    // 更新房间状态
    isDrone ? isDroneInRoom.value = false : isAirportInRoom.value = false
    isUsingAgora.value = false
  } catch (error) {
    console.error(`${isDrone ? '无人机' : '机场'} 离开房间失败:`, error)
  }
}

/**
 * 切换直播模式 (RTC/HLS)
 */
const toggleLiveMode = async () => {
  // 先停止当前模式的直播
  if (useRTCLive.value) {
    if (isDroneInRoom.value) await leaveRTCHandler(true);
    if (isAirportInRoom.value) await leaveRTCHandler(false);
  } else {
    if (drones.droneConverterId) await leaveRoom(true);
    if (drones.airportConverterId) await leaveRoom(false);
  }

  // 切换模式
  useRTCLive.value = !useRTCLive.value;
}

// 组件挂载时初始化MQTT连接
onMounted(async () => {
  await initMqttConnection()
  subscribeMqttTopic('thing/product/1581F6QAD241800B73B2/osd')
})

// 组件卸载前清理资源
onBeforeUnmount(() => {
  // 清理视频播放器
  const players = ['airport-video', 'drone-video']
    .map(id => videojs.getPlayer(id))
    .filter(player => player)
  players.forEach(player => player.dispose())
  console.log("视频播放器已清理")
  // 断开MQTT连接
  if (mqttClient.value) {
    mqttClient.value.end()
  }

  // 离开RTC房间
  if (isDroneInRoom.value) leaveRTCHandler(true)
  if (isAirportInRoom.value) leaveRTCHandler(false)
})
</script>

<style scoped>
/* Updated styles for the new layout */
.main-live {
  display: flex;
  flex-direction: column;
  height: 85vh;
  min-height: 85vh;
  max-height: 85vh;
  padding: 1% 2%;
}

.content-area {
  display: flex;
  flex: 1;
  height: calc(100% - 10vh);
  position: relative;
}

.left-panel {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
  text-align: center;
  min-width: 300px;

  max-width: calc(100% - 300px);
  height: 100%;
  position: relative;
  overflow: hidden;
}

.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 300px;
  border-radius: 20px;
  height: 100%;
  overflow: hidden;
  position: relative;
  background-color: rgba(255, 255, 255, 0.1); /* 半透明白色背景 */
  backdrop-filter: blur(10px) brightness(90%); /* 模糊 + 稍微调暗 */
  -webkit-backdrop-filter: blur(10px); /* Safari 兼容 */
  border: 1px solid rgba(255, 255, 255, 0.2); /* 可选：添加边框增强效果 */
}


.viewer-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

/* Other existing styles remain the same */
.video-js.vjs-default-skin {
  display: none;
}

.video-js.vjs-default-skin.vjs-playing {
  display: block;
}

#remote-player, #drone-player {
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.1); /* 半透明白色背景 */
  backdrop-filter: blur(10px) brightness(90%); /* 模糊 + 稍微调暗 */
  -webkit-backdrop-filter: blur(10px); /* Safari 兼容 */
  border: 1px solid rgba(255, 255, 255, 0.2); /* 可选：添加边框增强效果 */
}

.controls button {
  margin: 0 5px;
  padding: 4px 8px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.controls button:hover {
  background-color: #45a049;
}

.controls button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.airport-video-player{
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.video-player {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.airport-drawer {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 15;
  overflow: hidden;
  min-width: 300px;
  transition: background-color 0.1s;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.airport-drawer.drawer-open {
  height: 300px; /* Fixed height when open */
}

.airport-control {
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.splitter {
  width: 10px;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.1);
  cursor: col-resize;
  transition: background-color 0.2s;
}

.splitter:hover {
  background-color: rgba(0, 0, 0, 0.2);
}
/* 改进后的分割条样式 */
.splitterAirport {
  height: 10px;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.3);
  cursor: row-resize;
  position: absolute;
  bottom: 0;
  z-index: 20;
  transition: background-color 0.2s;
}

.splitterAirport:hover {
  background-color: rgba(0, 0, 0, 0.3);
}

.header-drawer {
  height: 3.5%;
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  width: 100%;
}

.drone-video-container {
  height: 100% /* 调整高度适应抽屉 */
}

/* 状态抽屉样式 */
.status-drawer {
  position: absolute;
  top: 5%;
  left: 0;
  right: 0;
  height: 0;
  transition: all 0.3s ease;
  overflow: hidden;
  z-index: 10;
  display: flex; /* 使用 Flexbox 布局 */
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
}

.status-drawer.drawer-open {
  height: 100px; /* 根据需要调整高度 */
}

.header-right-status {
  display: flex; /* 使用 Flexbox 布局 */
  flex-wrap: wrap; /* 允许换行 */
  gap: 10px; /* 控制子元素之间的间距 */
  padding: 5px; /* 可选：增加一些内边距以防内容紧贴边缘 */
  box-sizing: border-box; /* 确保内边距和边框包含在元素的宽度内 */
}

.header-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 80px; /* 设置一个最小宽度以适应内容 */
  box-sizing: border-box; /* 确保内部元素的尺寸计算方式一致 */
  border-radius: 6px;
  transition: all 0.3s ease;
}

.header-item:hover {
  transform: translateY(-1px);
}

.header-item h2 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
}

.header-item > div {
  font-size: 15px;
  font-weight: bold;
  margin-top: 4px;
}

.video-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.video-player {
  width: 100%;
  height: 100%;
  background-color: #000;
}

.hidden {
  visibility: hidden;
}

.loading-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  z-index: 10;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
</style>
