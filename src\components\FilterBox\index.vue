<template>
    <div ref="modalWrapper" class="dialog"
        :style="'position: absolute;top:' + dialogTop + 'px ;left: ' + dialogLeft + 'px;width: ' + dialogWidth + 'px;'">
        <div style="display: flex;">
            <el-button style="width: 95px;" @click="sort = 'up'; apply()" color="#EEEEEE" icon="Top">升序</el-button>
            <el-button style="width: 95px;" @click="sort = 'down'; apply()" color="#EEEEEE" icon="Bottom">降序</el-button>
        </div>
        <el-input @input="listSearch" style="margin-top: 30px;" v-model="searchKey" placeholder="搜索" />

        <el-checkbox v-model="allSwitch" @change="allChange" label="选择全部" size="large" />

        <div class="list-box">
            <el-checkbox-group class="checkbox-list" v-model="checkList">
                <el-checkbox style="padding-left: 10px;" @click="selectIndex = index"
                    :class="selectIndex == index ? 'selected' : ''" v-for="(item, index) in backupList" :key="index"
                    :label="item" :value="item" />
            </el-checkbox-group>
        </div>

        <div class="btn-box">
            <el-button color="#EEEEEE" size="small" @click="apply">应用</el-button>
            <el-button color="#EEEEEE" size="small" @click="dialogCancel">取消</el-button>
            <el-button color="#EEEEEE" size="small" @click="clean">清除</el-button>
        </div>
    </div>
</template>

<script setup>
const emit = defineEmits()
const props = defineProps({
    top: {
        type: Number,
    },
    left: {
        type: Number,
    },
    width: {
        type: Number,
    },
    listBox: {
        type: Array,
        default: () => []
    },
    checkListBox: {
        type: Array,
        default: () => []
    }
});

const dialogTop = ref('')
const dialogLeft = ref('')
const dialogWidth = ref('')

const searchKey = ref('')
const listSearch = (e) => {
    backupList.value = props.listBox.filter(item => item.includes(e))
    allSwitch.value = true
    checkList.value = backupList.value
}

const allSwitch = ref(true)
const backupList = ref([])
const checkList = ref([])
const selectIndex = ref(0)
const allChange = () => {
    checkList.value = allSwitch.value ? [...props.listBox] : []
}

const sort = ref('down')
const apply = () => {
    let data = {
        checkList: checkList.value,
        sort: sort.value
    }
    emit("apply", data)
}

const clean = () => {
    emit("clean")

}
const dialogCancel = () => {
    emit("cancel")
}

onMounted(() => {
    // 设置弹窗位置
    // 70 topNav 35 tagsView 200 side
    dialogTop.value = props.top - 70 - 35 + 40
    dialogLeft.value = props.left - 200
    dialogWidth.value = props.width + 50

    //备份
    backupList.value = props.listBox
    checkList.value = props.checkListBox

})
</script>

<style lang="scss" scoped>
.dialog {
    height: 350px;
    z-index: 999;
    background-color: #fff;
    min-width: 230px;
    border: 1px solid rgba(0, 0, 0, .2);
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 6px 13px rgba(0, 0, 0, .2);
    padding: 10px 10px;

    :deep(.el-checkbox__label) {
        color: #000;
        font-size: 16px;
    }

    :deep(.el-checkbox__input.is-checked+.el-checkbox__label) {
        color: #000;
        font-size: 16px;
    }

    :deep(.el-input__wrapper.is-focus) {
        box-shadow: 0 0 0 1px #dcdfe6;
    }

}

.list-box {
    height: 150px;
    border: 1px solid rgba(0, 0, 0, .2);
    border-radius: 4px;
    margin-bottom: 6px;
    overflow: hidden;
}

.checkbox-list {
    display: flex;
    flex-direction: column;

    .selected {
        background-color: #0085C7;
        width: 100%;
    }
}

.btn-box {
    display: flex;
    justify-content: end;
    margin-top: 20px;
}
</style>