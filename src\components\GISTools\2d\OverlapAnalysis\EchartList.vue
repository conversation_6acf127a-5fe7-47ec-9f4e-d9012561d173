<template>
  <div class="charts-container">
    <div v-for="(chart,index) in classifyList" :id="chart.layerId" ref="echarts"
         :key="index" class="charts"
    />
  </div>
</template>

<script setup name="图表">
import * as echarts from 'echarts'

const { proxy } = getCurrentInstance

const props = defineProps({
  classifyList: {
    type: Array,
    required: true
  }
})

const { classifyList } = toRef(props)

const initCharts = ()=> {
  const charts = proxy.$refs.echarts
  if (!charts) {
    return
  }
  const chartsOption = {}
  const chartsClassify = JSON.stringify(classifyList.value)
  JSON.parse(chartsClassify).forEach(chartClass => {
    if (chartClass.areaList){
      chartClass.areaList.forEach(chartItem => {
        chartItem.name = chartItem.label
        chartItem.value = Number((chartItem.value * 1e-4).toFixed(4))
        delete chartItem['label']
      })
    }
    chartClass.legendData = chartClass?.legendData ? chartClass.legendData.map(legendItem => {
      const obj = { "name": legendItem }
      return obj
    }) : []
    chartClass.areaList = chartClass?.areaList ? chartClass.areaList : []

    generateChartsOption(chartsOption,chartClass, 'pie')
  })
  charts.forEach(chart => {
    // 生成图表
    const myChart = echarts.init(chart)
    const option = chartsOption[chart.id]
    // 指定图表的配置项和数据
    option && myChart.setOption(option)
  })
}

const generateChartsOption = (chartsOption, charts, chartsType)=> {
  console.log("图表：",charts)
  chartsOption[charts.layerId] = {
    title: {
      text: charts.layerName,
      textStyle: {
        color: '#fff'
      },
      left: 'center',
      backgroundColor: '#3184d6',
      padding: [10, 320]
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c} 公顷 ({d}%)',
      position: function(point, params, dom, rect, size) {
        // 其中point为当前鼠标的位置，size中有两个属性：viewSize和contentSize，分别为外层div和tooltip提示框的大小
        const x = point[0] //
        const y = point[1]
        const boxWidth = size.contentSize[0]
        const boxHeight = size.contentSize[1]
        let posX = 0 // x坐标位置
        let posY = 0 // y坐标位置
        if (x < boxWidth) { // 左边放不开
          posX = 5
        } else { // 左边放的下
          posX = x - boxWidth
        }
        if (y < boxHeight) { // 上边放不开
          posY = 5
        } else { // 上边放得下
          posY = y - boxHeight
        }
        return [posX, posY]
      }
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      left: 10,
      top: 50,
      bottom: 20,
      data: charts.areaList
    },
    series: [{
      name: charts.layerName,
      type: chartsType,
      radius: [80, 40],
      center: ['65%', '60%'],
      itemStyle: {
        borderRadius: 5
      },
      avoidLabelOverlap: true,
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 15,
          shadowOffsetX: 2,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      // data: charts.areaList.sort(function(a, b) { return b.value - a.value })
      data: charts.areaList
    }]
  }
}

onMounted(()=>{
  setTimeout(() => {
    initCharts()
  }, 1000)
})

</script>

<style scoped lang="scss">
.charts-container{
  height: 100%;
  width: 100%;
  .tip{
    height: 760px;
    text-align: center;
    padding-top: 300px;
    color: #fff;
    .icon-zanwushuju{
      font-size: 100px;
    }
    span{
      display: block;
      font-size: 16px;
      margin-top: 20px;
      color: #504646d6;
    }
  }
  .charts{
    height: 300px;
    width: 314px;
    background: #f1f4f5eb;
    margin-bottom: 10px;
    border: 1px solid #3184d647;
    &:last-child{
      margin-bottom: -10px;
    }
  }
}
</style>
