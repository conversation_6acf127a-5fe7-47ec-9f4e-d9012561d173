import WMTS from "ol/source/WMTS";
import TWMTS from "ol/tilegrid/WMTS";
import TileGrid from 'ol/tilegrid/TileGrid';
import { Tianditu, TileSuperMapRest } from '@supermap/iclient-ol';
import Tile from "ol/layer/Tile";
import GeoJSON from "ol/format/GeoJSON";
import VectorLayer from "ol/layer/Vector";
import VectorSource from "ol/source/Vector";
import { geometryDefaultStyle } from "@/utils/OpenLayers/olStyles";
import { getResolutions } from "./olTileParamsCalculate"
import OlSRS from "@/utils/OpenLayers/olSRS.js";
import TileLayer from "ol/layer/Tile"
import { XYZ } from "ol/source.js"
/**
 * 椭球体编号
 * @type {number}
 */

const EPSGCODE = 4490
const TDTTOKEN = "1d109683f4d84198e37a38c442d68311"

/**
 * RESOLUTIONS4490：CGCS2000 4490 坐标系分辨率
 * RESOLUTIONS4522：CGCS2000 4522 坐标系分辨率
 */
const RESOLUTIONS4490 = [
  0.7039144156840451, 0.35195720784202256,
  0.17597860392101103, 0.08798930196050551,
  0.043994650980252646, 0.02199732549012637,
  0.010998662745063196, 0.005499331372531586,
  0.002749665686265805, 0.0013748328431328976,
  6.874164215664488E-4, 3.4370821078322564E-4,
  1.7185410539161233E-4, 8.592705269580617E-5,
  4.296352634790308E-5, 2.148176317395154E-5,
  1.074088158697577E-5, 5.370440793487897E-6, 2.6852203967439486E-6
];
/**
 * @description：可以是图层地理范围，也可以世界范围
 * extent4490：CGCS2000 4490 坐标系地理范围
 * extent4522：CGCS2000 4522 坐标系地理范围
 * extent4522：28876800.0,2720752.103970873,34617211.480822966,10002100.0
 */
// const EXTENT4490 = [-180.0,-90,180,90.0]
const EXTENT4490 = [101.56576627361721,23.83204492569996,104.38142393635337,26.647702588436132]
const EXTENT4522 = [34529651.06,2713243.91,34666910.62,2920641.33]
/**
 * 切片原点，切片起算点
 * origin4490：CGCS2000 4490 坐标系切片原点，[101.56576627361721, 26.647702588436132] 和 origin:[-180,90]
 * origin4522：CGCS2000 4522 坐标系切片原点
 */

const ORIGIN4490 = [-180.0, 90.0]
const ORIGIN4522 = [34516951.8037,2937687.3708999995]

const EXTENT = EPSGCODE == 4490 ? EXTENT4490 : EXTENT4522
const ORIGIN = EPSGCODE == 4490 ? ORIGIN4490 : ORIGIN4522
// const RESOLUTIONS = EPSGCODE == 4490 ? RESOLUTIONS4490
//   : getResolutions(EPSGCODE,{ left: EXTENT[0],right: EXTENT[2] })
const RESOLUTIONS = getResolutions(EPSGCODE,{ left: EXTENT[0],right: EXTENT[2] })

/**
 * @description：获取WMTS图层参数
 * @param options
 * @returns {format: string, version: string, layer: string, noWrap: boolean, requestEncoding:
 * string, tilematrixSet: *, layerId: *, tileSize: number, matrixIds: [],
 * attribution: string, style: (*|string), layerName: string, isBaseMap: *}
 */
export function getWMTSOption(options) {
  const matrixIds = [];
  const gridsetName = options.gridsetName
  const level = options.level || 18
  for (let i = 0; i <= level; ++i) {
    matrixIds[i] = gridsetName + ":" + i
  }
  const resolutions1 = [33.0729828126323, 16.933367200067735, 8.466683600033868, 4.233341800016934, 2.116670900008467, 1.0583354500042335, 0.5291677250021167, 0.26458386250105836, 0.13229193125052918];
  const resolutions2 = [66.1459656252646, 33.0729828126323, 16.933367200067735, 8.466683600033868, 4.233341800016934, 2.116670900008467, 1.0583354500042335, 0.5291677250021167, 0.26458386250105836]

  const resolutions = options.layerName === "CGQ_CY_HFYX_003_CGCS2000" ? resolutions1 : resolutions2
  // console.log("RESOLUTIONS:",RESOLUTIONS);
  console.log("服务地址：",import.meta.env.VITE_APP_GEOSERVER_URL);
  console.log("matrixIds：",matrixIds);
  const wmtsOptions = {
    url: import.meta.env.VITE_APP_GEOSERVER_URL,
    version: options.version || '1.0.0',
    style: options.style || "",
    // format: options.format || 'image/jpeg',
    format: "image/jpeg",
    layer: options.layerName,
    layerName: options.layerName,
    requestEncoding: options.requestEncoding || 'KVP',
    matrixSet: gridsetName,
    tileGrid: new TWMTS({
      tileSize: [256,256],
      // extent: EXTENT,
      extent: [-180.0,-90,180,90.0],
      // extent: [34571584.959829457,2732999.7518130955,34601067.700010672,2765724.3132891487],
      origin: [-180.0, 90.0],
      // origin: [28876800, 10002100],
      resolutions: RESOLUTIONS4490,
      // resolutions: resolutions,
      matrixIds: matrixIds
    }),
    wrapX: false
  }
  return wmtsOptions
}

/**
 * @description：添加Geoserver地图
 * @param options：加载图层参数
 * @param map：默认为全局地图
 */
export function addGeoServerLayer(options, map = window._map) {
  const paramsData = getWMTSOption(options)
  const WMTSSource = new WMTS(paramsData)
  const WMTSLayer = new Tile({
    source: WMTSSource,
    wrapX: false,
    isBasemap: options.isBasemap,
    layerId: options.layerId,
    layerName: options.layerName
  })
  map.addLayer(WMTSLayer)
  return WMTSLayer
}

/**
 * @description：添加Iserver地图
 * @param options：加载ISERVER图层配置
 * @param map：默认为全局地图
 */
export function addIserverLayer(options, map = window._map) {
  try{
    const source = new TileSuperMapRest({
      url: import.meta.env.VITE_APP_ISERVER_URL + options.url,
      tileGrid: new TileGrid({
        extent: [-180.0, -90, 180, 90.0],
        origin: [-180.0, 90.0],
        tileSize: [256, 256],
        resolutions: RESOLUTIONS
      })
      // zIndex: options.label.indexOf('影像') > -1 ? 1 : layer.id
    })
    const WMTSLayer = new Tile({
      source: source,
      prjCoordSys: "EPSG:4490",
      layerId: options.layerId,
      layerName: options.layerName,
      isBasemap: options.isBasemap
    })
    map.addLayer(WMTSLayer)
    return WMTSLayer
  }catch(e){
    throw new Error("iserver layer add failed with error: " + e.message)
  }

}

/**
 * @description：根据服务类型加载服务
 * @param serviceType
 * @param options
 * @param map：默认为全局地图
 */
export function addLayer(serviceType, options, map) {
  return serviceType === "geoserver" ? addGeoServerLayer(options, map) : addIserverLayer(options, map)
}

/**
 * 天地图影像服务
 * @param
 */

// 天地图瓦片服务的URL模板
// const tdTileLayer = 'http://{s}.tianditu.gov.cn/vec_c/wmts?service=WMTS&request=GetTile&version=1.0.0&LAYER={layer}&tilematrixset=c&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=fa7ec9766b2c00747e3dd60ab3d05892';
export function addTdtImageLayer() {
  const layer = new Tile({
    source: new Tianditu({
      key: "1d109683f4d84198e37a38c442d68311",
      projection: OlSRS[4490],
      // projection: projection,
      layerType: "img"
    })
  })
  window._map.addLayer(layer)
}

/**
 * 添加 GeoJSON Feature数据到地图中
 * @param geoJson: Feature or Geometry
 * @param featureStyle：几何对象样式
 * @param map：默认为全局地图
 */
export function addGeoJSON2Map(geoJson, featureStyle, map = window._map) {
  if (geoJson == undefined || !geoJson.type) {
    console.error("请传入正确格式的GeoJSON数据！")
    return
  }

  // 若图层存在，则将其删除
  // removeLayerByName("tempPolygon")
  // 判断是Feature还是Geometry
  let feature = null
  if (geoJson.type === "FeatureCollection" || geoJson.type === "Feature") {
    feature = geoJson
  } else {
    feature = {
      geometry: geoJson,
      properties: {},
      type: "Feature"
    }
  }
  const feats = new GeoJSON().readFeatures(feature)
  // console.log("feats:",feats);
  const geoType = feats[0].getGeometry().getType()
  const style = featureStyle || geometryDefaultStyle[geoType]

  if(!style.getFill()){
    throw new Error("请传递正确的样式！！！")
  }
  const polygon = new VectorLayer({
    source: new VectorSource({
      features: feats
    }),
    style: style
  })
  polygon.setZIndex(99)
  polygon.setProperties({ isBaseMap: false, layerName: "tempPolygon" })
  map.addLayer(polygon)

  // 直接获取图层范围
  const layerExtent = polygon.getSource().getExtent()
  map.getView().fit(layerExtent)
  return polygon
}


/**
 * 天地图影像服务
 * @param map：地图对象
 */
export function addTDTImageLayer(map = window._map){
  // 添加地图
  const url = "http://t{0-7}.tianditu.com/DataServer?x={x}&y={y}&l={z}&T=img_c&tk=" + TDTTOKEN;
  // let url = "https://wx.zrzyfw.cn/tdt/DataServer?x={x}&y={y}&l={z}&T=img_c&tk="+TDTTOKEN;
  const source = new XYZ({
    url: url,
    projection: "EPSG:4326"
  });
  const tdtLayer = new TileLayer({
    source: source,
    isBaseMap: true
  });
  tdtLayer.set("layerName","天地图影像")
  map.addLayer(tdtLayer)
}
/**
 * 天地图矢量服务
 * @param map：地图对象
 */
export function addTDTVecLayer(map = window._map){
  // 添加地图
  const url = "http://t{0-7}.tianditu.com/DataServer?x={x}&y={y}&l={z}&T=vec_c&tk=" + TDTTOKEN;
  // let url = "https://wx.zrzyfw.cn/tdt/DataServer?x={x}&y={y}&l={z}&T=vec_c&tk="+TDTTOKEN;
  const source = new XYZ({
    url: url,
    projection: "EPSG:4326"
  });
  const tdtLayer = new TileLayer({
    source: source,
    isBaseMap: true
  });
  tdtLayer.set("layerName","天地图矢量")
  map.addLayer(tdtLayer)
}


/**
 * 天地图注记服务
 * @param map：地图对象
 */
export function addTDTCvaLayer(map = window._map){
  // 添加地图
  const url = "http://t{0-7}.tianditu.com/DataServer?x={x}&y={y}&l={z}&T=cva_c&tk=" + TDTTOKEN;
  // let url = "https://wx.zrzyfw.cn/tdt/DataServer?x={x}&y={y}&l={z}&T=cva_c&tk="+TDTTOKEN;
  const source = new XYZ({
    url: url,
    projection: "EPSG:4326"
  });
  const tdtLayer = new TileLayer({
    source: source,
    zIndex: 999,
    isBaseMap: true
  });
  tdtLayer.set("layerName","天地图注记")
  map.addLayer(tdtLayer)
}

