<template>
  <div class="zoom-bar-container">
    <button
      class="zoom-bar-item"
      title="切换地图" @click="switchView"
    >
      <svg-icon icon-class="3D" />
    </button>
    <button
      class="zoom-bar-item"
      title="放大" @click="zoomIn"
    >
      <svg-icon icon-class="zoomin" />
    </button>
    <button
      class="zoom-bar-item"
      title="缩放到起始位置" @click="resetPosition"
    >
      <svg-icon icon-class="homepage" />
    </button>
    <button
      class="zoom-bar-item"
      title="缩小" @click="zoomOut"
    >
      <svg-icon icon-class="zoomout" />
    </button>
  </div>
</template>

<script setup name="ZoomBar">
import useMapViewStore from "@/store/modules/map/mapView.js"
import useMapInitStore from "@/store/modules/map/mapInit.js"

const store = useMapViewStore()
const mapInitStore = useMapInitStore()

const map = computed(() => store.map)
const viewer2d = computed(() => store.viewer2d)
/**
 * 视图切换
 */
const switchView = () => {
  store.switchMap("3D")
}
/**
 * 放大
 */
const zoomIn = () => {
  if (map.value) {
    const curZoom = viewer2d.value.getZoom();
    viewer2d.value.setZoom(curZoom + 1);
  }
}

/**
 * 缩小
 */
const zoomOut = () => {
  if (map.value) {
    const curZoom = viewer2d.value.getZoom();
    viewer2d.value.setZoom(curZoom - 1);
  }
}
/**
 * 复位
 */
const resetPosition = () => {
  const mapInfo = mapInitStore.mapInfo
  const center = mapInfo.center
  const zoom = mapInfo.zoom
  viewer2d.value.animate({ center: center }, { zoom: zoom });
}
</script>

<style scoped lang="scss">
.zoom-bar-container {
  position: absolute;
  z-index: 99;
  right: 10px;
  bottom: 20px;
  width: 40px;
  background-color: rgba(16, 79, 159, 0.8);
  border-radius: 5px;
}
.zoom-bar-item {
  position: relative;
  width: 100%;
  height: 40px;
  line-height: 40px;
  color: #ffff;
  cursor: pointer;
  transition: all 0.25s; /* 定义过渡效果 */
  border: none;
  border-bottom: 1px solid #cccccc61;
  &:hover {
    transition-delay: 0.25s;
    background-color: rgba(6,122,242,0.88);
  }
  .svg-icon {
    //width: 1.5em;
    //height: 1.5em;
    width: 1.25em;
    height: 1.25em;
  }
}
.active {
  background: #002549d9;
  color: #00d2ff;
}
</style>
