define(["exports","./arrayFill-4513d7ad","./buildModuleUrl-9085faaa","./Cartesian2-db21342c","./Cartographic-3309dd0d","./ComponentDatatype-c140a87d","./when-b60132fc","./Check-7b2a090c","./Rectangle-dee65d21","./GeometryAttribute-c65394ac","./GeometryAttributes-252e9929","./GeometryOffsetAttribute-fbeb6f1a","./IndexDatatype-8a5eead4","./Math-119be1a3","./FeatureDetection-806b12f0","./VertexFormat-6446fca0"],(function(t,e,a,i,r,n,o,m,s,u,l,c,f,d,C,p){"use strict";var y=new r.Cartesian3,_=new r.Cartesian3,h=new r.<PERSON><PERSON>ian3,v=new r.Car<PERSON>ian3,A=new r.<PERSON>3(1,1,1),x=Math.cos,b=Math.sin;function k(t){t=o.defaultValue(t,o.defaultValue.EMPTY_OBJECT);var e=o.defaultValue(t.radii,A),a=o.defaultValue(t.innerRadii,e),i=o.defaultValue(t.minimumClock,0),n=o.defaultValue(t.maximumClock,d.CesiumMath.TWO_PI),m=o.defaultValue(t.minimumCone,0),s=o.defaultValue(t.maximumCone,d.CesiumMath.PI),u=Math.round(o.defaultValue(t.stackPartitions,64)),l=Math.round(o.defaultValue(t.slicePartitions,64)),c=o.defaultValue(t.vertexFormat,p.VertexFormat.DEFAULT);this._radii=r.Cartesian3.clone(e),this._innerRadii=r.Cartesian3.clone(a),this._minimumClock=i,this._maximumClock=n,this._minimumCone=m,this._maximumCone=s,this._stackPartitions=u,this._slicePartitions=l,this._vertexFormat=p.VertexFormat.clone(c),this._offsetAttribute=t.offsetAttribute,this._workerName="createEllipsoidGeometry"}k.packedLength=2*r.Cartesian3.packedLength+p.VertexFormat.packedLength+7,k.pack=function(t,e,a){return a=o.defaultValue(a,0),r.Cartesian3.pack(t._radii,e,a),a+=r.Cartesian3.packedLength,r.Cartesian3.pack(t._innerRadii,e,a),a+=r.Cartesian3.packedLength,p.VertexFormat.pack(t._vertexFormat,e,a),a+=p.VertexFormat.packedLength,e[a++]=t._minimumClock,e[a++]=t._maximumClock,e[a++]=t._minimumCone,e[a++]=t._maximumCone,e[a++]=t._stackPartitions,e[a++]=t._slicePartitions,e[a]=o.defaultValue(t._offsetAttribute,-1),e};var F,w=new r.Cartesian3,P=new r.Cartesian3,g=new p.VertexFormat,V={radii:w,innerRadii:P,vertexFormat:g,minimumClock:void 0,maximumClock:void 0,minimumCone:void 0,maximumCone:void 0,stackPartitions:void 0,slicePartitions:void 0,offsetAttribute:void 0};k.unpack=function(t,e,a){e=o.defaultValue(e,0);var i=r.Cartesian3.unpack(t,e,w);e+=r.Cartesian3.packedLength;var n=r.Cartesian3.unpack(t,e,P);e+=r.Cartesian3.packedLength;var m=p.VertexFormat.unpack(t,e,g);e+=p.VertexFormat.packedLength;var s=t[e++],u=t[e++],l=t[e++],c=t[e++],f=t[e++],d=t[e++],C=t[e];return o.defined(a)?(a._radii=r.Cartesian3.clone(i,a._radii),a._innerRadii=r.Cartesian3.clone(n,a._innerRadii),a._vertexFormat=p.VertexFormat.clone(m,a._vertexFormat),a._minimumClock=s,a._maximumClock=u,a._minimumCone=l,a._maximumCone=c,a._stackPartitions=f,a._slicePartitions=d,a._offsetAttribute=-1===C?void 0:C,a):(V.minimumClock=s,V.maximumClock=u,V.minimumCone=l,V.maximumCone=c,V.stackPartitions=f,V.slicePartitions=d,V.offsetAttribute=-1===C?void 0:C,new k(V))},k.createGeometry=function(t){var i=t._radii;if(!(i.x<=0||i.y<=0||i.z<=0)){var m=t._innerRadii;if(!(m.x<=0||m.y<=0||m.z<=0)){var p,A,k=t._minimumClock,F=t._maximumClock,w=t._minimumCone,P=t._maximumCone,g=t._vertexFormat,V=t._slicePartitions+1,M=t._stackPartitions+1;(V=Math.round(V*Math.abs(F-k)/d.CesiumMath.TWO_PI))<2&&(V=2),(M=Math.round(M*Math.abs(P-w)/d.CesiumMath.PI))<2&&(M=2);var T=0,D=[w],G=[k];for(p=0;p<M;p++)D.push(w+p*(P-w)/(M-1));for(D.push(P),A=0;A<V;A++)G.push(k+A*(F-k)/(V-1));G.push(F);var O=D.length,I=G.length,L=0,E=1,z=m.x!==i.x||m.y!==i.y||m.z!==i.z,N=!1,R=!1,U=!1;z&&(E=2,w>0&&(N=!0,L+=V-1),P<Math.PI&&(R=!0,L+=V-1),(F-k)%d.CesiumMath.TWO_PI?(U=!0,L+=2*(M-1)+1):L+=1);var S=I*O*E,W=new Float64Array(3*S),B=e.arrayFill(new Array(S),!1),Y=e.arrayFill(new Array(S),!1),J=V*M*E,X=6*(J+L+1-(V+M)*E),Z=f.IndexDatatype.createTypedArray(J,X),j=g.normal?new Float32Array(3*S):void 0,q=g.tangent?new Float32Array(3*S):void 0,H=g.bitangent?new Float32Array(3*S):void 0,K=g.st?new Float32Array(2*S):void 0,Q=new Array(O),$=new Array(O);for(p=0;p<O;p++)Q[p]=b(D[p]),$[p]=x(D[p]);var tt=new Array(I),et=new Array(I);for(A=0;A<I;A++)et[A]=x(G[A]),tt[A]=b(G[A]);for(p=0;p<O;p++)for(A=0;A<I;A++)W[T++]=i.x*Q[p]*et[A],W[T++]=i.y*Q[p]*tt[A],W[T++]=i.z*$[p];var at,it,rt,nt,ot=S/2;if(z)for(p=0;p<O;p++)for(A=0;A<I;A++)W[T++]=m.x*Q[p]*et[A],W[T++]=m.y*Q[p]*tt[A],W[T++]=m.z*$[p],B[ot]=!0,p>0&&p!==O-1&&0!==A&&A!==I-1&&(Y[ot]=!0),ot++;for(T=0,p=1;p<O-2;p++)for(at=p*I,it=(p+1)*I,A=1;A<I-2;A++)Z[T++]=it+A,Z[T++]=it+A+1,Z[T++]=at+A+1,Z[T++]=it+A,Z[T++]=at+A+1,Z[T++]=at+A;if(z){var mt=O*I;for(p=1;p<O-2;p++)for(at=mt+p*I,it=mt+(p+1)*I,A=1;A<I-2;A++)Z[T++]=it+A,Z[T++]=at+A,Z[T++]=at+A+1,Z[T++]=it+A,Z[T++]=at+A+1,Z[T++]=it+A+1}if(z){if(N)for(nt=O*I,p=1;p<I-2;p++)Z[T++]=p,Z[T++]=p+1,Z[T++]=nt+p+1,Z[T++]=p,Z[T++]=nt+p+1,Z[T++]=nt+p;if(R)for(rt=O*I-I,nt=O*I*E-I,p=1;p<I-2;p++)Z[T++]=rt+p+1,Z[T++]=rt+p,Z[T++]=nt+p,Z[T++]=rt+p+1,Z[T++]=nt+p,Z[T++]=nt+p+1}if(U){for(p=1;p<O-2;p++)nt=I*O+I*p,rt=I*p,Z[T++]=nt,Z[T++]=rt+I,Z[T++]=rt,Z[T++]=nt,Z[T++]=nt+I,Z[T++]=rt+I;for(p=1;p<O-2;p++)nt=I*O+I*(p+1)-1,rt=I*(p+1)-1,Z[T++]=rt+I,Z[T++]=nt,Z[T++]=rt,Z[T++]=rt+I,Z[T++]=nt+I,Z[T++]=nt}var st=new l.GeometryAttributes;g.position&&(st.position=new u.GeometryAttribute({componentDatatype:n.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:W}));var ut,lt=0,ct=0,ft=0,dt=0,Ct=S/2,pt=s.Ellipsoid.fromCartesian3(i),yt=s.Ellipsoid.fromCartesian3(m);if(g.st||g.normal||g.tangent||g.bitangent){for(p=0;p<S;p++){ut=B[p]?yt:pt;var _t=r.Cartesian3.fromArray(W,3*p,y),ht=ut.geodeticSurfaceNormal(_t,_);if(g.st){var vt=Math.atan2(ht.y,ht.x);vt<0&&(vt+=d.CesiumMath.TWO_PI),K[lt++]=vt/d.CesiumMath.TWO_PI,K[lt++]=Math.asin(ht.z)/Math.PI+.5}if(g.normal&&(Y[p]&&r.Cartesian3.negate(ht,ht),j[ct++]=ht.x,j[ct++]=ht.y,j[ct++]=ht.z),g.tangent||g.bitangent){var At,xt=h,bt=0;if(B[p]&&(bt=Ct),At=!N&&p>=bt&&p<bt+2*I?r.Cartesian3.UNIT_X:r.Cartesian3.UNIT_Z,r.Cartesian3.cross(At,ht,xt),r.Cartesian3.normalize(xt,xt),g.tangent&&(q[ft++]=xt.x,q[ft++]=xt.y,q[ft++]=xt.z),g.bitangent){var kt=r.Cartesian3.cross(ht,xt,v);r.Cartesian3.normalize(kt,kt),H[dt++]=kt.x,H[dt++]=kt.y,H[dt++]=kt.z}}}g.st&&(st.st=new u.GeometryAttribute({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:K})),g.normal&&(st.normal=new u.GeometryAttribute({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:j})),g.tangent&&(st.tangent=new u.GeometryAttribute({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:q})),g.bitangent&&(st.bitangent=new u.GeometryAttribute({componentDatatype:n.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:H}))}if(o.defined(t._offsetAttribute)){var Ft=W.length,wt=new Uint8Array(Ft/3),Pt=t._offsetAttribute===c.GeometryOffsetAttribute.NONE?0:1;e.arrayFill(wt,Pt),st.applyOffset=new u.GeometryAttribute({componentDatatype:n.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:wt})}return new u.Geometry({attributes:st,indices:Z,primitiveType:C.PrimitiveType.TRIANGLES,boundingSphere:a.BoundingSphere.fromEllipsoid(pt),offsetAttribute:t._offsetAttribute})}}},k.getUnitEllipsoid=function(){return o.defined(F)||(F=k.createGeometry(new k({radii:new r.Cartesian3(1,1,1),vertexFormat:p.VertexFormat.POSITION_ONLY}))),F},t.EllipsoidGeometry=k}));
