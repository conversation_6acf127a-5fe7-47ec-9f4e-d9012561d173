<template>
  <div>
    <el-dialog
      @closed="closed"
      v-model="dialogVisible"
      :title="title"
      width="800"
    >
      <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
        <el-tab-pane
          v-for="(item, index) in tabsList"
          :key="index"
          :label="item.nodeName"
          :name="index"
        >
        </el-tab-pane>
      </el-tabs>
      <div class="flx-justify-space-evenly">
        <el-input
          v-model="auditIdea"
          style="width: 620px; margin-bottom: 20px"
          :rows="10"
          type="textarea"
          placeholder="请填写办理意见"
        />
      </div>
      <div class="flx-justify-space-evenly">
        <!-- 左侧树形结构 -->
        <div class="Box">
          <el-tree
            v-if="data.length > 0"
            ref="treeRef"
            :data="data"
            :show-checkbox="taskList[activeName].type === '1'"
            node-key="id"
            :props="defaultProps"
            @check-change="handleChoose"
          >
            <template #default="{ node, data }">
              <span
                class="custom-tree-node"
                @click="append(data, taskList[activeName], activeName, node)"
              >
                <span>{{ node.label }}</span>
                <span
                  v-if="!data.children && taskList[activeName].type === '0'"
                  class="custom-tree-btn"
                >
                  <a> 选择 </a>
                </span>
              </span>
            </template>
          </el-tree>
          <div v-else>
            <div
              v-for="(item, index) in treeData"
              :key="index"
              class="tree-box"
            >
              <div class="tree-box-item">
                <el-icon> <TakeawayBox /> </el-icon>{{ item.nodeName }}
              </div>
              <el-tree
                :ref="(el) => (treeRefArr[index] = el)"
                :data="item.tree"
                :show-checkbox="item.type === '1'"
                node-key="id"
                :props="defaultProps"
                @check-change="handleChooseArr(item, index)"
              >
                <template #default="{ node, data }">
                  <span class="custom-tree-node">
                    <span>{{ node.label }}</span>
                    <span
                      v-if="!data.children && item.type === '0'"
                      class="custom-tree-btn"
                      @click="append(data, item, index, node)"
                    >
                      <a> 选择 </a>
                    </span>
                  </span>
                </template>
              </el-tree>
            </div>
          </div>
        </div>

        <!-- 右侧选中叶子节点展示 -->
        <div class="Box" v-if="data.length > 0">
          <el-tag
            v-for="node in selectedLeafNodes"
            :key="node.id"
            class="mx-1 mr5"
            closable
            @close="removeNode(node)"
          >
            {{ node.name }}
          </el-tag>
        </div>
        <div class="Box" v-else>
          <div
            v-for="(item, index) in treeRefSelect"
            :key="index"
            class="tree-box"
          >
            <div
              v-if="item.selectNode.length >= 1"
              class="tree-box-item"
              style="margin-bottom: 10px"
            >
              <el-icon> <TakeawayBox /> </el-icon>{{ item.nodeName }}
            </div>
            <el-tag
              v-for="node in item.selectNode"
              :key="node.id"
              class="mx-1 mr5"
              closable
              @close="removeNodeArr(node, item.taskIndex)"
            >
              {{ node.name }}
            </el-tag>
          </div>
        </div>
      </div>
      <!-- <div v-else></div> -->
      <div class="mt15" style="width: 100%; text-align: right">
        <el-button type="info" @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="emitData">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { onMounted, ref } from "vue";
import { getDeptUserTreePost } from "@/api/system/user.js";
import { nextUserTask, getNodeList } from "@/api/document/common";
import { officeDivision, leadingDepartment } from "./selectMap.js";
const { proxy } = getCurrentInstance();

const props = defineProps({
  taskId: {
    type: String,
  },
  nodeId: {
    type: String,
  },
  processInstanceId: {
    type: String,
  },
  //意见
  msg: {
    type: String,
  },
  //仅第一阶段使用
  readType: {
    type: String,
  },
  //驳回开关
  rejectFlag: {
    type: Boolean,
  },
});
const returnData = reactive({});

const activeName = ref(0);
const handleClick = (pane, ev) => {
  // specialIds: JSON.stringify(tabsList.value[activeName.value].specialSourceOfData)
  let params = {
    postIds: JSON.stringify(taskList.value[pane.index].sourceOfData),
    specialIds: JSON.stringify(taskList.value[pane.index].specialSourceOfData),
  };
  getDeptUserTreeData(params);
  selectedLeafNodes.value =
    returnData[taskList.value[pane.index].assigneeVariable + "Node"];
  let arr = taskList.value[pane.index].branchControlVariable;
  arr?.map((it) => {
    let strArr = it.split("||");
    returnData[strArr[0]] = strArr[1];
  });
};

const dialogVisible = ref(false);
const defaultProps = {
  children: "children",
  label: "name",
};

const auditIdea = ref("");
const data = ref([]);
//获取数据
const getDeptUserTreeData = (query) => {
  if (query.postIds || query.specialIds) {
    getDeptUserTreePost(query).then((res) => {
      data.value = res.data;
    });
  } else {
    data.value = [];
  }
};

const closed = () => {
  dialogVisible.value = false;
  emit("back");
};

// 存储已选择的叶子节点
const selectedLeafNodes = ref([]);

// 处理左侧树选中事件
const handleChoose = () => {
  const tree = treeRef.value;
  const selectedNodes = tree.getCheckedNodes();
  let selectNode = selectedNodes.filter(
    (n) => !n.children || n.children.length === 0
  );
  returnData[taskList.value[activeName.value].assigneeVariable + "Node"] =
    selectNode;
  returnData[taskList.value[activeName.value].assigneeVariable] =
    selectNode.map((item) => item.id);
  selectedLeafNodes.value = selectNode;
};

// 移除右侧选中的叶子节点，并同步左侧树
const removeNode = (node) => {
  const tree = treeRef.value;
  tree.setChecked(node.id, false); // 取消左侧树节点选中
  selectedLeafNodes.value = selectedLeafNodes.value.filter(
    (n) => n.id !== node.id
  );
};

// emit选中数据
const emit = defineEmits(["saveData"]);
const emitData = () => {
  if (!auditIdea.value) {
    proxy.$modal.msgError("请填写意见");
    return;
  }

  // 通过策略表来处理最终提交数据
  let data = returnData;
  switch (props.nodeId) {
    case "Activity_1sfnhah":
      data = officeDivision(returnData);
      if (data === undefined) {
        return;
      }
      break;
    case "Activity_0by1yt6":
      data = leadingDepartment(returnData);
      if (data === undefined) {
        return;
      }
      break;
  }

  let result = {}
  if (props.rejectFlag) {
    // 驳回操作
    result = {
      message: auditIdea.value,
      variables: data.undefined,
    };
  } else {
    result = {
      message: auditIdea.value,
      variables: data,
    };
  }

  dialogVisible.value = false;
  emit("saveData", result);
};

// 互斥引用树组件
const treeRef = ref(null);
//并行树
const treeData = ref([]);
const treeRefArr = ref([]);
const treeRefSelect = ref([]);
const handleChooseArr = (item, index) => {
  const tree = treeRefArr.value[index];
  const selectedNodes = tree.getCheckedNodes();
  let selectNode = selectedNodes.filter(
    (n) => !n.children || n.children.length === 0
  );
  returnData[item.assigneeVariable] = selectNode.map((item) => item.id);
  let ArrIndex = treeRefSelect.value.findIndex(
    (it) => it.nodeName === item.nodeName
  );
  if (ArrIndex !== -1) {
    treeRefSelect.value[ArrIndex].selectNode = selectNode;
  } else {
    treeRefSelect.value.push({
      nodeName: item.nodeName,
      taskIndex: index,
      selectNode: selectNode,
    });
  }
  treeRefSelect.value.sort((a, b) => a.taskIndex - b.taskIndex);
};
const removeNodeArr = (node, index) => {
  console.log(index, treeRefArr.value);
  treeRefArr.value[index].setChecked(node.id, false); // 取消左侧树节点选中
  treeRefSelect.value[index].selectNode = treeRefSelect.value[
    index
  ].selectNode.filter((n) => n.id !== node.id);
};

//单选
const append = (treeData, item, index, node) => {
  returnData[item.assigneeVariable] = treeData.id;
  if (data.value.length > 0) {
    returnData[item.assigneeVariable + "Node"] = [treeData];
    selectedLeafNodes.value = [treeData];
  } else {
    let ArrIndex = treeRefSelect.value.findIndex(
      (it) => it.nodeName === item.nodeName
    );
    if (ArrIndex !== -1) {
      treeRefSelect.value[ArrIndex].selectNode = [treeData];
    } else {
      treeRefSelect.value.push({
        nodeName: item.nodeName,
        taskIndex: index,
        selectNode: [treeData],
      });
    }
    treeRefSelect.value.sort((a, b) => a.taskIndex - b.taskIndex);
  }
};

//流程节点
const title = ref("批转");
const taskList = ref([]);
const tabsList = ref([]);
onMounted(() => {
  auditIdea.value = props.msg;
  dialogVisible.value = true;

  if (props.rejectFlag) {
    title.value = "驳回";
    // 驳回
    getNodeList(props.processInstanceId, props.taskId).then((res) => {
      console.log("res", res);
      res.data.forEach((item) => {
        treeData.value.push({
          nodeName: item.nodeName,
          // nodeId: item.nodeId,
          type: "0",
          tree: [
            {
              id: item.assigneeDept,
              name: item.createDeptName,
              children: [
                {
                  id: item.nodeId,
                  name: item.assigneeNames,
                },
              ],
            },
          ],
        });
      });
    });
  } else {
    title.value = "批转";
    //互斥、并行
    let flag = false;
    nextUserTask(props.taskId).then((res) => {
      taskList.value = res.data;
      taskList.value.map((item) => {
        let arr = item.branchControlVariable;
        arr?.map((it) => {
          let strArr = it.split("||");
          flag = strArr[0] in returnData;
          returnData[strArr[0]] = strArr[1];
        });
      });
      if (flag) {
        //互斥
        taskList.value.map((item, index) => {
          //办理件、传阅件
          if (props.readType) {
            item.flag === props.readType
              ? tabsList.value.push({ ...item })
              : "";
          } else {
            tabsList.value.push({ ...item });
          }
        });
        taskList.value = tabsList.value;
        //初始化
        let arr = tabsList.value[activeName.value].branchControlVariable;
        arr?.map((it) => {
          let strArr = it.split("||");
          returnData[strArr[0]] = strArr[1];
        });
        getDeptUserTreeData({
          postIds: JSON.stringify(
            tabsList.value[activeName.value].sourceOfData
          ),
          specialIds: JSON.stringify(
            tabsList.value[activeName.value].specialSourceOfData
          ),
        });
      } else {
        //并行
        taskList.value.map((item) => {
          //空接口会返回全部列表
          if (
            (item.sourceOfData || item.specialSourceOfData) &&
            item.echoFlag === "0"
          ) {
            getDeptUserTreePost({
              postIds: JSON.stringify(item.sourceOfData),
              specialIds: JSON.stringify(item.specialSourceOfData),
            }).then((res) => {
              treeData.value.push({
                ...item,
                tree: res.data,
                selectNode: [],
              });
            });
          }
          // 单独处理只需回显数据( echoFlag 1回显 0不回显)
          else if (item.echoFlag === "1") {
            const [dept, name] = Object.entries(item.echoDetail.users)[0];
            treeData.value.push({
              nodeName: item.nodeName,
              tree: [
                {
                  id: "000000000000000000",
                  name: dept,
                  echoFlag: item.echoFlag,
                  children: [
                    {
                      id: "000000000000000001",
                      name: name + "(默认人员节点，无需选择)",
                      echoFlag: item.echoFlag,
                    },
                  ],
                },
              ],
            });
          } else {
            console.log("进入了");
            treeData.value.push({
              nodeName: item.nodeName,
              tree: [],
            });
          }
        });
      }
    });
  }
});
</script>

<style scoped lang="scss">
.Box {
  width: 270px;
  height: 400px;
  border: 1px solid #e7e4ed;
  overflow: auto;
  padding: 15px;
  border-radius: 6px;
}

.selected-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #e7e4ed;
  padding: 5px 15px;
  margin-bottom: 15px;
}

.tree-box {
  margin-bottom: 10px;

  .tree-box-item {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #409eff;
  }
}

.custom-tree-node {
  display: flex;
  width: 100%;
  justify-content: space-between;
  padding-right: 10px;

  .custom-tree-btn {
    color: #409eff;
  }
}
</style>