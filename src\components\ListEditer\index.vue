<template>
  <div>
    <el-table border :data="tableData" style="width: 100%">
      <el-table-column :label="column.label" align="center" v-for="(column, index) in columns" :key="index"
                       min-width="195">
        <template #default="scope">
          <el-select style="width: 100%" v-if="column.type === 'Select'" v-model="scope.row[column.name]">
            <el-option v-for="(sex,index) in column.data" :key="index" :value="sex.value"
                       :label="sex.label"></el-option>
          </el-select>
          <el-select style="width: 100%" multiple v-else-if="column.type === 'Multiple'"
                     v-model="scope.row[column.name]">
            <el-option v-for="(sex,index) in column.data" :key="index" :value="sex.value"
                       :label="sex.label"></el-option>
          </el-select>
          <el-date-picker style="width: 100%" v-else-if="column.type === 'Date'"
                          v-model="scope.row[column.name]"></el-date-picker>
          <el-input-number style="width: 100%" v-else-if="column.type === 'Number'"
                           v-model="scope.row[column.name]"></el-input-number>
          <el-switch v-else-if="column.type === 'Switch'" v-model="scope.row[column.name]"></el-switch>
          <el-tree-select v-else-if="column.type === 'Tree'" v-model="scope.row[column.name]" :data="column.data"
                          show-checkbox
                          check-strictly
                          filterable style="width: 100%"></el-tree-select>
          <el-upload v-else-if="column.type === 'File'"
                     action="none"
                     :auto-upload="false"
                     :limit="1"
                     :on-change="handleUploadSuccess(scope.row, column.name)">
            <el-icon size="30" style="color: #0f7dff;">
              <upload-filled/>
            </el-icon>
          </el-upload>
          <el-input v-else v-model="scope.row[column.name]"></el-input>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="100" fixed="right">
        <template #default="scope">
          <div class="group_class" style="justify-content: space-around">
            <el-icon
                :size="20"
                @click="removeGroup(scope.$index)"
                color="#f56c6c"
                v-if="tableData.length > 1"
            >
              <Remove
              />
            </el-icon>
            <el-icon
                :size="20"
                @click="pushGroup"
                color="#409eff"
                v-if="scope.$index === tableData.length - 1"
            >
              <CirclePlus
              />
            </el-icon>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import {CirclePlus, Remove} from "@element-plus/icons-vue";

const props = defineProps({
  tableData: {
    type: Array,
    default: () => [],
  },
  columns: {
    type: Array,
    default: () => [],
  }
});
const columns = ref(props.columns);
const tableData = ref(props.tableData);
if (tableData.value.length <= 0) {
  createDefault();
}

function pushGroup() {
  createDefault();
}

// 这里是使用索引来删除 考虑到当前项目不会出现很多数据 这样最快捷 如果涉及的数据过多 请自行生成id检索保证性能
function removeGroup(index) {
  tableData.value.splice(index, 1);
}

function createDefault() {
  const singleData = createColumns();
  tableData.value.push(singleData);
}

function createColumns() {
  const singleData = {};
  columns.value.forEach(property => {
    singleData[property.name] = property.default;
  })
  return singleData;
}

function handleUploadSuccess(row,column) {
  return (response, file) => {
    row[column] = response.raw;
  }
}
</script>

<style lang="scss" scoped>
.group_class {
  display: flex;
  align-items: center;

  i {
    transition: color 0.3s;
  }

  i:hover {
    color: #409eff;
  }
}
</style>