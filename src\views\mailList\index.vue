<template>
  <div>
    <div
      class="box"
      ref="box" v-show="!addShow"
    >
      <div class="left">
        <div class="mb5">
          <el-button
            type="primary" size="mini" @click="goToAdd"
            v-if="auth.hasPermi('mailList:add:add')"
          >新增</el-button>
          <el-button
            type="primary"
            size="mini" @click="goToView"
          >查看</el-button>
          <el-button
            type="primary"
            size="mini" @click="isViewMail"
          >显示通讯录</el-button>
        </div>
        <div class="left-body">
          <el-input
            v-model="deptNameKey"
            @input="getUnitList"
            placeholder="输入关键字搜索"
          >
            <template #prepend>全局搜索</template>
          </el-input>
          <el-table
            class="left-table"
            :data="tableData"
            border
            style="width: 100%"
            highlight-current-row
            @row-click="rowClick"
            @row-dblclick="goToView"
          >
            <el-table-column
              type="index"
              label="序号" width="60"
            />
            <el-table-column
              prop="deptName"
              label="单位名称" min-width="180"
            />
            <el-table-column
              prop="phone"
              label="办公室电话" width="180"
            />
            <el-table-column
              prop="addressName"
              label="单位地址"
              min-width="280"
            />
            <el-table-column
              prop="postalCode"
              label="邮政编号" width="180"
            />
            <el-table-column
              prop="email"
              label="电子邮箱" width="180"
            />
            <el-table-column
              prop="fax"
              label="传真" width="180"
            />
            <el-table-column
              fixed="right"
              label="操作" min-width="120"
            >
              <template #default="scope">
                <el-button
                  link
                  type="success"
                  size="small"
                  @click="goToEdit(scope.row)"
                  v-if="auth.hasPermi('mailList:edit:edit')"
                >编辑</el-button>
                <el-button
                  link
                  type="danger"
                  size="small"
                  @click="delDept(scope.row)"
                  v-if="auth.hasPermi('mailList:del:del')"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="left-footer mt5 flx-center">
            <div class="mr10">总记录 {{ tableDataTotal }} 条</div>
            <el-button
              size="mini"
              @click="reDeptList"
            >刷新</el-button>
          </div>
        </div>
      </div>
      <div
        class="resize"
        title="拖拽条"
      />
      <div class="mid">
        <!--右侧div内容-->
        <div>
          <div class="mid-header flx-justify-between">
            <div class="mid-header-title">通讯录</div>
            <el-radio-group v-model="radio1">
              <el-radio
                value="1"
                size="large"
              >左右</el-radio>
              <el-radio
                value="2"
                size="large"
              >上下</el-radio>
            </el-radio-group>
          </div>
          <el-button
            class="mt5 mb5 ml5"
            type="primary"
            size="mini"
            @click="goToUserAdd"
            v-if="auth.hasPermi('mailList:add:add')"
          >新增</el-button>
          <el-button
            class="mt5 mb5 ml5"
            type="primary"
            size="mini"
            @click="goToUserView"
          >查看</el-button>
          <div class="mid-body">
            <el-input
              v-model="userNameKey"
              @input="handleUser"
              placeholder="输入关键字搜索"
            >
              <template #prepend>全局搜索</template>
            </el-input>
            <el-table
              class="mid-table"
              :data="tableDataUser"
              style="width: 100%"
              row-key="id"
              border
              highlight-current-row
              :span-method="arraySpanMethod"
              @row-click="rowUserClick"
              @row-dblclick="goToUserView"
            >
              <el-table-column
                type="index"
                width="50"
              />
              <el-table-column
                label="姓名"
                sortable
              >
                <template #default="scope">
                  <span v-if="scope.row.userName">{{
                    scope.row.userName
                  }}</span>
                  <span v-else>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;部门：{{
                    scope.row.deptName
                  }}
                    （{{ scope.row.num }} 项目）</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="jobName"
                label="职务" sortable
              />
              <el-table-column
                prop="phonenumber"
                label="移动电话" sortable
              />
              <el-table-column
                prop="landline"
                label="座机" sortable
              />
              <el-table-column
                prop="phone"
                label="办公室电话" sortable
              />
              <el-table-column
                prop="remark"
                label="备注" sortable
              />
              <el-table-column
                fixed="right"
                label="操作" width="120"
              >
                <template #default="scope">
                  <el-button
                    link
                    type="success"
                    size="small"
                    @click="goToEditUser(scope.row)"
                    v-if="auth.hasPermi('mailList:edit:edit')"
                  >编辑</el-button>
                  <el-button
                    link
                    type="danger"
                    size="small"
                    @click="delUser(scope.row)"
                    v-if="auth.hasPermi('mailList:del:del')"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <div class="mid-footer mt5 flx-center">
              <div class="mr10">总记录 {{ tableDataUserTotal }} 条</div>
              <el-button
                size="mini"
                @click="reUserList"
              >刷新</el-button>
              <div class="ml10">总分组 {{ tableDataGroupTotal }} 条</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <Add
      v-if="addShow"
      :parentData="parentData"
      :type="type"
      :pageType="pageType"
      :deptId="deptId"
      @close="handelClose"
    />
  </div>
</template>

<script setup>
import { onMounted, ref } from "vue";
import Add from "./components/add.vue";
import {
  getMailListDept,
  delUnitDept,
  getBookUserList,
  delUserInfo
} from "@/api/mailList/index.js";
import { ElMessage, ElMessageBox } from "element-plus";
import auth from '@/plugins/auth';



// 新增
const deptNameKey = ref("");
const addShow = ref(false);
const pageType = ref("dept");
const deptId = ref("");
const type = ref("add");
const goToAdd = () => {
  addShow.value = true;
  type.value = "add";
  pageType.value = "dept";
};
//
const handelClose = (msg) => {
  addShow.value = msg;
  getUnitList();
  getUserList();
};
// 编辑
const goToEdit = (row) => {
  deptId.value = row.deptId;
  type.value = "edit";
  pageType.value = "dept";
  addShow.value = true;
};
// 查看
const goToView = () => {
  if (selectedRow.value) {
    deptId.value = selectedRow.value.deptId;
  } else {
    deptId.value = tableData.value[0].deptId;
  }
  pageType.value = "dept";
  type.value = "view";
  addShow.value = true;
};
// 删除
const delDept = (row) => {
  ElMessageBox.confirm(`您确定删除 ${row.deptName}单位通讯录?`, "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      delUnitDept(row.deptId).then((res) => {
        if (res.code === 200) {
          ElMessage({
            type: "success",
            message: "删除成功"
          });
          getUnitList();
        } else {
          ElMessage({
            type: "danger",
            message: res.msg
          });
        }
      });
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "操作已取消"
      });
    });
};

const radio1 = ref("1");

// 获取部门数据
const tableData = ref([]);
const tableDataTotal = ref(0);
const getUnitList = () => {
  let params = {
    parentId: "0",
    deptName: deptNameKey.value
  };
  getMailListDept(params).then((res) => {
    tableData.value = res.data.rows;
    tableDataTotal.value = res.data.total;
    getUserList();
  });
};
const reDeptList = () => {
  getUnitList();
};

// user
const userNameKey = ref("");
const tableDataUser = ref([]);
const tableDataUserTotal = ref(0);
const tableDataGroupTotal = ref(0);
const parentData = ref({});
const goToUserAdd = () => {
  if (selectedRow.value) {
    parentData.value = selectedRow.value;
  } else {
    parentData.value = tableData.value[0];
  }
  addShow.value = true;
  type.value = "addUser";
  pageType.value = "user";
};
//获取用户数据
const getUserList = () => {
  let parentDeptId = "";
  if (selectedRow.value) {
    parentDeptId = selectedRow.value.deptId;
  } else {
    parentDeptId = tableData.value[0].deptId;
  }
  let params = {
    deptId: parentDeptId,
    userName: userNameKey.value
  };
  getBookUserList(params).then((res) => {
    tableDataUser.value = res.data.list;
    tableDataUserTotal.value = res.data.total;
    tableDataGroupTotal.value = res.data.deptNum;
  });
};
// refresh
const reUserList = () => {
  getUserList();
};
// view
const goToUserView = () => {
  if (selectedRow.value) {
    parentData.value = selectedRow.value;
  } else {
    parentData.value = tableData.value[0];
  }
  if (selectedUserRow.value) {
    deptId.value = selectedUserRow.value.userId;
  } else {
    deptId.value = tableDataUser.value[0].children[0].userId;
  }
  addShow.value = true;
  type.value = "viewUser";
  pageType.value = "user";
};
// goToEditUser
const goToEditUser = (row) => {
  if (selectedRow.value) {
    parentData.value = selectedRow.value;
  } else {
    parentData.value = tableData.value[0];
  }
  deptId.value = row.userId;
  type.value = "editUser";
  pageType.value = "user";
  addShow.value = true;
};
// delUser
const delUser = (row) => {
  ElMessageBox.confirm(`您确定删除 ${row.userName} 通讯录资料?`, "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      delUserInfo(row.userId).then((res) => {
        if (res.code === 200) {
          ElMessage({
            type: "success",
            message: "删除成功"
          });
          getUnitList();
        } else {
          ElMessage({
            type: "danger",
            message: res.msg
          });
        }
      });
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "操作已取消"
      });
    });
};
const handleUser = () => {
  getUserList();
};

const arraySpanMethod = (row, column, rowIndex, columnIndex) => {
  let hideColArr = [0]; //数组是根据要合并哪几个来定
  if (row.row.flag == "DEPART") {
    if (hideColArr.includes(row.columnIndex)) {
      return { display: "none" };
    }
    return { rowspan: 1, colspan: 8 };
  }
};

//单位树
const selectedRow = ref(null);
const rowClick = (row) => {
  selectedRow.value = row;
  getUserList();
};
const selectedUserRow = ref(null);
const rowUserClick = (row) => {
  selectedUserRow.value = row;
  // getUserList();
};

//是否显示通讯录
const isView = ref(true);
const isViewMail = () => {
  var resize = document.getElementsByClassName("resize");
  var left = document.getElementsByClassName("left");
  var mid = document.getElementsByClassName("mid");
  var leftTable = document.getElementsByClassName("left-table");
  var midTable = document.getElementsByClassName("mid-table");
  if (isView.value) {
    isView.value = false;
    if (radio1.value === "2") {
      for (let i = 0; i < resize.length; i++) {
        left[i].style.height = "100%";
        resize[i].style.height = 0;
        mid[i].style.height = 0;
      }
    } else {
      for (let i = 0; i < resize.length; i++) {
        left[i].style.width = "100%";
        resize[i].style.width = 0;
        mid[i].style.width = 0;
      }
    }
  } else {
    isView.value = true;
    if (radio1.value === "2") {
      for (let i = 0; i < resize.length; i++) {
        left[i].style.height = "49%";
        resize[i].style.height = "10px";
        mid[i].style.height = "50%";
      }
    } else {
      for (let i = 0; i < resize.length; i++) {
        left[i].style.width = "31%";
        resize[i].style.width = "10px";
        mid[i].style.width = "68%";
      }
    }
  }
};

// 监听 radio1 和 addShow 的变化
watch([radio1, addShow], ([newRadioValue, newAddShow]) => {
  var resize = document.getElementsByClassName("resize");
  var left = document.getElementsByClassName("left");
  var mid = document.getElementsByClassName("mid");
  var leftTable = document.getElementsByClassName("left-table");
  var midTable = document.getElementsByClassName("mid-table");

  if (!newAddShow) {
    dragControllerDiv();
    // location.reload();
    // router.push('/mailList')
  }

  if (newRadioValue === "2") {
    for (let i = 0; i < resize.length; i++) {
      left[i].style.float = "";
      left[i].style.width = "100%";
      left[i].style.height = "49%";
      resize[i].style.float = "";
      resize[i].style.width = "100%";
      resize[i].style.height = "10px";
      resize[i].style.cursor = "row-resize";
      mid[i].style.float = "";
      mid[i].style.width = "100%";
      mid[i].style.height = "50%";
      // 获取实际像素高度
      const leftHeight = left[i].offsetHeight;
      const midHeight = mid[i].offsetHeight;
      leftTable[i].style.height = leftHeight - 5 - 37 - 32 - 37 + "px";
      midTable[i].style.height = midHeight - 37 - 42 - 42 - 32 + "px";
    }
  } else {
    for (let i = 0; i < resize.length; i++) {
      left[i].style.float = "left";
      left[i].style.width = "31%";
      left[i].style.height = "100%";
      resize[i].style.float = "left";
      resize[i].style.width = "10px";
      resize[i].style.height = "100%";
      resize[i].style.cursor = "col-resize";
      mid[i].style.float = "left";
      mid[i].style.width = "68%";
      mid[i].style.height = "100%";
      // 获取实际像素高度
      const leftHeight = left[i].offsetHeight;
      const midHeight = mid[i].offsetHeight;
      leftTable[i].style.height = leftHeight - 5 - 37 - 32 - 37 + "px";
      midTable[i].style.height = midHeight - 37 - 42 - 42 - 32 + "px";
    }
  }
});

const dragControllerDiv = () => {
  var resize = document.getElementsByClassName("resize");
  var left = document.getElementsByClassName("left");
  var mid = document.getElementsByClassName("mid");
  var box = document.getElementsByClassName("box");
  var leftTable = document.getElementsByClassName("left-table");
  var midTable = document.getElementsByClassName("mid-table");
  for (let i = 0; i < resize.length; i++) {
    // 鼠标按下事件
    resize[i].onmousedown = function (e) {
      //颜色改变提醒
      resize[i].style.background = "#818181";
      var startX = e.clientX;
      var startY = e.clientY;
      resize[i].left = resize[i].offsetLeft;
      if (radio1.value === "1") {
        // 鼠标拖动事件
        document.onmousemove = function (e) {
          var endX = e.clientX;
          var moveLen = resize[i].left + (endX - startX); // （endx-startx）=移动的距离。resize[i].left+移动的距离=左边区域最后的宽度
          var maxT = box[i].clientWidth - resize[i].offsetWidth; // 容器宽度 - 左边区域的宽度 = 右边区域的宽度

          if (moveLen < 32) moveLen = 320; // 左边区域的最小宽度为32px
          if (moveLen > maxT - 150) moveLen = maxT - 150; //右边区域最小宽度为150px

          resize[i].style.left = moveLen; // 设置左侧区域的宽度

          for (let j = 0; j < left.length; j++) {
            left[j].style.width = moveLen + "px";
            mid[j].style.width = box[i].clientWidth - moveLen - 10 + "px";
          }
        };
      } else {
        // 鼠标拖动事件
        document.onmousemove = function (e) {
          var endY = e.clientY;
          var moveLen = resize[i].left + (endY - startY);
          var maxT = box[i].clientHeight - resize[i].offsetHeight;

          if (moveLen < 32) moveLen = 320;
          if (moveLen > maxT - 150) moveLen = maxT - 150;

          resize[i].style.left = moveLen;

          for (let j = 0; j < left.length; j++) {
            left[j].style.height = moveLen + "px";
            mid[j].style.height = box[i].clientHeight - moveLen - 10 + "px";

            // 获取实际像素高度
            const leftHeight = left[j].offsetHeight;
            const midHeight = mid[j].offsetHeight;
            leftTable[j].style.height = leftHeight - 5 - 37 - 32 - 37 + "px";
            midTable[j].style.height = midHeight - 37 - 42 - 42 - 32 + "px";
          }
        };
      }

      // 鼠标松开事件
      document.onmouseup = function (evt) {
        //颜色恢复
        resize[i].style.background = "#d6d6d6";
        document.onmousemove = null;
        document.onmouseup = null;
        resize[i].releaseCapture && resize[i].releaseCapture(); //当你不在需要继续获得鼠标消息就要应该调用ReleaseCapture()释放掉
      };
      resize[i].setCapture && resize[i].setCapture(); //该函数在属于当前线程的指定窗口里设置鼠标捕获
      return false;
    };
  }
};

onMounted(() => {
  dragControllerDiv();
  getUnitList();
});
</script>

<style scoped lang="scss">
@import "@/styles/variables.module.scss";;
/* 拖拽相关样式 */
/*包围div样式*/
.box {
  width: 100vw;
  height: $contentHeight;
  background-color: #e9eef3;
  overflow: hidden;
}
/*左侧div样式*/
.left {
  width: calc(32% - 10px); /*左侧初始化宽度*/
  height: 100%;
  float: left;
  padding: 5px 0 0 5px;
  .left-table {
    height: calc(#{$contentHeight} - 5px - 37px - 32px - 37px);
  }
  .left-footer {
    width: 200px;
  }
}
/*拖拽区div样式*/
.resize {
  cursor: col-resize;
  float: left;
  position: relative;
  top: 0;
  background-color: #d6d6d6;
  width: 10px;
  height: 100%;
  //   width: 100%;
  //   height: 10px;
  background-size: cover;
  background-position: center;
  /*z-index: 99999;*/
  font-size: 32px;
  color: white;
  display: flex;
  align-items: center;
}
/*拖拽区鼠标悬停样式*/
.resize:hover {
  color: #444444;
}
/*右侧div'样式*/
.mid {
  float: left;
  width: 68%; /*右侧初始化宽度*/
  height: 100%;
  //   padding: 0 5px 0 0;
  .mid-header {
    background: #fff;
    padding: 0 5px;
    color: $--color-primary;
    .mid-header-title {
      padding: 10px;
      border-bottom: 1px solid $--color-primary;
    }
  }
  .mid-body {
    padding: 0 5px;
    .mid-table {
      height: calc(#{$contentHeight} - 42px - 42px - 32px - 37px);
    }
    .mid-footer {
      width: 400px;
    }
  }
}
//

.el-table__body tr.current-row > td {
  background: #409eff !important;
  color: #fff;
}
</style>
