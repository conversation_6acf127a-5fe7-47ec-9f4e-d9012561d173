<template>
  <div>
    <div class="table-box">
      <ProTable
        ref="proTable"
        :columns="columns"
        :request-api="getTableList"
        :init-param="initParam"
        :data-callback="dataCallback"
      >
        <!-- 表格 header 按钮 -->
        <template #tableHeader="">
          <el-button
            type="primary"
            icon="CirclePlus"
            @click="addHadleNews"
            v-if="auth.hasPermi('content:news:add')"
            >新增</el-button
          >
        </template>
        <template #operation="scope">
          <el-button
            link
            type="success"
            icon="View"
            @click="viewDetail(scope.scope.row)"
            >查看</el-button
          >
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="editDetail(scope.scope.row)"
            v-if="auth.hasPermi('content:news:edit')"
            >修改</el-button
          >
          <el-button
            link
            type="danger"
            icon="Delete"
            @click="delDetail(scope.scope.row)"
            v-if="auth.hasPermi('content:news:del')"
            >删除</el-button
          >
        </template>
      </ProTable>
    </div>
    <!-- 组件 -->
    <AddDialog
      ref="addDialog"
      v-if="addDialogShow"
      :data="dialogData"
      @closeMessage="handleCloseAddDialog"
    />
    <ViewDialog
      ref="viewDialogRef"
      v-if="viewDialogShow"
      :data="viewData"
      @closeViewDialog="handleCloseViewDialog"
    >
    </ViewDialog>
  </div>
</template>

<script setup name="content-news">
import { ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import auth from "@/plugins/auth";

//组件
import AddDialog from "./components/addDialog.vue";
import ViewDialog from "./components/ViewDialog.vue";
import { getNewsList, delNewsById } from "@/api/content/news.js";
import { getDicts } from "@/api/system/dict/data.js";
import ProTable from "@/components/ProTable/index.vue";

const proTable = ref();
//
const columns = reactive([
  { type: "index", label: "序号", width: 80 },
  {
    prop: "title",
    label: "标题",
    search: { el: "input" },
  },
  {
    prop: "workDate",
    label: "发布时间",
    search: {
      el: "date-picker",
      props: { type: "daterange", valueFormat: "YYYY-MM-DD" },
    },
  },
  {
    prop: "type",
    label: "新闻类型",
    enum: () => getDicts("doc_unit_name"),
  },
  {
    prop: "sendName",
    label: "新闻发布人",
    search: { el: "input" },
  },
  { prop: "operation", label: "操作", fixed: "right", width: 280 },
]);

// 如果表格需要初始化请求参数，直接定义传给 ProTable (之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({});

// dataCallback 是对于返回的表格数据做处理，如果你后台返回的数据不是 list && total 这些字段，可以在这里进行处理成这些字段
// 或者直接去 hooks/useTable.ts 文件中把字段改为你后端对应的就行
const dataCallback = (data) => {
  console.log("data1", data);
  return {
    list: data.list,
    total: data.total,
  };
};

const getTableList = (params) => {
  let newParams = JSON.parse(JSON.stringify(params));
  newParams.workDate && (newParams.startDay = newParams.workDate[0]);
  newParams.workDate && (newParams.endDay = newParams.workDate[1]);
  delete newParams.workDate;
  return getNewsList(newParams);
};

// 新增数据
const addDialogShow = ref(false);
const addHadleNews = () => {
  dialogData.value = {
    type: "add",
    show: true,
  };
  addDialogShow.value = true;
};
// 修改数据
const dialogData = ref({
  type: "add",
  show: true,
  data: {},
});
const editDetail = (row) => {
  console.log("row", row);
  dialogData.value = {
    type: "edit",
    show: true,
    data: row,
  };
  addDialogShow.value = true;
};
// 查看数据
const viewDialogShow = ref(false);
const viewData = ref({
  show: false,
  data: {},
});
const viewDetail = (row) => {
  viewData.value = {
    show: true,
    data: row,
  };
  viewDialogShow.value = true;
};
// 关闭新增弹窗
const handleCloseAddDialog = (msg) => {
  addDialogShow.value = msg;
  proTable.value?.getTableList();
};
//
const handleCloseViewDialog = () => {
  viewDialogShow.value = false;
  proTable.value?.getTableList();
};

//删除
const delDetail = (row) => {
  ElMessageBox.confirm("您确定删除本条新闻记录?", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      delNewsById(row.id).then((res) => {
        if (res.code === 200) {
          ElMessage({
            type: "success",
            message: "删除成功",
          });
          proTable.value?.getTableList();
        } else {
          ElMessage({
            type: "danger",
            message: res.msg,
          });
        }
      });
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "操作已取消",
      });
    });
};

onMounted(() => {});
</script>

<style scoped lang="scss">
.content {
  padding: 15px;

  .el-row {
    margin-bottom: 15px;
  }
}
</style>