define(["exports","./Check-7b2a090c","./when-b60132fc"],(function(e,t,s){"use strict";function i(){this._listeners=[],this._scopes=[],this._toRemove=[],this._insideRaiseEvent=!1}function n(e,t){return t-e}Object.defineProperties(i.prototype,{numberOfListeners:{get:function(){return this._listeners.length-this._toRemove.length}}}),i.prototype.addEventListener=function(e,t){this._listeners.push(e),this._scopes.push(t);var s=this;return function(){s.removeEventListener(e,t)}},i.prototype.removeEventListener=function(e,t){for(var s=this._listeners,i=this._scopes,n=-1,r=0;r<s.length;r++)if(s[r]===e&&i[r]===t){n=r;break}return-1!==n&&(this._insideRaiseEvent?(this._toRemove.push(n),s[n]=void 0,i[n]=void 0):(s.splice(n,1),i.splice(n,1)),!0)},i.prototype.raiseEvent=function(){var e;this._insideRaiseEvent=!0;var t=this._listeners,i=this._scopes,r=t.length;for(e=0;e<r;e++){var o=t[e];s.defined(o)&&t[e].apply(i[e],arguments)}var h=this._toRemove;if((r=h.length)>0){for(h.sort(n),e=0;e<r;e++){var v=h[e];t.splice(v,1),i.splice(v,1)}h.length=0}this._insideRaiseEvent=!1},e.Event=i}));
