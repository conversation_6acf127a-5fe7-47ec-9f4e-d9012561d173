import request from '@/utils/request'

export function exportLandPrice(data){
  return request({
    url: '/landExport/exportMerge',
    method: 'post',
    data: data,
    responseType: 'blob' // 将响应数据类型设置为blob
  })
}

export function exportLandAnalyse(data){
  return request({
    url: '/landExport/export',
    method: 'post',
    data: data,
    responseType: 'blob' // 将响应数据类型设置为blob
  })
}

export function downloadOnanalysis(query){
  return request({
    url: '/map/onanalysis/download?filename=' + query,
    method: 'get',
    responseType: 'blob' // 将响应数据类型设置为blob
  })
}

