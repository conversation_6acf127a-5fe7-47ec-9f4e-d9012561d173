<template>
  <div v-if="isShow" class="analyse-wrap">
    <!-- <Header :header-info="headerInfo" @closePanel="closePanel" /> -->
    <div class="tab-container">
      <div class="tool-tabs">
        <span
          :class="{ 'tab-active': isModel }"
          @click="waterAnalyseOfModel"
        >模型</span>
        <span
          :class="{ 'tab-active': isTerrain }"
          @click="waterAnalyseOfTerrain"
        >地形</span>
      </div>
      <div class="tool-tab-content">
        <div v-if="false" class="color-set clearfix">
          <label>选择色带</label>
          <div class="color-band">
            <button class="color-btn" />
            <el-select
              ref="colorSelect"
              v-model="colorValue"
              placeholder="请选择色带"
              class="color-select"
              @change="colorSelect"
            >
              <el-option
                v-for="item in colorOptions"
                :key="item.value"
                :label="item.value"
                :value="item.value"
                data-content="<span class='label band1'>&nbsp</span>"
              >
                <span class="label" :class="`band${item.value}`" />
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="height-value clearfix">
          <label>最大高度</label>
          <el-input
            v-model.number="maxValue"
            placeholder="请输入最大高度"
            class="input-value"
          />
        </div>
        <div class="height-value clearfix">
          <label>最低高度</label>
          <el-input
            v-model.number="minValue"
            placeholder="请输入最低高度"
            class="input-value"
          />
        </div>
        <div class="height-value clearfix">
          <label>淹没速度</label>
          <el-input
            v-model="speedValue"
            placeholder="请输入最大高度"
            class="input-value"
          />
        </div>
      </div>
      <div class="tool-bottom">
        <div class="tool-btn" @click="startFlood">
          <svg-icon icon-class="start_analyse" />
          <span>{{ isModel ? '开始分析':"绘制分析区域" }}</span>
        </div>
        <div class="tool-btn" @click="clearResult">
          <svg-icon icon-class="clear" />
          <span>清除</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="WaterAnalysis" text="水淹分析">
import useMapViewStore from "@/store/modules/map/mapView.js"
import { ElMessage } from 'element-plus'
import { createTooltip, setCursor, setLayerSelectStatus } from "@/utils/Cesium/CesiumTool.js"


const { proxy } = getCurrentInstance()


defineProps({
  isShow: {
    type: Boolean,
    default: true
  },
  headerInfo: {
    type: Object
  }
})

const isTerrain = ref(true)
const isModel = ref(false)
const maxValue = ref(2500)
const minValue = ref(1650)
// 淹没速度
const speedValue = ref(50)
// 当前高度
const currentHeight = ref(0)
const colorValue = ref("")
// 色带
const colorOptions = ref([
  {
    value: 1,
    label: "",
    background: "linear-gradient(to left, #95e8f9, #002794)"
  },
  {
    value: 2,
    label: "",
    background: "linear-gradient(to left, #a2fbc2, #ff6767)"
  },
  {
    value: 3,
    label: "",
    background: "linear-gradient(to left, #e6c6ff, #9d00ff)"
  },
  {
    value: 4,
    label: "",
    background:
        "linear-gradient(to left, #0909d4,#00a1ff 20%, #14bb12 40%, #dde007 60%, #d20f0f)"
  },
  {
    value: 5,
    label: "",
    background: "linear-gradient(to left, #baffe5, #1ab99c)"
  }
])
const s3mLayer = ref(undefined)
const timeer = ref(undefined)
const polygonHandler = ref(undefined)
const tooltip = ref(undefined)


const viewer3d = computed(()=>useMapViewStore().viewer3d)


watch(currentHeight,(newVal,oldValue)=>{
  if (newVal > maxValue.value) {
    clearInterval(timeer.value);
    timeer.value = null;
  }
})

watch(minValue,(newVal,oldValue)=>{
  if (newVal >= maxValue.value) {
    ElMessage({
      type: "warning",
      message: "最小值不能超过最大值，请重新输入"
    })
    minValue.value = 0
  }
})

watch(maxValue,(newVal,oldValue)=>{
  if (newVal >= minValue.value) {
    ElMessage({
      type: "warning",
      message: "最大值不能小于最小值，请重新输入"
    })
    maxValue.value = 0
  }
})

const emits = defineEmits(['closePanel'])

const closePanel = ()=>{
  emits('closePanel')
}

const startFlood = ()=> {
  setLayerSelectStatus(viewer3d.value,false)
  if (isModel.value) {
    modelFlood();
  } else if (isTerrain.value) {
    terrainFloodAnalyse();
  }
}

const clearResult = ()=> {
  clearInterval(timeer.value);

  polygonHandler.value && polygonHandler.value.deactivate();
  tooltip.value && tooltip.value.setVisible(false);
  viewer3d.value.scene.globe.HypsometricSetting = undefined;

  const hyp = new Cesium.HypsometricSetting();
  hyp.MaxVisibleValue = -1000;
  if (s3mLayer.value) {
    s3mLayer.value.hypsometricSetting = {
      hypsometricSetting: hyp,
      analysisMode: Cesium.HypsometricSettingEnum.AnalysisRegionMode.ARM_ALL
    };
  }
  currentHeight.value = 0;
}

// 模型淹没
const modelFlood = ()=> {
  clearResult();
  const scene = viewer3d.value.scene;
  currentHeight.value = minValue.value;
  const layerNames = ["倾斜模型", "fenghu", "fengceng"];
  layerNames.forEach((name) => {
    s3mLayer.value = scene.layers.find(name);
  });
  if (!s3mLayer.value) {
    ElMessage.warning("请先添加s3m图层！");
    return;
  }
  timeer.value = setInterval(() => {
    const hyp = new Cesium.HypsometricSetting();

    //创建分层设色对象   设置最大/最小可见高度   颜色表  显示模式   透明度及线宽
    const colorTable = new Cesium.ColorTable();

    hyp.MaxVisibleValue = maxValue.value;
    hyp.MinVisibleValue = minValue.value;

    setColorTable(colorTable, 6);

    hyp.ColorTable = colorTable;
    hyp.DisplayMode = Cesium.HypsometricSettingEnum.DisplayMode.FACE;
    hyp.Opacity = 0.5;

    hyp.LineInterval = 10.0;

    //设置图层分层设色属性
    s3mLayer.value.hypsometricSetting = {
      hypsometricSetting: hyp,
      analysisMode: Cesium.HypsometricSettingEnum.AnalysisRegionMode.ARM_ALL
    };
    currentHeight.value += speedValue.value;
  }, 1000);
}
// 地形淹没
const terrainFloodAnalyse = ()=> {
  clearResult();
  const hypFlood = new Cesium.HypsometricSetting();
  const floodColorTable = new Cesium.ColorTable();
  const colorTable = setColorTable(floodColorTable, 4);
  hypFlood.DisplayMode = Cesium.HypsometricSettingEnum.DisplayMode.FACE;
  hypFlood._lineColor = new Cesium.Color(1.0, 0.0, 0.0, 1.0);
  hypFlood.MinVisibleValue = 0;
  hypFlood.MaxVisibleValue = 0;
  hypFlood.ColorTableMinKey = 1;
  hypFlood.ColorTableMaxKey = 9000;
  hypFlood.ColorTable = colorTable;
  hypFlood.Opacity = 0.8;
  hypFlood.LineInterval = 200.0;

  // 绘制多边形
  tooltip.value = createTooltip(document.body);
  const polygonHandler = new Cesium.DrawHandler(
    viewer3d.value,
    Cesium.DrawMode.Polygon
  );
  // 激活绘制
  polygonHandler.activate();

  polygonHandler.activeEvt.addEventListener((isActive) => {

  });

  polygonHandler.movingEvt.addEventListener((windowPosition) => {
    setCursor(viewer3d.value,"crosshair")
    tooltip.value.showAt(
      windowPosition,
      "<p>开始绘制分析区域，右键结束绘制</p>"
    );
  });

  polygonHandler.drawEvt.addEventListener((polygon) => {
    setCursor(viewer3d.value,"pointer")
    polygonHandler.polygon.show = false;
    polygonHandler.polyline.show = false;
    tooltip.value.setVisible(false);
    const array = [].concat(polygon.object.positions);
    const positions = [];
    for (let i = 0, len = array.length; i < len; i++) {
      const cartographic = Cesium.Cartographic.fromCartesian(array[i]);
      const longitude = Cesium.Math.toDegrees(cartographic.longitude);
      const latitude = Cesium.Math.toDegrees(cartographic.latitude);
      const h = cartographic.height;
      if (
        positions.indexOf(longitude) == -1 &&
        positions.indexOf(latitude) == -1
      ) {
        positions.push(longitude);
        positions.push(latitude);
        positions.push(h);
      }
    }
    floodUpdate(hypFlood, positions, minValue.value, maxValue.value);
    polygonHandler.deactivate();
  });
}

// 监听淹没速度
const floodUpdate = (hypFlood, positions, minValue, maxValue)=> {
  if (currentHeight.value > maxValue) {
    clearInterval(timeer.value);
    return;
  }

  hypFlood.CoverageArea = positions;
  currentHeight.value = minValue;
  hypFlood.MinVisibleValue = minValue;
  timeer.value = setInterval(() => {
    hypFlood.MaxVisibleValue = currentHeight.value;
    viewer3d.value.scene.globe.HypsometricSetting = {
      hypsometricSetting: hypFlood,
      analysisMode: Cesium.HypsometricSettingEnum.AnalysisRegionMode.ARM_REGION
    };
    currentHeight.value += speedValue.value;
  }, 100);
  // eventBus.$emit('showLoading', false)
}

const setColorTable = (colorTable, index)=> {
  //创建分层设色对象   设置最大/最小可见高度   颜色表  显示模式   透明度及线宽
  switch (index) {
    case 1:
      colorTable.insert(1960, new Cesium.Color(0, 39 / 255, 148 / 255));
      colorTable.insert(1900, new Cesium.Color(149 / 255, 232 / 255, 249 / 255));
      break;
    case 2:
      colorTable.insert(1960, new Cesium.Color(162 / 255, 251 / 255, 194 / 255));
      colorTable.insert(1900, new Cesium.Color(1, 103 / 255, 103 / 255));
      break;
    case 3:
      colorTable.insert(1960, new Cesium.Color(230 / 255, 198 / 255, 1));
      colorTable.insert(1900, new Cesium.Color(157 / 255, 0, 1));
      break;
    case 4:
      colorTable.insert(2500, new Cesium.Color(210 / 255, 15 / 255, 15 / 255));
      colorTable.insert(2250, new Cesium.Color(221 / 255, 224 / 255, 7 / 255));
      colorTable.insert(2050, new Cesium.Color(20 / 255, 187 / 255, 18 / 255));
      colorTable.insert(1850, new Cesium.Color(0, 161 / 255, 1));
      colorTable.insert(1650, new Cesium.Color(9 / 255, 9 / 255, 212 / 255));
      break;
    case 5:
      colorTable.insert(1960, new Cesium.Color(186 / 255, 1, 229 / 255));
      colorTable.insert(1900, new Cesium.Color(26 / 255, 185 / 255, 156 / 255));
      break;
    case 6:
      colorTable.insert(1960, new Cesium.Color(9 / 255, 9 / 255, 212 / 255));
      colorTable.insert(1930, new Cesium.Color(0, 39 / 255, 148 / 255));
      colorTable.insert(1920, new Cesium.Color(149 / 255, 232 / 255, 249 / 255));
      colorTable.insert(1910, new Cesium.Color(0, 39 / 255, 148 / 255));
      colorTable.insert(1900, new Cesium.Color(149 / 255, 232 / 255, 249 / 255));
      break;
    default:
      break;
  }
  return colorTable;
}

const waterAnalyseOfModel = ()=> {
  isModel.value = true;
  isTerrain.value = false;
}
const waterAnalyseOfTerrain = ()=>{
  isTerrain.value = true;
  isModel.value = false;
}


const colorSelect = ($event)=> {
  const targetEle = document.querySelector(".color-btn");
  if (targetEle) {
    targetEle.remove();
  }
  // console.log("event:",$event)
  const selectEle = proxy.$refs.colorSelect.$el;
  const targetOpt = colorOptions.value.find((opt) => opt.value === +$event);
  // console.log(targetOpt);
  const buttonEle = document.createElement("button");
  buttonEle.className = "color-btn";
  buttonEle.style.background = targetOpt.background;
  selectEle.prepend(buttonEle);

  const floodColorTable = new SuperMap3D.ColorTable();
  const colorTable = setColorTable(floodColorTable, $event);
  const hypFlood = new SuperMap3D.HypsometricSetting();
  hypFlood.DisplayMode = SuperMap3D.HypsometricSettingEnum.DisplayMode.FACE;
  hypFlood._lineColor = new SuperMap3D.Color(1.0, 0.0, 0.0, 1.0);
  hypFlood.MinVisibleValue = currentHeight.value;
  hypFlood.MaxVisibleValue = minValue.value;
  hypFlood.ColorTableMinKey = 1;
  hypFlood.ColorTableMaxKey = 9000;
  hypFlood.ColorTable = colorTable;
  hypFlood.Opacity = 0.8;
  hypFlood.LineInterval = isModel.value ? 10 : 200;

  if (isModel.value && s3mLayer.value) {
    //设置图层分层设色属性
    s3mLayer.value.hypsometricSetting = {
      hypsometricSetting: hypFlood,
      analysisMode: Cesium.HypsometricSettingEnum.AnalysisRegionMode.ARM_ALL
    };
  } else if (isTerrain.value) {
    viewer3d.value.scene.globe.HypsometricSetting = {
      hypsometricSetting: hypFlood,
      analysisMode: Cesium.HypsometricSettingEnum.AnalysisRegionMode.ARM_REGION
    };
  }
}

onBeforeUnmount(()=>{
  clearResult();
  setLayerSelectStatus(viewer3d.value,true)
})
</script>

<style scoped lang="scss">
.analyse-wrap {
  color: #fff;
  background-image: url(@/assets/images/map/tool.png);
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-size: cover;
  box-shadow: 0 0 8px 0 #057595;
}
.tool-tabs {
  display: flex;
  justify-items: center;
  justify-content: space-between;
  background-color: #4f7287;
  span {
    display: inline-block;
    width: 50%;
    padding: 10px 5px;
    text-align: center;
    color: #fff;
    &:hover {
      cursor: pointer;
      background-color: #0f7dff;
      filter: brightness(110%);
      color: #fff;
      transition: background-color 0.25s;
    }
  }
}
.tab-active {
  color: #fff;
  background: linear-gradient(to bottom right, #00baff, #0f7dff);
}
.tool-tab-content {
  padding-top: 20px;
}
:deep(.el-form-item__label) {
  width: 60px !important;
  color: #b5b5b5;
}
.tab-container {
  display: flex;
  padding: 10px;
  flex-direction: column;
  column-count: 2;
}
.height-value {
  margin: 10px;
  label {
    line-height: 40px;
  }
}
.input-value {
  float: right;
  width: 80%;
}
.tool-bottom {
  display: flex;
  justify-content: space-between;
  justify-items: center;
}
.tool-btn {
  margin-top: 20px;
  padding: 10px;
  width: 48%;
  background-color: #0f7dff;
  border-color: #fff;
  text-align: center;
  border-radius: 4px;
  color: #ffffff;
  &:hover {
    cursor: pointer;
    color: #fff;
    filter: brightness(110%) opacity(100%);
    transition: all 0.5s ease-in;
    background: linear-gradient(to bottom right, #00baff, #0f7dff);
  }
  svg {
    margin-right: 10px;
  }
}
.band1 {
  background: -webkit-linear-gradient(left, #95e8f9, #002794) !important;
}
.band2 {
  background: -webkit-linear-gradient(left, #a2fbc2, #ff6767) !important;
}
.band3 {
  background: -webkit-linear-gradient(left, #e6c6ff, #9d00ff) !important;
}
.band4 {
  background: -webkit-linear-gradient(
      left,
      #0909d4,
      #00a1ff 20%,
      #14bb12 40%,
      #dde007 60%,
      #d20f0f
  ) !important;
}
.band5 {
  background: -webkit-linear-gradient(left, #baffe5, #1ab99c) !important;
}
.band6 {
  background: -webkit-linear-gradient(
      left,
      #93f602,
      #2fac01,
      #74cb01,
      #d0ef01,
      #e1ce01,
      #e16a01,
      #f70701
  ) !important;
}
.label {
  display: inline;
  padding: 0.01em 3.5em 0.01em 3.5em;
  font-size: 150%;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25em;
  line-height: 1.1;
}
.color-set {
  margin: 10px;
  line-height: 40px;
}
.color-band {
  position: relative;
  float: right;
  width: 80%;
}
:deep(.color-select) {
  width: 100%;
}
:deep(.color-btn) {
  position: absolute;
  z-index: 99;
  top: 50%;
  left: 45%;
  transform: translate(-50%, -50%);
  height: 70%;
  width: 80%;
  border-radius: 2.5px;
  background: linear-gradient(left, #95e8f9, #002794);
}
</style>
