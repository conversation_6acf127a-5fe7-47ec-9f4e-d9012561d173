define(["exports","./earcut-2.2.1-20c8012f","./Cartesian2-db21342c","./Cartographic-3309dd0d","./Check-7b2a090c","./ComponentDatatype-c140a87d","./when-b60132fc","./Rectangle-dee65d21","./EllipsoidRhumbLine-30b5229b","./GeometryAttribute-c65394ac","./Math-119be1a3","./FeatureDetection-806b12f0","./WebGLConstants-4ae0db90"],(function(a,e,t,i,n,r,s,o,u,h,p,d,l){"use strict";var C={CLOCKWISE:l.WebGLConstants.CW,COUNTER_CLOCKWISE:l.WebGLConstants.CCW,NONE:l.WebGLConstants.NONE,validate:function(a){return a===C.CLOCKWISE||a===C.COUNTER_CLOCKWISE}},c=Object.freeze(C),m=new i.Cartesian3,y=new i.Cartesian3,f={computeArea2D:function(a){for(var e=a.length,t=0,i=e-1,n=0;n<e;i=n++){var r=a[i],s=a[n];t+=r.x*s.y-s.x*r.y}return.5*t},computeWindingOrder2D:function(a){return f.computeArea2D(a)>0?c.COUNTER_CLOCKWISE:c.CLOCKWISE},triangulate:function(a,i){var n=t.Cartesian2.packArray(a);return e.earcut(n,i,2)}},g=new i.Cartesian3,v=new i.Cartesian3,E=new i.Cartesian3,b=new i.Cartesian3,S=new i.Cartesian3,w=new i.Cartesian3,A=new i.Cartesian3;f.computeSubdivision=function(a,e,t,n,o){o=s.defaultValue(o,!1),n=s.defaultValue(n,p.CesiumMath.RADIANS_PER_DEGREE);var u,l=t.slice(0),C=e.length,c=new Array(3*C),m=0;for(u=0;u<C;u++){var y=e[u];c[m++]=y.x,c[m++]=y.y,c[m++]=y.z}for(var f=[],x={},R=a.maximumRadius,L=p.CesiumMath.chordLength(n,R),M=L*L;l.length>0;){var D,G,O=l.pop(),W=l.pop(),T=l.pop(),z=i.Cartesian3.fromArray(c,3*T,g),N=i.Cartesian3.fromArray(c,3*W,v),P=i.Cartesian3.fromArray(c,3*O,E),I=o?z:i.Cartesian3.multiplyByScalar(i.Cartesian3.normalize(z,b),R,b),B=o?N:i.Cartesian3.multiplyByScalar(i.Cartesian3.normalize(N,S),R,S),U=o?P:i.Cartesian3.multiplyByScalar(i.Cartesian3.normalize(P,w),R,w),_=i.Cartesian3.magnitudeSquared(i.Cartesian3.subtract(I,B,A)),K=i.Cartesian3.magnitudeSquared(i.Cartesian3.subtract(B,U,A)),V=i.Cartesian3.magnitudeSquared(i.Cartesian3.subtract(U,I,A)),F=Math.max(_,K,V);F>M?_===F?(u=x[D=Math.min(T,W)+" "+Math.max(T,W)],s.defined(u)||(G=i.Cartesian3.add(z,N,A),i.Cartesian3.multiplyByScalar(G,.5,G),c.push(G.x,G.y,G.z),u=c.length/3-1,x[D]=u),l.push(T,u,O),l.push(u,W,O)):K===F?(u=x[D=Math.min(W,O)+" "+Math.max(W,O)],s.defined(u)||(G=i.Cartesian3.add(N,P,A),i.Cartesian3.multiplyByScalar(G,.5,G),c.push(G.x,G.y,G.z),u=c.length/3-1,x[D]=u),l.push(W,u,T),l.push(u,O,T)):V===F&&(u=x[D=Math.min(O,T)+" "+Math.max(O,T)],s.defined(u)||(G=i.Cartesian3.add(P,z,A),i.Cartesian3.multiplyByScalar(G,.5,G),c.push(G.x,G.y,G.z),u=c.length/3-1,x[D]=u),l.push(O,u,W),l.push(u,T,W)):(f.push(T),f.push(W),f.push(O))}return new h.Geometry({attributes:{position:new h.GeometryAttribute({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:c})},indices:f,primitiveType:d.PrimitiveType.TRIANGLES})};var x=new i.Cartographic,R=new i.Cartographic,L=new i.Cartographic,M=new i.Cartographic;f.computeRhumbLineSubdivision=function(a,e,t,n){n=s.defaultValue(n,p.CesiumMath.RADIANS_PER_DEGREE);var o,l=t.slice(0),C=e.length,c=new Array(3*C),m=0;for(o=0;o<C;o++){var y=e[o];c[m++]=y.x,c[m++]=y.y,c[m++]=y.z}for(var f=[],b={},S=a.maximumRadius,w=p.CesiumMath.chordLength(n,S),D=new u.EllipsoidRhumbLine(void 0,void 0,a),G=new u.EllipsoidRhumbLine(void 0,void 0,a),O=new u.EllipsoidRhumbLine(void 0,void 0,a);l.length>0;){var W=l.pop(),T=l.pop(),z=l.pop(),N=i.Cartesian3.fromArray(c,3*z,g),P=i.Cartesian3.fromArray(c,3*T,v),I=i.Cartesian3.fromArray(c,3*W,E),B=a.cartesianToCartographic(N,x),U=a.cartesianToCartographic(P,R),_=a.cartesianToCartographic(I,L);D.setEndPoints(B,U);var K=D.surfaceDistance;G.setEndPoints(U,_);var V=G.surfaceDistance;O.setEndPoints(_,B);var F,q,k,j,H=O.surfaceDistance,J=Math.max(K,V,H);J>w?K===J?(o=b[F=Math.min(z,T)+" "+Math.max(z,T)],s.defined(o)||(q=D.interpolateUsingFraction(.5,M),k=.5*(B.height+U.height),j=i.Cartesian3.fromRadians(q.longitude,q.latitude,k,a,A),c.push(j.x,j.y,j.z),o=c.length/3-1,b[F]=o),l.push(z,o,W),l.push(o,T,W)):V===J?(o=b[F=Math.min(T,W)+" "+Math.max(T,W)],s.defined(o)||(q=G.interpolateUsingFraction(.5,M),k=.5*(U.height+_.height),j=i.Cartesian3.fromRadians(q.longitude,q.latitude,k,a,A),c.push(j.x,j.y,j.z),o=c.length/3-1,b[F]=o),l.push(T,o,z),l.push(o,W,z)):H===J&&(o=b[F=Math.min(W,z)+" "+Math.max(W,z)],s.defined(o)||(q=O.interpolateUsingFraction(.5,M),k=.5*(_.height+B.height),j=i.Cartesian3.fromRadians(q.longitude,q.latitude,k,a,A),c.push(j.x,j.y,j.z),o=c.length/3-1,b[F]=o),l.push(W,o,T),l.push(o,z,T)):(f.push(z),f.push(T),f.push(W))}return new h.Geometry({attributes:{position:new h.GeometryAttribute({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:c})},indices:f,primitiveType:d.PrimitiveType.TRIANGLES})},f.scaleToGeodeticHeight=function(a,e,t,n){t=s.defaultValue(t,o.Ellipsoid.WGS84);var r=m,u=y;if(e=s.defaultValue(e,0),n=s.defaultValue(n,!0),s.defined(a))for(var h=a.length,p=0;p<h;p+=3)i.Cartesian3.fromArray(a,p,u),n&&(u=t.scaleToGeodeticSurface(u,u)),0!==e&&(r=t.geodeticSurfaceNormal(u,r),i.Cartesian3.multiplyByScalar(r,e,r),i.Cartesian3.add(u,r,u)),a[p]=u.x,a[p+1]=u.y,a[p+2]=u.z;return a},a.PolygonPipeline=f,a.WindingOrder=c}));
