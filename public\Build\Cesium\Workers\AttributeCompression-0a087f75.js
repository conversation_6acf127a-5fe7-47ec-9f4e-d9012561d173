define(["exports","./Cartesian2-db21342c","./Cartographic-3309dd0d","./Check-7b2a090c","./when-b60132fc","./Math-119be1a3"],(function(t,o,e,a,n,r){"use strict";var c=1/256,i={octEncodeInRange:function(t,o,e){if(e.x=t.x/(Math.abs(t.x)+Math.abs(t.y)+Math.abs(t.z)),e.y=t.y/(Math.abs(t.x)+Math.abs(t.y)+Math.abs(t.z)),t.z<0){var a=e.x,n=e.y;e.x=(1-Math.abs(n))*r.CesiumMath.signNotZero(a),e.y=(1-Math.abs(a))*r.CesiumMath.signNotZero(n)}return e.x=r.CesiumMath.toSNorm(e.x,o),e.y=r.CesiumMath.toSNorm(e.y,o),e},octEncode:function(t,o){return i.octEncodeInRange(t,255,o)}},s=new o.Cartesian2,u=new Uint8Array(1);function h(t){return u[0]=t,u[0]}i.octEncodeToCartesian4=function(t,o){return i.octEncodeInRange(t,65535,s),o.x=h(s.x*c),o.y=h(s.x),o.z=h(s.y*c),o.w=h(s.y),o},i.octDecodeInRange=function(t,o,a,n){if(n.x=r.CesiumMath.fromSNorm(t,a),n.y=r.CesiumMath.fromSNorm(o,a),n.z=1-(Math.abs(n.x)+Math.abs(n.y)),n.z<0){var c=n.x;n.x=(1-Math.abs(n.y))*r.CesiumMath.signNotZero(c),n.y=(1-Math.abs(c))*r.CesiumMath.signNotZero(n.y)}return e.Cartesian3.normalize(n,n)},i.octDecode=function(t,o,e){return i.octDecodeInRange(t,o,255,e)},i.octDecodeFromCartesian4=function(t,o){var e=256*t.x+t.y,a=256*t.z+t.w;return i.octDecodeInRange(e,a,65535,o)},i.octPackFloat=function(t){return 256*t.x+t.y};var d=new o.Cartesian2;function f(t){return t>>1^-(1&t)}i.octEncodeFloat=function(t){return i.octEncode(t,d),i.octPackFloat(d)},i.octDecodeFloat=function(t,o){var e=t/256,a=Math.floor(e),n=256*(e-a);return i.octDecode(a,n,o)},i.octPack=function(t,o,e,a){var n=i.octEncodeFloat(t),r=i.octEncodeFloat(o),c=i.octEncode(e,d);return a.x=65536*c.x+n,a.y=65536*c.y+r,a},i.octUnpack=function(t,o,e,a){var n=t.x/65536,r=Math.floor(n),c=65536*(n-r);n=t.y/65536;var s=Math.floor(n),u=65536*(n-s);i.octDecodeFloat(c,o),i.octDecodeFloat(u,e),i.octDecode(r,s,a)},i.compressTextureCoordinates=function(t){return 4096*(4095*t.x|0)+(4095*t.y|0)},i.decompressTextureCoordinates=function(t,o){var e=t/4096,a=Math.floor(e);return o.x=a/4095,o.y=(t-4096*a)/4095,o},i.zigZagDeltaDecode=function(t,o,e){for(var a=t.length,r=0,c=0,i=0,s=0;s<a;++s)r+=f(t[s]),c+=f(o[s]),t[s]=r,o[s]=c,n.defined(e)&&(i+=f(e[s]),e[s]=i)},i.octShortToFloat=function(t){return r.CesiumMath.clamp(3051850947599719e-20*t,-1,1)},i.octShortDecode=function(t,o,n){if(a.Check.defined("result",n),n.x=i.octShortToFloat(t),n.y=i.octShortToFloat(o),n.z=1-(Math.abs(n.x)+Math.abs(n.y)),n.z<0){var c=n.x;n.x=(1-Math.abs(n.y))*r.CesiumMath.signNotZero(c),n.y=(1-Math.abs(c))*r.CesiumMath.signNotZero(n.y)}return e.Cartesian3.normalize(n,n)},t.AttributeCompression=i}));
