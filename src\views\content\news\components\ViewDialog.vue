<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      title="查看详情"
      width="75%"
      :before-close="handleClose"
    >
      <div class="form flx-center">
        <el-form :model="form" label-width="auto">
          <el-form-item label="">
            <div>{{ form.id }}</div>
          </el-form-item>
          <el-form-item label="">
            <div
              style="
                font-size: 30px;
                text-align: center;
                font-weight: 600;
                width: 100%;
              "
            >
              通知公告
            </div>
          </el-form-item>
          <el-form-item label="标题">
            <div>{{ form.title }}</div>
          </el-form-item>
          <el-form-item>
            <div class="flx-justify-between" style="width: 100%">
              <el-form-item label="发布人" class="mr15" style="flex: 1">
                <div style="width: 140px">{{ form.sendName }}</div>
              </el-form-item>
              <el-form-item label="新闻类别" class="mr15" style="flex: 1">
                <div v-if="dictTypeOptions.length > 0" style="width: 140px">
                  {{ findDictName(form.type) }}
                </div>
              </el-form-item>
              <el-form-item label="发布时间" style="flex: 1">
                <div style="width: 140px">{{ form.workDate }}</div>
              </el-form-item>
            </div>
          </el-form-item>
          <el-form-item label="附件">
            <div v-for="item in form.affixes" :key="item.id">
              <el-link type="primary" target="_blank" @click="handleClickRouter(item)">{{
                item.name
              }}</el-link>
            </div>
          </el-form-item>
          <el-form-item label="内容">
            <div v-html="form.content"></div>
          </el-form-item>
          <el-form-item label="查阅人员">
            <div>
              <div class="flx-justify-flex-start">
                <!-- <div class="mr10">查阅概览</div> -->
                <!-- <div class="mr10">
                  <el-tag type="success" effect="dark" class="mr5"></el-tag>
                  <span>已读：{{ isRead }} 人</span>
                </div>
                <div>
                  <el-tag type="info" effect="dark" class="mr5"></el-tag>
                  <span>未读：{{ isUnRead }} 人</span>
                </div> -->
              </div>
              <el-tag
                effect="dark"
                class="mr5"
                :type="item.isRead === '0' ? 'info' : 'success'"
                v-for="item in form.users"
                :key="item.userId"
                >{{ item.name }}</el-tag
              >
            </div>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { getNewsById } from "@/api/content/news.js";
import { getDicts } from "@/api/system/dict/data.js";
import { useRouter } from 'vue-router';
import { base64EncodeUnicode } from '@/utils/base64.js';

const router = useRouter();
const dialogVisible = ref(false);

const props = defineProps({
  data: Object,
});

const form = ref({
  id: "",
  title: "",
  workDate: "",
  type: "",
  sendName: "",
  content: "",
  users: [],
  choiceType: false,
  affixes: [],
});

//文件预览
const handleClickRouter = (item) => {
  window.open(`${import.meta.env.VITE_APP_FILE_PERVIEW_URL}?url=${base64EncodeUnicode(item.url)}`);
  // window.open(`/office?url=${item.url}&id=${item.id}&isEdit=true`);
}

//detail
const isRead = ref(0);
const isUnRead = ref(0);
const getNewsDetail = async () => {
  try {
    const res = await getNewsById(props.data.data.id);
    form.value = res.data || {};

    const { affixes = [], users = [] } = form.value;

    if (affixes.length > 0) {
      form.value.affixes = affixes[0].affixs || [];
      form.value.affixes.forEach((item) => {
        item.url = `${import.meta.env.VITE_APP_FILE_SERVICE_URL}${item.url}`;
      });
    }
    isUnRead.value = Array.isArray(users) ? users.filter(user => user.isRead === "0").length : 0;
    isRead.value = Array.isArray(users) ? users.filter(user => user.isRead === "1").length : 0;
  } catch (error) {
    console.error('Error fetching news detail:', error);
  }
};

// 字典查询
const dictTypeOptions = ref([]);
const getTypeList = () => {
  getDicts("yw_content_news_type").then((res) => {
    dictTypeOptions.value = res.data;
  });
};
const findDictName = (value) => {
  let obj = dictTypeOptions.value.filter((item) => item.dictValue === value);
  return obj[0]?.dictLabel;
};

const emit = defineEmits(["closeViewDialog"]);
const handleClose = () => {
  dialogVisible.value = false;
  emit("closeViewDialog", dialogVisible.value);
};

onMounted(() => {
  console.log("props.data", props.data);
  
  dialogVisible.value = props.data.show;
  getNewsDetail();
  getTypeList();
});
</script>

<style scoped>
  .form {
    background: #dce2f1;
  }
</style>