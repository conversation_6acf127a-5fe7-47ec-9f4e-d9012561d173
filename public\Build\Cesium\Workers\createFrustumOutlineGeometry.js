define(["./when-b60132fc","./buildModuleUrl-9085faaa","./Cartographic-3309dd0d","./Check-7b2a090c","./ComponentDatatype-c140a87d","./FrustumGeometry-d656d5b9","./GeometryAttribute-c65394ac","./GeometryAttributes-252e9929","./FeatureDetection-806b12f0","./Rectangle-dee65d21","./Math-119be1a3","./Event-16a2dfbf","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Cartesian4-3ca25aab","./Plane-a3d8b3d2","./VertexFormat-6446fca0","./Cartesian2-db21342c"],(function(e,t,r,a,n,i,u,o,c,s,p,m,d,h,f,g,_,l){"use strict";function k(t){var a,n,o=t.frustum,c=t.orientation,s=t.origin,p=e.defaultValue(t._drawNearPlane,!0);o instanceof i.PerspectiveFrustum?(a=0,n=i.PerspectiveFrustum.packedLength):o instanceof i.OrthographicFrustum&&(a=1,n=i.OrthographicFrustum.packedLength),this._frustumType=a,this._frustum=o.clone(),this._origin=r.Cartesian3.clone(s),this._orientation=u.Quaternion.clone(c),this._drawNearPlane=p,this._workerName="createFrustumOutlineGeometry",this.packedLength=2+n+r.Cartesian3.packedLength+u.Quaternion.packedLength}k.pack=function(t,a,n){n=e.defaultValue(n,0);var o=t._frustumType,c=t._frustum;return a[n++]=o,0===o?(i.PerspectiveFrustum.pack(c,a,n),n+=i.PerspectiveFrustum.packedLength):(i.OrthographicFrustum.pack(c,a,n),n+=i.OrthographicFrustum.packedLength),r.Cartesian3.pack(t._origin,a,n),n+=r.Cartesian3.packedLength,u.Quaternion.pack(t._orientation,a,n),a[n+=u.Quaternion.packedLength]=t._drawNearPlane?1:0,a};var y=new i.PerspectiveFrustum,v=new i.OrthographicFrustum,F=new u.Quaternion,b=new r.Cartesian3;return k.unpack=function(t,a,n){a=e.defaultValue(a,0);var o,c=t[a++];0===c?(o=i.PerspectiveFrustum.unpack(t,a,y),a+=i.PerspectiveFrustum.packedLength):(o=i.OrthographicFrustum.unpack(t,a,v),a+=i.OrthographicFrustum.packedLength);var s=r.Cartesian3.unpack(t,a,b);a+=r.Cartesian3.packedLength;var p=u.Quaternion.unpack(t,a,F),m=1===t[a+=u.Quaternion.packedLength];if(!e.defined(n))return new k({frustum:o,origin:s,orientation:p,_drawNearPlane:m});var d=c===n._frustumType?n._frustum:void 0;return n._frustum=o.clone(d),n._frustumType=c,n._origin=r.Cartesian3.clone(s,n._origin),n._orientation=u.Quaternion.clone(p,n._orientation),n._drawNearPlane=m,n},k.createGeometry=function(e){var r=e._frustumType,a=e._frustum,s=e._origin,p=e._orientation,m=e._drawNearPlane,d=new Float64Array(24);i.FrustumGeometry._computeNearFarPlanes(s,p,r,a,d);for(var h,f,g=new o.GeometryAttributes({position:new u.GeometryAttribute({componentDatatype:n.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:d})}),_=m?2:1,l=new Uint16Array(8*(_+1)),k=m?0:1;k<2;++k)f=4*k,l[h=m?8*k:0]=f,l[h+1]=f+1,l[h+2]=f+1,l[h+3]=f+2,l[h+4]=f+2,l[h+5]=f+3,l[h+6]=f+3,l[h+7]=f;for(k=0;k<2;++k)f=4*k,l[h=8*(_+k)]=f,l[h+1]=f+4,l[h+2]=f+1,l[h+3]=f+5,l[h+4]=f+2,l[h+5]=f+6,l[h+6]=f+3,l[h+7]=f+7;return new u.Geometry({attributes:g,indices:l,primitiveType:c.PrimitiveType.LINES,boundingSphere:t.BoundingSphere.fromVertices(d)})},function(t,r){return e.defined(r)&&(t=k.unpack(t,r)),k.createGeometry(t)}}));
