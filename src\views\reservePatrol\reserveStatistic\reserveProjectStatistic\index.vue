<template>
  <div class="reserve-project-statistic">
    <!-- 查询条件 -->
    <!-- <el-card
      class="search-card"
      shadow="never"
    >
      <el-form
        :model="queryParams"
        ref="queryForm" :inline="true" label-width="80px"
      >
        <el-form-item
          label="年份"
          prop="nf"
        >
          <el-date-picker
            v-model="queryParams.nf"
            type="year"
            placeholder="请选择年份"
            format="YYYY"
            value-format="YYYY"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="Search" @click="handleQuery"
          >查询</el-button>
          <el-button
            icon="Refresh"
            @click="resetQuery"
          >重置</el-button>
          <el-button
            type="success"
            icon="Download" @click="handleExport"
          >导出</el-button>
        </el-form-item>
      </el-form>
    </el-card> -->

    <!-- 统计卡片 -->
    <el-row
      :gutter="20"
      class="statistics-cards"
    >
      <el-col :span="12">
        <el-card class="statistic-card">
          <div class="statistic-item">
            <div class="statistic-value">{{ totalProjects }}</div>
            <div class="statistic-label">项目数量（个）</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="statistic-card">
          <div class="statistic-item">
            <div class="statistic-value">{{ totalArea }}</div>
            <div class="statistic-label">土地总面积(公顷)</div>
          </div>
        </el-card>
      </el-col>

    </el-row>

    <!-- 图表展示 -->
    <el-row
      :gutter="20"
      class="chart-container"
    >
      <el-col :span="12">
        <el-card
          class="chart-card"
          shadow="never"
        >
          <template #header>
            <div class="card-header">
              <span>年度项目数量统计</span>
            </div>
          </template>
          <div
            ref="projectChart"
            class="chart" style="height: 400px;"
          />
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card
          class="chart-card"
          shadow="never"
        >
          <template #header>
            <div class="card-header">
              <span>年度地块总面积统计</span>
            </div>
          </template>
          <div
            ref="areaChart"
            class="chart" style="height: 400px;"
          />
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据表格 -->
    <el-card
      class="table-card"
      shadow="never"
    >
      <template #header>
        <div class="card-header">
          <span>详细数据</span>
        </div>
      </template>
      <el-table
        v-loading="loading"
        :data="statisticList"
        border
        stripe
        style="width: 100%"
      >
        <el-table-column
          label="序号"
          type="index" width="60" align="center"
        />
        <el-table-column
          label="年份"
          prop="nf" align="center" width="120"
        >
          <template #default="scope">
            <span>{{ scope.row.nf }}年</span>
          </template>
        </el-table-column>
        <el-table-column
          label="数量"
          prop="num" align="center" width="120"
        >
          <template #default="scope">
            <el-tag type="primary">{{ scope.row.num }}个</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="地块总面积(公顷)"
          prop="dkzmj" align="center"
        >
          <template #default="scope">
            <span class="area-text">{{ formatNumber(scope.row.dkzmj) }}</span>
          </template>
        </el-table-column>

        <el-table-column
          label="面积占比"
          align="center" width="120"
        >
          <template #default="scope">
            <el-progress
              :percentage="getPercentage(scope.row.yzdzmj || scope.row.dkzmj)"
              :stroke-width="8"
              :show-text="true"
              :format="() => getPercentage(scope.row.yzdzmj || scope.row.dkzmj) + '%'"
            />
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup name="reserveProjectStatistic">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as echarts from 'echarts'
import { getReserveProjectStatistic } from '@/api/reserve/reserveStatistic.js'

/**
 * CHN：储备项目统计数据
 */

// 响应式数据
const loading = ref(false)
const statisticList = ref([])
const projectChart = ref(null)
const areaChart = ref(null)
const queryForm = ref(null)

// 查询参数
const queryParams = reactive({
  nf: null // 年份
})

// 计算属性
const totalProjects = computed(() => {
  return statisticList.value.reduce((sum, item) => sum + item.num, 0)
})

const totalArea = computed(() => {
  const total = statisticList.value.reduce((sum, item) => sum + parseFloat(item.dkzmj), 0)
  return formatNumber(total.toFixed(2))
})

const averageArea = computed(() => {
  if (totalProjects.value === 0) return '0'
  const avg = statisticList.value.reduce((sum, item) => sum + parseFloat(item.dkzmj), 0) / totalProjects.value
  return formatNumber(avg.toFixed(2))
})

// 获取统计数据
const getStatisticList = async () => {
  loading.value = true
  try {
    const response = await getReserveProjectStatistic(queryParams)

    if (response.code === 200) {
      statisticList.value = response.data || []
      // 更新图表
      nextTick(() => {
        initCharts()
      })
    } else {
      ElMessage.error(response.msg || '获取数据失败')
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    ElMessage.error('获取数据失败')

    // 开发环境下使用模拟数据
    if (process.env.NODE_ENV === 'development') {
      statisticList.value = [
        { yzdzmj: "3076.35", num: 1, nf: "2024" },
        { yzdzmj: "823611.87", num: 8, nf: "2025" },
        { yzdzmj: "456789.12", num: 5, nf: "2023" },
        { yzdzmj: "234567.89", num: 3, nf: "2022" }
      ].filter(item => !queryParams.nf || item.nf === queryParams.nf)

      nextTick(() => {
        initCharts()
      })
    }
  } finally {
    loading.value = false
  }
}

// 查询
const handleQuery = () => {
  getStatisticList()
}

// 重置
const resetQuery = () => {
  queryParams.nf = null
  getStatisticList()
}

// 导出
const handleExport = () => {
  if (statisticList.value.length === 0) {
    ElMessage.warning('暂无数据可导出')
    return
  }

  ElMessageBox.confirm('确认导出当前数据吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const response = await getReserveProjectStatistic(queryParams)

      // 创建下载链接
      const blob = new Blob([response], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `储备项目统计数据_${new Date().getTime()}.xlsx`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      ElMessage.success('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      ElMessage.error('导出失败')
    }
  }).catch(() => {
    ElMessage.info('已取消导出')
  })
}

// 格式化数字
const formatNumber = (num) => {
  if (!num) return '0'
  return parseFloat(num).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

// 计算面积占比
const getPercentage = (area) => {
  const total = statisticList.value.reduce((sum, item) => sum + parseFloat(item.yzdzmj || item.dkzmj || 0), 0)
  if (total === 0) return 0
  const percentage = (parseFloat(area || 0) / total) * 100
  // 对于进度条组件，如果百分比小于1%，至少显示1%，否则进度条看不见
  return Math.max(Math.round(percentage * 100) / 100, percentage > 0 ? 0.01 : 0)
}

// 初始化图表
const initCharts = () => {
  initProjectChart()
  initAreaChart()
}

// 初始化数量图表
const initProjectChart = () => {
  if (!projectChart.value) return

  const chart = echarts.init(projectChart.value)

  const years = statisticList.value.map(item => item.nf + '年')
  const projectNums = statisticList.value.map(item => item.num)

  const option = {
    title: {
      text: '年度数量',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: '{b}: {c}个'
    },
    xAxis: {
      type: 'category',
      data: years,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '数量(个)',
      minInterval: 1
    },
    series: [{
      data: projectNums,
      type: 'bar',
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#83bff6' },
          { offset: 0.5, color: '#188df0' },
          { offset: 1, color: '#188df0' }
        ])
      },
      emphasis: {
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#2378f7' },
            { offset: 0.7, color: '#2378f7' },
            { offset: 1, color: '#83bff6' }
          ])
        }
      }
    }]
  }

  chart.setOption(option)

  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

// 初始化面积图表
const initAreaChart = () => {
  if (!areaChart.value) return

  const chart = echarts.init(areaChart.value)

  const years = statisticList.value.map(item => item.nf + '年')
  const areas = statisticList.value.map(item => parseFloat(item.dkzmj ))

  const option = {
    title: {
      text: '年度地块总面积',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: '{b}: {c}公顷'
    },
    xAxis: {
      type: 'category',
      data: years,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '面积(亩)',
      axisLabel: {
        formatter: function(value) {
          if (value >= 10000) {
            return (value / 10000).toFixed(1) + '万'
          }
          return value
        }
      }
    },
    series: [{
      data: areas,
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 8,
      lineStyle: {
        color: '#67C23A',
        width: 3
      },
      itemStyle: {
        color: '#67C23A'
      },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(103, 194, 58, 0.3)' },
          { offset: 1, color: 'rgba(103, 194, 58, 0.1)' }
        ])
      }
    }]
  }

  chart.setOption(option)

  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize()
  })
}

// 生命周期
onMounted(() => {
  getStatisticList()
})
</script>

<style scoped lang="scss">
.reserve-project-statistic {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 84px);

  .search-card {
    margin-bottom: 20px;

    .el-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }

  .statistics-cards {
    margin-bottom: 20px;

    .statistic-card {
      height: 120px;

      .statistic-item {
        text-align: center;
        padding: 20px 0;

        .statistic-value {
          font-size: 32px;
          font-weight: bold;
          color: #409EFF;
          margin-bottom: 8px;
        }

        .statistic-label {
          font-size: 14px;
          color: #666;
        }
      }
    }
  }

  .chart-container {
    margin-bottom: 20px;

    .chart-card {
      .card-header {
        font-weight: bold;
        color: #303133;
      }

      .chart {
        width: 100%;
      }
    }
  }

  .table-card {
    .card-header {
      font-weight: bold;
      color: #303133;
    }

    .area-text {
      font-weight: 600;
      color: #67C23A;
    }

    .el-table {
      .el-table__header {
        th {
          background-color: #fafafa;
          color: #606266;
          font-weight: 600;
        }
      }

      .el-table__body {
        tr:hover {
          background-color: #f5f7fa;
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 10px;

    .statistics-cards {
      .el-col {
        margin-bottom: 10px;
      }
    }

    .chart-container {
      .el-col {
        margin-bottom: 20px;
      }
    }

    .el-form--inline .el-form-item {
      display: block;
      margin-bottom: 10px;
    }
  }
}

// 全局样式调整
:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table .el-table__cell) {
  padding: 12px 0;
}

:deep(.el-progress-bar__outer) {
  border-radius: 4px;
}

:deep(.el-progress-bar__inner) {
  border-radius: 4px;
}
</style>
