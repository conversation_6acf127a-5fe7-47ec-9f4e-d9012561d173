<template>
  <div class="main-content">
    <div v-if="!showDetail">
      <transition>
        <div
          v-show="showSearch"
          class="mb-10"
        >
          <el-card
            shadow="hover"
            class="search"
          >
            <el-form
              ref="queryFormRef"
              :model="queryParams" :inline="true"
              class="query-form"
            >
              <el-form-item
                label="土地用途"
                prop="tdyt"
              >
                <el-input
                  v-model="queryParams.tdyt"
                  placeholder="请输入土地用途"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item
                label="年份"
                prop="dataYear"
              >
                <el-input
                  class="fixed-clearable"
                  v-model="queryParams.dataYear"
                  placeholder="请输入数据年份"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item
                label="批准文号"
                prop="pzwh"
              >
                <el-input
                  v-model="queryParams.pzwh"
                  placeholder="请输入批准文号"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  icon="Search" @click="handleQuery"
                >搜索
                </el-button>
                <el-button
                  icon="Refresh"
                  @click="resetQuery"
                >重置</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </div>
      </transition>
      <el-row
        :gutter="20"
        class="mb8"
      >
        <el-col :span="1.5">
          <el-button
            type="success" plain icon="Upload"
            @click="handleImport"
          >
            批量导入（Shape）
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
          >导出
          </el-button>
        </el-col>
        <right-toolbar
          v-model:showSearch="showSearch"
          @queryTable="getList"
        />
      </el-row>
      <el-card class="result-wrap">
        <el-table
          v-loading="loading"
          :data="landList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            label="土地用途"
            align="center" prop="tdyt"
          />
          <el-table-column
            label="年份"
            align="center" prop="dataYear"
          />
          <el-table-column
            label="总面积"
            align="center" prop="zmj"
          />
          <el-table-column
            label="批准文号"
            align="center"
            prop="pzwh"
          />
          <el-table-column
            label="批准机关"
            align="center" prop="pzjg"
          />
          <el-table-column
            label="批准日期"
            align="center" prop="pzrq"
          />
          <el-table-column
            label="上报人"
            align="center"
            prop="sbr"
          />
          <el-table-column
            label="上报时间"
            align="center" prop="sbsj"
          />
          <el-table-column
            label="操作"
            align="center"
            width="200"
          >
            <template #default="scope">
              <el-button
                plain
                type="primary" size="small" @click="handlePreview(scope.row)"
              >查看</el-button>
              <el-button
                plain
                type="danger" size="small" @click="handleDelete(scope.row)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>
    <div
      v-else
      class="content"
    >
      <!-- 添加或修改储备项目对话框 -->
      <div class="add-header-title">
        <div class="add-title">{{xmxiTitle}}</div>
        <div
          class="add-title-return"
          @click="cancel"
        >
          <img
            src="@/assets/images/img-return.png"
            class="listimage"
          >
          <div class="return">返回列表</div>
        </div>
      </div>
      <div class="add-content">
        <!--项目基本信息-->
        <div>
          <div class="content-project">
            <img src="@/assets/images/left.png" >
            <p>项目基本信息</p>
          </div>
          <el-form
            ref="landFormRef"
            :model="form"
            :rules="rules"
            label-width="180px"
            class="information"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item
                  label="项目名称"
                  prop="xmmc"
                >
                  <el-input
                    v-model="form.xmmc"
                    placeholder="请输入项目名称"
                  />
                </el-form-item>
                <el-form-item
                  label="项目编号"
                  prop="xmbh"
                >
                  <el-input
                    v-model="form.xmbh"
                    placeholder="请输入项目编号"
                  />
                </el-form-item>
                <el-form-item
                  label="项目总面积(公顷)"
                  prop="xmzmj"
                >
                  <el-input
                    v-model="form.xmzmj"
                    placeholder="请输入项目总面积(公顷)"
                  >
                    <template #append>公顷</template>
                  </el-input>
                </el-form-item>

                <el-form-item
                  label="项目坐落"
                  prop="xmzl"
                >
                  <el-input
                    v-model="form.xmzl"
                    placeholder="请输入项目坐落"
                  />
                </el-form-item>
                <el-form-item
                  label="收储面积"
                  prop="scmj"
                >
                  <el-input
                    v-model="form.scmj"
                    placeholder="请输入收储面积"
                  >
                    <template #append>公顷</template>
                  </el-input>
                </el-form-item>
                <el-form-item
                  label="项目涉及地块数"
                  prop="xmsjdks"
                >
                  <el-input
                    v-model="form.xmsjdks"
                    placeholder="请输入项目涉及地块数"
                  >
                    <template #append>&nbsp&nbsp&nbsp块</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="行政区"
                  prop="xzqdm"
                >
                  <el-input
                    v-model="form.xzqdm"
                    placeholder="请输入行政区代码"
                    style="width: 100%"
                  />
                </el-form-item>
                <el-form-item
                  label="收储日期"
                  prop="scrq"
                >
                  <el-date-picker
                    clearable
                    v-model="form.scrq"
                    type="datetime"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    placeholder="请选择收储日期"
                    style="width: 100%"
                  />
                </el-form-item>
                <el-form-item
                  label="批准日期"
                  prop="pzrq"
                >
                  <el-date-picker
                    clearable
                    v-model="form.pzrq"
                    type="datetime"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    placeholder="请选择批准日期"
                    style="width: 100%"
                  />
                </el-form-item>
                <el-form-item
                  label="批准文号"
                  prop="pzwh"
                >
                  <el-input
                    v-model="form.pzwh"
                    placeholder="请输入批准文号"
                  />
                </el-form-item>
                <el-form-item
                  label="批准文件名"
                  prop="pzwjm"
                >
                  <el-input
                    v-model="form.pzwjm"
                    placeholder="请输入批准文件名"
                  />
                </el-form-item>
                <el-form-item
                  label="是否跨机构联动收储"
                  prop="sfkjgldsc"
                >
                  <el-input
                    v-model="form.sfkjgldsc"
                    placeholder="请输入是否跨机构联动收储"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="备注"
                  prop="bz"
                >
                  <el-input
                    v-model="form.bz"
                    :rows="2"
                    type="textarea"
                    placeholder="请输入备注"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div>
          <div class="content-title">
            <div class="content-project">
              <img src="@/assets/images/left.png" >
              <p>地块信息</p>
            </div>
            <div>
              <el-button
                type="primary"
                plain
              >
                <el-icon>
                  <Upload />
                </el-icon>
                导入
              </el-button>
            </div>
          </div>
          <list-editer
            :table-data="tableDataDK"
            :columns="columnsDK"
          />
        </div>

      </div>
      <div class="footer-button">
        <el-button
          :loading="buttonLoading"
          type="primary"
          @click="submitForm"
          plain
        >
          <el-icon>
            <CircleCheckFilled />
          </el-icon>
          提交项目信息
        </el-button>
        <el-button
          @click="cancel"
          type="info" plain
        >
          <el-icon>
            <CircleCloseFilled />
          </el-icon>
          取 消
        </el-button>
      </div>
    </div>
    <!--  项目模板数据导入对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="导入项目范围数据"
      width="500"
      :before-close="handleDialogClose"
    >
      <el-upload
        ref="projectUploadRef"
        class="upload-demo"
        drag
        multiple
        accept=".zip"
        :action="uploadModelActionUrl"
        :headers="headers"
        :before-upload="beforeUploadProject"
        :on-success="handleProjectSuccess"
        :on-error="handleProjectError"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          拖动文件 或者<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            请上传 Shapefile 【.zip】 压缩文件 <span class="upload-tip">（坐标系：CGCS2000）</span>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="submitProjectModel"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="LandSupply">
/**
 * CHN：土地供应数据
 */

import {
  listTdgy,
  getTdgy,
  delTdgy,
  addTdgy,
  updateTdgy
} from "@/api/landSupply/landSuplly.js";
import { useRouter } from "vue-router"
import { getToken } from "@/utils/auth.js"
import { ElMessage } from "element-plus"

const { proxy } = getCurrentInstance();
const router = useRouter()

const showDetail = ref(false);
const landList = ref([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
//标题变量
const xmxiTitle = ref('项目信息录入')

const headers = {
  Authorization: "Bearer " + getToken(),
  clientid: import.meta.env.VITE_APP_CLIENT_ID
}
// 批量上传征拆项目范围数据接口
const uploadModelActionUrl = import.meta.env.VITE_APP_BASE_API + "/patrol/relProject/importShape"
const dialogVisible = ref(false)
const projectUploadRef = ref()

const queryFormRef = ref();
const landFormRef = ref();

const initFormData = {
  id: undefined,
  "cogeometry": "",
  "importOid": 0,
  "valid": "",
  "projectId": 0,
  "dataYear": 2022,
  "dataMonth": 0,
  "dataDay": 0,
  "xzqdm": "",
  "nf": "",
  "zdszx": "",
  "zdbh": "",
  "zmj": 0,
  "tdyt": "",
  "ydjdsj": "",
  "pzwh": "",
  "pzrq": "",
  "pzjg": "",
  "sbr": "",
  "sbsj": "",
  "sjjdsj": ""
};
const data = reactive({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    "dataYear": 2022,
    "xzqdm": "",
    "nf": "",
    "zdszx": "",
    "zdbh": "",
    "zmj": 0,
    "tdyt": "",
    "ydjdsj": "",
    "pzwh": "",
    "pzrq": "",
    "pzjg": "",
    "sbr": ""
  },
  rules: {

  }
});

const { queryParams, form, rules } = toRefs(data);

/**
 * 项目Shape数据批量导入
 */
const handleImport = ()=>{
  dialogVisible.value = true
}


/** 查询储备项目列表 */
const getList = async () => {
  loading.value = true;
  const res = await listTdgy(queryParams.value);

  const newRes = res.rows.map(item=>{
    item.xmzmj = Number(item.xmzmj).toFixed(2);
    item.scmj = Number(item.scmj).toFixed(2);
    return item
  })
  landList.value = newRes;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  showDetail.value = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  landFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection) => {
  // ids.value = selection.map((item) => item.id);
  // single.value = selection.length != 1;
  // multiple.value = !selection.length;
};


/** 提交按钮 */
const submitForm = () => {
  landFormRef.value?.validate(async (valid) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateTdgy(form.value).finally(
          () => (buttonLoading.value = false)
        );
      } else {
        await addTdgy(form.value).finally(
          () => (buttonLoading.value = false)
        );
      }
      proxy?.$modal.msgSuccess("操作成功");
      showDetail.value = false;
      await getList();
    }
  });
};


/**
 * 项目信息查看
 */
const handlePreview = (row)=>{
  router.push({ path: "/planStorage/planStorageDetail",query: { id: row.id } })
}


/** 删除按钮操作 */
const handleDelete = async (row) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal
    .confirm('是否确认删除储备项目为"' + _ids + '"的数据项？')
    .finally(() => (loading.value = false));
  await delTdgy(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
};



const submitProjectModel = ()=>{
  dialogVisible.value = false
}
// 关闭导入对话框
const handleDialogClose = ()=>{
  projectUploadRef.value.clearFiles()
  dialogVisible.value = false
}


onMounted(() => {
  getList();
});
</script>
<style lang="scss" scoped>
.main-content {
  padding: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}

.result-wrap {
  margin-top: 10px;
}

.content {
  border: 1px solid rgb(233, 233, 233);
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.add-header-title {
  padding: 10px;
  display: flex;
  justify-content: space-between;
  height: 50px;
  background-color: rgb(222, 239, 255);
  box-sizing: border-box;
  border-bottom: 1px solid rgb(233, 233, 233);
  font-weight: 700;
  font-size: 14px;
  line-height: 28px;
}

.add-title-return {
  display: flex;
  align-content: center;
  color: rgb(32, 119, 255);
  cursor: pointer;
  font-weight: normal;
}

.add-content {
  padding: 0px 10px;
}

.content-title {
  line-height: 70px;
  font-weight: bold;
  display: flex;
  justify-content: space-between;
}

.footer-button {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  border-top: 1px solid #e0e0e0;
  padding: 20px 0;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  button {
    height: 40px;
  }
}
.listimage {
  height: 18px;
  width: 18px;
  margin-top: 5px;
}
.return {
  padding-left: 6px;
  font-size: 14px;
}
.information {
  // border: 1px solid #dcdfe6;
  padding: 10px;
}
.content-project {
  display: flex;
  align-items: center;
  p {
    color: #333333;
    font-weight: bold;
    margin-left: 8px;
  }
}

.el-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
.pagination-container {
  margin: 10px 0 30px !important;
}
.el-card__header {
  border: none;
}
.mb8 {
  margin: 20px 0 9px 0 !important;
}
.upload-tip{
  color:red;
}
:deep(.search){
  margin: 8px 0 3px 0;
}
.el-form--inline .el-form-item {
  margin-bottom: 1px;
}
:deep(.el-dialog .el-dialog__header) {
  border-bottom: 1px solid #2077ff !important;
  background: #2077ff !important;
}
:deep(.el-dialog__title) {
  color: #ffffff;
}
:deep(.el-dialog__headerbtn) {
  height: 55px;
}
:deep(.el-dialog__headerbtn .el-dialog__close) {
  color: #ffffff;
}
:deep(.el-dialog .el-dialog__footer) {
  border-top: 1px solid #ececec;
}
.query-form{
  /**防止输入框出现清除按钮时输入框产生宽度变化**/
  :deep(.el-input--suffix) {
    // 固定宽度
    width: 200px !important;
  }
}
</style>
