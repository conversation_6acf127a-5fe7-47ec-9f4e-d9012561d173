import request from '@/utils/request'

/**
 * postgis 数据查询接口
 * return 单条记录
 */
export function queryByProperty(layerProperty) {
  return request({
    url: '/map/geo/geoMap',
    method: 'get',
    params: layerProperty
  })
}

/**
 * postgis 数据查询接口
 * return 分页查询数据列表
 */
export function queryListByProperty(layerProperties) {
  return request({
    url: '/map/geo/list',
    method: 'get',
    params: layerProperties
  })
}
/**
 * postgis 数据查询接口
 * @tableName：空间数据表名
 * return 整个图层数据列表
 */
export function queryAllByTableName(tableName){
  return request({
    url: "/map/geo/geoList",
    method: 'get',
    params: {
      tableName
    }
  })
}

/**
 * postgis 数据查询接口之村级行政区查询
 * @tableName：空间数据表名
 * return 整个图层数据列表
 */
export function queryCountryRegion(layerProperties){
  return request({
    url: "/map/cjxzq/getData",
    method: 'get',
    params: layerProperties
  })
}

/**
 * postgis 数据查询接口之乡级行政区查询
 * @tableName：空间数据表名
 * return 整个图层数据列表
 */
export function queryTownRegion(layerProperties){
  return request({
    url: "/map/xzjxzq/getData",
    method: 'get',
    params: layerProperties
  })
}
/**
 * postgis 数据查询接口之县级行政区查询
 * @tableName：空间数据表名
 * return 整个图层数据列表
 */
export function queryCountyRegion(layerProperties){
  return request({
    url: "/map/xjxzq/getData",
    method: 'get',
    params: layerProperties
  })
}

/**
 * postgis 属性查询之几何查询：点选数据查询接口
 * @tableName：空间数据表名
 * return 整个图层数据列表
 */
export function pointQuery(layerProperties){
  return request({
    url: "/map/geo/pointArea",
    method: 'get',
    params: layerProperties
  })
}

/**
 * postgis 属性查询之关联查询：通过项目id查找导入空间表，import_oid查找与属性相关联的空间数据
 * @linkProperty：查询关联属性
 * {"projectId":'',"import_oid":""}
 * return 整个图层数据列表
 */
export function queryByLinkedId(linkProperty){
  return request({
    url: "/map/geo/LinkedGeo",
    method: 'get',
    params: linkProperty
  })
}

/**
 * postgis 缓冲区分析接口
 * @returns {AxiosPromise}
 */
export function bufferAnalyse(analyseParams){
  return request({
    url: '/map/geo/bufferGeom',
    method: 'post',
    data: analyseParams
  })
}

/**
 * postgis 表字段查询接口
 * @param tableName：空间表名
 * @returns {AxiosPromise}
 */
export function getCommentByTableName(tableName){
  return request({
    url: "/map/allTable/allColumn",
    method: 'get',
    params: {
      tableName
    }
  })
}
