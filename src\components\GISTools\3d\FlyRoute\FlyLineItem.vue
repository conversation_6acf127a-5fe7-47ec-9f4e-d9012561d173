<template>
  <div class="fly-line-item" @click="onClick">
    <el-image :src="roamingOption.img" fit="cover">
      <template v-slot:placeholder>
        <div  class="image-slot">
          <div class="placeholder" />
        </div>
      </template>
      <template v-slot:error>
        <div  class="image-slot">
          <div class="placeholder" />
        </div>
      </template>
    </el-image>
    <span>{{ roamingOption.name }}</span>
  </div>
</template>

<script setup>

const { proxy } = getCurrentInstance()

defineProps({
  roamingOption: {
    type: Object
  }
})

const onClick = ()=> {
  proxy.$emit("onItemClick");
}

</script>

<style scoped lang="scss">
.fly-line-item {
  display: flex;
  flex-direction: column;
  cursor: pointer;
  width: 130px;

  .el-image {
    height: 86px;

    .image-slot {
      height: 100%;

      .placeholder {
        height: 100%;
        background-color: #a9a9a9;
      }
    }
  }
  span {
    font-size: 14px;
    color: white;
    text-align: center;
    background-color: #0f7dff;
    padding: 4px 0;
  }
}
</style>
