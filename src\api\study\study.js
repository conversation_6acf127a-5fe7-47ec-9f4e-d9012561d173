/**
 * 学习栏
 */

import request from '@/utils/request'

// 查询新闻通知列表
export function getStudyList(query) {
    return request({
      url: '/document/study/list',
      method: 'get',
      params: query
    })
  }

/** 
 * 新增学习栏
 * @param {object} params 学习栏业务对象 doc_study
 * @param {number} params.createDept 创建部门
 * @param {number} params.createBy 创建者
 * @param {object} params.createTime 创建时间
 * @param {number} params.updateBy 更新者
 * @param {object} params.updateTime 更新时间
 * @param {object} params.params 请求参数
 * @param {string} params.id id
 * @param {array} params.affixList 附件
 * @param {string} params.choiceType 选择类型(0:全部;1:部分)
 * @param {array} params.userIds 接收人
 * @param {string} params.title 学习名称
 * @param {object} params.sendDate 发布时间
 * @param {string} params.content 学习内容
 * @param {string} params.remark 备注
 * @param {string} params.startDay 开始日期
 * @param {string} params.endDay 结束日期
 * @returns
 */
export function addStudy(data) {
  return request({
    url: '/document/study',
    method: 'post',
    data: data
  })
}

/** 
 * 修改学习栏
 * @param {object} params 学习栏业务对象 doc_study
 * @param {number} params.createDept 创建部门
 * @param {number} params.createBy 创建者
 * @param {object} params.createTime 创建时间
 * @param {number} params.updateBy 更新者
 * @param {object} params.updateTime 更新时间
 * @param {object} params.params 请求参数
 * @param {string} params.id id
 * @param {array} params.affixList 附件
 * @param {string} params.choiceType 选择类型(0:全部;1:部分)
 * @param {array} params.userIds 接收人
 * @param {string} params.title 学习名称
 * @param {object} params.sendDate 发布时间
 * @param {string} params.content 学习内容
 * @param {string} params.remark 备注
 * @param {string} params.startDay 开始日期
 * @param {string} params.endDay 结束日期
 * @returns
 */
export function editStudy(params) {
  return request.put(`/document/study`, params);
}

/** 
 * 删除学习栏
 * @param {string} id 主键串
  * @returns
 */
export function delStudyById(id) {
    return request({
      url: '/document/study/' + id,
      method: 'delete'
    })
  }

/** 
 * 获取学习栏详细信息
 * @param {string} id 主键
  * @returns
 */
export function getStudyById(id) {
  return request.get(`/document/study/${id}`);
}