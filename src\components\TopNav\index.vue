<template>
  <el-menu
    :default-active="activeMenu"
    mode="horizontal"
    @select="handleSelect"
    :ellipsis="false"
  >
    <template v-for="(item, index) in topMenus">
      <el-menu-item
        :style="{ '--theme': theme }"
        :index="item.path"
        :key="index"
        v-if="item.meta && !item.meta.topHide"
      >
        <el-badge
          v-if="item.meta.title === '消息管理' && unReadStore.unreadNumber"
          :value="unReadStore.unreadNumber"
          :offset="[2, 5]"
          style="height: 20px; height: 20px"
        >
          <svg-icon
            v-if="item.meta && item.meta.icon && item.meta.icon !== '#'"
            :icon-class="item.meta.icon"
          />
        </el-badge>
        <div
          v-else
          style="height: 20px; height: 20px"
        >
          <svg-icon
            v-if="item.meta && item.meta.icon && item.meta.icon !== '#'"
            :icon-class="item.meta.icon"
          />
        </div>
        {{ item.meta.title }}
      </el-menu-item>
    </template>

    <!-- 顶部菜单超出数量折叠 -->
    <!-- <el-sub-menu :style="{'--theme': theme}" index="more" v-if="topMenus.length > visibleNumber">
      <template #title>更多菜单</template>
      <template v-for="(item, index) in topMenus">
        <el-menu-item
          :index="item.path"
          :key="index"
          v-if="index >= visibleNumber">
        <svg-icon
          v-if="item.meta && item.meta.icon && item.meta.icon !== '#'"
          :icon-class="item.meta.icon"/>
        {{ item.meta.title }}
        </el-menu-item>
      </template>
    </el-sub-menu> -->
  </el-menu>
</template>

<script setup>
import { constantRoutes } from "@/router";
import { isHttp } from "@/utils/validate";
import useAppStore from "@/store/modules/app";
import useSettingsStore from "@/store/modules/settings";
import usePermissionStore from "@/store/modules/permission";
import useUserUnRead from "@/store/modules/unReadNum";
//消息未读
import { initSSE } from "@/utils/sse";
const unReadStore = useUserUnRead();

// 顶部栏初始数
const visibleNumber = ref(5);
// 当前激活菜单的 index
const currentIndex = ref(null);
// 隐藏侧边栏路由
const hideList = ["/index", "/user/profile"];

const appStore = useAppStore();
const settingsStore = useSettingsStore();
const permissionStore = usePermissionStore();
const route = useRoute();
const router = useRouter();
const topbarRouters = computed(() => permissionStore.topbarRouters);

// 主题颜色
const theme = computed(() => settingsStore.theme);
// 所有的路由信息
const routers = computed(() => permissionStore.topbarRouters);

// 顶部显示菜单
const topMenus = computed(() => {
  let topMenus = [];
  topbarRouters.value.map((menu) => {
    if (menu.hidden !== true) {
      // 兼容顶部栏一级菜单内部跳转
      if (menu.path === "/") {
        topMenus.push(menu.children[0]);
      } else {
        topMenus.push(menu);
      }
    }
  });
  let constant = [];
  constantRoutes.map((item) => {
    if (!item.children) return;
    constant.push(...item.children);
  });
  return constant.concat(topMenus);
});

// console.log("topMenus", topMenus);

// 设置子路由
const childrenMenus = computed(() => {
  let childrenMenus = [];
  routers.value.map((router) => {
    for (let item in router.children) {
      if (router.children[item].parentPath === undefined) {
        if (router.path === "/") {
          router.children[item].path = "/" + router.children[item].path;
        } else {
          if (!isHttp(router.children[item].path)) {
            router.children[item].path = router.path + "/" + router.children[item].path;
          }
        }
        router.children[item].parentPath = router.path;
      }
      childrenMenus.push(router.children[item]);
    }
  });
  return constantRoutes.concat(childrenMenus);
});

// 默认激活的菜单
const activeMenu = computed(() => {
  const path = route.path;
  let activePath = path;
  if (
    path !== undefined &&
    path.lastIndexOf("/") > 0 &&
    hideList.indexOf(path) === -1
  ) {
    const tmpPath = path.substring(1, path.length);
    activePath = "/" + tmpPath.substring(0, tmpPath.indexOf("/"));
    if (!route.meta.link) {
      appStore.toggleSideBarHide(false);
    }
  } else if (!route.children) {
    activePath = path;
    appStore.toggleSideBarHide(true);
  }
  activeRoutes(activePath);
  return activePath;
});

// function setVisibleNumber() {
//   const width = document.body.getBoundingClientRect().width / 3;
//   visibleNumber.value = parseInt(width / 85);
// }

function handleSelect(key, keyPath) {
  currentIndex.value = key;
  const route = routers.value.find((item) => item.path === key);
  if (isHttp(key)) {
    // http(s):// 路径新窗口打开
    window.open(key, "_blank");
  } else if (!route || !route.children) {
    // 没有子路由路径内部打开
    const routeMenu = childrenMenus.value.find((item) => item.path === key);
    if (routeMenu && routeMenu.query) {
      let query = JSON.parse(routeMenu.query);
      router.push({ path: key, query: query });
    } else {
      router.push({ path: key });
    }
    appStore.toggleSideBarHide(true);
  } else {
    // 默认显示侧边栏菜单第一个子路由
    router.push({ path: route.children[0].path });
    // 显示左侧联动菜单
    activeRoutes(key);
    appStore.toggleSideBarHide(false);
  }
}

function activeRoutes(key) {
  let routes = [];
  if (childrenMenus.value && childrenMenus.value.length > 0) {
    childrenMenus.value.map((item) => {
      if (key == item.parentPath || (key == "index" && "" == item.path)) {
        routes.push(item);
      }
    });
  }
  if (routes.length > 0) {
    permissionStore.setSidebarRouters(routes);
  } else {
    appStore.toggleSideBarHide(true);
  }
  return routes;
}

onMounted(() => {
  initSSE(import.meta.env.VITE_APP_BASE_API + "/resource/sse");
});
onBeforeUnmount(() => {
  initSSE(import.meta.env.VITE_APP_BASE_API + "/resource/sse/close");
});

// onMounted(() => {
//   setVisibleNumber()
// })
</script>

<style lang="scss">
@import "@/styles/variables.module.scss";

.topmenu-container.el-menu {
  --el-menu-bg-color: rgba(255, 255, 255, 0);
  --el-menu-text-color: #fff;
  --el-menu-active-color: #fff;
  border: 0px;
  height: $topBarHeight !important;
}

.topmenu-container.el-menu--horizontal > .el-menu-item {
  float: left;
  height: $topBarHeight !important;
  width: 100px !important;
  line-height: 35px !important;
  padding: 5px 5px !important;
  margin: 0 10px !important;
  display: flex;
  flex-direction: column;
  justify-content: end;
  font-size: 16px;
  border-bottom: 0px;

  .svg-icon {
    width: 1.25rem;
    height: 1.25rem;
  }
}

.topmenu-container.el-menu--horizontal > .el-menu-item.is-active,
.el-menu--horizontal > .el-sub-menu.is-active .el-submenu__title {
  border-bottom: 0px solid #{"var(--theme)"} !important;
  background-color: #1557a3;
}

/* sub-menu item */
.topmenu-container.el-menu--horizontal > .el-sub-menu .el-sub-menu__title {
  float: left;
  line-height: 35px !important;
  padding: 5px 5px !important;
  color: #999093 !important;
  margin: 0 10px !important;
}

/* 背景色隐藏 */
// .topmenu-container.el-menu--horizontal>.el-menu-item:not(.is-disabled):focus,
// .topmenu-container.el-menu--horizontal>.el-menu-item:not(.is-disabled):hover,
// .topmenu-container.el-menu--horizontal>.el-submenu .el-submenu__title:hover {
//   background-color: #ffffff !important;
// }

/* 图标右间距 */
.topmenu-container .svg-icon {
  margin-right: 4px;
}

/* topmenu more arrow */
.topmenu-container .el-sub-menu .el-sub-menu__icon-arrow {
  position: static;
  vertical-align: middle;
  margin-left: 8px;
  margin-top: 0px;
}
</style>
