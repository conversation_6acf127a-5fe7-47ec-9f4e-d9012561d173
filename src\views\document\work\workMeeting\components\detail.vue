<template>
  <div>
    <div class="header">
      <el-page-header @back="goBack">
        <template #content>
          <span class="text-large font-600 mr-3"> 批转会议 </span>
        </template>
      </el-page-header>
      <div class="btn-submit">
        <el-button v-if="!readOnly" type="primary" v-loading="showLoding" @click="selectUsers('')">批转</el-button>
        <el-button v-if="!readOnly && rejectNodeState(nodeId)" type="danger"
          @click="selectUsers('reject')">驳回</el-button>
        <!-- <el-button type="warning" @click="gotoHistory('0')">查看办理过程</el-button> -->
        <el-button type="success" @click="gotoHistory('1')">查看批转记录</el-button>
        <el-button >打印</el-button>
        <!-- @click="handleExport" -->
      </div>
    </div>
    <el-row :gutter="20" class="container">
      <el-col :span="18" class="card" v-loading="showLoding">
        <SelectUserTransfer :processInstanceId="processInstanceId" v-if="userDialog" @back="handleUserTransfer"
          :msg="form.docAudit?.auditIdea" :taskId="taskId" :nodeId="nodeId" :rejectFlag="rejectFlag"
          :readType="readType" @saveData="handleSelectUsers">
        </SelectUserTransfer>
        <div class="form">
          <div class="form-content">
            <el-form ref="receiptFormRef" :model="form" :rules="rules" label-width="100px">
              <table>
                <tr>
                  <td colspan="4">
                    <h1 style="text-align: center">会议通知单</h1>
                  </td>
                </tr>
                <tr>
                  <td>
                    <el-form-item label="编制单位" prop="workUnit">
                      <el-input :disabled="formDisabled" v-model="form.workUnit" placeholder="请输入编制单位" />
                    </el-form-item>
                  </td>
                  <td>
                    <el-form-item label="编制时间" prop="workDate">
                      <el-date-picker :disabled="formDisabled" style="width: 100%;" clearable v-model="form.workDate"
                        type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择编制时间">
                      </el-date-picker>
                    </el-form-item>
                  </td>
                </tr>
                <tr>
                  <td>
                    <el-form-item label="会议编号" prop="meetNum">
                      <el-input :disabled="formDisabled" v-model="form.meetNum" placeholder="请输入会议编号" />
                    </el-form-item>
                  </td>
                  <td>
                    <el-form-item label="办理状态" prop="handleStatus">
                      <el-input :disabled="formDisabled" v-model="form.handleStatus" placeholder="请输入办理状态" />
                    </el-form-item>
                  </td>
                </tr>
                <tr>
                  <td colspan="4">
                    <el-form-item label="会议名称" prop="meetName">
                      <el-input :disabled="formDisabled" v-model="form.meetName" placeholder="请输入会议名称" />
                    </el-form-item>
                  </td>
                </tr>
                <tr>
                  <td colspan="4">
                    <el-form-item label="会议要求" prop="meetAsk">
                      <el-input :disabled="formDisabled" type="textarea" v-model="form.meetAsk" placeholder="请输入会议要求" />
                    </el-form-item>
                  </td>
                </tr>
                <tr>
                  <td colspan="4">
                    <el-form-item label="会议地点" prop="meetSite">
                      <el-input :disabled="formDisabled" type="textarea" v-model="form.meetSite"
                        placeholder="请输入会议地点" />
                    </el-form-item>
                  </td>
                </tr>
                <tr>
                  <td colspan="4">
                    <el-form-item label="组会单位" prop="meetUnit">
                      <el-input :disabled="formDisabled" type="textarea" v-model="form.meetUnit"
                        placeholder="请输入组会单位" />
                    </el-form-item>
                  </td>
                </tr>
                <tr v-for="(item, index) in form.audits" :key="index">
                  <td v-if="item.docData === '1'" colspan="4">
                    <el-form-item v-if="item.nodeId === nodeId && !readOnly" :label="item.nodeName" label-width="150px">
                      <el-input type="textarea" v-model="form.docAudit.auditIdea" placeholder="请输入意见" />
                      <el-form-item>
                        <el-form-item label="签字">
                          <!-- <div class="sign-box" v-if="form.docAudit.auditSign">{{
                                                        form.docAudit.auditSign }}
                                                    </div> -->
                          <img v-if="form.docAudit.auditSign" style="width: 100px;height: 40px;"
                            :src="perviewUrl + form.docAudit.auditSign">
                          <div class="sign-box" v-else-if="form.docAudit.name">{{
                            form.docAudit.name }}</div>
                          <div class="sign-box" v-else @click="docSign">点击签字</div>
                        </el-form-item>
                        <el-form-item label="日期">
                          <el-date-picker :disabled="!(item.nodeId === nodeId)" clearable
                            v-model="form.docAudit.auditTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss"
                            placeholder="请选择日期">
                          </el-date-picker>
                        </el-form-item>
                      </el-form-item>
                    </el-form-item>
                    <el-form-item v-else :label="item.nodeName" label-width="150px">
                      <el-input :disabled="true" type="textarea" v-model="item.data[0].auditIdea" placeholder="请输入意见" />
                      <el-form-item>
                        <el-form-item label="签字">
                          <!-- <div class="sign-box" style="background-color: #F5F7FA;"
                                                        v-if="item.data[0].auditSign">{{
                                                            item.data[0].auditSign }}
                                                    </div> -->
                          <img style="width: 100px;height: 40px;" v-if="item.data[0].auditSign"
                            :src="perviewUrl + item.data[0].auditSign">
                          <div class="sign-box" style="background-color: #F5F7FA;" v-else>{{
                            item.data[0].name }}</div>
                        </el-form-item>
                        <el-form-item label="日期">
                          <el-date-picker :disabled="true" clearable v-model="item.data[0].auditTime" type="datetime"
                            value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择日期">
                          </el-date-picker>
                        </el-form-item>
                      </el-form-item>
                    </el-form-item>
                  </td>
                  <td v-if="item.docData === '2'" colspan="4">
                    <div class="el-form-item__label" style="width: 150px;border: 0px;">{{
                      item.nodeName
                    }}
                    </div>
                    <el-table :data="item.data" border style="width: 100%" height="200">
                      <el-table-column prop="auditIdea" label="意见" />
                      <el-table-column label="签字" width="180">
                        <template #default="scope">
                          <img style="width: 100px;height: 40px;" v-if="scope.row.auditSign"
                            :src="perviewUrl + scope.row.auditSign">
                          <div v-else>{{
                            scope.row.name
                          }}
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column prop="auditTime" label="日期" width="180" />
                    </el-table>
                    <div class="audit-btn flx-justify-between">
                      <el-input v-if="item.nodeId === nodeId && !readOnly" v-model="auditIdea" placeholder="请输入意见" />
                      <el-input v-else disabled placeholder="请输入意见" />
                      <el-button @click="docSignList(item)" :disabled="!(item.nodeId === nodeId && !readOnly)"
                        type="primary" plain>保存意见</el-button>
                      <el-button @click="docSignRevoke(item)" :disabled="!(item.nodeId === nodeId && !readOnly)"
                        type="info" plain>撤销意见</el-button>
                    </div>
                  </td>
                </tr>
              </table>


            </el-form>
          </div>
        </div>

      </el-col>
      <el-col :span="6" v-loading="showLoding">
        <div class="file-content card">
          <FileTree v-if="fileTypeList.length > 0" :key="formKey" :data="fileTypeList" :upload="!formDisabled"
            @onSuccess="onUploadSuccess" @onRemove="onUploadRemove" />
        </div>
      </el-col>
    </el-row>
    <History :activeName="historyType" :businessId="businessId" @back="goBackHistory" v-if="showHistory" />
  </div>
</template>

<script setup>
import FileTree from '@/components/FileTree'
import { onMounted, ref } from 'vue';
import { getDicts } from '@/api/system/dict/data';
import { getTmpJson } from '@/api/document/common';
import { getDetail, updateForm } from '@/api/document/meeting';
import { startWorkFlow, completeTask, signTask, backProcess } from '@/api/document/common';
import SelectUserTransfer from "@/components/SelectUserTransfer/index.vue";
import useUserStore from '@/store/modules/user'
import { parseTime } from '@/utils/ruoyi';
import History from "@/components/History/index.vue";
import { rejectNodeState } from "@/utils/rejectNodeMap.js";
import { exportWordForId } from "@/utils/common.js";

//print
const handleExport = ()=>{
  exportWordForId('会议通知单','5', props.businessId)
}

const showHistory = ref(false)
const historyType = ref('0')
const goBackHistory = () => {
  showHistory.value = false
}
const gotoHistory = (type) => {
  showHistory.value = true
  historyType.value = type
}

const emit = defineEmits()
const props = defineProps({
  businessId: {
    type: String,
  },
  taskId: {
    type: String,
  },
  nodeId: {
    type: String,
  },
  readOnly: {
    type: Boolean,
    default: () => false
  },
  processInstanceId: {
    type: String,
  },
});

const { proxy } = getCurrentInstance();
const perviewUrl = import.meta.env.VITE_APP_FILE_SERVICE_URL

const formKey = ref(new Date().getTime())
const fileTypeList = ref([])
//表单修改开关
const formDisabled = ref(true)
const showLoding = ref(false)

const readTypeList = [
  { label: '办理件', value: '1' },
  { label: '传阅件', value: '2' },
]
const readType = ref('')

//需要请求的字典类型
const dictsList = ['doc_receipt_file_type']
const dicts = ref({})
dictsList.map(item => dicts.value[item] = [])

const receiptFormRef = ref(null)
const initFormData = {
  workUnit: undefined,
  workDate: undefined,
  handleStatus: undefined,
  meetName: undefined,
  meetAsk: undefined,
  meetSite: undefined,
  affixList: [],
}
const data = reactive({
  form: { ...initFormData },
  rules: {
    meetNum: [
      { required: true, message: "请输入会议编号", trigger: "blur" }
    ],
    meetName: [
      { required: true, message: "请输入会议名称", trigger: "blur" }
    ],
  }
});
const { form, rules } = toRefs(data);
const tmpJson = ref([])
const auditIdea = ref('')

const handleUserTransfer = () => {
  userDialog.value = false
  rejectFlag.value = false
}

const userDialog = ref(false)
function goBack () {
  emit('back')
}
//上传成功
function onUploadSuccess (fileList) {
  form.value.affixList.push(fileList)
}
//删除文件
function onUploadRemove (file) {
  form.value.affixList = form.value.affixList.filter(item => item.name !== file.response.data.url)
}
function docSign () {
  let userInfo = useUserStore().user
  if (userInfo.signUrl)
  {
    form.value.docAudit.auditSign = userInfo.signUrl
  } else
  {
    form.value.docAudit.name = userInfo.nickName
  }
  let date = parseTime(new Date().getTime())
  form.value.docAudit.auditTime = date
}
//多人签字
function docSignList (item) {
  let userInfo = useUserStore().user
  if (userInfo.signUrl)
  {
    form.value.docAudit.auditSign = userInfo.signUrl
  } else
  {
    form.value.docAudit.name = userInfo.nickName
  }
  form.value.docAudit.auditIdea = auditIdea.value
  let date = parseTime(new Date().getTime())

  let ArrIndex = item.data.findIndex(it => it.del);
  if (ArrIndex !== -1)
  {
    item.data[ArrIndex] = {
      auditSign: userInfo.signUrl,
      name: userInfo.nickName,
      auditIdea: auditIdea.value,
      auditTime: date,
      del: true//删除标志
    }
  } else
  {
    form.value.docAudit.auditTime = date
    item.data.push({
      auditSign: userInfo.signUrl,
      name: userInfo.nickName,
      auditIdea: auditIdea.value,
      auditTime: date,
      del: true//删除标志
    })
  }

}
//撤销签字
function docSignRevoke (item) {
  form.value.docAudit.auditSign = ''
  form.value.docAudit.name = ''
  form.value.docAudit.auditIdea = ''
  form.value.docAudit.auditTime = ''
  item.data = item.data.filter(it => !it.del)
}

const rejectFlag = ref(false)
//调起选择框
function selectUsers (type) {
  let flag = true
  tmpJson.value.map(item => {
    if (proxy.nodeId === item.nodeId)
    {
      if (!form.value.docAudit.auditTime)
      {
        proxy.$modal.msgError('请在下方完成' + item.nodeName + '并签字');
        flag = false
      }
    }
  })
  if (!flag) return
  if (type === 'reject') rejectFlag.value = true
  userDialog.value = true
}
//批转回调
function handleSelectUsers (result) {
  console.log('result', result);
  receiptFormRef.value.validate(valid => {
    if (valid)
    {
      showLoding.value = true
      if (form.value.docAudit)
      {
        form.value.docAudit.auditIdea = result.message
      }
      let userInfo = useUserStore().user
      // 修改收文
      updateForm(form.value).then(() => {
        let data = {
          taskId: props.taskId,
          messageType: ['1'],
          message: JSON.stringify({
            msg: result.message,
            sign: userInfo.signUrl ? userInfo.signUrl : '',
            name: userInfo.signUrl ? '' : userInfo.nickName,
          }),
          variables: { entity: form.value, ...result.variables }
        }
        if (rejectFlag.value)
        {
          let params = {
            taskId: data.taskId,
            messageType: data.messageType,
            message: data.message,
            targetActivityId: result.variables
          }
          backProcess(params).then(res => {
            showLoding.value = false
            proxy.$modal.msgSuccess('驳回成功');
            goBack()
          })
        } else
        {
          // 办理任务
          completeTask(data).then(res => {
            showLoding.value = false
            proxy.$modal.msgSuccess('批转成功');
            goBack()
          })
        }

      })
    }
  })

}

onMounted(() => {
  if (!props.readOnly)
  {
    // 签收任务
    signTask(props.taskId)
  }
  getDetail(props.businessId).then(res => {
    form.value = res.data
    if (form.value.affixes)
    {
      form.value.affixes.map(item => {
        item.dictLabel = item.typeName
        item.fileList = item.affixs
      })
      fileTypeList.value = form.value.affixes
    }
    tmpJson.value = form.value.audits
    tmpJson.value.map(item => {
      if (proxy.nodeId === item.nodeId)
      {
        form.value.docAudit = {
          "businessId": props.businessId,
          "nodeName": item.nodeName,
          "nodeId": item.nodeId,
          "userId": useUserStore().id,
          "name": "",
          "auditIdea": "",
          "auditTime": "",
          "auditSign": "",
        }
      }
    })
  })
  dictsList.map(item => {
    getDicts(item).then(res => {
      dicts.value[item] = res.data
    })
  })
  getTmpJson({ type: '5' }).then(res => {
    tmpJson.value = res.data
  })
})
</script>

<style scoped lang="scss">
@import "@/styles/variables.module.scss";;

.container {
  padding: 10px 20px;
  background-color: #E9EEF3;
  height: calc($contentHeight - 50px);
  overflow-x: hidden;
  overflow-y: scroll;
}

.form {
  display: flex;
  justify-content: center;
  background: #dce2f1;
}

.form-content {
  width: 770px;
}

table,
th,
td {
  border: none;
}

table {
  width: 100%;
  // border: 1px solid #e2e2e2;
  border-collapse: collapse;

  :deep(.el-form-item) {
    margin-bottom: 0;
  }

  :deep(.el-form-item__label) {
    justify-content: center;
    // border-right: 1px solid #e2e2e2;
    margin-right: 1px;
    height: auto;
    padding: 10px 10px;
  }

  :deep(.el-form-item__error) {
    position: static;
  }

  td {
    // border: 1px solid #e2e2e2;
    background-color: rgb(246, 246, 246);
  }
}

.header {
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  box-shadow: 0 0 12px rgba(0, 0, 0, 0.05);
  border-bottom: 1px solid #e2e2e2;
  background-color: #fff;
}

.btn-submit {
  display: flex;
  justify-content: flex-end;
}

.audit-btn {
  padding: 5px 5px;
}

.sign-box {
  background-color: #fff;
  height: 100%;
  width: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #606266;
  font-family: inherit;
  font-size: inherit;
  cursor: pointer;
  border-right: 1px solid #e2e2e2;
}
</style>
