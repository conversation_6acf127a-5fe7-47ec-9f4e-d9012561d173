<template>
  <div class="login">
    <h3 class="title">储备项目与地块巡查管理系统</h3>
    <el-form
      ref="loginRef"
      :model="loginForm"
      :rules="loginRules"
      class="login-form"
    >
      <div class="contaninone">
        <div class="loginwenzi">用户登录</div>
      </div>
      <el-form-item prop="username">
        <el-input
          clearable
          @input="userChange"
          @focus="showSelect = true"
          v-model="loginForm.username"
          type="text"
          size="large"
          auto-complete="off"
          placeholder="账号"
        >
          <template #prefix><el-icon> <User /> </el-icon></template>
        </el-input>
      </el-form-item>
      <div
        v-show="false"
        class="select-down"
      >
        <div
          v-for="item in deptUserTree"
          :key="item.id"
        >
          <div
            class="tow"
            @click="item.open = !item.open"
          >
            <div
              class="metro"
              :class="item.open ? 'center_open' : 'roots_close'"
            />
            <div class="node-flx">
              <div
                class="metro"
                :class="item.open ? 'ico_open' : 'ico_close'"
              />
              <div class="node-name">{{ item.name }}</div>
            </div>
          </div>
          <div v-show="item.open">
            <div
              class="line"
              v-for="it in item.children"
              :key="it.id"
              @click="onSelect(it)"
            >
              <div class="one">
                <div class="metro switch center_docu"/>
                <div class="node-flx">
                  <div class="metro ico_docu"/>
                  <div class="node-name">{{ it.name }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div>
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            size="large"
            auto-complete="off"
            placeholder="密码"
            @keyup.enter="handleLogin"
          >
            <template #prefix><svg-icon
              icon-class="password"
              class="el-input__icon input-icon"
            /></template>
          </el-input>
        </el-form-item>
        <!-- <el-form-item prop="code" v-if="captchaEnabled">
        <el-input v-model="loginForm.code" size="large" auto-complete="off" placeholder="验证码" style="width: 63%"
          @keyup.enter="handleLogin">
          <template #prefix><svg-icon icon-class="validCode" class="el-input__icon input-icon" /></template>
        </el-input>
        <div class="login-code">
          <img :src="codeUrl" @click="getCode" class="login-code-img" />
        </div>
      </el-form-item> -->
        <el-checkbox
          v-model="loginForm.rememberMe"
          class="mima"
        >记住登录密码</el-checkbox>
        <el-form-item style="width: 100%">
          <el-button
            :loading="loading"
            size="large"
            type="primary"
            class="denglu"
            style=""
            @click.prevent="handleLogin"
          >
            <span v-if="!loading">登 录</span>
            <span v-else>登 录 中...</span>
          </el-button>
          <div
            style="float: right"
            v-if="register"
          >
            <router-link
              class="link-type"
              :to="'/register'"
            >立即注册</router-link>
          </div>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { getCodeImg } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from "@/utils/jsencrypt";
import useUserStore from "@/store/modules/user";
import { ref } from "vue";
import { useRoute, useRouter } from "vue-router"

const userStore = useUserStore();
const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();

const deptUserTree = ref([]);

const loginForm = ref({
  name: "",
  username: "tdcb",
  password: "123456",
  rememberMe: false,
  code: "",
  uuid: ""
});

const loginRules = {
  username: [{ required: true, trigger: "manual", message: "请选择您的账号" }],
  password: [{ required: true, trigger: "blur", message: "请输入您的密码" }]
  // code: [{ required: true, trigger: "change", message: "请输入验证码" }],
};

const codeUrl = ref("");
const loading = ref(false);
// 验证码开关
const captchaEnabled = ref(true);
// 注册开关
const register = ref(false);
const redirect = ref(undefined);

watch(
  route,
  (newRoute) => {
    redirect.value = newRoute.query && newRoute.query.redirect;
  },
  { immediate: true }
);
function onSelect(item) {
  loginForm.value.name = item.name;
  loginForm.value.username = item.accountName;
  showSelect.value = false;
}
function userChange(e) {
  // showSelect.value = true
  //
  // if (!e) {
  //   deptUserTree.value = deptUserTreeBackUp.value
  // }
  // deptUserTree.value = deptUserTree.value.map(department => {
  //   const matchedUsers = department.children ? department.children.filter(user => user.name.includes(e)) : [];
  //   // 如果匹配到用户，则返回包含部门和用户的对象，否则返回null或空部门对象
  //   if (matchedUsers.length > 0) {
  //     department.open = true
  //   }
  //   return matchedUsers.length > 0 ? { ...department, children: matchedUsers } : null;
  // }).filter(result => result !== null);
}
function handleLogin() {
  proxy.$refs.loginRef.validate((valid) => {
    if (valid) {
      loading.value = true;
      // 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
      if (loginForm.value.rememberMe) {
        Cookies.set("name", loginForm.value.name, { expires: 30 });
        Cookies.set("username", loginForm.value.username, { expires: 30 });
        Cookies.set("password", encrypt(loginForm.value.password), {
          expires: 30
        });
        Cookies.set("rememberMe", loginForm.value.rememberMe, { expires: 30 });
      } else {
        // 否则移除
        Cookies.remove("name");
        Cookies.remove("username");
        Cookies.remove("password");
        Cookies.remove("rememberMe");
      }
      // 调用action的登录方法
      userStore
        .login(loginForm.value)
        .then(() => {
          const query = route.query;
          const otherQueryParams = Object.keys(query).reduce((acc, cur) => {
            if (cur !== "redirect") {
              acc[cur] = query[cur];
            }
            return acc;
          }, {});
          router.push({ path: redirect.value || "/", query: otherQueryParams });
        })
        .catch(() => {
          loading.value = false;
          // 重新获取验证码
          // if (captchaEnabled.value) {
          //   getCode();
          // }
        });
    }
  });
}

function getCode() {
  getCodeImg().then((res) => {
    captchaEnabled.value = res.captchaEnabled === undefined ? true : res.captchaEnabled;
    if (captchaEnabled.value) {
      codeUrl.value = "data:image/gif;base64," + res.data.img;
      loginForm.value.uuid = res.data.uuid;
    }
  });
}

function getCookie() {
  const name = Cookies.get("name");
  const username = Cookies.get("username");
  const password = Cookies.get("password");
  const rememberMe = Cookies.get("rememberMe");
  loginForm.value = {
    name: name === undefined ? loginForm.value.name : name,
    username: username === undefined ? loginForm.value.username : username,
    password:
      password === undefined ? loginForm.value.password : decrypt(password),
    rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
  };
}

const showSelect = ref(false);
// getCode();
getCookie();
</script>

<style lang="scss" scoped>
.login {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: url(../assets/images/tdcb.png) 0% 0% / 100% 100% no-repeat;
}
.contaninone {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}
.loginwenzi {
  color: #fff;
  text-align: center;
  margin-top: 20px;
  font-size: 26px;
  letter-spacing: 5px;
  font-weight: bolder;
  margin-bottom: 15px;
}
.one {
  display: flex;
  margin-left: 15px;
}
.two {
  display: flex;
  line-height: 27px;
}
.title {
  position: absolute;
  top: 16%;
  font-size: 46px;
  text-align: center;
  color: #0879d6;
  font-weight: bold;
  letter-spacing: 2px;
  text-shadow: 2px 2px 0px #f9f9f9;
}
.mima {
  margin: 0px 0px 25px 10px;
  color: #9e9fa0;
}
.denglu {
  width: 100%;
  background-color: #00a7ff;
  font-size: 20px;
  border: 0px;
}
.header {
  position: absolute;
  top: 4.5%;
  height: 70px;
  width: 100%;
}

.login-form {
  background-color: #000000b0;
  // opacity: 0.6;
  width: 420px;
  border-radius: 8px;
  height: 350px;
  padding: 0px 20px 5px 20px;
  box-shadow: #666 0px 0px 10px;

  .el-input {
    height: 40px;

    input {
      height: 40px;
    }
  }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 0px;
  }
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.el-form-item--default {
  margin-bottom: 24px;
  padding: 0 10px 0 10px;
}
.login-code {
  width: 33%;
  height: 40px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}

.login-code-img {
  height: 40px;
  padding-left: 12px;
}

.select-down {
  width: 100%;
  height: 220px;
  z-index: 10;
  background: rgba(255, 255, 255);
  border-width: 1px;
  border-style: solid;
  border-color: rgb(204, 204, 204);
  border-image: initial;
  overflow-y: auto;
  margin-top: -17px;
  padding-top: 5px;
  padding-left: 5px;
}

.metro {
  line-height: 0;
  width: 21px;
  height: 25px;
  display: inline-block;
  vertical-align: middle;
  cursor: pointer;
  background-color: transparent;
  background-repeat: no-repeat;
  background-attachment: scroll;
  background-image: url(../assets/images/metro.png);
  margin: 0px;
  border-width: 0px;
  border-style: none;
  border-color: initial;
  border-image: initial;
  outline: none;
}

.roots_close {
  background-position: -126px 0px;
}

.center_open {
  background-position: -105px -21px;
}

.ico_close {
  margin-right: 2px;
  vertical-align: top;
  background-position: -148px 0px;
}

.ico_open {
  margin-right: 2px;
  vertical-align: top;
  background-position: -148px -21px;
}

.switch {
  width: 21px;
  height: 25px;
}

.line {
  background: url(../assets/images/line_conn.gif) 0px 0px repeat-y;
}

.center_docu {
  background-position: -84px -21px;
}

.node-flx {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.ico_docu {
  margin-right: 2px;
  vertical-align: top;
  background-position: -148px -42px;
}

.node-name {
  font-size: 12px;
  font-family: Verdana, Arial, Helvetica, AppleGothic, sans-serif;
  color: rgb(51, 51, 51);
}

.node-name:hover {
  text-decoration: underline;
}
</style>
