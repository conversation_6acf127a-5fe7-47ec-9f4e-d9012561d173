define(["exports","./arrayFill-4513d7ad","./buildModuleUrl-9085faaa","./Cartesian2-db21342c","./Cartographic-3309dd0d","./Check-7b2a090c","./ComponentDatatype-c140a87d","./when-b60132fc","./EllipseGeometryLibrary-a39b75ad","./Rectangle-dee65d21","./GeometryAttribute-c65394ac","./GeometryAttributes-252e9929","./GeometryInstance-6bd4503d","./GeometryOffsetAttribute-fbeb6f1a","./GeometryPipeline-7a733318","./IndexDatatype-8a5eead4","./Math-119be1a3","./FeatureDetection-806b12f0","./VertexFormat-6446fca0"],(function(e,t,r,a,i,n,o,s,l,u,m,c,p,y,d,f,A,h,x){"use strict";var g=new i.Cartesian3,b=new i.Cartesian3,_=new i.Cartesian3,v=new i.Cartesian3,C=new a.Cartesian2,w=new h.Matrix3,M=new h.Matrix3,E=new m.Quaternion,I=new i.Cartesian3,T=new i.Cartesian3,G=new i.Cartesian3,N=new i.Cartographic,F=new i.Cartesian3,P=new a.Cartesian2,V=new a.Cartesian2;function D(e,n,u){var p=n.vertexFormat,d=n.center,f=n.semiMajorAxis,A=n.semiMinorAxis,x=n.ellipsoid,v=n.stRotation,D=u?e.length/3*2:e.length/3,O=n.shadowVolume,S=p.st?new Float32Array(2*D):void 0,L=p.normal?new Float32Array(3*D):void 0,R=p.tangent?new Float32Array(3*D):void 0,j=p.bitangent?new Float32Array(3*D):void 0,k=O?new Float32Array(3*D):void 0,z=0,B=I,Y=T,H=G,U=new r.GeographicProjection(x),Q=U.project(x.cartesianToCartographic(d,N),F),W=x.scaleToGeodeticSurface(d,g);x.geodeticSurfaceNormal(W,W);var J=w,q=M;if(0!==v){var Z=m.Quaternion.fromAxisAngle(W,v,E);J=h.Matrix3.fromQuaternion(Z,J),Z=m.Quaternion.fromAxisAngle(W,-v,E),q=h.Matrix3.fromQuaternion(Z,q)}else J=h.Matrix3.clone(h.Matrix3.IDENTITY,J),q=h.Matrix3.clone(h.Matrix3.IDENTITY,q);for(var K=a.Cartesian2.fromElements(Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY,P),X=a.Cartesian2.fromElements(Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY,V),$=e.length,ee=u?$:0,te=ee/3*2,re=0;re<$;re+=3){var ae=re+1,ie=re+2,ne=i.Cartesian3.fromArray(e,re,g);if(p.st){var oe=h.Matrix3.multiplyByVector(J,ne,b),se=U.project(x.cartesianToCartographic(oe,N),_);i.Cartesian3.subtract(se,Q,se),C.x=(se.x+f)/(2*f),C.y=(se.y+A)/(2*A),K.x=Math.min(C.x,K.x),K.y=Math.min(C.y,K.y),X.x=Math.max(C.x,X.x),X.y=Math.max(C.y,X.y),u&&(S[z+te]=C.x,S[z+1+te]=C.y),S[z++]=C.x,S[z++]=C.y}(p.normal||p.tangent||p.bitangent||O)&&(B=x.geodeticSurfaceNormal(ne,B),O&&(k[re+ee]=-B.x,k[ae+ee]=-B.y,k[ie+ee]=-B.z),(p.normal||p.tangent||p.bitangent)&&((p.tangent||p.bitangent)&&(Y=i.Cartesian3.normalize(i.Cartesian3.cross(i.Cartesian3.UNIT_Z,B,Y),Y),h.Matrix3.multiplyByVector(q,Y,Y)),p.normal&&(L[re]=B.x,L[ae]=B.y,L[ie]=B.z,u&&(L[re+ee]=-B.x,L[ae+ee]=-B.y,L[ie+ee]=-B.z)),p.tangent&&(R[re]=Y.x,R[ae]=Y.y,R[ie]=Y.z,u&&(R[re+ee]=-Y.x,R[ae+ee]=-Y.y,R[ie+ee]=-Y.z)),p.bitangent&&(H=i.Cartesian3.normalize(i.Cartesian3.cross(B,Y,H),H),j[re]=H.x,j[ae]=H.y,j[ie]=H.z,u&&(j[re+ee]=H.x,j[ae+ee]=H.y,j[ie+ee]=H.z))))}if(p.st){$=S.length;for(var le=0;le<$;le+=2)S[le]=(S[le]-K.x)/(X.x-K.x),S[le+1]=(S[le+1]-K.y)/(X.y-K.y)}var ue=new c.GeometryAttributes;if(p.position){var me=l.EllipseGeometryLibrary.raisePositionsToHeight(e,n,u);ue.position=new m.GeometryAttribute({componentDatatype:o.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:me})}if(p.st&&(ue.st=new m.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:S})),p.normal&&(ue.normal=new m.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:L})),p.tangent&&(ue.tangent=new m.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:R})),p.bitangent&&(ue.bitangent=new m.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:j})),O&&(ue.extrudeDirection=new m.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:k})),u&&s.defined(n.offsetAttribute)){var ce=new Uint8Array(D);if(n.offsetAttribute===y.GeometryOffsetAttribute.TOP)ce=t.arrayFill(ce,1,0,D/2);else{var pe=n.offsetAttribute===y.GeometryOffsetAttribute.NONE?0:1;ce=t.arrayFill(ce,pe)}ue.applyOffset=new m.GeometryAttribute({componentDatatype:o.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:ce})}return ue}function O(e){var t,r,a,i,n,o=new Array(e*(e+1)*12-6),s=0;for(t=0,a=1,i=0;i<3;i++)o[s++]=a++,o[s++]=t,o[s++]=a;for(i=2;i<e+1;++i){for(a=i*(i+1)-1,t=(i-1)*i-1,o[s++]=a++,o[s++]=t,o[s++]=a,r=2*i,n=0;n<r-1;++n)o[s++]=a,o[s++]=t++,o[s++]=t,o[s++]=a++,o[s++]=t,o[s++]=a;o[s++]=a++,o[s++]=t,o[s++]=a}for(r=2*e,++a,++t,i=0;i<r-1;++i)o[s++]=a,o[s++]=t++,o[s++]=t,o[s++]=a++,o[s++]=t,o[s++]=a;for(o[s++]=a,o[s++]=t++,o[s++]=t,o[s++]=a++,o[s++]=t++,o[s++]=t,++t,i=e-1;i>1;--i){for(o[s++]=t++,o[s++]=t,o[s++]=a,r=2*i,n=0;n<r-1;++n)o[s++]=a,o[s++]=t++,o[s++]=t,o[s++]=a++,o[s++]=t,o[s++]=a;o[s++]=t++,o[s++]=t++,o[s++]=a++}for(i=0;i<3;i++)o[s++]=t++,o[s++]=t,o[s++]=a;return o}var S=new i.Cartesian3;var L=new r.BoundingSphere,R=new r.BoundingSphere;function j(e){var n=e.center,u=e.ellipsoid,A=e.semiMajorAxis,x=i.Cartesian3.multiplyByScalar(u.geodeticSurfaceNormal(n,g),e.height,g);L.center=i.Cartesian3.add(n,x,L.center),L.radius=A,x=i.Cartesian3.multiplyByScalar(u.geodeticSurfaceNormal(n,x),e.extrudedHeight,x),R.center=i.Cartesian3.add(n,x,R.center),R.radius=A;var M=l.EllipseGeometryLibrary.computeEllipsePositions(e,!0,!0),S=M.positions,j=M.numPts,k=M.outerPositions,z=r.BoundingSphere.union(L,R),B=D(S,e,!0),Y=O(j),H=Y.length;Y.length=2*H;for(var U=S.length/3,Q=0;Q<H;Q+=3)Y[Q+H]=Y[Q+2]+U,Y[Q+1+H]=Y[Q+1]+U,Y[Q+2+H]=Y[Q]+U;var W=f.IndexDatatype.createTypedArray(2*U/3,Y),J=new m.Geometry({attributes:B,indices:W,primitiveType:h.PrimitiveType.TRIANGLES}),q=function(e,n){var l=n.vertexFormat,u=n.center,p=n.semiMajorAxis,d=n.semiMinorAxis,f=n.ellipsoid,A=n.height,x=n.extrudedHeight,M=n.stRotation,D=e.length/3*2,O=new Float64Array(3*D),S=l.st?new Float32Array(2*D):void 0,L=l.normal?new Float32Array(3*D):void 0,R=l.tangent?new Float32Array(3*D):void 0,j=l.bitangent?new Float32Array(3*D):void 0,k=n.shadowVolume,z=k?new Float32Array(3*D):void 0,B=0,Y=I,H=T,U=G,Q=new r.GeographicProjection(f),W=Q.project(f.cartesianToCartographic(u,N),F),J=f.scaleToGeodeticSurface(u,g);f.geodeticSurfaceNormal(J,J);for(var q=m.Quaternion.fromAxisAngle(J,M,E),Z=h.Matrix3.fromQuaternion(q,w),K=a.Cartesian2.fromElements(Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY,P),X=a.Cartesian2.fromElements(Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY,V),$=e.length,ee=$/3*2,te=0;te<$;te+=3){var re,ae=te+1,ie=te+2,ne=i.Cartesian3.fromArray(e,te,g);if(l.st){var oe=h.Matrix3.multiplyByVector(Z,ne,b),se=Q.project(f.cartesianToCartographic(oe,N),_);i.Cartesian3.subtract(se,W,se),C.x=(se.x+p)/(2*p),C.y=(se.y+d)/(2*d),K.x=Math.min(C.x,K.x),K.y=Math.min(C.y,K.y),X.x=Math.max(C.x,X.x),X.y=Math.max(C.y,X.y),S[B+ee]=C.x,S[B+1+ee]=C.y,S[B++]=C.x,S[B++]=C.y}ne=f.scaleToGeodeticSurface(ne,ne),re=i.Cartesian3.clone(ne,b),Y=f.geodeticSurfaceNormal(ne,Y),k&&(z[te+$]=-Y.x,z[ae+$]=-Y.y,z[ie+$]=-Y.z);var le=i.Cartesian3.multiplyByScalar(Y,A,v);if(ne=i.Cartesian3.add(ne,le,ne),le=i.Cartesian3.multiplyByScalar(Y,x,le),re=i.Cartesian3.add(re,le,re),l.position&&(O[te+$]=re.x,O[ae+$]=re.y,O[ie+$]=re.z,O[te]=ne.x,O[ae]=ne.y,O[ie]=ne.z),l.normal||l.tangent||l.bitangent){U=i.Cartesian3.clone(Y,U);var ue=i.Cartesian3.fromArray(e,(te+3)%$,v);i.Cartesian3.subtract(ue,ne,ue);var me=i.Cartesian3.subtract(re,ne,_);Y=i.Cartesian3.normalize(i.Cartesian3.cross(me,ue,Y),Y),l.normal&&(L[te]=Y.x,L[ae]=Y.y,L[ie]=Y.z,L[te+$]=Y.x,L[ae+$]=Y.y,L[ie+$]=Y.z),l.tangent&&(H=i.Cartesian3.normalize(i.Cartesian3.cross(U,Y,H),H),R[te]=H.x,R[ae]=H.y,R[ie]=H.z,R[te+$]=H.x,R[te+1+$]=H.y,R[te+2+$]=H.z),l.bitangent&&(j[te]=U.x,j[ae]=U.y,j[ie]=U.z,j[te+$]=U.x,j[ae+$]=U.y,j[ie+$]=U.z)}}if(l.st){$=S.length;for(var ce=0;ce<$;ce+=2)S[ce]=(S[ce]-K.x)/(X.x-K.x),S[ce+1]=(S[ce+1]-K.y)/(X.y-K.y)}var pe=new c.GeometryAttributes;if(l.position&&(pe.position=new m.GeometryAttribute({componentDatatype:o.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:O})),l.st&&(pe.st=new m.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:S})),l.normal&&(pe.normal=new m.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:L})),l.tangent&&(pe.tangent=new m.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:R})),l.bitangent&&(pe.bitangent=new m.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:j})),k&&(pe.extrudeDirection=new m.GeometryAttribute({componentDatatype:o.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:z})),s.defined(n.offsetAttribute)){var ye=new Uint8Array(D);if(n.offsetAttribute===y.GeometryOffsetAttribute.TOP)ye=t.arrayFill(ye,1,0,D/2);else{var de=n.offsetAttribute===y.GeometryOffsetAttribute.NONE?0:1;ye=t.arrayFill(ye,de)}pe.applyOffset=new m.GeometryAttribute({componentDatatype:o.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:ye})}return pe}(k,e);Y=function(e){for(var t=e.length/3,r=f.IndexDatatype.createTypedArray(t,6*t),a=0,i=0;i<t;i++){var n=i,o=i+t,s=(n+1)%t,l=s+t;r[a++]=n,r[a++]=o,r[a++]=s,r[a++]=s,r[a++]=o,r[a++]=l}return r}(k);var Z=f.IndexDatatype.createTypedArray(2*k.length/3,Y),K=new m.Geometry({attributes:q,indices:Z,primitiveType:h.PrimitiveType.TRIANGLES}),X=d.GeometryPipeline.combineInstances([new p.GeometryInstance({geometry:J}),new p.GeometryInstance({geometry:K})]);return{boundingSphere:z,attributes:X[0].attributes,indices:X[0].indices}}function k(e,t,r,a,n,o,s){for(var m=l.EllipseGeometryLibrary.computeEllipsePositions({center:e,semiMajorAxis:t,semiMinorAxis:r,rotation:a,granularity:n},!1,!0).outerPositions,c=m.length/3,p=new Array(c),y=0;y<c;++y)p[y]=i.Cartesian3.fromArray(m,3*y);var d=u.Rectangle.fromCartesianArray(p,o,s);return d.width>A.CesiumMath.PI&&(d.north=d.north>0?A.CesiumMath.PI_OVER_TWO-A.CesiumMath.EPSILON7:d.north,d.south=d.south<0?A.CesiumMath.EPSILON7-A.CesiumMath.PI_OVER_TWO:d.south,d.east=A.CesiumMath.PI,d.west=-A.CesiumMath.PI),d}function z(e){var t=(e=s.defaultValue(e,s.defaultValue.EMPTY_OBJECT)).center,r=s.defaultValue(e.ellipsoid,u.Ellipsoid.WGS84),a=e.semiMajorAxis,n=e.semiMinorAxis,o=s.defaultValue(e.granularity,A.CesiumMath.RADIANS_PER_DEGREE),l=s.defaultValue(e.vertexFormat,x.VertexFormat.DEFAULT),m=s.defaultValue(e.height,0),c=s.defaultValue(e.extrudedHeight,m);this._center=i.Cartesian3.clone(t),this._semiMajorAxis=a,this._semiMinorAxis=n,this._ellipsoid=u.Ellipsoid.clone(r),this._rotation=s.defaultValue(e.rotation,0),this._stRotation=s.defaultValue(e.stRotation,0),this._height=Math.max(c,m),this._granularity=o,this._vertexFormat=x.VertexFormat.clone(l),this._extrudedHeight=Math.min(c,m),this._shadowVolume=s.defaultValue(e.shadowVolume,!1),this._workerName="createEllipseGeometry",this._offsetAttribute=e.offsetAttribute,this._rectangle=void 0,this._textureCoordinateRotationPoints=void 0}z.packedLength=i.Cartesian3.packedLength+u.Ellipsoid.packedLength+x.VertexFormat.packedLength+9,z.pack=function(e,t,r){return r=s.defaultValue(r,0),i.Cartesian3.pack(e._center,t,r),r+=i.Cartesian3.packedLength,u.Ellipsoid.pack(e._ellipsoid,t,r),r+=u.Ellipsoid.packedLength,x.VertexFormat.pack(e._vertexFormat,t,r),r+=x.VertexFormat.packedLength,t[r++]=e._semiMajorAxis,t[r++]=e._semiMinorAxis,t[r++]=e._rotation,t[r++]=e._stRotation,t[r++]=e._height,t[r++]=e._granularity,t[r++]=e._extrudedHeight,t[r++]=e._shadowVolume?1:0,t[r]=s.defaultValue(e._offsetAttribute,-1),t};var B=new i.Cartesian3,Y=new u.Ellipsoid,H=new x.VertexFormat,U={center:B,ellipsoid:Y,vertexFormat:H,semiMajorAxis:void 0,semiMinorAxis:void 0,rotation:void 0,stRotation:void 0,height:void 0,granularity:void 0,extrudedHeight:void 0,shadowVolume:void 0,offsetAttribute:void 0};z.unpack=function(e,t,r){t=s.defaultValue(t,0);var a=i.Cartesian3.unpack(e,t,B);t+=i.Cartesian3.packedLength;var n=u.Ellipsoid.unpack(e,t,Y);t+=u.Ellipsoid.packedLength;var o=x.VertexFormat.unpack(e,t,H);t+=x.VertexFormat.packedLength;var l=e[t++],m=e[t++],c=e[t++],p=e[t++],y=e[t++],d=e[t++],f=e[t++],A=1===e[t++],h=e[t];return s.defined(r)?(r._center=i.Cartesian3.clone(a,r._center),r._ellipsoid=u.Ellipsoid.clone(n,r._ellipsoid),r._vertexFormat=x.VertexFormat.clone(o,r._vertexFormat),r._semiMajorAxis=l,r._semiMinorAxis=m,r._rotation=c,r._stRotation=p,r._height=y,r._granularity=d,r._extrudedHeight=f,r._shadowVolume=A,r._offsetAttribute=-1===h?void 0:h,r):(U.height=y,U.extrudedHeight=f,U.granularity=d,U.stRotation=p,U.rotation=c,U.semiMajorAxis=l,U.semiMinorAxis=m,U.shadowVolume=A,U.offsetAttribute=-1===h?void 0:h,new z(U))},z.computeRectangle=function(e,t){var r=(e=s.defaultValue(e,s.defaultValue.EMPTY_OBJECT)).center,a=s.defaultValue(e.ellipsoid,u.Ellipsoid.WGS84),i=e.semiMajorAxis,n=e.semiMinorAxis,o=s.defaultValue(e.granularity,A.CesiumMath.RADIANS_PER_DEGREE);return k(r,i,n,s.defaultValue(e.rotation,0),o,a,t)},z.createGeometry=function(e){if(!(e._semiMajorAxis<=0||e._semiMinorAxis<=0)){var a=e._height,n=e._extrudedHeight,u=!A.CesiumMath.equalsEpsilon(a,n,0,A.CesiumMath.EPSILON2);e._center=e._ellipsoid.scaleToGeodeticSurface(e._center,e._center);var c,p={center:e._center,semiMajorAxis:e._semiMajorAxis,semiMinorAxis:e._semiMinorAxis,ellipsoid:e._ellipsoid,rotation:e._rotation,height:a,granularity:e._granularity,vertexFormat:e._vertexFormat,stRotation:e._stRotation};if(u)p.extrudedHeight=n,p.shadowVolume=e._shadowVolume,p.offsetAttribute=e._offsetAttribute,c=j(p);else if(c=function(e){var t=e.center;S=i.Cartesian3.multiplyByScalar(e.ellipsoid.geodeticSurfaceNormal(t,S),e.height,S),S=i.Cartesian3.add(t,S,S);var a=new r.BoundingSphere(S,e.semiMajorAxis),n=l.EllipseGeometryLibrary.computeEllipsePositions(e,!0,!1),o=n.positions,s=n.numPts,u=D(o,e,!1),m=O(s);return{boundingSphere:a,attributes:u,indices:m=f.IndexDatatype.createTypedArray(o.length/3,m)}}(p),s.defined(e._offsetAttribute)){var d=c.attributes.position.values.length,x=new Uint8Array(d/3),g=e._offsetAttribute===y.GeometryOffsetAttribute.NONE?0:1;t.arrayFill(x,g),c.attributes.applyOffset=new m.GeometryAttribute({componentDatatype:o.ComponentDatatype.UNSIGNED_BYTE,componentsPerAttribute:1,values:x})}return new m.Geometry({attributes:c.attributes,indices:c.indices,primitiveType:h.PrimitiveType.TRIANGLES,boundingSphere:c.boundingSphere,offsetAttribute:e._offsetAttribute})}},z.createShadowVolume=function(e,t,r){var a=e._granularity,i=e._ellipsoid,n=t(a,i),o=r(a,i);return new z({center:e._center,semiMajorAxis:e._semiMajorAxis,semiMinorAxis:e._semiMinorAxis,ellipsoid:i,rotation:e._rotation,stRotation:e._stRotation,granularity:a,extrudedHeight:n,height:o,vertexFormat:x.VertexFormat.POSITION_ONLY,shadowVolume:!0})},Object.defineProperties(z.prototype,{rectangle:{get:function(){return s.defined(this._rectangle)||(this._rectangle=k(this._center,this._semiMajorAxis,this._semiMinorAxis,this._rotation,this._granularity,this._ellipsoid)),this._rectangle}},textureCoordinateRotationPoints:{get:function(){return s.defined(this._textureCoordinateRotationPoints)||(this._textureCoordinateRotationPoints=function(e){var t=-e._stRotation;if(0===t)return[0,0,0,1,1,0];for(var r=l.EllipseGeometryLibrary.computeEllipsePositions({center:e._center,semiMajorAxis:e._semiMajorAxis,semiMinorAxis:e._semiMinorAxis,rotation:e._rotation,granularity:e._granularity},!1,!0).outerPositions,a=r.length/3,n=new Array(a),o=0;o<a;++o)n[o]=i.Cartesian3.fromArray(r,3*o);var s=e._ellipsoid,u=e.rectangle;return m.Geometry._textureCoordinateRotationPoints(n,t,s,u)}(this)),this._textureCoordinateRotationPoints}}}),e.EllipseGeometry=z}));
