<template>
  <div
    v-if="isShow"
    class="analyse-wrap"
  >
    <div class="tab-container">
      <div class="tool-bottom">
        <div
          class="tool-btn"
          @click="startAnalyse"
        >
          <svg-icon icon-class="huizhimian" />
          <span>绘制挖方区域</span>
        </div>
        <div
          class="tool-btn"
          @click="clearResult"
        >
          <svg-icon icon-class="clear" />
          <span>清除</span>
        </div>
      </div>
    </div>
    <div
      v-if="isShowAction"
      ref="modelTool" class="model-action"
    >
      <div class="action-title">
        <h5>属性编辑</h5>
      </div>
      <div class="action-panel">
        <div class="action-item clearfix">
          <label
            for=""
            class="action-label"
          >开挖深度（米）：</label>
          <el-input
            v-model.number="excavationHeight"
            placeholder="0<= h <=100"
            min="0"
            max="100"
            size="small"
            clearable
            @clear="clearHeight"
          />
        </div>
        <div class="action-item clearfix">
          <label
            for=""
            class="action-label"
          >调整深度（米）：</label>
          <el-slider
            v-model="excavationHeight"
            class="action-slider"
            :min="0"
            :max="100"
            :step="1"
          />
        </div>
        <div class="action-item">
          <div class="terrain-pull clearfix">
            <label
              for=""
              class="action-label label-pull"
            >是否抽出地形</label>
            <el-radio-group
              v-model="isExcavation"
              size="small"
            >
              <el-radio
                label="1"
                border
              >是</el-radio>
              <el-radio
                label="0"
                border
              >否</el-radio>
            </el-radio-group>
          </div>
          <div
            v-show="isExcavation === '1'"
            class="clearfix"
          >
            <div class="action-item clearfix">
              <label
                for=""
                class="action-label"
              >抽出高度（米）：</label>
              <el-input
                v-model.number="extractHeight"
                placeholder="10<= h <=100"
                min="10"
                max="100"
                size="small"
                clearable
                @clear="clearExtractHeight"
              />
            </div>
            <div class="action-item clearfix">
              <label
                for=""
                class="action-label"
              >调整高度（米）：</label>
              <el-slider
                v-model="extractHeight"
                class="action-slider"
                :min="50"
                :max="300"
                :step="10"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="TerrainExcavationAnalysis" text="挖方分析">
import useMapViewStore from "@/store/modules/map/mapView.js"
import { createTooltip, setCursor, setLayerSelectStatus } from "@/utils/Cesium/CesiumTool.js"

const { proxy } = getCurrentInstance()

defineProps({
  isShow: {
    type: Boolean,
    default: true
  },
  headerInfo: {
    type: Object
  }
})

const isExcavation = ref("0")
const isShowAction = ref(false)
// 开挖深度
const excavationHeight = ref(30)
const excavationPositions = ref([])
// 抽取高度
const extractHeight = ref(100)
const toolTip = ref(undefined)
const handlerPolygon = ref(undefined)

const viewer3d = computed(()=>useMapViewStore().viewer3d)


watch(excavationHeight,(value)=>{
  excavationHeight.value = value === "" ? 0 : +value
  terrainExcavation(excavationPositions.value,+value)
})

watch(extractHeight,(value)=>{
  terrainExtract(excavationPositions.value,excavationHeight.value,+value)
})

watch(isExcavation,(value)=>{
  if (+value === 1){
    terrainExtract(excavationPositions.value,excavationHeight.value,extractHeight.value)
  } else {
    terrainExcavation(excavationPositions.value,excavationHeight.value)
  }
})


const emits = defineEmits(["closePanel"]);
const closePanel = ()=>{
  emits('closePanel')
}

const startAnalyse = ()=> {
  setLayerSelectStatus(viewer3d.value,false)
  toolTip.value = createTooltip(document.body)
  // 空间模式
  handlerPolygon.value = new Cesium.DrawHandler(viewer3d.value,Cesium.DrawMode.Polygon, 0);

  // 激活绘制事件
  handlerPolygon.value.activate();
  handlerPolygon.value.activeEvt.addEventListener((isActive) => {});
  handlerPolygon.value.movingEvt.addEventListener((windowPosition) => {
    setCursor(viewer3d.value,'crosshair')
    if (windowPosition.x < 200 && windowPosition.y < 150) {
      toolTip.value.setVisible(false);
      return;
    }
    if (handlerPolygon.value.isDrawing) {
      toolTip.value.showAt(windowPosition, "<p>右键单击结束绘制,进行开挖</p>");
    } else {
      toolTip.value.showAt(windowPosition, "<p>点击绘制开挖区域第一个点</p>");
    }
  });
  handlerPolygon.value.drawEvt.addEventListener((result) => {
    setCursor(viewer3d.value,'pointer')
    if (!result.object.positions) {
      toolTip.value.showAt(result, "<p>请绘制正确的多边形</p>");
      handlerPolygon.value.polygon.show = false;
      handlerPolygon.value.polyline.show = false;
      handlerPolygon.value.deactivate();
      return;
    }
    toolTip.value.setVisible(false);
    isShowAction.value = true
    const array = [].concat(result.object.positions);

    excavationPositions.value = [];
    for (let i = 0,len = array.length; i < len; i++) {
      const cartographic = Cesium.Cartographic.fromCartesian(array[i]);
      const lon = Cesium.Math.toDegrees(cartographic.longitude);
      const lat = Cesium.Math.toDegrees(cartographic.latitude);
      const h = cartographic.height;
      excavationPositions.value = excavationPositions.value.concat([lon, lat, h]);
    }
    terrainExcavation(excavationPositions.value,excavationHeight.value)

    handlerPolygon.value.polygon.show = false;
    handlerPolygon.value.polyline.show = false;
    handlerPolygon.value.clear();
    handlerPolygon.value.deactivate();
  });
}

/**
 * 地形开挖
 * @param positions；经纬度数组，如[lon,lat,h,lon,lat,h]
 * @param excavationHeight：地形开挖深度，单位米
 */
const terrainExcavation = (positions,excavationHeight)=>{
  const scene = viewer3d.value.scene
  scene.globe.removeAllExcavationRegion();
  // 移除所有开挖区域和抽出部分。
  scene.globe.removeAllExtractRegion();
  setTimeout(() => {
    scene.globe.addExcavationRegion({
      name: "excavationRegion",
      position: positions,
      height: excavationHeight,
      transparent: false
    });
  })
}


/**
 * 地形抽取
 * @param positions；经纬度数组，如[lon,lat,h,lon,lat,h]
 * @param excavationHeight：地形开挖深度，单位米
 * @param extractHeight：地形开挖高度，单位米
 */
const terrainExtract = (positions,excavationHeight,extractHeight)=> {
  const scene = viewer3d.value.scene
  scene.globe.removeAllExcavationRegion();
  scene.globe.removeAllExtractRegion();
  scene.globe.addExtractRegion({
    name: "extractRegion", //名称
    position: positions, //区域
    height: excavationHeight, //开挖深度
    transparent: false, //封边是否透明
    extractHeight: extractHeight, //抽出高度
    granularity: 1 //精度
  });
}

const clearHeight = ()=>{
  excavationHeight.value = 0
}

const clearExtractHeight = ()=>{
  extractHeight.value = 10
}

const clearResult = ()=> {
  isShowAction.value = false
  if (handlerPolygon.value){
    handlerPolygon.value.clear();
    handlerPolygon.value.deactivate();
  }
  viewer3d.value.scene.globe.removeAllExcavationRegion();
  viewer3d.value.scene.globe.removeAllExtractRegion();
}

onBeforeUnmount(()=>{
  clearResult();
  setLayerSelectStatus(viewer3d.value,true)
})

</script>

<style scoped lang="scss">
.analyse-wrap {
  color: #fff;
  background-image: url("@/assets/images/map/tool.png");
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-size: cover;
  box-shadow: 0 0 8px 0 #057595;
}
.btn-box {
  display: flex;
  padding: 10px 10px;
  justify-content: center;
  width: 100%;
  .svg-icon {
    margin-right: 10px;
    width: 1.05em;
    height: 1.05em;
  }
  button {
    width: 140px;
  }
}
.height-value {
  margin: 10px;
  label {
    line-height: 40px;
  }
}
.tool {
  padding: 10px 10px;
}
.tool-tabs {
  display: flex;
  justify-items: center;
  justify-content: space-between;
  background-color: #4f7287;
  span {
    display: inline-block;
    width: 50%;
    padding: 10px 5px;
    text-align: center;
    color: #fff;
    &:hover {
      cursor: pointer;
      background-color: #0f7dff;
      filter: brightness(110%);
      color: #fff;
      transition: background-color 0.25s;
    }
  }
}
.tab-active {
  color: #fff;
  background: linear-gradient(to bottom right, #00baff, #0f7dff);
}

.tool-tabs {
  display: flex;
  justify-items: center;
  justify-content: space-between;
  background-color: #4f7287;
  span {
    display: inline-block;
    width: 50%;
    padding: 10px 5px;
    text-align: center;
    color: #fff;
    &:hover {
      cursor: pointer;
      background-color: #0f7dff;
      filter: brightness(110%);
      color: #fff;
      transition: background-color 0.25s;
    }
  }
}
.tab-active {
  color: #fff;
  background: linear-gradient(to bottom right, #00baff, #0f7dff);
}
.tool-tab-content {
  padding-top: 20px;
}
:deep(.el-form-item__label) {
  width: 60px !important;
  color: #b5b5b5;
}
.tab-container {
  display: flex;
  padding: 10px;
  flex-direction: column;
  column-count: 2;
}
.height-value {
  margin: 10px;
  label {
    line-height: 40px;
  }
}
.input-value {
  float: right;
  width: 80%;
}
.tool-bottom {
  display: flex;
  justify-content: space-between;
  justify-items: center;
}
.tool-btn {
  margin-top: 20px;
  padding: 10px;
  width: 48%;
  background-color: #0f7dff;
  border-color: #fff;
  text-align: center;
  border-radius: 4px;
  color: #ffffff;
  &:hover {
    cursor: pointer;
    color: #fff;
    filter: brightness(110%) opacity(100%);
    transition: all 0.5s ease-in;
    background: linear-gradient(to bottom right, #00baff, #0f7dff);
  }
  svg {
    margin-right: 10px;
  }
}
.draw-btn{
  margin-left: 10px;
}
.label {
  display: inline;
  padding: 0.01em 3.5em 0.01em 3.5em;
  font-size: 150%;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25em;
  line-height: 1.1;
}

.model-action {
  position: absolute;
  top: 50px;
  left: -270px;
  width: 80%;
  background-color: #001d3bdb;
  border-radius: 5px;
  z-index: 999;
}
.action-title {
  padding: 15px;
  background-image: url("@/assets/images/map/queryResultTitle2.png");
  background-repeat: no-repeat;
  font-size: 14px;
}
.action-panel {
  padding: 10px;
}
.action-item {
  line-height: 35px;
}
.action-slider {
  float: right;
  width: 55%;
}
.action-move {
  margin: 5px 0;
}
.color-picker {
  float: right;
  margin-right: 44px;
}
.action-header{
  padding: 5px;
  margin: 10px 0;
  background-color: #113c56;
}
:deep(.el-select){
  width: 75%;
  float: right;
}
:deep(.el-input) {
  width: 55%;
  float: right;
}
.terrain-pull{
  margin-bottom: 20px;
}
:deep(.el-radio--mini){
  margin-right: 0px;
  margin-left: 30px;
}
</style>
