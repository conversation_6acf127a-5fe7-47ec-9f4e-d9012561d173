<template>
  <div class="main-content">
    <div v-if="!showDetail">
      <transition>
        <div
          v-show="showSearch"
          class="mb-[10px]"
        >
          <el-card shadow="hover">
            <el-form
              ref="queryFormRef"
              class="query-form"
              :model="queryParams" :inline="true"
            >
              <el-form-item
                label="项目名称"
                prop="xmmc" class="sone"
              >
                <el-input
                  v-model="queryParams.xmmc"
                  placeholder="请输入项目名称"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item
                label="地块编号"
                prop="dkbh" class="sone"
              >
                <el-input
                  v-model="queryParams.dkbh"
                  placeholder="请输入地块编号"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item
                label="年度"
                prop="year" class="sone"
              >
                <el-input
                  v-model="queryParams.year"
                  placeholder="请输入年份"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item class="form-bottom-set">
                <el-button
                  type="primary"
                  icon="Search" @click="handleQuery"
                >搜索</el-button>
                <el-button
                  icon="Refresh"
                  @click="resetQuery"
                >重置</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </div>
      </transition>

      <el-row
        :gutter="10"
        class="mb8 new"
      >
        <el-col :span="1.5">
          <el-button
            type="primary" plain icon="Plus"
            @click="handleAdd"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success" plain icon="Upload"
            @click="handleImport"
          >
            批量导入（Shape）
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning" plain icon="Download"
            @click="handleExport"
          >导出</el-button>
        </el-col>
        <right-toolbar
          v-model:showSearch="showSearch"
          @queryTable="getList"
        />
      </el-row>
      <el-card class="result-wrap">
        <el-table
          v-loading="loading"
          :data="relLandList"
        >
          <el-table-column
            label="项目名称"
            align="center" prop="xmmc"
          />
          <el-table-column
            label="地块编号"
            align="center" prop="dkbh"
          />
          <el-table-column
            label="已征地总面积（公顷）"
            align="center" prop="yzdzmj"
          />
          <el-table-column
            label="年度"
            align="center" prop="year"
          />
          <el-table-column
            label="操作"
            align="center"
            width="280"
          >
            <template #default="scope">
              <el-button
                plain
                type="success" size="small" @click="handlePublish(scope.row)"
              >发布</el-button>
              <el-button
                plain
                type="primary" size="small" @click="handlePreview(scope.row)"
              >查看</el-button>
              <el-button
                plain
                type="warning" size="small" @click="handleUpdate(scope.row)"
              >修改</el-button>
              <el-button
                plain
                type="danger" size="small" @click="handleDelete(scope.row)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>
    <!-- 添加或修改拆迁地块信息对话框 -->
    <div
      v-else
      class="content"
    >
      <div class="add-header-title">
        <div class="add-title">{{ ghTitle }}</div>
        <div
          class="add-title-return"
          @click="cancel"
        >
          <img
            src="@/assets/images/img-return.png"
            class="back"
          >
          <div class="backlist">返回列表</div>
        </div>
      </div>
      <div class="add-content-temp">
        <el-row :gutter="10">
          <el-col
            :span="14"
            class="contentlist"
          >
            <div class="project-header">
              <h2
                class="project-title"
                :class="{'is-active':showProject}"
                @click="updateProject"
              >
                项目信息
              </h2>
            </div>
            <el-form
              ref="projectFormRef"
              label-width="150px"
              :model="projectForm"
              :rules="projectFormRules"
              v-show="showProject"
            >
              <el-row>
                <el-col :span="12">
                  <el-form-item
                    label="项目名称"
                    prop="xmmc"
                  >
                    <el-input
                      v-model="projectForm.xmmc"
                      placeholder="请输入项目名称"
                      clearable
                    />
                  </el-form-item>
                  <!--                  <el-form-item label="批复面积">-->
                  <!--                    <el-input-->
                  <!--                      v-model="projectForm.pfmj"-->
                  <!--                      placeholder="请输入批复面积"-->
                  <!--                    >-->
                  <!--                      <template #append>公顷</template>-->
                  <!--                    </el-input>-->
                  <!--                  </el-form-item>-->
                  <el-form-item
                    label="行政区域"
                    prop="xzqmc"
                  >
                    <el-tree-select
                      v-model="projectForm.xzqmc"
                      check-strictly
                      filterable
                      node-key="id"
                      :props="props"
                      :data="region"
                      @node-click="selectRegion"
                    />
                  </el-form-item>
                  <el-form-item label="已征地总面积">
                    <el-input
                      v-model="projectForm.yzdzmj"
                      placeholder="请输入已征地总面积"
                    >
                      <template #append>公顷</template>
                    </el-input>
                  </el-form-item>
                  <!--                  <el-form-item label="总批复金额">-->
                  <!--                    <el-input-->
                  <!--                      v-model="projectForm.zpfje"-->
                  <!--                      placeholder="请输入总批复金额"-->
                  <!--                    />-->
                  <!--                  </el-form-item>-->
                  <!--                  <el-form-item label="收到资金">-->
                  <!--                    <el-input-->
                  <!--                      v-model="projectForm.sdzj"-->
                  <!--                      placeholder="请输入收到资金"-->
                  <!--                    />-->
                  <!--                  </el-form-item>-->
                  <el-form-item label="项目开始期限">
                    <el-date-picker
                      clearable
                      v-model="projectForm.xmksqx"
                      type="datetime"
                      value-format="YYYY-MM-DD HH:mm:ss"
                      placeholder="请选择项目开始期限"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="已拆迁总户数">
                    <el-input
                      v-model="projectForm.ycqzhs"
                      placeholder="请输入已拆迁总户数"
                    />
                  </el-form-item>
                  <!--                  <el-form-item label="剩余资金">-->
                  <!--                    <el-input-->
                  <!--                      v-model="projectForm.syzj"-->
                  <!--                      placeholder="请输入剩余资金"-->
                  <!--                    />-->
                  <!--                  </el-form-item>-->
                  <!--                  <el-form-item label="征迁批复金额">-->
                  <!--                    <el-input-->
                  <!--                      v-model="projectForm.zqpfje"-->
                  <!--                      placeholder="请输入征迁批复金额"-->
                  <!--                    />-->
                  <!--                  </el-form-item>-->
                  <!--                  <el-form-item label="支出资金">-->
                  <!--                    <el-input-->
                  <!--                      v-model="projectForm.zczj"-->
                  <!--                      placeholder="请输入支出资金"-->
                  <!--                    />-->
                  <!--                  </el-form-item>-->
                  <!--                  <el-form-item label="通车时间">-->
                  <!--                    <el-date-picker-->
                  <!--                      clearable-->
                  <!--                      v-model="projectForm.tcsj"-->
                  <!--                      type="datetime"-->
                  <!--                      value-format="YYYY-MM-DD HH:mm:ss"-->
                  <!--                      placeholder="请选择通车时间"-->
                  <!--                      style="width: 100%"-->
                  <!--                    />-->
                  <!--                  </el-form-item>-->
                  <el-form-item label="项目结束期限">
                    <el-date-picker
                      clearable
                      v-model="projectForm.xmjsqx"
                      type="datetime"
                      value-format="YYYY-MM-DD HH:mm:ss"
                      placeholder="请选择项目结束期限"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-upload
                    ref="projectUploadRef"
                    class="upload-demo"
                    drag
                    multiple
                    accept=".zip"
                    :http-request="customHttpRequest"
                  >
                    <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                    <div class="el-upload__text">
                      拖动文件 或者<em>点击上传</em>项目范围文件
                    </div>
                    <template #tip>
                      <div class="el-upload__tip">
                        请上传 Shapefile <span class="upload-tip">【.zip】</span> 压缩文件 <span class="upload-tip">（坐标系：CGCS2000）</span>
                      </div>
                    </template>
                  </el-upload>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
          <el-col
            :span="10"
            style="padding-top: 10px"
          >
            <div class="attachment">
              <div>
                <div class="related_accessories"/>
                <div class="related">相关附件</div>
                <div
                  class="attachment-content"
                  v-loading="isFileLoading"
                  element-loading-text="正在上传..."
                  :element-loading-spinner="svg"
                  element-loading-svg-view-box="-10, -10, 50, 50"
                  element-loading-background="rgba(122, 122, 122, 0.25)"
                >
                  <ul>
                    <li
                      v-for="(item,index) in dictList"
                      :key="index"
                      class="attachment-item"
                    >
                      <div class="attachment-item-header">
                        <span
                          class="arrow-icon"
                          @click="getFiles(item,false)"
                          v-if="isEdit"
                        >
                          <el-icon v-if="item.isArrowUp"><ArrowDownBold /></el-icon>
                          <el-icon v-else><ArrowUpBold /></el-icon>
                        </span>
                        <span>{{ item.dictLabel }}</span>
                        <el-upload
                          class="upload-icon"
                          :action="actionUrl"
                          accept=".pdf,.ppt,.doc,.docx"
                          :headers="headers"
                          :data="{'directory':'','dictCode':item.dictCode}"
                          :show-file-list="false"
                          :before-upload="handleBefore"
                          :on-success="(response,file,fileList)=>handleSuccess(response,file,fileList,item)"
                        >
                          <el-icon><UploadFilled /></el-icon>
                        </el-upload>
                      </div>
                      <div
                        v-if="item.isArrowUp"
                        class="attachment-item-content"
                      >
                        <ul>
                          <li
                            class="file-item clearfix"
                            v-for="(file,i) in allFiles[item.dictValue]" :key="i"
                          >
                            <span v-if="file.wjmc.length<20">{{file.wjmc}}</span>
                            <el-popover
                              v-else
                              placement="top-start"
                              width="500"
                              trigger="hover"
                              :content="file.wjmc"
                            >
                              <template #reference>
                                <span>{{file.wjmc.substring(0,20)+"···"}}</span>
                              </template>
                            </el-popover>
                            <span class="action-btn">
                              <el-button
                                type="primary"
                                plain
                                @click="previewFile(file)"
                              >预览</el-button>
                              <el-button
                                type="success"
                                plain
                                @click="downloadFile(file)"
                              >下载</el-button>
                              <el-button
                                v-if="isEdit"
                                type="danger"
                                plain
                                @click="deleteFile(file,item)"
                              >删除</el-button>
                            </span>
                          </li>
                        </ul>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="add-content-wrap">
        <div class="footer-button">
          <el-button
            :loading="buttonLoading"
            type="primary" @click="submitForm" plain
          >
            <el-icon>
              <CircleCheckFilled/>
            </el-icon>
            提交项目信息
          </el-button>
          <el-button
            @click="cancel"
            plain
          >
            <el-icon>
              <CircleCloseFilled/>
            </el-icon>
            取 消
          </el-button>
        </div>
      </div>
    </div>
    <!--  项目模板数据导入对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="导入项目范围数据"
      width="500"
      :before-close="handleDialogClose"
    >
      <el-upload
        ref="projectUploadRef"
        class="upload-demo"
        drag
        multiple
        accept=".zip"
        :action="uploadModelActionUrl"
        :headers="headers"
        :before-upload="beforeUploadProject"
        :on-success="handleProjectSuccess"
        :on-error="handleProjectError"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          拖动文件 或者<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            请上传 Shapefile <span class="upload-tip">【.zip】</span> 压缩文件 <span class="upload-tip">（坐标系：CGCS2000）</span>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="submitProjectModel"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="RelocationManage">
/**
 * CHN：征拆项目管理
 */
import {
  listRelProject,
  getRelProject,
  delRelProject,
  addRelProject,
  updateRelProject,
  getAttachments, deleteFileById, downloadProjectModel, downloadProjectList
} from "@/api/relocationPatrol/relocationLand.js"
import { getRegionTreeList } from "@/api/gis/layerTree.js";
import { reserveLandlist } from "@/api/patrol/reserveProject.js"
import { getDicts } from "@/api/system/dict/data.js"
import { getToken } from "@/utils/auth.js"
import { fetchAndDownload, filePreview } from "@/utils/index.js"
import { ElMessage } from "element-plus"
import { downloadFileByArrayBuffer } from "@/utils/common.js"
import { addRelPatrolTask } from "@/api/relocationPatrol/patrolTask.js"
import { useRouter } from "vue-router"

const { proxy } = getCurrentInstance();
const router = useRouter()

const relLandList = ref([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const total = ref(0);
const showProject = ref(true)
const showRelativeLand = ref(false)
const queryFormRef = ref();
const landFormRef = ref();
const projectFormRef = ref()
const land = ref({});
const isEdit = ref(false)
const showDetail = ref(false);
const dictList = ref()
const attachments = ref([])
const svg = `
        <path class="path" d="
          M 30 15
          L 28 17
          M 25.61 25.61
          A 15 15, 0, 0, 1, 15 30
          A 15 15, 0, 1, 1, 27.99 7.5
          L 15 15
        "style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
      `
const isFileLoading = ref(false)
const allFiles = reactive({
  province: [],
  approve: [],
  other: []
})

const headers = {
  Authorization: "Bearer " + getToken(),
  clientid: import.meta.env.VITE_APP_CLIENT_ID,
  dataType: "relocation"
}
// 上传征拆项目附件数据接口
const actionUrl = import.meta.env.VITE_APP_BASE_API + "/patrol/attachmentFile/upload"
// 批量上传征拆项目范围数据接口
// const uploadModelActionUrl = import.meta.env.VITE_APP_BASE_API + "/patrol/relProject/importShape"
const uploadModelActionUrl = import.meta.env.VITE_APP_BASE_API + "/patrol/relProject/importShapeBatch"
const projectUploadRef = ref()
const dialogVisible = ref(false)

const dialog = reactive({
  visible: false,
  title: ""
});
const regionInfo = reactive({
  xzqmc: "",
  xzqdm: ""
})

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    xzqdm: undefined,
    xzqmc: undefined,
    dkbh: undefined,
    tbbh: undefined,
    dkmc: undefined,
    ghyt: undefined,
    dkmj: undefined,
    dz: undefined,
    tdzt: undefined,
    smgeometry: undefined,
    bz: undefined,
    year: 2022
  },
  projectForm: {
    "xzqdm": undefined,
    "xzqmc": undefined,
    "smgeometry": undefined,
    "bz": undefined,
    "xmmc": undefined,
    "pfmj": 0,
    "xmfl": undefined,
    "xmksqx": undefined,
    "xmjsqx": undefined,
    "yzdzmj": null,
    "ycqzhs": null,
    "tcsj": undefined,
    "zpfje": null,
    "zqpfje": null,
    "sdzj": null,
    "zczj": null,
    "syzj": null,
    "dkbh": undefined
  },
  landForm: {
    "xmId": 0,
    "xzqdm": undefined,
    "xzqmc": undefined,
    "dkbh": undefined,
    "tbbh": undefined,
    "dkmc": undefined,
    "ghyt": undefined,
    "dkmj": 0,
    "dz": undefined,
    "tdzt": undefined,
    "bz": undefined,
    "zdszd": undefined,
    "zdszn": undefined,
    "zdszx": undefined,
    "zdszb": undefined,
    "jhghfs": undefined,
    "jhlyfs": undefined,
    "xmmc": undefined,
    "ly": undefined,
    "dkbsm": undefined,
    "cbjg": undefined
  },
  projectFormRules: {
    xmmc: [{ required: true, message: "项目名称不能为空", trigger: "blur" }]
  },
  landFormRules: {
    dkmc: [{ required: true, message: "地块名称不能为空", trigger: "blur" }],
    ghyt: [{ required: true, message: "规划用途不能为空", trigger: "blur" }],
    tdzt: [{ required: true, message: "土地状态不能为空", trigger: "blur" }]
  }
});

const { queryParams,projectForm,landForm,projectFormRules } = toRefs(data);

/** 查询拆迁地块信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await listRelProject(queryParams.value);
  relLandList.value = res.rows.map(item=>{
    // 转换面积单位：平方米->公顷
    item.yzdzmj = ((item.yzdzmj) * 1e-4).toFixed(4);
    return item
  });
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  showDetail.value = false;
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  Object.assign(allFiles,{
    province: [],
    approve: [],
    other: []
  })
  projectFormRef.value?.resetFields();
  landFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

// 标题变量
const ghTitle = ref('新增征拆地块信息')


const createFilter = async (queryString) => {
  return await reserveLandlist({
    dkbh: queryString,
    pageNum: 1,
    pageSize: 10
  });
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  projectForm.value.id = undefined
  showDetail.value = true;
  ghTitle.value = '新增征拆地块信息'
};

/**
 * 项目数据导入
 */
const handleImport = ()=>{
  dialogVisible.value = true
}
/**
 * 项目任务发布
 */
const handlePublish = (row)=>{
  const query = {
    projectId: row.id,
    name: row.xmmc
  }
  try {
    addRelPatrolTask(query).then(()=>{
      ElMessage.success("【" + row.xmmc + "】任务发布成功，请到巡查任务管理页面查看！")
    })
  }catch (err){
    ElMessage.success("【" + row.xmmc + "】任务发布失败，请检查？" + err)
  }
}
/**
 * 项目信息查看
 */
const handlePreview = (row)=>{
  // 传递状态参数
  // router.push({ name: "ProjectDetail",state: { ...row } })
  router.push({ name: "ProjectDetail",state: row })
}

/** 修改按钮操作 */
const handleUpdate = async (row) => {
  reset();
  isEdit.value = true
  ghTitle.value = `修改【${row.xmmc}】项目信息`
  const _id = row?.id || ids.value[0];
  const res = await getRelProject(_id);

  const transformData = {}
  Object.keys(res.data).forEach(key=>{
    if(key === "yzdzmj" || key === "pfmj"){
      // 转换面积单位：平方米->公顷
      transformData[key] = ((res.data[key]) * 1e-4).toFixed(4);
      return
    }
    transformData[key] = res.data[key]
  })
  Object.assign(projectForm.value, transformData);
  land.value = res.data;
  showDetail.value = true;
};

const selectRegion = (region)=>{
  regionInfo.xzqmc = region.name
  regionInfo.xzqdm = region.areaCode
}

const customHttpRequest = (response)=>{
  projectForm.value.file = response.file
}



/** 提交按钮 */
const submitForm = () => {
  projectFormRef.value?.validate(async (valid) => {
    if (valid) {
      buttonLoading.value = true;

      const formData = new FormData()

      projectForm.value.xzqmc = regionInfo.xzqmc
      projectForm.value.xzqdm = regionInfo.xzqdm
      projectForm.value.files = attachments

      const bussRelProjectData = {
        xzqmc: regionInfo.xzqmc,
        xzqdm: regionInfo.xzqdm,
        files: attachments
      }
      Object.keys(projectForm.value).forEach((key) => {
        if(key === "file"){
          return
        }
        bussRelProjectData[key] = projectForm.value[key]
      })

      console.log("文件数据：",projectForm.value.file)

      formData.append("bussRelProjectData",JSON.stringify(bussRelProjectData));
      formData.append("file", projectForm.value.file);

      if (projectForm.value?.id) {
        await updateRelProject(projectForm.value).finally(
          () => (buttonLoading.value = false)
        );
      } else {
        await addRelProject(formData).finally(
          () => (buttonLoading.value = false)
        );
      }
      proxy?.$modal.msgSuccess("操作成功");
      showDetail.value = false;
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row) => {
  const _ids = row?.id || ids.value;
  const prjName = row?.xmmc
  await proxy?.$modal
    .confirm('是否确认删除项目名称为【' + prjName + '】 的数据项？')
    .finally(() => (loading.value = false));
  await delRelProject(_ids);
  proxy?.$modal.msgSuccess( "【" + prjName + "】数据项删除成功");
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  downloadProjectList().then(res=>{
    const mineType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    downloadFileByArrayBuffer(res,"项目数据" + new Date().getTime() + ".xlsx",mineType)
  })
};
const props = {
  value: 'areaCode',
  children: 'children',
  label: 'name'
}
const region = ref([]);
const regionTree = async () => {
  const tree = await getRegionTreeList();
  region.value = tree.data;
}

// 更新项目信息
const updateProject = ()=>{
  showProject.value = true
  showRelativeLand.value = false
}

/**
 * 根据字典类型查询附件目录
 */
const getDictData = ()=>{
  getDicts('tdcb_file_type').then((res) => {
    dictList.value = res.data.map(item=>{
      item.isArrowUp = false
      getFiles(item,true)
      return item
    })
  })
}
/**
 * 文件上传前处理事件
 */
const handleBefore = (res)=>{
  isFileLoading.value = true
}

/**
 * 文件上传成功处理事件
 */
const handleSuccess = (response,file,fileList,item)=>{
  const data = response.data
  const fileObj = {
    wjmc: data.fileName,
    wjlj: data.url
  }
  item.isArrowUp = true
  allFiles[item.dictValue].push(fileObj)
  attachments.value.push(response.data)
  isFileLoading.value = false
}

/**
 * 获取附件数据
 */
const getFiles = (item,isArrowUp)=>{
  // isFileLoading.value = true
  item.isArrowUp = !item.isArrowUp
  if(isArrowUp){
    item.isArrowUp = true
  }
  item.wjflId = item.dictCode
  item.createTime = new Date(item.createTime)
  item.zbId = projectForm.value.id
  getAttachments(item).then(res=>{
    allFiles[item.dictValue] = res.rows
    console.log(allFiles)
    isFileLoading.value = false
  })
}

/**
 * 文件预览
 */
const previewFile = (file)=>{
  filePreview(file.wjlj,"_blank")
}

/**
 * 文件下载
 */
const downloadFile = (file)=>{
  fetchAndDownload(file.wjlj, file.wjmc);
}

/**
 * 文件删除
 */
const deleteFile = (file,item)=>{
  deleteFileById(file.id).then(res=>{
    ElMessage.success("【" + file.name + "】文件删除成功！")
    getFiles(item,true)
  }).catch(error=>{
    ElMessage.warning("【" + file.name + "】文件删除失败！" + error)
  })
}

/**
 * 下载项目模板文件
 */
const downloadProject = ()=>{
  downloadProjectModel().then(res=>{
    const mineType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    downloadFileByArrayBuffer(res,"模板数据.xlsx",mineType)
  })
}

const beforeUploadProject = (file)=>{
  return true;
}

const handleProjectSuccess = (response,uploadFile)=>{
  projectUploadRef.value.clearFiles()
  ElMessage.success("项目数据上传城功")
  dialogVisible.value = false
  getList()
}

const handleProjectError = ()=>{
  projectUploadRef.value.clearFiles()
  ElMessage.error("项目数据上传失败")
}

const submitProjectModel = ()=>{
  dialogVisible.value = false
}
// 关闭导入对话框
const handleDialogClose = ()=>{
  projectUploadRef.value.clearFiles()
  dialogVisible.value = false
}

onMounted(() => {
  getList();
  regionTree();
  getDictData()
});
</script>
<style scoped lang="scss">
.main-content {
  padding: 10px;
  height: 100%;
}
.result-wrap {
  margin-top: 10px;
}
.add-content-temp{
  padding: 10px 20px;
}
.project-header{
  display: flex;
  flex-direction: row;
  margin-bottom: 20px;
  background: rgba(235,245,255,0.6);
  border-radius: 2.5px;
  .project-title{
    padding: 10px;
    margin-right: 10px;
    margin-left: 10px;
    font-size: 16px;
    border-radius: 2.5px;
    &:hover{
      cursor: pointer;
      background: rgba(32,119,255);
      color: #fff;
      transition: all ease-in .25s;
    }
  }
  .is-active{
    background:rgba(32,119,255,0.8);
    border-radius: 2.5px;
    color: #fff;
  }
}
.footer-button {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  border-top: 1px solid #e0e0e0;
  padding: 20px 0;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.new {
  margin-top: 10px;
}
.el-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
:deep(.el-dialog .el-dialog__header) {
  border-bottom: 1px solid #2077ff !important;
  background: #2077ff !important;
}
:deep(.el-dialog__title) {
  color: #ffffff;
}
:deep(.el-dialog__headerbtn) {
  height: 55px;
}
:deep(.el-dialog__headerbtn .el-dialog__close) {
  color: #ffffff;
}
:deep(.el-dialog .el-dialog__footer) {
  border-top: 1px solid #ececec;
}
.content {
  border: 1px solid rgb(233, 233, 233);
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.add-header-title {
  padding: 10px;
  display: flex;
  justify-content: space-between;
  height: 50px;
  background-color: rgb(222, 239, 255);
  box-sizing: border-box;
  border-bottom: 1px solid rgb(233, 233, 233);
  font-weight: 700;
  font-size: 14px;
  line-height: 28px;
}

.add-title-return {
  display: flex;
  align-content: center;
  color: rgb(32, 119, 255);
  cursor: pointer;
  font-weight: normal;
  &:hover{
    cursor: pointer;
    font-size: 16px;
    transform: scale(1.15);
    transition: all ease-in .25s;
  }
}

.add-content {
  padding: 0px 10px;
}

.main-content {
  padding: 10px;
}

.result-wrap {
  margin-top: 10px;
}

.add-header-title {
  padding: 10px;
  display: flex;
  justify-content: space-between;
  height: 50px;
  background-color: rgb(222, 239, 255);
  box-sizing: border-box;
  border-bottom: 1px solid rgb(233, 233, 233);
  font-weight: 700;
  font-size: 14px;
  line-height: 28px;
}

.add-title-return {
  display: flex;
  align-content: center;
  color: rgb(32, 119, 255);
  cursor: pointer;
  font-weight: normal;
}

.add-content-rev {
  padding: 0px 10px;
}

.content-project-rev {
  display: flex;
  align-items: center;

  p {
    color: #333333;
    font-weight: bold;
    margin-left: 8px;
  }
}

.tab-active {
  color: #0f7dff;
  font-weight: bold;
}

.back {
  height: 18px;
  width: 18px;
  margin-top: 5px;
}

.backlist {
  padding-left: 6px;
  font-size: 14px;
}
.top {
  margin-bottom: 15px;
}

.state {
  width: 250px;
}

.el-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.content {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #dadada;
}

.contentlist {
  padding: 10px;

  .laberone {
    height: 60px;
    width: 100%;
    display: flex;
  }

  .titleone {
    height: 100%;
    width: 120px;
    text-align: center;
  }

  .information-rev {
    margin-top: 4px;

    img {
      width: 30px;
      height: auto;
      transform: rotate(-1deg);
    }
  }

  .two {
    border: 1px solid rgb(173, 211, 246);
    padding: 10px 10px 10px 20px;
    border-radius: 4px;
  }

  .three {
    border: 1px solid rgb(173, 211, 246);
    padding: 10px 10px 10px 20px;
    border-radius: 4px;
  }

  .four {
    background-color: white;
    z-index: 999;
    width: 300px;
  }

  .patrol {
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid rgb(230, 230, 230);
  }

  .xcxx {
    text-align: center;
    line-height: 51px;
  }

  .xx {
    margin-top: 15px;
  }

  .xcxx span {
    font-size: 12px;
    color: rgb(140, 140, 140);
  }

  .personnel {
    display: flex;
    height: 40px;
    text-align: center;
    line-height: 40px;
  }

  .xcry {
    width: 120px;
    font-size: 13px;
    border: 1px solid #e9e9e9;
    font-weight: bold;
    background-color: #fafafa;
  }

  .xcry-none {
    width: 120px;
    font-size: 13px;
    border: 1px solid #e9e9e9;
    font-weight: bold;
    background-color: #fafafa;
    border-top: none !important;
  }

  .xcrymz {
    width: 180px;
    font-size: 14px;
    border: 1px solid #e9e9e9;
    border-left: none;
    color: rgb(85, 85, 85);
  }

  .xcrymz-none {
    width: 180px;
    font-size: 14px;
    border: 1px solid #e9e9e9;
    border-left: none;
    color: rgb(85, 85, 85);
    border-top: none !important;

    :deep(.el-input__wrapper) {
      box-shadow: none;
    }
  }

  .viewer3d {
    padding-left: 310px;
  }
}

.content-button {
  height: 68px;
  line-height: 68px;
}

.attachment {
  border: 1px solid #e6e6e6;
  border-radius: 4px;
}
.attachment-content {
  min-height: 300px;
  max-height: 350px;
  overflow: auto;
}
.related_accessories {
  height: 8px;
  background-color: rgba(33, 120, 255, 1);
  border-radius: 4px 4px 0 0;
}

.related {
  border-bottom: 1px solid rgb(230, 230, 230);
  height: 40px;
  line-height: 40px;
  align-content: center;
  padding-left: 10px;
  font-size: small;
  font-weight: bold;
}
.region-select{
  margin-bottom: 0!important;
  width: 200px;
}
.arrow-icon{
  margin-right: 5px;
  &:hover{
    cursor: pointer;
  }
}
.upload-icon{
  display: inline;
  margin-left: 5px;
  .el-icon:hover{
    cursor: pointer;
    color: #0d84ff;
    transform: scale(1.5);
    margin-left: 5px;
    transition: all ease-in .25s;
  }
}
.attachment-item{
  font-weight: bold;
  margin: 5px 0;
  color: #444444bd;
}
.attachment-item-content{
  font-weight: normal;
  .file-item{
    padding-bottom: 2.5px;
    margin: 5px 0;
    border-bottom: 1px solid #eee;
  }
}
.action-btn{
  float: right;
  margin-right: 5px;
}
.download-btn{
  margin-bottom: 10px;
}
.upload-tip{
  color:red;
}
:deep(.el-select){
  width: 100%!important;
}

.query-form{
  /**防止输入框出现清除按钮时输入框产生宽度变化**/
  :deep(.el-input--suffix) {
    // 固定宽度
    width: 200px !important;
  }
}
@media(max-width: 1000px){
  .query-form{
    :deep(.el-form-item) {
      margin-bottom: 18px !important;
    }
  }
}
@media(min-width: 1500px){
  .query-form{
    :deep(.el-form-item) {
      margin-bottom: 0 !important;
    }
  }
}
</style>
