define(["./when-b60132fc","./EllipsoidOutlineGeometry-2cff2e8c","./arrayFill-4513d7ad","./Check-7b2a090c","./buildModuleUrl-9085faaa","./Cartographic-3309dd0d","./Math-119be1a3","./Rectangle-dee65d21","./FeatureDetection-806b12f0","./Cartesian4-3ca25aab","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Event-16a2dfbf","./ComponentDatatype-c140a87d","./GeometryAttribute-c65394ac","./Cartesian2-db21342c","./GeometryAttributes-252e9929","./GeometryOffsetAttribute-fbeb6f1a","./IndexDatatype-8a5eead4"],(function(e,t,a,r,i,n,d,c,o,f,u,b,l,s,y,m,p,G,C){"use strict";return function(a,r){return e.defined(a.buffer)&&(a=t.EllipsoidOutlineGeometry.unpack(a,r)),t.EllipsoidOutlineGeometry.createGeometry(a)}}));
