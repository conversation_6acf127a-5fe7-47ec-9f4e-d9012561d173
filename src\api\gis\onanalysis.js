import request from '@/utils/request'

// 查询分析结果列表
export function listOnanalysis(query) {
  return request({
    url: '/map/onanalysis/list',
    method: 'get',
    params: query
  })
}

// 查询分析结果详细
export function getOnanalysis(id) {
  return request({
    url: '/map/onanalysis/' + id,
    method: 'get'
  })
}

// 新增分析结果
export function addOnanalysis(data) {
  return request({
    url: '/map/onanalysis',
    method: 'post',
    data: data
  })
}

// 修改分析结果
export function updateOnanalysis(data) {
  return request({
    url: '/map/onanalysis',
    method: 'put',
    data: data
  })
}

// 删除分析结果
export function delOnanalysis(id) {
  return request({
    url: '/map/onanalysis/' + id,
    method: 'delete'
  })
}
