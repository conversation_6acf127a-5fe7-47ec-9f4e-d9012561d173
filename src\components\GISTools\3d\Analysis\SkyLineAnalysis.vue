<template>
  <div
    v-if="isShow"
    class="analyse-wrap"
  >
    <div class="tab-container">
      <el-button
        type="primary"
        @click="drawObservePoint"
      >
        <svg-icon icon-class="draw" />
        <span class="sight-btn">绘制观测点</span>
      </el-button>
      <el-button
        type="primary"
        @click="getSkyline()"
      >
        <svg-icon icon-class="skyLine" />
        <span class="sight-btn">生成天际线</span>
      </el-button>
      <el-button
        v-if="showSkyBtn"
        type="primary" @click="initChat"
      >
        <svg-icon icon-class="skyLine" />
        <span class="sight-btn">提取天际线</span>
      </el-button>
      <div class="tool-bottom">
        <div
          class="tool-btn"
          @click="clearResult"
        >
          <svg-icon icon-class="clear" />
          <span>清除</span>
        </div>
      </div>
    </div>
    <div
      v-if="showSkyPanel"
      ref="modelTool" class="model-action"
    >
      <div
        class="action-title clearfix"
        @click="closeSkyLineChart"
      >
        <h5>属性编辑</h5>
        <span class="close-span">X</span>
      </div>
      <div class="action-panel">
        <div class="action-item clearfix">
          <label
            for=""
            class="action-label"
          >方向角(度)</label>
          <el-input
            v-model="directionAngle"
            placeholder="0°到360°"
            min="0"
            max="360"
            size="small"
            clearable
          />
        </div>
        <div class="action-item clearfix">
          <label
            for=""
            class="action-label"
          >生成半径(米)</label>
          <el-input
            v-model="radius"
            placeholder="0到5000"
            min="0"
            max="5000"
            size="small"
            clearable
          />
        </div>
      </div>
    </div>
    <div
      v-if="showSkyChart"
      ref="modelTool" class="model-action"
    >
      <div
        class="action-title clearfix"
        @click="closeSkyLineChart"
      >
        <h5>二维天际线</h5>
        <span class="close-span">X</span>
      </div>
      <div class="sky-chat">
        <div
          ref="sky2D"
          style="height: 260px"
        />
      </div>
    </div>
  </div>
</template>

<script setup name="SkyLineAnalysis" text="天际线分析">
import useMapViewStore from "@/store/modules/map/mapView.js"
import { cartesian2Degrees, createTooltip, setCursor, setLayerSelectStatus } from "@/utils/Cesium/CesiumTool.js"
import { ElMessage } from "element-plus"
import * as echarts from "echarts";

const { proxy } = getCurrentInstance()

defineProps({
  isShow: {
    type: Boolean,
    default: true
  }
})

const showSkyChart = ref(false)
// 观察点
const observePoint = ref(undefined)
// 天际线对象
const skyLine = ref(undefined)
const drawPolygonHandler = ref(undefined)
const showSkyBtn = ref(false)
const showSkyPanel = ref(false)
// 俯仰角
const pitchAngle = ref(0)
// 方向角
const directionAngle = ref(0)
const radius = ref(1000) // 天际线分析半径

const viewer3d = computed(()=>useMapViewStore().viewer3d)


watch(directionAngle,(value)=>{
  if (value){
    directionAngle.value = value <= 0 ? 0 : value >= 360 ? 360 : value
    if (String(value).startsWith("0")){
      directionAngle.value = 0
    }
    skyLine.value.clear()
    skyLine.value.pitch = pitchAngle.value
    skyLine.value.direction = directionAngle.value
    // 天际线分析半径设置为10000米
    skyLine.value.radius = radius.value;
  }
})

watch(radius,(value)=>{
  if (value){
    radius.value = value <= 0 ? 0 : value >= 5000 ? 5000 : value
    if (String(value).startsWith("0")){
      radius.value = 0
    }
    skyLine.value.clear()
    skyLine.value.pitch = pitchAngle.value
    skyLine.value.direction = directionAngle.value
    //天际线分析半径设置为10000米
    skyLine.value.radius = radius.value;
  }
})

const initSkyLine = ()=>{
  // 创建天际线分析对象
  skyLine.value = new Cesium.Skyline(viewer3d.value.scene)
}

/**
 * 绘制观测点（目标点）
 */
const drawObservePoint = ()=>{
  clearResult()
  setLayerSelectStatus(viewer3d.value,false)
  const toolTip = createTooltip(document.body)
  // 空间绘制模式
  const polygonHandler = new Cesium.DrawHandler(viewer3d.value,Cesium.DrawMode.Point,Cesium.ClampMode.Space);
  drawPolygonHandler.value = polygonHandler
  polygonHandler.activate()
  // 激活事件
  polygonHandler.activeEvt.addEventListener((isActive) => {

  })
  // 移动事件
  polygonHandler.movingEvt.addEventListener((windowPosition) => {
    setCursor(viewer3d.value,'crosshair')
    toolTip.showAt(windowPosition,"<p>左键单击绘制点，右键绘制结束</p>")
  })
  // 绘制事件
  polygonHandler.drawEvt.addEventListener((result) => {
    setCursor(viewer3d.value,'pointer')
    toolTip.setVisible(false)
    observePoint.value = result.object._position
    polygonHandler.deactivate()
  })
}

/**
 * 获取天际线
 */
const getSkyline = ()=>{
  if (!observePoint.value){
    ElMessage.warning("请先绘制观察点")
    return
  }
  if (skyLine.value){
    skyLine.value.clear()
    skyLine.value = null
  }
  const scene = viewer3d.value.scene;
  const positionDegrees = cartesian2Degrees(observePoint.value)

  // 创建天际线分析对象
  skyLine.value = new Cesium.Skyline(scene);
  // 地球表面是否参与天际线分析，默认值为false表示参与，当设置为true则表示不参与。
  skyLine.value.ignoreGlobe = false
  // 天际线分析的视口位置设置成当前相机位置
  skyLine.value.viewPosition = [positionDegrees.longitude, positionDegrees.latitude,positionDegrees.height];

  //设置俯仰和方向
  const targetPitch = Cesium.Math.toDegrees(scene.camera.pitch);
  const targetDirection = Cesium.Math.toDegrees(scene.camera.heading);

  pitchAngle.value = targetPitch > 0 ? Math.floor(targetPitch) : 0
  directionAngle.value = Math.floor(targetDirection)

  skyLine.value.pitch = pitchAngle.value
  skyLine.value.direction = directionAngle.value
  // 天际线分析半径设置为10000米
  skyLine.value.radius = radius.value;
  // 设置线宽
  skyLine.value.lineWidth = 2
  skyLine.value.build()
  showSkyBtn.value = true
  showSkyPanel.value = true
}


const initChat = ()=> {
  showSkyChart.value = true;
  showSkyPanel.value = false
  const that = this
  setTimeout(() => {
    const object = skyLine.value.getSkyline2D()
    const skyCharts = echarts.init(proxy.$refs.sky2D, "sky2D");
    skyCharts.setOption({
      // backgroundColor: "rgba(73,139,156,0.9)",
      title: {
        text: "",
        textStyle: {
          color: "#fff"
        }
      },

      tooltip: {
        trigger: "axis"
      },

      calculable: true,
      xAxis: [
        {
          type: "category",
          boundaryGap: false,
          data: object.x,
          show: false
        }
      ],

      yAxis: [
        {
          type: "value",
          min: 0,
          max: 1,
          axisLine: {
            show: true,
            lineStyle: {
              color: "#fff"
            }
          }
        }
      ],

      series: [
        {
          name: "",
          type: "line",
          data: object.y
        }
      ]
    });

    // 获取三维天际线对象
    const locations = skyLine.value.getSkyline3D();
    // 添加点击位置点
    skyCharts.on('click',params => {
      that.viewer3d.entities.removeAll()
      that.viewer3d.entities.add({
        positions: Cesium.Cartesian3.fromDegrees(locations.x[params.dataIndex], locations.y[params.dataIndex], locations.z[params.dataIndex]),
        billboard: {
          // horizontalOrigin:Cesium.HorizontalOrigin.LEFT,
          // image: locationImg
          image: "../../../../assets/image/map/location.png"
          //  pixelOffset: new SuperMap3D.Cartesian2(0, -32)
        }
      })
    })
  })
}

/**
 * 关闭二维天际线
 */
const closeSkyLineChart = ()=>{
  showSkyChart.value = false
  showSkyPanel.value = false
  pitchAngle.value = 0
  directionAngle.value = 0
  radius.value = 1000
}

const clearResult = ()=> {
  viewer3d.value.entities.removeAll();
  observePoint.value = null
  if (drawPolygonHandler.value){
    drawPolygonHandler.value.clear()
  }
  if (skyLine.value){
    skyLine.value.clear();
    skyLine.value = null
  }
  showSkyChart.value = false
  showSkyBtn.value = false
  showSkyPanel.value = false
}


onBeforeMount(()=>{
  initSkyLine()
})

onBeforeUnmount(()=>{
  clearResult();
  setLayerSelectStatus(viewer3d.value,true)
  skyLine.value = null;
})

</script>

<style scoped lang="scss">
.analyse-wrap {
  color: #fff;
  background-image: url("@/assets/images/map/tool.png");
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-size: cover;
  box-shadow: 0 0 8px 0 #057595;
}
.tab-container {
  display: flex;
  padding: 10px;
  flex-direction: column;
  column-count: 2;
  .svg-icon{
    margin-right: 5px;
  }
}
.height-value {
  margin: 10px;
  label {
    line-height: 40px;
  }
}
.input-value {
  float: right;
  width: 80%;
}
.tool-bottom {
  display: flex;
  justify-content: center;
  justify-items: center;
}
.tool-btn {
  margin-top: 20px;
  padding: 10px;
  width: 50%;
  background-color: #0f7dff;
  border-color: #fff;
  text-align: center;
  border-radius: 4px;
  color: #ffffff;
  &:hover {
    cursor: pointer;
    color: #fff;
    filter: brightness(110%) opacity(100%);
    transition: all 0.5s ease-in;
    background: linear-gradient(to bottom right, #00baff, #0f7dff);
  }
  svg {
    margin-right: 10px;
  }
}
.btn-box {
  display: flex;
  padding: 10px 10px;
  justify-content: center;
  width: 100%;
  .svg-icon {
    margin-right: 10px;
    width: 1.05em;
    height: 1.05em;
  }
  button {
    width: 100%;
  }
}
.sky-chat {
  width: 100%;
  height: 240px;
  padding: 10px 10px;
}
.close-span{
  float: right;
  margin-top: -17px;
  cursor: pointer;
}
:deep(.el-button){
  margin: 10px 0;
}

.model-action {
  position: absolute;
  top: 50px;
  left: -270px;
  width: 80%;
  background-color: #001d3bdb;
  border-radius: 5px;
  z-index: 999;
}
.action-title {
  padding: 15px;
  background-image: url("@/assets/images/map/queryResultTitle2.png");
  background-repeat: no-repeat;
  font-size: 14px;
}
.action-panel {
  padding: 10px;
}
.action-item {
  line-height: 35px;
}
.action-slider {
  float: right;
  width: 70%;
}
.action-move {
  margin: 5px 0;
}
.color-picker {
  float: right;
  margin-right: 44px;
}
.action-header{
  padding: 5px;
  margin: 10px 0;
  background-color: #113c56;
}
:deep(.el-select){
  width: 75%;
  float: right;
}
:deep(.el-input) {
  width: 65%;
  float: right;
}
</style>
