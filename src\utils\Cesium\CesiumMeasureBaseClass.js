/**
 * @name: MeasureBase
 * @description: 量测基类
 * @author: zyc
 * @time: 2024-05-20
 **/

export default class CesiumMeasureBaseClass {
  /**
   * 测量模式【measureMode】
   * measureMode=Space：空间量算
   * measureMode=CLAMP_TO_GROUND：贴地量算
   * @param option
   */
  constructor(option) {
    this.viewer3d = option.viewer3d
    this.measureMode = option.measureMode
    this.infoBox = option.infoBox

    // 测量模式
    this.heightMode = this.measureMode === 'Space' ? Cesium.HeightReference.NONE : Cesium.HeightReference.CLAMP_TO_GROUND
    this.clampToGround = this.measureMode !== 'Space'
    this.isPerPositionHeight = this.measureMode === 'Space'
    this.handler = undefined
  }
}
