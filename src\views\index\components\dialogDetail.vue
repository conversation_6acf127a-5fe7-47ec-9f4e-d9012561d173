<template>
  <div>
    <NewsView
      v-if="pageType === 0"
      :data="viewData"
      @closeViewDialog="handleCloseViewDialog"
    ></NewsView>
    <el-dialog
      v-else
      v-model="dialogVisible"
      title="查看详情"
      width="75%"
      :before-close="handleCloseViewDialog"
    >
      <PolicyView
        v-if="pageType === 1"
        :id="props.id"
        type="view"
        indexType="index"
      ></PolicyView>
      <StudyView
        v-if="pageType === 2"
        :id="props.id"
        type="view"
        indexType="index"
      ></StudyView>
    </el-dialog>
  </div>
</template>

<script setup>
import { onMounted, ref } from "vue";
import NewsView from "../../content/news/components/ViewDialog.vue";
import PolicyView from "../../content/policy/components/addDialog.vue";
import StudyView from "../../study/study/components/addDialog.vue";

const pageType = ref("");
const dialogVisible = ref(true);

const props = defineProps({
  type: Number,
  id: String,
});
const viewData = ref({
  show: false,
  data: {},
});
const emit = defineEmits(["closeDialogDetail"]);
const handleCloseViewDialog = () => {
  emit("closeDialogDetail", false);
};
onMounted(() => {
  console.log("props", props);
  pageType.value = props.type;
  if (pageType.value === 0) {
    viewData.value = {
      show: true,
      data: { id: props.id },
    };
  }
});
</script>

<style scoped>
</style>