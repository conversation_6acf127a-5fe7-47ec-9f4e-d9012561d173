<template>
  <div class="body">
    <div class="mb5">
      <el-button
        type="primary"
        size="mini"
        @click="submit"
        v-show="props.type !== 'view'"
        >发送</el-button
      >
      <el-button type="primary" size="mini" @click="handleClose"
        >关闭</el-button
      >
    </div>
    <div class="card">
      <div class="flx-center mt50">
        <div style="width: 60%">
          <el-form :model="form" label-width="120px">
            <el-form-item label="收件人">
              <div style="width: 100%">
                <div
                  class="mb5"
                  style="display: flex"
                  v-show="props.type !== 'view'"
                >
                  <SelectUserTransfer
                    text="选择人员"
                    @saveData="handleSelectUsers"
                    ref="refSelectUser"
                    :userArr="selectUserArr"
                  ></SelectUserTransfer>
                  <!-- <SelectUserTransfer
                    class="ml5"
                    text="删除人员"
                    @saveData="handleSelectUsers"
                    ref="refSelectUser"
                    :userArr="selectUserArr"
                  ></SelectUserTransfer> -->
                </div>
                <el-input
                  v-model="nameString"
                  :rows="3"
                  type="textarea"
                  style="flex: 1"
                  disabled
                />
              </div>
            </el-form-item>
            <el-form-item label="主题">
              <el-input
                v-show="props.type !== 'view'"
                v-model="form.title"
                style="width: 100%"
              />
              <div
                v-show="props.type === 'view'"
                style="padding: 5px 15px; border: 1px solid #e9eef3;width: 100%;border-radius: 8px;"
              >
                {{ form.title }}
              </div>
            </el-form-item>
            <el-form-item label="       ">
              <table class="styled-table">
                <tr>
                  <td></td>
                  <td>材料项</td>
                  <td>类别</td>
                  <td>必须</td>
                  <td>数量</td>
                  <td>文件</td>
                </tr>
                <tr>
                  <td>1</td>
                  <td>附件</td>
                  <td>消息管理</td>
                  <td>否</td>
                  <td>1</td>
                  <td>
                    <FileUpload
                      v-show="props.type !== 'view'"
                      fileSize="10"
                      :fileType="['doc', 'docx', 'pdf']"
                      :modelValue="form.affixList"
                      @update:modelValue="handleFileList"
                    ></FileUpload>
                    <div v-show="props.type === 'view'">
                      <div
                        v-for="obj in form.affixList"
                        :key="obj.id"
                        class="flx-center"
                      >
                        <div
                          class="cursor-pointer"
                          style="color: #409efc"
                          @click="onPreview(obj)"
                        >
                          {{ obj.name }}
                        </div>
                        <div class="ml5">
                          <el-tag
                            class="cursor-pointer"
                            type="primary"
                            @click="onPreview(obj)"
                            size="mini"
                          >
                            <el-icon class="mr5"> <View /></el-icon>阅览
                          </el-tag>
                          <el-tag
                            class="ml5 cursor-pointer"
                            type="success"
                            @click="downloadFile(obj)"
                            size="mini"
                          >
                            <el-icon class="mr5"> <Download /></el-icon>下载
                          </el-tag>
                        </div>
                      </div>
                    </div>
                  </td>
                </tr>
              </table>
            </el-form-item>
            <el-form-item label="消息">
              <div v-show="props.type !== 'view'">
                <Editor
                  minHeight="300"
                  @update:modelValue="handleEditor"
                  :modelValue="form.content"
                ></Editor>
              </div>

              <div
                v-show="props.type === 'view' && form.content"
                style="
                  padding: 15px;
                  border: 1px solid #e9eef3;
                  border-radius: 8px;
                  width: 100%;
                "
                v-html="form.content"
              ></div>
              <div v-show="props.type === 'view' && !form.content">无额外消息内容</div>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref } from "vue";
import SelectUserTransfer from "@/components/SelectUsers/index.vue";
import { addStudy, getStudyById, editStudy } from "@/api/study/study.js"; //addMessage
import { addMessage, getMessageDetail } from "@/api/message/index.js";
import { ElMessage } from "element-plus";
import { base64EncodeUnicode } from "@/utils/base64.js";
import { downloadFileFromUrl } from "@/utils/common.js";
import Editor from "@/components/Editor/index.vue";

const emit = defineEmits(["close"]);

const props = defineProps({
  id: String,
  type: String,
});

const form = ref({
  receivers: [
    {
      userId: "",
      nickName: "",
    },
  ],
  affixList: [],
  title: "",
  content: "",
});

// 选择人员处理
const selectUserArr = ref([]);
const refSelectUser = ref(null);
const nameString = ref("");
const handleSelectUsers = (e) => {
  nameString.value = e.map((item) => item.name).join(",");
  form.value.receivers = e.map((item) => {
    let obj = {
      userId: item.id,
      nickName: item.name,
    };
    return obj;
  });
};

// 处理文件选择
const handleFileList = (msg) => {
  form.value.affixList = msg.map((item) => {
    console.log("item", item);
    let obj = {
      name: item.name,
      url: item.url,
    };
    return obj;
  });
};

//富文本编辑器
const handleEditor = (msg) => {
  form.value.content = msg;
};

const handleClose = () => {
  form.value = {}
  emit("close", false);
};

const submit = () => {
  // console.log("form", form.value);
  if (props.type === "edit") {
    editStudy(form.value).then((res) => {
      if (res.code === 200) {
        ElMessage({
          message: "修改完成",
          type: "success",
        });
        handleClose();
      }
    });
  } else {
    addMessage(form.value).then((res) => {
      if (res.code === 200) {
        ElMessage({
          message: "新增完成",
          type: "success",
        });
        handleClose();
      }
    });
  }
};

// 根据id获取详情
const getDetail = () => {
  getMessageDetail(props.id).then((res) => {
    if (res.code === 200) {
      form.value = res.data;
      nameString.value = res.data.receivers
        .map((item) => item.nickName)
        .join(",");
      selectUserArr.value = res.data.receivers.map((item) => {
        let obj = {
          name: item.nickName,
          id: item.userId,
        };
        return obj;
      });
    }
  });
};

// 阅览
function onPreview(obj) {
  const url = import.meta.env.VITE_APP_FILE_SERVICE_URL + obj.url;
  window.open(
    `${import.meta.env.VITE_APP_FILE_PERVIEW_URL}?url=${base64EncodeUnicode(
      url
    )}`
  );
}
// 下载
function downloadFile(obj) {
  const url = import.meta.env.VITE_APP_FILE_SERVICE_URL + obj.url;
  downloadFileFromUrl(url, obj.name);
}

onMounted(() => {
  console.log("props.type", props.type);

  if (props.type === "view" || props.type === "edit") {
    getDetail();
  }
});

// beforeUnmount(()=>{
//   form.value = {}
// })
</script>

<style scoped lang="scss">
@import "@/styles/variables.module.scss";;
.body {
  width: 100%;
  height: $contentHeight;
  background-color: #e9eef3;
  overflow: hidden;
  padding: 5px;
  .card {
    height: calc($contentHeight - 42px);
    overflow: scroll;
  }
  .styled-table {
    width: 100%;
    border-collapse: collapse;
    border-radius: 8px;
  }
  .styled-table td {
    border: 1px solid #e9eef3;
    text-align: center;
    font-size: 14px;
    font-weight: 700;
    color: rgb(96, 98, 102);
    flex-shrink: 1;
  }
}
</style>
