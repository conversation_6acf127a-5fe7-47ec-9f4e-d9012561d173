import request from "@/utils/request";

// 分页查询航飞任务
export const flightTaskList = (query) => {
    return request({
        url: "/uav/flightHub2/flight_task/page",
        method: "get",
        params: query
    });
};

// 分页查询航飞任务
export const flightTaskListAll = (query) => {
    return request({
        url: "/uav/flightHub2/flight_task/page/all",
        method: "get",
        params: query
    });
};

// 获取飞行任务详情，开启航线任务飞行后，可通过此接口获取任务信息，任务运行过程中的状态，以及任务产生的媒体文件信息等。
export const getFlightTask = (task_uuid) => {
    return request({
        url: "/uav/flightHub2/flight_task/"+task_uuid,
        method: "get",
    });
};

// 获取飞行任务轨迹信息.获取飞行任务的轨迹信息。飞机任务完成后，可通过此接口获取飞行任务的轨迹信息，用于生成飞行轨迹。
export const getFlightTaskTrack = (task_uuid) => {
    return request({
        url: "/uav/flightHub2/flight_task/"+task_uuid+"/track",
        method: "get",
    });
};

// 获取飞行任务产生的媒体资源。当前支持 image、video、ppk 类型的资源。最多返回前 10000 个资源。
export const getFlightTaskMedia = (task_uuid) => {
    return request({
        url: "/uav/flightHub2/flight_task/"+task_uuid+"/media",
        method: "get",
    });
};

// 获取飞行任务产生的媒体资源。当前支持 image、video、ppk 类型的资源。最多返回前 10000 个资源。
export const apiPic = (task_uuid) => {
    return request({
        url: "/third_party/api/pic",
        method: "get",
    });
};
