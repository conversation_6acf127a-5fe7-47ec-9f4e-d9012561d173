define(["./when-b60132fc","./PrimitivePipeline-3db2c374","./createTaskProcessorWorker","./buildModuleUrl-9085faaa","./Cartographic-3309dd0d","./Check-7b2a090c","./Math-119be1a3","./Rectangle-dee65d21","./FeatureDetection-806b12f0","./Cartesian4-3ca25aab","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Event-16a2dfbf","./ComponentDatatype-c140a87d","./GeometryAttribute-c65394ac","./Cartesian2-db21342c","./GeometryAttributes-252e9929","./GeometryPipeline-7a733318","./AttributeCompression-0a087f75","./EncodedCartesian3-f1396b05","./IndexDatatype-8a5eead4","./IntersectionTests-0d6905a3","./Plane-a3d8b3d2","./WebMercatorProjection-a4b885f9"],(function(e,t,r,a,n,i,o,c,s,d,u,f,b,l,m,p,y,C,v,P,k,h,G,W){"use strict";var g={};function A(t){var r=g[t];return e.defined(r)||("object"==typeof exports?g[r]=r=require("Workers/"+t):require(["Workers/"+t],(function(e){g[r=e]=e}))),r}return r((function(r,a){for(var n=r.subTasks,i=n.length,o=new Array(i),c=0;c<i;c++){var s=n[c],d=s.geometry,u=s.moduleName;if(e.defined(u)){var f=A(u);o[c]=f(d,s.offset)}else o[c]=d}return e.when.all(o,(function(e){return t.PrimitivePipeline.packCreateGeometryResults(e,a)}))}))}));
