/**
 * 内容管理-政策法规
 */

import request from '@/utils/request'

// 查询政策法规管理列表
export function getPolicyList(query) {
    return request({
      url: '/document/policy/list',
      method: 'get',
      params: query
    })
  }

// 新增政策法规管理
export function addDocumentPolicy(data) {
  return request({
    url: '/document/policy',
    method: 'post',
    data: data
  })
}

/** 
 * 修改政策法规管理
 * @param {object} params 政策法规管理业务对象 doc_policy
 * @param {number} params.createDept 创建部门
 * @param {number} params.createBy 创建者
 * @param {object} params.createTime 创建时间
 * @param {number} params.updateBy 更新者
 * @param {object} params.updateTime 更新时间
 * @param {object} params.params 请求参数
 * @param {string} params.id id
 * @param {array} params.affixList 附件
 * @param {string} params.choiceType 选择类型(0:全部;1:部分)
 * @param {array} params.userIds 接收人
 * @param {string} params.title 标题
 * @param {string} params.type 业务类型
 * @param {string} params.docNum 文号
 * @param {object} params.sendTime 发文时间
 * @param {string} params.mechanism 发布机构
 * @param {string} params.applicableArea 适用区域
 * @param {string} params.level 效力级别
 * @param {string} params.abolish 废止记录
 * @param {string} params.agingState 时效状态
 * @param {string} params.createName 创建人名称
 * @param {number} params.createId 创建者id
 * @param {string} params.remark 备注
 * @param {string} params.startDay 开始日期
 * @param {string} params.endDay 结束日期
 * @param {string} params.flag 政策法规标记 0：政策 1：法规
 * @returns
 */
export function updataDocumentPolicy(params) {
  return request.put(`/document/policy`, params);
}

/** 
 * 获取政策法规管理详细信息
 * @param {string} id 主键
  * @returns
 */
export function getPolicyForId(id) {
  return request.get(`/document/policy/${id}`);
}

/** 
 * 删除政策法规管理
 * @param {string} ids 主键串
  * @returns
 */
export function dePolicy(ids) {
  return request.delete(`/document/policy/${ids}`);
}