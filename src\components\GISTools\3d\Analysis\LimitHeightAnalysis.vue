<template>
  <div
    v-if="isShow"
    class="analyse-wrap"
  >
    <!-- <Header :header-info="headerInfo" @closePanel="closePanel" /> -->
    <div class="tab-container">
      <el-button
        type="primary"
        @click="drawPolygon"
      >
        <svg-icon icon-class="huizhimian" />
        <span class="draw-btn">绘制限高范围</span>
      </el-button>
      <div class="tool-bottom">
        <div
          class="tool-btn"
          @click="startAnalyse"
        >
          <svg-icon icon-class="start_analyse" />
          <span>开始分析</span>
        </div>
        <div
          class="tool-btn"
          @click="clearResult"
        >
          <svg-icon icon-class="clear" />
          <span>清除</span>
        </div>
      </div>
    </div>

    <div
      v-if="isShowAction"
      ref="modelTool" class="model-action"
    >
      <div class="action-title">
        <h5>属性编辑</h5>
      </div>
      <div class="action-panel">
        <div class="action-item clearfix">
          <label
            for=""
            class="action-label"
          >高度(米)</label>
          <el-input
            v-model="limitHeight"
            placeholder="0<= h <=100"
            min="0"
            max="100"
            size="small"
            clearable
            @clear="clearLimitHeight"
          />
        </div>
        <div class="action-item clearfix">
          <label
            for=""
            class="action-label"
          >限制高度</label>
          <el-slider
            v-model="limitHeight"
            class="action-slider"
            :min="0"
            :max="100"
            :step="1"
            @change="handleAction()"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="LimitHeightAnalysis" text="限高分析">


import useMapViewStore from "@/store/modules/map/mapView.js"
import { createTooltip, setCursor, setLayerSelectStatus } from "@/utils/Cesium/CesiumTool.js"

defineProps({
  isShow: {
    type: Boolean,
    default: true
  },
  headerInfo: {
    type: Object
  }
})

const limitHeight = ref(30)
const isShowAction = ref(false)
const polygonEntity = ref(undefined)
const polygonPositions = ref([])
const terrainHeight = ref(1928)
// terrainHeight=ref(1940)
const limitHeightPrimitive = ref({})


const viewer3d = computed(()=>useMapViewStore().viewer3d)


watch(limitHeight,(value)=>{
  limitHeight.value = value === "" ? 0 : +value;
  handleAction()
})


const emits = defineEmits(["closePanel"])
const closePanel = ()=>{
  emits("closePanel")
}

/**
 * 绘制限高范围
 */
const drawPolygon = ()=>{
  // 采用空间模式
  const polygonHandler = new Cesium.DrawHandler(viewer3d.value,Cesium.DrawMode.Polygon,0);
  polygonHandler.activate();

  const toolTip = createTooltip(document.body);
  polygonHandler.activeEvt.addEventListener((isActive) => {

  })
  polygonHandler.movingEvt.addEventListener(windowPosition => {
    setCursor(viewer3d.value,'crosshair')
    toolTip.showAt(windowPosition,"单击开始绘制，右键结束绘制！")
  })
  polygonHandler.drawEvt.addEventListener(result => {
    // console.log("绘制结果：",result);
    polygonEntity.value = result.object
    polygonPositions.value = result.positions
    setCursor(viewer3d.value,'pointer')
    toolTip.setVisible(false)
  })
}

/**
 * 分析初始化参数
 */
const highLimitInit = ()=>{
  if (!polygonPositions.value.length){
    ElMessage.warning("请先绘制限高区域！")
    return
  }
  const scene = viewer3d.value.scene;
  const layer = scene.layers.find("fengceng") || viewer3d.value.scene.layers.find("fenghu")
  const instance = new Cesium.S3MInstanceCollection(scene._context);
  scene.primitives.add(instance)

  layer.clipLineColor = Cesium.Color.WHITE.withAlpha(0.0);

  polygonEntity.value = viewer3d.value.entities.add({
    id: 'polygonEntity',
    polygon: {
      hierarchy: polygonPositions.value,
      // perPositionHeight: true,
      height: terrainHeight.value,
      material: new Cesium.Color(1, 1, 0.20, 0.5),
      outline: true,
      outlineColor: Cesium.Color.RED
    }
  });

  polygonEntity.value.polygon.height = terrainHeight.value + limitHeight.value
  isShowAction.value = true
}

/**
 * 添加东西
 */
const addPrimitive = (drawPositions)=>{
  if (!polygonPositions.value.length){
    ElMessage.warning("请先绘制限高区域！")
    return
  }

  const polygonInstance = new Cesium.GeometryInstance({
    geometry: new Cesium.PolygonGeometry({
      polygonHierarchy: new Cesium.PolygonHierarchy(drawPositions),
      height: terrainHeight.value,
      extrudedHeight: 100
    }),
    attributes: {
      color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.fromCssColorString("rgba(23,92,239)").withAlpha(0.5)),
      show: new Cesium.ShowGeometryInstanceAttribute(true)
    }
  });

  limitHeightPrimitive.value = viewer3d.value.scene.primitives.add(
    new Cesium.ClassificationPrimitive({
      geometryInstances: polygonInstance,
      releaseGeometryInstances: true,
      classificationType: Cesium.ClassificationType.BOTH // SuperMap3D.ClassificationType.S3M_TILE // 3；SuperMap3D.ClassificationType.BOTH, // 2
    })
  )

  limitHeightPrimitive.value.readyPromise.then(primitive => {
    changeHeight();
  });
}


/**
 * 高度变化
 */
const changeHeight = ()=>{
  isShowAction.value = true
  const cartographic = Cesium.Cartographic.fromCartesian(limitHeightPrimitive.value._primitive._boundingSpheres[0].center); //弧度
  console.log("cartographic:",cartographic)
  const surface = Cesium.Cartesian3.fromRadians(cartographic.longitude, cartographic.latitude, cartographic);
  const offset = Cesium.Cartesian3.fromRadians(cartographic.longitude, cartographic.latitude, terrainHeight.value + limitHeight.value);
  const translation = Cesium.Cartesian3.subtract(offset, surface, new Cesium.Cartesian3());
  limitHeightPrimitive.value._primitive.modelMatrix = Cesium.Matrix4.fromTranslation(translation);
}
/**
 * 清除限制高度
 */
const clearLimitHeight = ()=>{
  limitHeight.value = 0
}


/**
 * 开始分析
 */
const startAnalyse = ()=> {
  setLayerSelectStatus(viewer3d.value,false)
  viewer3d.value.entities.removeAll();
  highLimitInit()
}

const handleAction = ()=>{
  console.log("change事件：")
  // this.changeHeight()
  polygonEntity.value.polygon.height = terrainHeight.value + limitHeight.value
}

const clearResult = ()=> {
  viewer3d.value.entities.removeAll();
  isShowAction.value = false
  if (polygonPositions.value.length){
    polygonPositions.value.length = 0
  }
}

onBeforeUnmount(()=>{
  clearResult();
  setLayerSelectStatus(viewer3d.value,true)
})

</script>

<style scoped lang="scss">
.analyse-wrap {
  color: #fff;
  background-image: url("@/assets/images/map/tool.png");
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-size: cover;
  box-shadow: 0 0 8px 0 #057595;
}
.tool-tabs {
  display: flex;
  justify-items: center;
  justify-content: space-between;
  background-color: #4f7287;
  span {
    display: inline-block;
    width: 50%;
    padding: 10px 5px;
    text-align: center;
    color: #fff;
    &:hover {
      cursor: pointer;
      background-color: #0f7dff;
      filter: brightness(110%);
      color: #fff;
      transition: background-color 0.25s;
    }
  }
}
.tab-active {
  color: #fff;
  background: linear-gradient(to bottom right, #00baff, #0f7dff);
}
.tool-tab-content {
  padding-top: 20px;
}
:deep(.el-form-item__label) {
  width: 60px !important;
  color: #b5b5b5;
}
.tab-container {
  display: flex;
  padding: 10px;
  flex-direction: column;
  column-count: 2;
}
.height-value {
  margin: 10px;
  label {
    line-height: 40px;
  }
}
.input-value {
  float: right;
  width: 80%;
}
.tool-bottom {
  display: flex;
  justify-content: space-between;
  justify-items: center;
}
.tool-btn {
  margin-top: 20px;
  padding: 10px;
  width: 48%;
  background-color: #0f7dff;
  border-color: #fff;
  text-align: center;
  border-radius: 4px;
  color: #ffffff;
  &:hover {
    cursor: pointer;
    color: #fff;
    filter: brightness(110%) opacity(100%);
    transition: all 0.5s ease-in;
    background: linear-gradient(to bottom right, #00baff, #0f7dff);
  }
  svg {
    margin-right: 10px;
  }
}
.draw-btn{
  margin-left: 10px;
}
.label {
  display: inline;
  padding: 0.01em 3.5em 0.01em 3.5em;
  font-size: 150%;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25em;
  line-height: 1.1;
}

.model-action {
  position: absolute;
  top: 50px;
  left: -270px;
  width: 80%;
  background-color: #001d3bdb;
  border-radius: 5px;
  z-index: 999;
}
.action-title {
  padding: 15px;
  background-image: url("@/assets/images/map/queryResultTitle2.png");
  background-repeat: no-repeat;
  font-size: 14px;
}
.action-panel {
  padding: 10px;
}
.action-item {
  line-height: 35px;
}
.action-slider {
  float: right;
  width: 70%;
}
.action-move {
  margin: 5px 0;
}
.color-picker {
  float: right;
  margin-right: 44px;
}
.action-header{
  padding: 5px;
  margin: 10px 0;
  background-color: #113c56;
}
:deep(.el-select){
  width: 75%;
  float: right;
}
:deep(.el-input) {
  width: 70%;
  float: right;
}
</style>
