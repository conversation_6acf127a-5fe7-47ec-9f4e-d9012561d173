import request from "@/utils/request";

// 查询第三方调用申请列表【后台管理功能】
export const partApiApplyList = (query) => {
    return request({
        url: "/uav/partApiApply/list",
        method: "get",
        params: query
    });
};

// 获取第三方调用申请详细信息
export const partApiApplyById = (id) => {
    return request({
        url: "/uav/partApiApply/" + id,
        method: "get",
    });
};

// 获取第三方调用申请详细信息
export const currentUser = (query) => {
    return request({
        url: "/uav/partApiApply/current-user",
        method: "get",
        params: query
    });
};

// 申请当前系统指定的API接口调用密钥
export const partApiApplyPartApiApply = (data) => {
    return request({
        url: "/uav/partApiApply/"+data,
        method: "post",
    });
};

// 导出第三方调用申请列表
export const partApiApplyExport = (data) => {
    return request({
        url: "/uav/partApiApply/export",
        method: "post",
        data: data
    });
};


// 修改第三方调用记录
export const putPartApiCall = (data) => {
    return request({
        url: "/uav/partApiCall",
        method: "put",
        data: data
    });
};

// 新增第三方调用记录
export const addPartApiCall = (data) => {
    return request({
        url: "/uav/partApiCall",
        method: "post",
        data: data
    });
};

// 导出第三方调用记录列表
export const exportPartApiCall = (data) => {
    return request({
        url: "/uav/partApiCall/export",
        method: "post",
        data: data
    });
};

// 获取第三方调用记录详细信息
export const getPartApiCallById = (id) => {
    return request({
        url: "/uav/partApiCall/"+id,
        method: "get",
    });
};

// 查询第三方调用记录列表
export const partApiCallList = (query) => {
    return request({
        url: "/uav/partApiCall/list",
        method: "get",
        params: query
    });
};

// 删除第三方调用记录
export const deleteApiCallList = (ids) => {
    return request({
        url: "/uav/partApiCall/"+ids,
        method: "delete",
    });
};
