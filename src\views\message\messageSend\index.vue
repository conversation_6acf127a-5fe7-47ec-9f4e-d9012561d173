<template>
  <div>
    <div class="table-box" v-if="!addDialogShow">
      <ProTable
        ref="proTable"
        :columns="columns"
        :request-api="getTableList"
        :init-param="initParam"
        :data-callback="dataCallback"
      >
        <!-- 表格 header 按钮 -->
        <template #tableHeader="">
          <el-button
            type="primary"
            icon="CirclePlus"
            @click="addHadleNews"
            >新增</el-button
          >
        </template>
        <template #receivers="scope">
          <div>{{ handleUser(scope.scope.row.receivers) }}</div>
        </template>
        <template #operation="scope">
          <el-button
            link
            type="success"
            icon="View"
            @click="viewDetail(scope.scope.row)"
            >查看</el-button
          >
          <!-- <el-button
            link
            type="primary"
            icon="Edit"
            @click="editDetail(scope.scope.row)"
            >修改</el-button
          > -->
          <el-button
            link
            type="danger"
            icon="Delete"
            @click="delDetail(scope.scope.row)"
            >删除</el-button
          >
        </template>
      </ProTable>
    </div>
    <!-- 组件 -->
    <AddDialog
      v-else
      @close="handleClose"
      :id="detailId"
      :type="type"
    ></AddDialog>
  </div>
</template>

<script setup name="content-news">
import { ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { getOutboxQuery, delMessage } from "@/api/message/index.js"; //getOutboxQuery
import ProTable from "@/components/ProTable/index.vue";
import AddDialog from "./components/addDialog.vue";


const proTable = ref();
//
const columns = reactive([
  { type: "index", label: "序号", width: 80 },
  // { type: "sort", label: "Sort", width: 80 },
  {
    prop: "receivers",
    label: "收件人",
    width: 150,
    // search: { el: "input" },
  },
  {
    prop: "title",
    label: "主题",
    search: { el: "input" },
  },
  {
    prop: "sendTime",
    label: "发送时间",
    width: 180,
    search: {
      el: "date-picker",
      props: { type: "daterange", valueFormat: "YYYY-MM-DD" },
    },
  },
  { prop: "operation", label: "操作", fixed: "right", width: 180 },
]);

// 如果表格需要初始化请求参数，直接定义传给 ProTable (之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({});

// dataCallback 是对于返回的表格数据做处理，如果你后台返回的数据不是 list && total 这些字段，可以在这里进行处理成这些字段
// 或者直接去 hooks/useTable.ts 文件中把字段改为你后端对应的就行
const dataCallback = (data) => {
  return {
    list: data.list,
    total: data.total,
  };
};

const getTableList = (params) => {
  let newParams = JSON.parse(JSON.stringify(params));
  newParams.sendTime && (newParams.sendTimeStart = newParams.sendTime[0]);
  newParams.sendTime && (newParams.sendTimeEnd = newParams.sendTime[1]);
  delete newParams.sendTime;
  return getOutboxQuery(newParams);
};

// 收件人显示
const handleUser = (users) => {
  if(users && users.length >0){
    let nameString = users.map(item => item.nickName).join(',')
    return nameString
  }
}

//
const type = ref("add");
const detailId = ref("");
// 新增数据
const addDialogShow = ref(false);
const addHadleNews = () => {
  addDialogShow.value = true;
};
const handleClose = (e) => {
  addDialogShow.value = e;
};

//view
const viewDetail = (row) => {
  addDialogShow.value = true;
  (type.value = "view"), (detailId.value = row.id);
};
//view
// const editDetail = (row) => {
//   addDialogShow.value = true;
//   (type.value = "edit"), (detailId.value = row.id);
// };

//删除
const delDetail = (row) => {
  ElMessageBox.confirm(`您确定删除 ${row.title}?`, "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      delMessage(row.id).then((res) => {
        if (res.code === 200) {
          ElMessage({
            type: "success",
            message: "删除成功",
          });
          proTable.value?.getTableList();
        } else {
          ElMessage({
            type: "danger",
            message: res.msg,
          });
        }
      });
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "操作已取消",
      });
    });
};

onMounted(() => {});
</script>

<style scoped lang="scss">
.content {
  padding: 15px;

  .el-row {
    margin-bottom: 15px;
  }
}
</style>