define(["./when-b60132fc","./EllipsoidGeometry-c2107184","./arrayFill-4513d7ad","./Check-7b2a090c","./buildModuleUrl-9085faaa","./Cartographic-3309dd0d","./Math-119be1a3","./Rectangle-dee65d21","./FeatureDetection-806b12f0","./Cartesian4-3ca25aab","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Event-16a2dfbf","./Cartesian2-db21342c","./ComponentDatatype-c140a87d","./GeometryAttribute-c65394ac","./GeometryAttributes-252e9929","./GeometryOffsetAttribute-fbeb6f1a","./IndexDatatype-8a5eead4","./VertexFormat-6446fca0"],(function(e,t,a,r,d,i,n,o,c,b,f,u,l,s,m,y,p,G,C,E){"use strict";return function(a,r){return e.defined(r)&&(a=t.EllipsoidGeometry.unpack(a,r)),t.EllipsoidGeometry.createGeometry(a)}}));
