<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="75%"
    :before-close="handleClose"
  >
    <div class="form flx-center">
      <el-form
        ref="formRef"
        :rules="formRules"
        :model="form"
        label-width="auto"
      >
        <el-form-item label="">
          <el-input v-model="form.id" />
        </el-form-item>
        <el-form-item label="">
          <div
            style="
              font-size: 30px;
              text-align: center;
              font-weight: 600;
              width: 100%;
            "
          >
            通知公告
          </div>
        </el-form-item>
        <el-form-item label="标题" prop="title">
          <el-input
            v-model="form.title"
            :rows="2"
            type="textarea"
            placeholder="请输入新闻标题"
          />
        </el-form-item>
        <el-form-item>
          <div class="flx-justify-between" style="width: 100%">
            <el-form-item
              label="发布人"
              class="mr15"
              style="flex: 1"
              prop="sendName"
            >
              <el-input v-model="form.sendName" />
            </el-form-item>
            <el-form-item
              label="新闻类别"
              class="mr15"
              style="flex: 1"
              prop="type"
            >
              <el-select v-model="form.type" placeholder="选择新闻类别">
                <el-option
                  v-for="item in dictTypeOptions"
                  :key="item.dictCode"
                  :label="item.dictLabel"
                  :value="item.dictValue"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="发布时间" style="flex: 1" prop="workDate">
              <el-date-picker
                v-model="form.workDate"
                type="date"
                placeholder="请选择发布时间"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </div>
        </el-form-item>
        <el-form-item label="附件">
          <FileUpload
            fileSize="10"
            :fileType="['doc', 'docx', 'pdf']"
            :modelValue="form.affixList"
            @update:modelValue="handleFileList"
          ></FileUpload>
        </el-form-item>
        <el-form-item label="查阅人员">
          <SelectUserTransfer
            text="选择人员"
            @saveData="handleSelectUsers"
            ref="refSelectUser"
            :userArr="selectUserArr"
          ></SelectUserTransfer>
          <el-checkbox
            class="ml15"
            v-model="form.choiceType"
            label="全员可见"
            size="large"
          />
        </el-form-item>
        <el-form-item label="已选择人员" v-if="nameString && !form.choiceType">
          <el-input
            type="textarea"
            :autosize="{ minRows: 1, maxRows: 4 }"
            v-model="nameString"
            readonly
          />
        </el-form-item>
        <el-form-item>
          <Editor
            minHeight="300"
            @update:modelValue="handleEditor"
            :modelValue="form.content"
          ></Editor>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          v-show="props.data.type === 'add'"
          type="primary"
          @click="submit"
        >
          保存
        </el-button>
        <el-button
          v-show="props.data.type === 'edit'"
          type="primary"
          @click="submitEdit"
        >
          编辑
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { onMounted, ref } from "vue";
import FileUpload from "@/components/FileUpload/index.vue";
import Editor from "@/components/Editor/index.vue";
import { getAuthNickname } from "@/utils/auth";
import { getDicts } from "@/api/system/dict/data.js";
import {
  addDocumentNews,
  updateDocumentNews,
  getNewsById,
} from "@/api/content/news.js";
import SelectUserTransfer from "@/components/SelectUsers/index.vue";
import { ElMessage } from "element-plus";

const dialogVisible = ref(false);

const props = defineProps({
  data: Object,
});

const emit = defineEmits(["closeMessage"]);
const handleClose = () => {
  dialogVisible.value = false;
  emit("closeMessage", dialogVisible.value);
};

const formRef = ref();
const form = ref({
  id: "",
  title: "",
  workDate: "",
  type: "",
  sendName: "",
  content: "",
  userIds: [],
  choiceType: false,
  affixList: [
    {
      name: "",
      url: "",
    },
  ],
});
const formRules = reactive({
  title: [
    {
      required: true,
      message: "请填写标题",
      trigger: "blur",
    },
  ],
  sendName: [
    {
      required: true,
      message: "请填写发布人",
      trigger: "blur",
    },
  ],
  type: [
    {
      required: true,
      message: "请填写新闻类别",
      trigger: "blur",
    },
  ],
  workDate: [
    {
      required: true,
      message: "请填写发布时间",
      trigger: "blur",
    },
  ],
});

// 处理文件选择
const handleFileList = (msg) => {
  form.value.affixList = msg.map((item) => {
    let obj = {
      name: item.name,
      url: item.url,
    };
    return obj;
  });
};

//富文本编辑器
const handleEditor = (msg) => {
  console.log("msg", msg);
  
  form.value.content = msg;
};

// 字典查询
const dictTypeOptions = ref([]);
const getTypeList = () => {
  getDicts("yw_content_news_type").then((res) => {
    console.log("dict", res);
    dictTypeOptions.value = res.data;
  });
};

// 选择人员处理
const selectUserArr = ref([]);
const refSelectUser = ref(null);
const nameString = ref("");
const handleSelectUsers = (e) => {
  nameString.value = e.map((item) => item.name).join(",");
  form.value.userIds = e.map((item) => item.id);
};

const editerShow = ref(true)
const getNewsDetail = () => {
  getNewsById(props.data.data.id).then((res) => {
    form.value.id = res.data.id;
    form.value.title = res.data.title;
    form.value.workDate = res.data.workDate;
    form.value.type = res.data.type;
    form.value.choiceType = res.data.choiceType === "0" ? true : false;
    form.value.userIds = res.data.users?.map((item) => item.userId);
    nameString.value = res.data.users?.map((item) => item.name).join(",");
    selectUserArr.value = res.data.users?.map((item) => {
      let obj = {
        name: item.name,
        id: item.userId,
      };
      return obj;
    });
    form.value.affixList = res.data.affixes[0].affixs;
    form.value.content = res.data.content?res.data.content:'';
  });
};

// submit
const submit = () => {
  form.value.choiceType = form.value.choiceType ? "0" : "1";
  console.log("baocun", form.value);
  addDocumentNews(form.value).then((res) => {
    if (res.code === 200) {
      ElMessage({
        message: "通知公告新增完成",
        type: "success",
      });
      dialogVisible.value = false;
      emit("closeMessage", dialogVisible.value);
    }
  });
};
// submitEdit
const submitEdit = () => {
  form.value.choiceType = form.value.choiceType ? "0" : "1";
  console.log("baocun", form.value);
  updateDocumentNews(form.value).then((res) => {
    if (res.code === 200) {
      ElMessage({
        message: "通知公告编辑完成",
        type: "success",
      });
      dialogVisible.value = false;
      emit("closeMessage", dialogVisible.value);
    }
  });
};

const title = ref("新增通知公告");
onMounted(() => {
  dialogVisible.value = props.data.show;
  if (props.data.type === "edit") {
    title.value = "编辑通知公告";
    getNewsDetail();
  } else {
    form.value = {};
  }
  
  form.value.sendName = getAuthNickname() === "admin" ? "管理员" : getAuthNickname();

  getTypeList();
});
</script>
<style>
  .form {
    background: #dce2f1;
    padding: 15px;
  }
</style>