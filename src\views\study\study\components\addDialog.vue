<template>
  <div class="body">
    <div class="mb5" v-if="props.indexType !== 'index'">
      <el-button
        type="primary"
        size="mini"
        @click="submit"
        v-show="props.type !== 'view'"
        >保存</el-button
      >
      <el-button type="primary" size="mini" @click="handleClose"
        >关闭</el-button
      >
    </div>
    <div class="card">
      <div
        style="
          font-size: 30px;
          text-align: center;
          font-weight: 600;
          width: 100%;
        "
      >
        学习栏
      </div>
      <div class="flx-center mt50">
        <div style="width: 65%">
          <el-form :model="form" label-width="auto">
            <el-form-item label="学习名称">
              <el-input
                v-show="props.type !== 'view'"
                v-model="form.title"
                style="width: 100%"
              />
              <div v-show="props.type === 'view'">{{ form.title }}</div>
            </el-form-item>
            <el-form-item label="学习内容">
              <el-input
                v-model="form.content"
                :rows="5"
                type="textarea"
                style="width: 100%"
                v-show="props.type !== 'view'"
              />
              <div v-show="props.type === 'view'">{{ form.content }}</div>
            </el-form-item>
            <el-form-item label="学习人员">
              <div
                class="flx-center"
                style="width: 100%"
                v-show="props.type !== 'view'"
              >
                <el-input
                  v-model="nameString"
                  :rows="3"
                  type="textarea"
                  style="flex: 1"
                  disabled
                />
                <div class="ml5">
                  <SelectUserTransfer
                    text="选择人员"
                    @saveData="handleSelectUsers"
                    ref="refSelectUser"
                    :userArr="selectUserArr"
                  ></SelectUserTransfer>
                  <el-checkbox
                    class="mt5"
                    v-model="form.choiceType"
                    label="全员可见"
                    size="large"
                  />
                </div>
              </div>
              <div v-show="props.type === 'view'" style="width: 100%;">
                <div v-show="form.choiceType">
                  <span>全员可见</span>
                  <!-- <el-tag class="ml5 cursor-pointer" v-show="!userShow" @click="userShow = true">展开</el-tag>
                  <el-tag class="ml5 cursor-pointer" v-show="userShow" @click="userShow = false">关闭</el-tag> -->
                </div>
                <div v-if="form.users && userShow" style="padding: 5px 10px;border: 1px solid #e9eef3;border-radius: 8px;width: 100%;">
                  <!-- <div>
                    <el-tag type="success" effect="dark" class="mr5">已读</el-tag>
                    <el-tag type="info" effect="dark" class="mr5">未读</el-tag>
                  </div> -->
                  <el-tag
                    effect="dark"
                    class="mr5"
                    :type="item.isRead === '0' ? 'info' : 'success'"
                    v-for="item in form.users"
                    :key="item.userId"
                    >{{ item.name }}</el-tag
                  >
                </div>
              </div>
            </el-form-item>
            <el-form-item label="       ">
              <table class="styled-table">
                <tr>
                  <td></td>
                  <td>材料项</td>
                  <td>类别</td>
                  <td>必须</td>
                  <td>数量</td>
                  <td>文件</td>
                </tr>
                <tr>
                  <td>1</td>
                  <td>附件</td>
                  <td>消息管理</td>
                  <td>否</td>
                  <td>1</td>
                  <td>
                    <FileUpload
                      v-show="props.type !== 'view'"
                      fileSize="10"
                      :fileType="['doc', 'docx', 'pdf']"
                      :modelValue="form.affixList"
                      @update:modelValue="handleFileList"
                    ></FileUpload>
                    <div v-show="props.type === 'view'">
                      <div
                        v-for="obj in form.affixList"
                        :key="obj.id"
                        class="flx-center"
                      >
                        <div
                          class="cursor-pointer"
                          style="color: #409efc"
                          @click="onPreview(obj)"
                        >
                          {{ obj.name }}
                        </div>
                        <div class="ml5">
                          <el-tag
                            class="cursor-pointer"
                            type="primary"
                            @click="onPreview(obj)"
                            size="mini"
                          >
                            <el-icon class=" mr5">
                              <View /></el-icon
                            >阅览
                          </el-tag>
                          <el-tag
                            class="ml5 cursor-pointer"
                            type="success"
                            @click="downloadFile(obj)"
                            size="mini"
                          >
                            <el-icon class=" mr5">
                              <Download /></el-icon
                            >下载
                          </el-tag>
                        </div>
                      </div>
                    </div>
                  </td>
                </tr>
              </table>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref } from "vue";
import SelectUserTransfer from "@/components/SelectUsers/index.vue";
import { addStudy, getStudyById, editStudy } from "@/api/study/study.js";
import { ElMessage } from "element-plus";
import { base64EncodeUnicode } from "@/utils/base64.js";
import { downloadFileFromUrl } from "@/utils/common.js";

const emit = defineEmits(["close"]);

const props = defineProps({
  id: String,
  type: String,
  indexType:String,
});

const form = ref({
  userIds: [],
  affixList: [],
  title: "",
  sendDate: "",
  content: "",
  remark: "",
  startDay: "",
  endDay: "",
});

// 选择人员处理
const selectUserArr = ref([]);
const refSelectUser = ref(null);
const nameString = ref("");
const handleSelectUsers = (e) => {
  nameString.value = e.map((item) => item.name).join(",");
  form.value.userIds = e.map((item) => item.id);
};

// 处理文件选择
const handleFileList = (msg) => {
  form.value.affixList = msg.map((item) => {
    console.log("item", item);
    let obj = {
      name: item.name,
      url: item.url,
    };
    return obj;
  });
};

const handleClose = () => {
  emit("close", false);
};

const submit = () => {
  form.value.choiceType = form.value.choiceType ? "0" : "1";
  console.log("form", form.value);
  if (props.type === "edit") {
    editStudy(form.value).then((res) => {
      if (res.code === 200) {
        ElMessage({
          message: "修改完成",
          type: "success",
        });
        handleClose();
      }
    });
  } else {
    addStudy(form.value).then((res) => {
      if (res.code === 200) {
        ElMessage({
          message: "新增完成",
          type: "success",
        });
        handleClose();
      }
    });
  }
};

// 根据id获取详情
const userShow = ref(true)
const getDetail = () => {
  getStudyById(props.id).then((res) => {
    if (res.code === 200) {
      form.value = res.data;
      form.value.affixList = form.value.affixes[0].affixs;
      nameString.value = res.data.users.map((item) => item.name).join(",");
      form.value.choiceType = res.data.choiceType === "0" ? true : false;
      form.value.userIds = res.data.users.map((item) => item.userId);
      selectUserArr.value = res.data.users.map((item) => {
        let obj = {
          name: item.name,
          id: item.userId,
        };
        return obj;
      });
    }
  });
};

// 阅览
function onPreview(obj) {
  const url = import.meta.env.VITE_APP_FILE_SERVICE_URL + obj.url;
  window.open(
    `${import.meta.env.VITE_APP_FILE_PERVIEW_URL}?url=${base64EncodeUnicode(
      url
    )}`
  );
}
// 下载
function downloadFile(obj) {
  const url = import.meta.env.VITE_APP_FILE_SERVICE_URL + obj.url;
  downloadFileFromUrl(url, obj.name);
}

onMounted(() => {
  console.log("props.type", props.type);

  if (props.type === "view" || props.type === "edit") {
    getDetail();
  }
});
</script>

<style scoped lang="scss">
@import "@/styles/variables.module.scss";;
.body {
  width: 100%;
  height: $contentHeight;
  background-color: #e9eef3;
  overflow: hidden;
  padding: 5px;
  .card {
    height: calc($contentHeight - 42px);
    overflow: scroll;
    background: #dce2f1;
  }
  .styled-table {
    width: 100%;
    border-collapse: collapse;
    border-radius: 8px;
  }
  .styled-table td {
    border: 1px solid #e9eef3;
    text-align: center;
    font-size: 14px;
    font-weight: 700;
    color: rgb(96, 98, 102);
    flex-shrink: 1;
  }
}
</style>
