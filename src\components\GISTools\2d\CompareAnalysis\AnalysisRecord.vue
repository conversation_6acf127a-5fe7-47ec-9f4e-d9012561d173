<template>
  <div v-loading="isLoading" style="height: 100%">
    <div class="search-page">
      <el-input v-model="queryParams.xmmc" placeholder="请输入项目名称" clearable>
        <el-button slot="append" icon="el-icon-search" @click="search" />
        <el-button slot="append" icon="el-icon-refresh" @click="reset" />
      </el-input>
    </div>
    <div v-if="tableData.length" style="height: 100%">
      <div class="result-div">
        <div v-for="(item, index) in tableData" :key="index" class="table-page"
             @click="rowClick(item)"
        >
          <div style="display: flex;justify-content: space-between;">
            <div style="font-weight: 800;">{{ item.xmmc }}</div>
            <el-tooltip class="item" effect="dark" content="删除"
                        placement="bottom-start"
            >
              <i class="el-icon-delete" @click.stop="deleteRecord(item)" />
            </el-tooltip>
          </div>
          <div style="display: flex;justify-content: space-between;">
            <div>分析人：{{ name }}</div>
            <div>{{ item.createTime }}</div>
          </div>
        </div>
        <div class="pagination">
          <el-pagination
            background
            layout="prev, pager, next"
            :total="queryParams.total"
            :pager-count="queryParams.pagerCount"
            :current-page="queryParams.pageNum"
            :page-size="queryParams.pageSize"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
    <div v-else class="no-data-tip">
      <i class="iconfont icon-zanwushuju" />
      <span>暂无数据</span>
    </div>
    <!-- 叠加分析组件   -->
    <transition name="collapse-fade">
      <div v-if="isCanShow" class="overlap-div">
        <Overlap
          v-if="isCanShow && isSmartSelect"
          :key="overlapId"
          :over-layers="overLayers"
          :result-layers="resultLayers"
          :classify-list="classifyList"
          :draw-area="drawArea"
          :select-layers="selectLayers"
          :land-price-obj="landPriceObj"
          :geometry="geometry"
        />
        <OverlapAnalysis
          v-if="isCanShow && isCompare"
          :key="overlapId"
          :result-layers="resultLayers"
          :over-layers="overLayers"
          :classify-list="classifyList"
          :draw-area="drawArea"
          :geometry="geometry"
        />
      </div>
    </transition>
  </div>
</template>

<script setup name="分析记录">


import { listOnanalysis } from "@/api/gis/onanalysis.js"
import eventBus from "@/utils/eventBus.js"
import { addGeoJSON2Map } from "@/utils/OpenLayers/olLayer.js"
import { removeAllLayer } from "@/utils/OpenLayers/olTool.js"

const props = defineProps({
  isCompare: Boolean,
  isSmartSelect: Boolean,
  analysisType: String
})

const { isCompare,isSmartSelect,analysisType } = toRefs(props)

const isLoading = ref(true)
const overlapId = ref('')
const locationTitle = ref('分析记录')
const tableData = ref([])
const queryParams = reactive({
  pageSize: 10,
  pagerCount: 5, // 分页组件页码按钮数量
  type: analysisType.value,
  xmmc: '',
  orderByColumn: 'createTime',
  isAsc: 'desc'
})
const isCanShow = ref(false)
const resultLayers = ref([])
const overLayers = ref([])
const classifyList = ref([])
const drawArea = ref(0)
const isCollapse = ref(false)
const jsonLayer = ref('')
const landPriceObj = ref({})
const selectLayers = ref({})
// 分析几何对象
const geometry = ref({})

const getAnalysisRecords = ()=> {
  listOnanalysis(queryParams).then(res => {
    tableData.value = res.rows.map(item => {
      item.createTime = item.createTime.slice(0, 10)
      return item
    })
    queryParams.total = res.total
    isLoading.value = false
  })
}

const handleCurrentChange = (val)=> {
  queryParams.pageNum = val
  getAnalysisRecords()
}

const search = ()=> {
  queryParams.pageNum = 1
  getAnalysisRecords()
}

const reset = ()=> {
  queryParams.pageNum = 1
  queryParams.xmmc = ''
  getAnalysisRecords()
}

const rowClick = (row)=> {
  eventBus.emit('showLoading', true)
  isCanShow.value = false
  overlapId.value = row.id
  removeLayer()
  addGeoJSON2Map(JSON.parse(row.geojson), undefined, undefined)
  const proinfo = JSON.parse(row.proinfo)
  resultLayers.value = proinfo.resultLayers
  overLayers.value = proinfo.overLayers
  classifyList.value = proinfo.classifyArr
  drawArea.value = +(proinfo.drawArea * 1e-4).toFixed(4)
  selectLayers.value = proinfo.selectLayers
  geometry.value = JSON.parse(row.geojson)
  landPriceObj.value = proinfo.landPriceObj
  eventBus.emit('showLoading', false)
  isCanShow.value = true
}

// 移除图层或几何对象
const removeLayer = ()=> {
  removeAllLayer()
}

const deleteRecord = (record)=> {
  // console.log(record)
  // this.$confirm('是否确定删除数据', '提示', {
  //   confirmButtonText: "确定",
  //   cancelButtonText: "取消",
  //   type: 'warning'
  // }).then((res) => {
  //   if (res === 'confirm') {
  //     this.removeLayer()
  //     delOnanalysis(record.id).then(res => {
  //       if (res.code === 200) {
  //         this.$message.success("删除成功")
  //         this.getAnalysisRecords()
  //       }
  //     })
  //   }
  // })
  //   .catch(e => {
  //     this.$message({
  //       type: 'success',
  //       message: '取消删除'
  //     });
  //   });
}

onBeforeMount(()=>{
  getAnalysisRecords()
})



</script>

<style scoped lang="scss">

</style>
