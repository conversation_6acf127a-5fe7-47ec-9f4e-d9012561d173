<template>
  <div class="compare-warp">
    <div class="compare-bar">
      <div
        class="analyse-btn"
        :class="isQuickAnalyse ? 'title-active' : ''"
        @click="quickAnalyse"
      >
        一键分析
      </div>
      <div
        class="analyse-btn"
        :class="isDataAnalyse ? 'title-active' : ''"
        @click="dataAnalyse"
      >
        条件分析
      </div>
      <div
        class="analyse-btn"
        :class="isScreenCompare ? 'title-active' : ''"
        @click="screenCompare"
      >
        分屏对比
      </div>
      <div
        class="analyse-btn"
        :class="isAnalyseRecord ? 'title-active' : ''"
        @click="analyseRecord"
      >
        分析记录
      </div>
    </div>
    <div
      v-if="isQuickAnalyse"
      class="data-analyse-div"
    >
      <div class="analyse quick-analysis">
        <div class="description">
          <p class="attention">注:</p>
          <p class="attention-word">
            通过上传shp文件（4490）或者手动绘制几何对象完成后，系统自动进行叠加分析操作！
          </p>
        </div>
        <div class="prj-info">
          <p class="title"><a style="margin-left:20px;line-height:35px">项目信息</a></p>
          <div class="form-item">
            <label>项目名称：</label>
            <el-input
              v-model="prjName"
              placeholder="请填写项目名称"
            />
          </div>
        </div>

        <p class="title"><a style="margin-left:20px;line-height:35px">分析范围设置</a></p>
        <div class="draw-div clearfix">
          <FileUpload
            ref="fileUpload"
            :title="'上传分析范围'"
            class="geometry-wrap"
            @getCoords="getCoords"
          />
          <el-button
            class="draw-btn"
            size="small"
            type="primary"
            @click="drawPolygon"
          >点击开始绘制</el-button>
        </div>
      </div>
      <div class="over-analyse-div">
        <AnalyseBtn
          :analyse-title="analyseTitle"
          :is-disabled="quickAnalyseStatues"
          @startAnalyse="generateOverlapAnalyseResult"
        />
      </div>
    </div>
    <div
      v-if="isDataAnalyse"
      v-loading="!layers.length"
      class="data-analyse-div"
    >
      <div class="analyse">
        <p class="title"><a style="margin-left:20px;line-height:35px">项目信息</a></p>
        <div class="form-item">
          <label>项目名称：</label>
          <el-input
            v-model="prjName"
            placeholder="请填写项目名称"
          />
        </div>
      </div>
      <div class="analyse">
        <p class="title"><a style="margin-left:20px;line-height:35px">分析范围设置</a></p>
        <div class="draw-div">
          <FileUpload
            ref="fileUpload"
            :title="'上传分析范围'"
            class="geometry-wrap"
            @getCoords="getCoords"
          />
          <el-button
            class="analyse-btn"
            size="small"
            type="primary"
            @click="drawPolygon"
          >点击开始绘制</el-button>
        </div>
      </div>
      <div class="con-select">
        <p class="title"><a style="margin-left:29px;line-height:35px">图层设置</a></p>
        <div class="select-layer">
          <div class="select-div clearfix">
            <div
              class="select"
              @click="isExpand = !isExpand"
            >
              <el-icon>
                <ArrowUp v-if="isExpand" />
                <ArrowDown v-else />
              </el-icon>
              <span>选择图层</span>
            </div>
            <div class="select select-all">
              <span>全选</span>
              <input
                ref="selectAll"
                class="select-all-input"
                type="checkbox"
                @click="selectAll"
              >
            </div>
          </div>
          <transition name="layer-fade">
            <div
              v-show="isExpand"
              :class="isExpand ? 'layer' : ''"
            >
              <ul>
                <li
                  v-for="(layer, index) in layers"
                  :key="index"
                >
                  <input
                    :ref="index"
                    class="checkbox"
                    type="checkbox"
                    @click="handleCheck(layer, index)"
                  >
                  <span class="layer-name">
                    {{ layer.label }}
                  </span>
                </li>
              </ul>
            </div>
          </transition>
        </div>
      </div>
      <div class="over-analyse-div">
        <AnalyseBtn
          :analyse-title="analyseTitle"
          :is-disabled="isDisabled"
          @startAnalyse="generateOverlapAnalyseResult"
        />
      </div>
    </div>
    <!--  地图分屏组件  -->
    <div
      v-if="isScreenCompare"
      class="screen-compare-div"
    >
      <SplitScreen />
    </div>
    <!--  分析记录  -->
    <div
      v-if="isAnalyseRecord"
      style="height: 100%"
    >
      <AnalysisRecord
        :analysis-type="'1'"
        :isCompare="isCompare"
      />
    </div>
    <!-- 叠加分析组件   -->
    <transition name="collapse-fade">
      <div
        v-if="isShow"
        class="overlap-div"
      >
        <div
          class="collapse-btn"
          @click="isCollapse = !isCollapse"
        />
        <OverlapAnalysis
          :over-layers="overLayers"
          :geometry="geometry"
          :classify-list="classifyList"
          :draw-area="drawArea"
          :prj-info="prjInfo"
        />
      </div>
    </transition>
    <MeasureArea
      :area="drawArea"
      class="draw-polygon"
    />
  </div>
</template>

<script setup name="CompareAnalysis">
import useMapViewStore from "@/store/modules/map/mapView.js";
import eventBus from "@/utils/eventBus.js";
import { getConfigKey } from "@/api/system/config.js";
import { getAnalysisTreeList } from "@/api/gis/analysis.js";
import { setCursor } from "@/utils/OpenLayers/olTool.js";
import FileUpload from "@/components/GISTools/2d/FileUpload/FileUpload.vue";
import AnalyseBtn from "@/components/GISTools/2d/Common/AnalysisBtn.vue";
import SplitScreen from "./SplitScreen";
import AnalysisRecord from "@/components/GISTools/2d/CompareAnalysis/AnalysisRecord.vue";
import OverlapAnalysis from "@/components/GISTools/2d/OverlapAnalysis/OverlapAnalysis";

import GeoJSON from "ol/format/GeoJSON";
import { PlanarArea } from "@/utils/mapFunction/geometryTool.js";
import OlDraw from "@/utils/OpenLayers/OlDraw";
import { addOnanalysis } from "@/api/gis/onanalysis.js";
import { removeAllLayer } from "@/utils/OpenLayers/olTool.js";
import { ElMessage } from "element-plus";
import MeasureArea from "@/components/GISTools/2d/Measure/MeasureArea.vue"

const { proxy } = getCurrentInstance();

const isCompare = ref(true);
const olDraw = ref(undefined);
const analyseTitle = ref("开始分析");
const isExpand = ref(true); // 默认为展开状态
const currentIndex = ref(-1);
const isShow = ref(false); // 是否展示图表
const layers = ref([]); // 叠加分析图层对象
const overLayers = ref([]); // 选择叠加分析图层
const classifyList = ref([]); // 地类列表

const isCollapse = ref(false); // 图表收缩状态
const isSelectAll = ref(false); // 是否全选
const isDataAnalyse = ref(false); // 是否点击数据分析按钮
const isScreenCompare = ref(false); // 是否点击分屏对比
const isQuickAnalyse = ref(true); // 快速分析
const isDisabled = ref(true); // 默认禁用状态

const drawArea = ref(0); // 绘制几何对象面积
const tempDataSource = ref("");
const isAnalyseRecord = ref(false);
const prjName = ref("");
const prjParam = ref("");
const prjInfo = ref({});
const geometry = ref({});

const map = computed(() => useMapViewStore().map);
const viewer2d = computed(() => useMapViewStore().viewer2d);

const changeStatues = computed(() => {
  const boolValue = (Object.keys(geometry.value).length && overLayers.value.length) ||
    (overLayers.value.length && Object.keys(geometry.value).length);
  return Boolean(boolValue);
});
const quickAnalyseStatues = computed(() => {
  console.log(geometry.value);
  // debugger
  return !(
    isQuickAnalyse.value &&
    Object.keys(geometry.value).length &&
    layers.value.length
  );
});

watch(changeStatues, (value) => {
  isDisabled.value = !value;
});

// 监控工具条清除按钮，响应坐标变化
const clearCoords = () => {
  eventBus.on("clearCoords", () => {
    geometry.value = {};
    drawArea.value = 0;
    isShow.value = false;
  });
};

// 请求叠加图层：获取空间分析服务下拉树列表
const getOverlapLayer = () => {
  getConfigKey("sys.id").then((res) => {
    getAnalysisTreeList(res.data.toString()).then((res) => {
      layers.value = res.data[0].children;
    });
  });
  getConfigKey("sys.map.tempDataSource").then((res) => {
    tempDataSource.value = res.msg;
  });
};

// 一键分析
const quickAnalyse = () => {
  clearAll();
  isQuickAnalyse.value = true;
  isDataAnalyse.value = false;
  isScreenCompare.value = false;
  isAnalyseRecord.value = false;
};
// 数据分析按钮
const dataAnalyse = () => {
  clearAll();
  isDataAnalyse.value = true;
  isQuickAnalyse.value = false;
  isScreenCompare.value = false;
  isAnalyseRecord.value = false;
  isSelectAll.value = false;
};
// 分屏对比按钮
const screenCompare = () => {
  clearAll();
  isScreenCompare.value = true;
  isDataAnalyse.value = false;
  isQuickAnalyse.value = false;
  isAnalyseRecord.value = false;
};
// 分析记录
const analyseRecord = () => {
  clearAll();
  isAnalyseRecord.value = true;
  isDataAnalyse.value = false;
  isQuickAnalyse.value = false;
  isScreenCompare.value = false;
};

// 绘制几何对象
const drawPolygon = () => {
  if (olDraw.value?.getDrawSource()) {
    // 结束上一次的绘制并清除绘制对象
    const drawSource = olDraw.value.getDrawSource();
    olDraw.value.clearGeometry(drawSource);
  }
  setCursor("crosshair");
  olDraw.value = new OlDraw(map.value, viewer2d.value);
  olDraw.value.drawGeometry("Polygon");
  const drawAction = olDraw.value.getDrawAction();
  drawAction.on("drawend", (evt) => {
    // 字符串
    geometry.value = new GeoJSON().writeGeometryObject(
      evt.feature.getGeometry()
    );
    const area = PlanarArea(geometry.value.coordinates, "Polygon");
    drawArea.value = +(area * 1e-4).toFixed(4);
    olDraw.value.deactiveAction(drawAction);
    setCursor("pointer");
  });
};

const getCoords = (param) => {
  geometry.value = param.geometry;
  // 叠加分析绘制总面积
  drawArea.value = +param.drawArea;
};

// 复选框点击事件
const handleCheck = (layer, index) => {
  // 选中复选框添加图层，取消复选框则移除图层
  isShow.value = false;
  if (proxy.$refs[index][0].checked) {
    overLayers.value.push(layer);
  } else {
    removeOverLayer(overLayers.value, layer);
  }
  // 判断是否全选
  if (overLayers.value.length === layers.value.length) {
    proxy.$refs["selectAll"].checked = true;
    isSelectAll.value = true;
  } else {
    isSelectAll.value = false;
    proxy.$refs["selectAll"].checked = false;
  }
};

// 全选
const selectAll = () => {
  isShow.value = false;
  overLayers.value = [];
  isSelectAll.value = !isSelectAll.value;
  for (let i = 0, length = layers.value.length; i < length; i++) {
    if (isSelectAll.value) {
      proxy.$refs[i][0].checked = true;
      // 数组深拷贝，防止改变this.overLayers改变this.layers
      overLayers.value = JSON.parse(JSON.stringify(layers.value));
    } else {
      proxy.$refs[i][0].checked = false;
      overLayers.value = [];
    }
  }
};

// 叠加分析服务
const generateOverlapAnalyseResult = () => {
  isShow.value = false;
  if (!Object.keys(geometry.value).length) {
    ElMessage.error("获取分析范围错误，请重新绘制或上传文件!");
    return;
  }
  if (isQuickAnalyse.value) {
    overLayers.value = layers.value;
  }
  // 每次开始叠加分析前清空数据
  // resultLayers.value = [];
  classifyList.value = [];
  // 页面加载蒙层
  eventBus.emit("showLoading", true);
  /**
   * @description:数据裁剪
   * @materialJSON: 用于裁剪的几何对象
   * @overLayerStr: 用于裁剪的数据集名称列表,用逗号分格
   * @tableTypeStr: 结果数据集列表，与tableNames对应，用逗号分格
   * @level：结果数据统计级别（地类数据统计层级）
   */
  const crs = { type: "name", properties: { name: "EPSG:4490" } };
  geometry.value.crs = crs;
  prjParam.value = {
    json: JSON.stringify(geometry.value),
    overLayerStr: JSON.stringify(overLayers.value),
    level: "2",
    prjName: prjName.value,
    dkName: "",
    type: "1"
  };
  console.log("分析参数：", prjParam.value);
  addOnanalysis(prjParam.value).then((res) => {
    if (res.code !== 200) {
      console.error("填入分析结果出错，" + res.msg);
    } else {
      console.log("分析结果：", res);
      classifyList.value = res.data.classifyArr;
      prjInfo.value = {
        prjName: prjName.value || "对比分析",
        region: "晋宁区",
        filename: res.data.filename
      };
      isShow.value = true;
      eventBus.emit("showLoading", false);
    }
  });
};

// 移除叠加图层
const removeOverLayer = (layers, layer) => {
  layers.forEach((item, layerIndex) => {
    if (item.label === layer.label) {
      layers.splice(layerIndex, 1);
    }
  });
};
// 移除绘制对象
const clearAll = () => {
  removeAllLayer();
  geometry.value = {};
  isShow.value = false;
};

onBeforeMount(() => {
  getOverlapLayer();
});

onMounted(() => {
  clearCoords();
});
</script>

<style scoped lang="scss">
@import "@/styles/map2d.scss";
@import "@/styles/variables.module.scss";


.compare-warp {
  width: $functionContentWidth;
  height: 100%;
  color: $mapMenuText;
}

.compare-bar {
  display: flex;
  flex-direction: row;
  background: url(/src/assets/icons/svg/u218.svg) no-repeat center center /
    cover;
  border-bottom: 1px solid #00cffa;
  justify-content: center;
  align-items: center;
  line-height: 45px;
  text-align: center;
  font-size: 14px;
  color: #ffffff;

  .analyse-btn {
    cursor: pointer;
    height: 45px;
    width: 50%;
  }
}

// 激活样式
.title-active {
  border-bottom: 2px solid #00cffa;
  color: #00cffa;
}

.data-analyse-div {
  position: relative;
  align-items: center;
  margin-top: 20px;
  height: calc(100vh - 150px);

  .quick-analysis {
    .description {
      background: #409eff21;
      margin-bottom: 10px;

      .attention {
        background: #0f7dff9e;
        font-size: 16px;
        font-weight: bold;
        padding: 5px;
        color: #fff;
        line-height: 24px;
      }

      .attention-word {
        padding: 10px;
        font-size: 14px;
        font-family: "Arial Negreta", "Arial Normal", "Arial";
        line-height: 23px;
        color: #fff;
      }
    }
  }
}

.iconfont {
  font-size: 20px;
  vertical-align: middle;
}

.draw-polygon {
  position: absolute;
  left: 320px;
  top: 140px;
  width: 200px;
  background: #031f3085;
  color: #fff;
}
.analyse {
  label {
    font-weight: 500;
  }
  .form-item {
    margin: 10px;
    color: #fff;
  }
  .el-input {
    width: 170px;
  }
  .title {
    color: #ffffff;
    font-size: 14px;
    font-weight: bold;
    color: #ffffff;
    background: url(/src/assets/images/yy_19.png) no-repeat center center /
      cover;
    line-height: 45px;
    width: 100%;
  }

  // .title:before {
  //   content: "|";
  //   color: #00cffa;
  //   width: 16px;
  //   font-size: 16px;
  //   font-weight: bold;
  // }

  .draw-div {
    margin-top: 10px;
    margin-left: 10px;

    .geometry-wrap {
      float: left;
      margin-right: 20px;
    }
  }
}

.con-select {

  .title {
    color: #fff;
    font-size: 14px;
    font-weight: bold;
    background: url(/src/assets/images/yy_19.png) no-repeat center center /
      cover;
  }
  .select-layer {
    margin: 10px;
    .select-div {
      padding-left: 6px;
      border-top-left-radius: 5px;
      border-top-right-radius: 5px;
      background-color: #0f7dffd9;
      color: #fff;

      .select {
        float: left;
        height: 30px;
        line-height: 30px;
      }

      .select-all {
        float: right;
        margin-right: 5px;
        span {
          font-size: 14px;
          margin-right: 5px;
          vertical-align: middle;
        }

        .select-all-input {
          width: 16px;
          height: 16px;
          vertical-align: middle;
        }
      }

      span {
        margin-left: 5px;
        //font-weight: bold;
      }
    }

    &:hover {
      cursor: pointer;
    }

    .layer {
      border: 1.5px solid #00cffaba;
      border-bottom-left-radius: 5px;
      border-bottom-right-radius: 5px;
      border-top: none;
      font-size: 14px;
      background: #409eff21;
      margin-top: -14px;
      ul {
        padding: 5px 0;
        max-height: 420px;
        overflow-y: auto;
        li {
          margin: 5px 0 5px -1px;
          padding: 5px 10px;

          &:hover {
            background: #00cffa;
          }
        }
      }

      .checkbox {
        width: 16px;
        height: 16px;
        vertical-align: bottom;
      }

      .layer-name {
        font-size: 14px;
        margin-left: 5px;
        color: #fff;
      }
    }

    .layer-fade-enter-active,
    .layer-fade-leave-active {
      transition: height ease-in 0.35s;
    }

    .layer-fade-enter,
    .layer-fade-leave-to {
      transition: height ease-out 0.35s;
      opacity: 0;
    }
  }
}

.over-analyse-div {
  margin: 20px;
}

.overlap-div {
  position: absolute;
  left: 1486px;
  top: 0;
  width: 18vw;
  height: 100%;
  z-index: 999;
  background: #ffffffcc;

  .collapse-btn {
    position: absolute;
    left: -36px;
    top: 368px;

    &:hover {
      cursor: pointer;
    }

    .overlap-expand {
      background: url("../../../../assets/images/icon_arrow.png") -19px -61px
        no-repeat;
      width: 38px;
      height: 61px;
      display: inline-block;
      background-size: cover;
      transform: rotate(-180deg);
    }

    .overlap-collapse {
      background: url("../../../../assets/images/icon_arrow.png") -19px 0px no-repeat;
      width: 38px;
      height: 61px;
      display: inline-block;
      background-size: cover;
      transform: rotate(-180deg);
    }
  }
}

</style>
