<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="6" :xs="24">
        <el-card class="box-card">
          <template v-slot:header>
            <div class="clearfix">
              <span>个人信息</span>
            </div>
          </template>
          <div>
            <div class="text-center">
              <!-- <userAvatar /> -->
            </div>
            <ul class="list-group list-group-striped">
              <li class="list-group-item">
                <svg-icon icon-class="user" />用户名称
                <div class="pull-right">{{ state.user.nickName }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="phone" />手机号码
                <div class="pull-right">{{ state.user.phonenumber }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="email" />用户邮箱
                <div class="pull-right">{{ state.user.email }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="tree" />所属部门
                <div class="pull-right">{{ state.user.deptName }}</div>
              </li>
              <!-- <li class="list-group-item">
                        <svg-icon icon-class="peoples" />所属角色
                        <div class="pull-right">{{ state.roleGroup }}</div>
                     </li> -->
              <li class="list-group-item">
                <svg-icon icon-class="date" />创建日期
                <div class="pull-right">{{ state.user.createTime }}</div>
              </li>
            </ul>
          </div>
        </el-card>
      </el-col>
      <el-col :span="18" :xs="24">
        <el-card>
          <template v-slot:header>
            <div class="clearfix">
              <span>基本资料</span>
            </div>
          </template>
          <el-tabs v-model="activeTab">
            <el-tab-pane label="修改密码" name="resetPwd">
              <resetPwd :id="state.user.userId" />
            </el-tab-pane>
            <el-tab-pane label="签名图片" name="userSign">
              <!-- <userAvatar /> -->
              <el-upload
                class="upload-demo"
                :action="uploadFileUrl"
                :on-preview="handlePreview"
                :on-remove="handleRemove"
                :on-success="handleUploadSuccess"
                :headers="headers"
                :limit="1"
                :show-file-list="false"
                list-type="picture"
              >
                <el-button type="primary">点击上传</el-button>
                <template #tip>
                  <div class="el-upload__tip">
                    点击上传图片，即自动替换签名图片
                  </div>
                </template>
              </el-upload>
              <!-- <el-button type="primary" @click="upSignImg">确认替换</el-button> -->
              <el-image
                class="mt15"
                style="width: 120px; height: 90px; border: 1px solid #dcdfe6"
                :src="signUrl"
                :preview-src-list="[signUrl]"
                :initial-index="4"
                fit="cover"
              />
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="Profile">
import { ref } from "vue";
import userAvatar from "./userAvatar";
import userInfo from "./userInfo";
import resetPwd from "./resetPwd";
import { getUserProfile, changeSignURL } from "@/api/system/user";
import { getToken } from "@/utils/auth";

const { proxy } = getCurrentInstance();

const uploadFileUrl = ref(
  import.meta.env.VITE_APP_BASE_API + "/document/affix/upload"
);
const headers = ref({
  Authorization: "Bearer " + getToken(),
  clientid: import.meta.env.VITE_APP_CLIENT_ID,
});

const signUrl = ref("");

const activeTab = ref("resetPwd");
const state = reactive({
  user: {},
  roleGroup: {},
  postGroup: {},
});

function getUser() {
  getUserProfile().then((response) => {
    state.user = response.data.user;
    state.roleGroup = response.roleGroup;
    state.postGroup = response.postGroup;
    signUrl.value =
      import.meta.env.VITE_APP_FILE_C_SERVICE_URL + response.data.user.signUrl;
  });
}

// 上传成功回调
function handleUploadSuccess(res, file) {
  signUrl.value = import.meta.env.VITE_APP_FILE_C_SERVICE_URL + res.data.url;
  changeSignURL(state.user.userId, res.data.url).then((response) => {
    if (response.code === 200) {
      proxy.$modal.msgSuccess("修改成功");
    }
  });
}
getUser();
</script>
<style lang="scss" scoped>
:deep(.el-card__header) {
  line-height: 30px;
  background: #cee6ff;
}
.list-group-item {
  border-top: none;
}
.list-group {
  line-height: 22px;
}
</style>
