import { getToken } from '@/utils/auth';
import { ElNotification } from 'element-plus';
import { useEventSource } from '@vueuse/core'

import useUserUnRead from '@/store/modules/unReadNum'


// 初始化
export const initSSE = (url) => {
  const appStore = useUserUnRead()

  url = url + '?Authorization=Bearer ' + getToken() + '&clientid=' + import.meta.env.VITE_APP_CLIENT_ID;
  const { data, error } = useEventSource(url, [], {
    autoReconnect: {
      retries: 10,
      delay: 3000,
      onFailed() {
        console.log('Failed to connect after 10 retries');
      }
    }
  });

  watch(error, () => {
    console.log('SSE connection error:', error.value);
    error.value = null;
  });

  watch(data, () => {
    if (!data.value) return;
    // if (JSON.parse(data.value).type === 'msg_unread_num') {
    //   // console.log('appStore', appStore.unreadNumber);
    //   appStore.update(JSON.parse(data.value).data)
    //   if (JSON.parse(data.value).data > 0) {
    //     ElNotification({
    //       title: '消息',
    //       message: `您有未读消息${JSON.parse(data.value).data}条`,
    //       type: 'success',
    //       duration: 3000
    //     });
    //   }
    // }
    // data.value = null;
  });
};
