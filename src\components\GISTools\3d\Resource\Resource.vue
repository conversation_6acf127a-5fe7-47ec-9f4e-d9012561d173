<template>
  <div class="resource-warp">
    <div class="simple-tabs">
      <div class="tab-header">
        <div
          class="tab-button"
          :class="{ active: activeTab === 'first' }"
          @click="activeTab = 'first'"
        >
          储备地块
        </div>
        <div
          class="tab-button"
          :class="{ active: activeTab === 'second' }"
          @click="activeTab = 'second'"
        >
          无人机管理
        </div>
      </div>
      <div class="tab-content">
        <div v-show="activeTab === 'first'">
          <div v-show="layerResources.length" class="map-tree-warp">
            <div class="search-div">
              <el-input
                v-model="filterText"
                class="search-input"
                placeholder="请输入图层名"
                clearable
              >
                <template v-slot:prefix>
                  <i class="el-input__icon el-icon-search" />
                </template>
              </el-input>
              <span class="search-btn" type="primary">
                <i class="el-icon-search" />
                搜索
              </span>
            </div>
            <el-tree
              ref="layerTree"
              v-loading="isShowLoading"
              class="map-tree"
              :data="layerResources"
              node-key="id"
              :props="layerDefaultProps"
              :default-expanded-keys="idArr"
              default-expand-all
              :default-checked-keys="checkedLayerResources"
              :filter-node-method="filterNode"
              show-checkbox
              icon-class="none"
              element-loading-text="正在加载"
              @check-change="handleCheckChange"
            >
              <template v-slot="{ node, data }">
                <span class="custom-tree-node">
                  <!--目录-->
                  <template
                    v-if="data.serviceValue === 'catalog' && data.children"
                  >
                    <span class="layer-parent">
                      <svg-icon
                        v-if="data.icon"
                        :icon-class="data.icon"
                        class-name="layer-parent-icon"
                      />
                      <span v-if="data.name.length > 10">
                        <el-tooltip
                          class="item"
                          effect="dark"
                          :content="data.name"
                          placement="top"
                        >
                          <span>{{
                            data.name.toString().substring(0, 10)
                          }}
                            ···</span>
                        </el-tooltip>
                      </span>
                      <span v-else>{{ data.name }}</span>
                      <span class="layer-count">{{
                        " (" + data.children.length + ")"
                      }}</span>
                    </span>
                  </template>
                  <!--子图层-->
                  <template v-else>
                    <span class="layer-children">
                      <span v-if="data.name.length > 10">
                        <el-tooltip
                          class="item"
                          effect="dark"
                          :content="data.name"
                          placement="top"
                        >
                          <span>{{
                            data.name.toString().substring(0, 10)
                          }}
                            ···</span>
                        </el-tooltip>
                      </span>
                      <span v-else>{{ data.name }}</span>
                    </span>
                  </template>
                </span>
              </template>
            </el-tree>
          </div>
          <div v-if="hasFloor" class="right-tool">
            <!--      <RightNav />-->
          </div>
        </div>
        <div v-show="activeTab === 'second'">
          <!-- 无人机管理内容 -->
          <slot name="second">
            <div class="button-container">
              <div class="button-row top-row">
                <el-button type="primary" class="button-item">上传shape文件</el-button>
                <el-button type="primary" class="button-item">面选</el-button>
              </div>
              <div class="button-row">
                <el-button type="primary" class="full-width-button">开始查询</el-button>
              </div>
            </div>

            <div class="map-list-warp">
              <div class="list-warp-div">
                <p>查询结果: 共{{ list.length }}条记录</p>
              </div>
              <div class="list-container">
                <div class="list-item" v-for="(a, index) in list" :key="index">
                  <div class="item-name">
                    {{ a.name }}
                  </div>
                  <div class="item-meta">
                    <div class="item-type">
                      {{ a.type }}
                    </div>
                    <div class="item-time">更新时间: {{ a.creatime }}</div>
                  </div>
                </div>
              </div>
            </div>
          </slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="Resource">
import { getConfigKey } from "@/api/system/config.js";
import { getCurrentInstance } from "vue";
import {
  addLayerByServiceType,
  addTerrain2Map,
  removeLayerByName
} from "@/utils/Cesium/CesiumLayer.js";
import useUserStore from "@/store/modules/user.js";
import { getTreeDataByRole } from "@/api/gis/layerTree.js";
// 组件实例代理对象
const { proxy } = getCurrentInstance();
/**
 * 属性数据
 */
const props = defineProps({
  // 三维视图对象
  // viewer3d: {
  //   type: Object,
  //   required: true,
  //   default: ()=>({})
  // },
  // 是否需要进行楼层查询
  hasFloor: {
    type: Boolean,
    required: true
  },
  // 是否只加载地形数据
  isOnlyAddTerrainLayer: {
    type: Boolean,
    default: true
  }
});
// 响应式解构属性数据
// const { viewer3d, hasFloor, isOnlyAddTerrainLayer } = toRefs(props);
const { hasFloor, isOnlyAddTerrainLayer } = toRefs(props);

// const viewer3d = toRefs(props.viewer3d)
const viewer3dProp = inject("viewer3d");
const viewer3d = ref(viewer3dProp.viewer3d);

/**
 * 响应数据
 */
const layerResources = ref([]);
const checkedLayerResources = ref([]);
const filterText = ref("");
const idArr = ref([]);
const isShowLoading = ref(true);
const activeTab = ref("first");

// Vue 3 的响应式数据
const list = ref([
  {
    name: "北京市朝阳区地块",
    type: "Matrice 3D",
    creatime: "2025-05-12"
  },
  {
    name: "上海市浦东新区地块",
    type: "Matrice 4",
    creatime: "2025-04-28"
  },
  {
    name: "广州市天河区地块",
    type: "Matrice 4D",
    creatime: "2025-03-15"
  },
  {
    name: "深圳市南山区地块",
    type: "Mavic 3",
    creatime: "2025-05-02"
  },
  {
    name: "成都市武侯区地块",
    type: "Matrice 3D",
    creatime: "2025-02-18"
  },
  {
    name: "杭州市西湖区地块",
    type: "Matrice 3D",
    creatime: "2025-01-30"
  },
  {
    name: "武汉市江汉区地块",
    type: "Matrice 4D",
    creatime: "2025-01-11"
  },
  {
    name: "西安市雁塔区地块",
    type: "Mavic 3",
    creatime: "2025-01-10"
  }
]);

// 点击事件处理
const handleItemClick = (item) => {
  console.log("点击了地块:", item.name);
  // 这里可以添加点击后的逻辑，如跳转详情页等
};
const layerDefaultProps = reactive({
  disabled: (data, node) => !data?.children && !data.url
});
// 计算属性
const isAddDefaultLayer = computed(() => {
  return viewer3d.value && layerResources.value.length > 0;
});

// 用户信息
const store = useUserStore();

/**
 * 图层树数据过滤
 */
watch(filterText, (value) => {
  proxy.$refs.layerTree.filter(value);
});
/**
 * 监听是否能够添加默认加载图层
 */
watch(isAddDefaultLayer, (value) => {
  if (value) {
    setDefaultCheckeds(layerResources.value);
  }
});

/**
 * 树节点数据过滤
 */
const filterNode = (value, node) => {
  if (!value) return true;
  return node.name.indexOf(value) > -1;
};

/**
 * 获取图层数据列表
 */
const getLayers = async () => {
  const getSysId = await getConfigKey("sys.id");
  const map3dId = await getConfigKey("sys.map3d.id");
  const userId = store.user.userId;
  getTreeDataByRole({
    userId: userId,
    systemId: getSysId.data,
    mapId: map3dId.data
  }).then((res) => {
    layerResources.value = res.data[0].children;

    isShowLoading.value = false;
    // 默认展开前两个图层
    if (layerResources.value.length > 1) {
      idArr.value.push(layerResources.value[0].id);
      idArr.value.push(layerResources.value[1].id);
    }
  });
};

/**
 * 设置默认显示的图层
 * 在WebGPU模式下显式设置勾选默认图层，直接设置【checkedLayerResources】无效
 * @param data：图层列表
 */
const setDefaultCheckeds = (data) => {
  data.forEach((item) => {
    if (item?.visible === "1") {
      // 默认加载底图和显示图层
      if (isOnlyAddTerrainLayer.value) {
        if (item.url && item.serviceValue === "terrain") {
          checkedLayerResources.value.push(item.id);
          proxy.$refs.layerTree.setChecked(item.id, true, true);
          addTerrain2Map(item.url, viewer3d);
        }
      } else {
        if (item.url) {
          checkedLayerResources.value.push(item.id);
          proxy.$refs.layerTree.setChecked(item.id, true, true);
          addLayerByServiceType(item, viewer3d);
        }
      }
    }
    item.children?.length && setDefaultCheckeds(item.children);
  });
};
/**
 * 根据复选框状态决定图层的显示与隐藏
 * @param node
 * @param isChecked
 */
const handleCheckChange = (node, isChecked) => {
  if (isChecked) {
    if (node?.children) {
      idArr.value.push(node.id);
    }
    if (node.url) {
      addLayerByServiceType(node, viewer3d.value);
    }
    if (node.serviceValue !== "catalog" && !node.url) {
      // sysMessageTip("error",LAYERERROR["INVALID_URL"])
      throw new Error("无效的服务地址：INVALID_URL");
    }
  } else {
    idArr.value.pop();
    removeLayerByName(node.serviceValue, node, viewer3d.value);
  }
};

onBeforeMount(() => {
  getLayers();
});
onMounted(() => {
  // console.log("viewer3d~~~~Mounted阶段:",viewer3d)
});
</script>

<style scoped lang="scss">
@import "@/styles/map2d.scss";
@import "@/styles/variables.module.scss";
.simple-tabs {
  width: 100%;
  height: 100%; /* 容器占满父元素高度 */
  display: flex;
  flex-direction: column;
  font-family: system-ui, sans-serif;
}
.button-container {
  padding: 10px;
}
.tab-header {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1px;
  flex-shrink: 0; /* 防止头部被压缩 */
  width: 100%;
  height: 48px;
  background: url("@/assets/icons/svg/u218.svg") no-repeat center center / cover;
}

.tab-button {
  padding: 12px 0;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #ffffff;
}

.tab-button:hover {
  color: #00ffff;
}

.tab-button.active {
  color: #00ffff;
  font-weight: 500;
  position: relative;
}

.tab-button.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: #00ffff;
}

.tab-content {
  flex: 1;
  width: 100%;
  min-width: 100%;
  border-right: 1px solid #1b76d1;
  border-top: none;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  /* 滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    background-color: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 19, 46, 0.68);
    border-radius: 3px;

    &:hover {
      background-color: rgba(5, 55, 206, 0.35);
    }
  }

  &::-webkit-scrollbar-track {
    background-color: rgba(0, 19, 46, 0.3);
  }
}

.parent-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: fit-content;
}

.button-row {
  display: flex;
}

.top-row {
  gap: 16px;
}

.button-item {
  width: 160px;
  background: #2d8befa6;
  border: 1px solid #2d8cf0;
  &:hover {
    background: #0d6ccf; // 鼠标悬停时变亮
    box-shadow: 0 2px 8px rgba(0, 122, 242, 0.5);
  }

  &:active {
    background: #055cb9; // 鼠标按下时变暗
    transform: translateY(1px);
  }
}

.full-width-button {
  width: calc(100% + 16px);
  margin-top: 15px;
  background: #067af2;
  border: none;
  transition: all 0.3s ease;
  &:hover {
    background: #0d6ccf; // 鼠标悬停时变亮
    box-shadow: 0 2px 8px rgba(0, 122, 242, 0.5);
  }

  &:active {
    background: #055cb9; // 鼠标按下时变暗
    transform: translateY(1px);
  }
}

.map-list-warp {
  padding-top: 10px;
  font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
  width: 100%;
  box-sizing: border-box;
}

.list-warp-div {
  height: 35px;
  font-size: 16px;
  color: #f1f3f6;
  font-weight: 500;
  background-image: url(/src/assets/images/yy_15.png);
  background-size: 100% 100%; // 自适应容器大小
  background-repeat: no-repeat; // 不重复
  background-position: center; // 居中显示
  display: flex; /* 弹性布局 */
  align-items: center; /* 垂直居中 */
  // border-left: 4px solid #409eff;  /* 左侧装饰条 */
  &.with-icon {
    padding-left: 12px;
    display: flex;
    align-items: center;

    .search-icon {
      margin-right: 8px;
      font-size: 14px;
      color: inherit; // 继承父元素颜色
    }
  }
}
.list-warp-div p {
  margin-left: 48px;
}
.list-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
  padding: 10px;
}

.list-item {
  background: #003382ae;
  border-radius: 6px;
  padding: 10px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  border: none; // 默认无边框

  // 渐变边框伪元素
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 6px;
    border: 1px solid transparent; // 1px透明边框
    background: linear-gradient(to bottom, #00f4fd, #195ec9) border-box;
    -webkit-mask:
      linear-gradient(#fff 0 0) padding-box,
      linear-gradient(#fff 0 0);
    -webkit-mask-composite: destination-out;
    mask-composite: exclude;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover::before,
  &:active::before {
    opacity: 1; // 鼠标经过和点击时显示1px渐变边框
  }

  &:hover {
    background: #0048a8;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &:active {
    background: #002c6e;
    transform: translateY(0);
  }
}

.list-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  opacity: 0.9;
}

.item-name {
  font-size: 16px;
  color: #f8f8f8;
  font-weight: 600;
  margin-bottom: 12px; /* 增加下边距 */
  padding-left: 4px; /* 添加左侧内边距 */
}

.item-meta {
  display: grid;
  grid-template-columns: 1fr auto; /* 使用网格布局确保对齐 */
  align-items: center;
  gap: 8px;
}

.item-type {
  color: #f4f5f6;
  // padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
  margin-left: 4px;
  display: flex;
  align-items: center;

  &::before {
    content: "";
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url("@/assets/images/wrj.png") no-repeat center;
    background-size: contain;
    margin-right: 4px;
  }
}
.item-time {
  font-size: 13px;
  color: #eaecf1;
  text-align: right;
}

/* 新增：确保类型标签与名称左对齐 */
.item-type-wrapper {
  display: flex;
  align-items: center;
}
.resource-warp {
  width: $functionContentWidth;
  height: $resourceContentHeight;
  color: $mapMenuText;
}
// 激活样式
.title-active {
  color: #00cffa;
  border-bottom: 4px solid #00cffa;
  background-image: linear-gradient(#d5e5fd, #ffffff);
}
.resource-btn-bar {
  display: flex;
  flex-direction: row;
  border-bottom: 1px solid #00d2ff;
  justify-content: center;
  align-items: center;
  line-height: 45px;
  text-align: center;
  font-size: 14px;
  font-weight: bold;
  .resource-btn {
    cursor: pointer;
    height: 45px;
    width: 50%;
  }
}
.map-tree-warp {
  display: flex;
  flex-direction: column;
  padding: 10px;
  height: 100%;
  .search-div {
    display: flex;
    justify-content: space-between;
    .search-btn {
      display: inline-block;
      width: 100px;
      height: 40px;
      line-height: 38px;
      text-align: center;
      color: #fff;
      background: #067af2;
      border: 1px solid #067af2;
      border-top-right-radius: 3px;
      border-bottom-right-radius: 3px;
      &:hover {
        cursor: pointer;
        filter: brightness(95%);
      }
    }
  }
  .map-tree {
    margin-top: 20px;
    height: 85%;
    width: 100%;
    color: #fff;
    background: none;
    overflow-y: auto;
  }
  .map-region-btn {
    height: 45px;
    width: 50%;
  }
  .map-tree-filter {
    width: 95%;
  }
  .layer-parent {
    font-size: 16px;
    font-family: "Arial Negreta", "Arial Normal", "Arial";
    // font-weight: 550;
    .layer-parent-icon {
      height: 16px;
      width: 16px;
      color: #0f7dff;
    }
    .layer-count {
      color: #fff849;
    }
  }
  .layer-children {
    font-size: 14px;
  }
}

.el-divider--vertical {
  height: 2em;
  margin: 0 !important;
}
.map-tree {
  .el-tree-node {
    position: relative;
    padding-left: 16px; // 缩进量
  }

  .el-tree-node__children {
    padding-left: 16px; // 缩进量
  }

  .el-tree-node__content {
    padding-left: 0px !important;
  }

  // 竖线
  .el-tree-node::before {
    content: "";
    height: 100%;
    width: 1px;
    position: absolute;
    left: -3px;
    top: -26px;
    border-width: 1px;
    border-left: 1px dashed #52627c;
  }

  // 当前层最后一个节点的竖线高度固定
  .el-tree-node:last-child::before {
    height: 38px; // 可以自己调节到合适数值
  }

  // 横线
  .el-tree-node::after {
    content: "";
    width: 24px;
    height: 20px;
    position: absolute;
    left: -3px;
    top: 12px;
    border-width: 1px;
    border-top: 1px dashed #52627c;
  }

  // 去掉最顶层的虚线，放最下面样式才不会被上面的覆盖了
  & > .el-tree-node::after {
    border-top: none;
  }

  & > .el-tree-node::before {
    border-left: none;
  }

  // 展开关闭的icon
  .el-tree-node__expand-icon {
    font-size: 16px;
    // 叶子节点（无子节点）
    &.is-leaf {
      color: transparent;
      // display: none; // 也可以去掉
    }
  }
}
.right-tool {
  position: absolute;
  top: 67px;
  left: 350px;
  width: 150px;
  background: #031f3085;
  color: #d9d9d9;
  border-radius: 5px;
}
:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #0f7dff;
  border-color: #0f7dff;
}
:deep(.el-tree-node:focus > .el-tree-node__content) {
  background-color: #0256a2;
  color: #ffffff;
  .svg-icon {
    color: #ffffff;
  }
  .layer-count {
    color: #fff;
  }
}
:deep(.el-tree-node__content:hover) {
  background-color: #074b8b;
}
:deep(.el-input__inner) {
  //background-color: #ffffff45;
  //// border: 1px solid #00cffa;
  //border: 1px solid #409EFF;
  //color: #fff;
  //border-top-right-radius: 0;
  //border-bottom-right-radius: 0;
}
:deep(.el-input__prefix) {
  color: #ffffff;
}
:deep(.el-loading-mask) {
  background-color: #f9f9f9 !important;
  filter: brightness(95%) opacity(25%);
}
</style>
