define(["./EllipsoidTangentPlane-1dfa0a87","./buildModuleUrl-9085faaa","./Cartesian2-db21342c","./Cartographic-3309dd0d","./when-b60132fc","./Rectangle-dee65d21","./TerrainEncoding-bc895579","./Math-119be1a3","./FeatureDetection-806b12f0","./OrientedBoundingBox-3b145304","./RuntimeError-4a5c8994","./GeometryAttribute-c65394ac","./WebMercatorProjection-a4b885f9","./createTaskProcessorWorker","./Check-7b2a090c","./Cartesian4-3ca25aab","./IntersectionTests-0d6905a3","./Plane-a3d8b3d2","./Event-16a2dfbf","./AttributeCompression-0a087f75","./ComponentDatatype-c140a87d","./WebGLConstants-4ae0db90","./PolygonPipeline-d83979ed","./earcut-2.2.1-20c8012f","./EllipsoidRhumbLine-30b5229b"],(function(e,t,i,a,n,r,o,s,u,h,d,c,l,g,m,p,v,E,I,f,C,T,M,b,N){"use strict";var x=Uint16Array.BYTES_PER_ELEMENT,S=Int32Array.BYTES_PER_ELEMENT,P=Uint32Array.BYTES_PER_ELEMENT,w=Float32Array.BYTES_PER_ELEMENT,B=Float64Array.BYTES_PER_ELEMENT;function y(e,t,i){i=n.defaultValue(i,s.CesiumMath);for(var a=e.length,r=0;r<a;++r)if(i.equalsEpsilon(e[r],t,s.CesiumMath.EPSILON12))return r;return-1}var R=new a.Cartographic,A=new a.Cartesian3,_=new a.Cartesian3,F=new a.Cartesian3,W=new u.Matrix4;function O(e,t,r,o,h,d,c,l,g,m){for(var p=c.length,v=0;v<p;++v){var E=c[v],I=E.cartographic,f=E.index,C=e.length,T=I.longitude,M=I.latitude;M=s.CesiumMath.clamp(M,-s.CesiumMath.PI_OVER_TWO,s.CesiumMath.PI_OVER_TWO);var b=I.height-d.skirtHeight;d.hMin=Math.min(d.hMin,b),a.Cartographic.fromRadians(T,M,b,R),g&&(R.longitude+=l),g?v===p-1?R.latitude+=m:0===v&&(R.latitude-=m):R.latitude+=l;var N=d.ellipsoid.cartographicToCartesian(R);e.push(N),t.push(b),r.push(i.Cartesian2.clone(r[f])),o.length>0&&o.push(o[f]),u.Matrix4.multiplyByPoint(d.toENU,N,A);var x=d.minimum,S=d.maximum;a.Cartesian3.minimumByComponent(A,x,x),a.Cartesian3.maximumByComponent(A,S,S);var P=d.lastBorderPoint;if(n.defined(P)){var w=P.index;h.push(w,C-1,C,C,f,w)}d.lastBorderPoint=E}}return g((function(g,m){g.ellipsoid=r.Ellipsoid.clone(g.ellipsoid),g.rectangle=r.Rectangle.clone(g.rectangle);var p=function(r,g,m,p,v,E,I,f,C,T){var M,b,N,U,Y,k;n.defined(p)?(M=p.west,b=p.south,N=p.east,U=p.north,Y=p.width,k=p.height):(M=s.CesiumMath.toRadians(v.west),b=s.CesiumMath.toRadians(v.south),N=s.CesiumMath.toRadians(v.east),U=s.CesiumMath.toRadians(v.north),Y=s.CesiumMath.toRadians(p.width),k=s.CesiumMath.toRadians(p.height));var V,L,H=[b,U],D=[M,N],G=c.Transforms.eastNorthUpToFixedFrame(g,m),j=u.Matrix4.inverseTransformation(G,W);f&&(V=l.WebMercatorProjection.geodeticLatitudeToMercatorAngle(b),L=1/(l.WebMercatorProjection.geodeticLatitudeToMercatorAngle(U)-V));var z=new DataView(r),q=Number.POSITIVE_INFINITY,J=Number.NEGATIVE_INFINITY,K=_;K.x=Number.POSITIVE_INFINITY,K.y=Number.POSITIVE_INFINITY,K.z=Number.POSITIVE_INFINITY;var Q=F;Q.x=Number.NEGATIVE_INFINITY,Q.y=Number.NEGATIVE_INFINITY,Q.z=Number.NEGATIVE_INFINITY;var X,Z,$=0,ee=0,te=0;for(Z=0;Z<4;++Z){var ie=$;X=z.getUint32(ie,!0),ie+=P;var ae=s.CesiumMath.toRadians(180*z.getFloat64(ie,!0));ie+=B,-1===y(D,ae)&&D.push(ae);var ne=s.CesiumMath.toRadians(180*z.getFloat64(ie,!0));ie+=B,-1===y(H,ne)&&H.push(ne),ie+=2*B;var re=z.getInt32(ie,!0);ie+=S,ee+=re,te+=3*(re=z.getInt32(ie,!0)),$+=X+P}var oe=[],se=[],ue=new Array(ee),he=new Array(ee),de=new Array(ee),ce=f?new Array(ee):[],le=new Array(te),ge=[],me=[],pe=[],ve=[],Ee=0,Ie=0;for($=0,Z=0;Z<4;++Z){X=z.getUint32($,!0);var fe=$+=P,Ce=s.CesiumMath.toRadians(180*z.getFloat64($,!0));$+=B;var Te=s.CesiumMath.toRadians(180*z.getFloat64($,!0));$+=B;var Me=s.CesiumMath.toRadians(180*z.getFloat64($,!0)),be=.5*Me;$+=B;var Ne=s.CesiumMath.toRadians(180*z.getFloat64($,!0)),xe=.5*Ne;$+=B;var Se=z.getInt32($,!0);$+=S;var Pe=z.getInt32($,!0);$+=S,$+=S;for(var we=new Array(Se),Be=0;Be<Se;++Be){var ye=Ce+z.getUint8($++)*Me;R.longitude=ye;var Re=Te+z.getUint8($++)*Ne;R.latitude=Re;var Ae=z.getFloat32($,!0);if($+=w,0!==Ae&&Ae<T&&(Ae*=-Math.pow(2,C)),Ae*=6371010*E,R.height=Ae,-1!==y(D,ye)||-1!==y(H,Re)){var _e=y(oe,R,a.Cartographic);if(-1!==_e){we[Be]=se[_e];continue}oe.push(a.Cartographic.clone(R)),se.push(Ee)}we[Be]=Ee,Math.abs(ye-M)<be?ge.push({index:Ee,cartographic:a.Cartographic.clone(R)}):Math.abs(ye-N)<be?pe.push({index:Ee,cartographic:a.Cartographic.clone(R)}):Math.abs(Re-b)<xe?me.push({index:Ee,cartographic:a.Cartographic.clone(R)}):Math.abs(Re-U)<xe&&ve.push({index:Ee,cartographic:a.Cartographic.clone(R)}),q=Math.min(Ae,q),J=Math.max(Ae,J),de[Ee]=Ae;var Fe=m.cartographicToCartesian(R);ue[Ee]=Fe,f&&(ce[Ee]=(l.WebMercatorProjection.geodeticLatitudeToMercatorAngle(Re)-V)*L),u.Matrix4.multiplyByPoint(j,Fe,A),a.Cartesian3.minimumByComponent(A,K,K),a.Cartesian3.maximumByComponent(A,Q,Q);var We=(ye-M)/(N-M);We=s.CesiumMath.clamp(We,0,1);var Oe=(Re-b)/(U-b);Oe=s.CesiumMath.clamp(Oe,0,1),he[Ee]=new i.Cartesian2(We,Oe),++Ee}for(var Ue=3*Pe,Ye=0;Ye<Ue;++Ye,++Ie)le[Ie]=we[z.getUint16($,!0)],$+=x;if(X!==$-fe)throw new d.RuntimeError("Invalid terrain tile.")}ue.length=Ee,he.length=Ee,de.length=Ee,f&&(ce.length=Ee);var ke=Ee,Ve=Ie,Le={hMin:q,lastBorderPoint:void 0,skirtHeight:I,toENU:j,ellipsoid:m,minimum:K,maximum:Q};ge.sort((function(e,t){return t.cartographic.latitude-e.cartographic.latitude})),me.sort((function(e,t){return e.cartographic.longitude-t.cartographic.longitude})),pe.sort((function(e,t){return e.cartographic.latitude-t.cartographic.latitude})),ve.sort((function(e,t){return t.cartographic.longitude-e.cartographic.longitude}));var He=1e-5;if(O(ue,de,he,ce,le,Le,ge,-He*Y,!0,-He*k),O(ue,de,he,ce,le,Le,me,-He*k,!1),O(ue,de,he,ce,le,Le,pe,He*Y,!0,He*k),O(ue,de,he,ce,le,Le,ve,He*k,!1),ge.length>0&&ve.length>0){var De=ge[0].index,Ge=ke,je=ve[ve.length-1].index,ze=ue.length-1;le.push(je,ze,Ge,Ge,De,je)}ee=ue.length;var qe,Je=t.BoundingSphere.fromPoints(ue);n.defined(p)&&(qe=h.OrientedBoundingBox.fromRectangle(p,q,J,m));for(var Ke=new o.EllipsoidalOccluder(m).computeHorizonCullingPointPossiblyUnderEllipsoid(g,ue,q),Qe=new e.AxisAlignedBoundingBox(K,Q,g),Xe=new o.TerrainEncoding(Qe,Le.hMin,J,G,!1,f),Ze=new Float32Array(ee*Xe.getStride()),$e=0,et=0;et<ee;++et)$e=Xe.encode(Ze,$e,ue[et],he[et],de[et],void 0,ce[et]);var tt=ge.map((function(e){return e.index})).reverse(),it=me.map((function(e){return e.index})).reverse(),at=pe.map((function(e){return e.index})).reverse(),nt=ve.map((function(e){return e.index})).reverse();return it.unshift(at[at.length-1]),it.push(tt[0]),nt.unshift(tt[tt.length-1]),nt.push(at[0]),{vertices:Ze,indices:new Uint16Array(le),maximumHeight:J,minimumHeight:q,encoding:Xe,boundingSphere3D:Je,orientedBoundingBox:qe,occludeePointInScaledSpace:Ke,vertexCountWithoutSkirts:ke,indexCountWithoutSkirts:Ve,westIndicesSouthToNorth:tt,southIndicesEastToWest:it,eastIndicesNorthToSouth:at,northIndicesWestToEast:nt}}(g.buffer,g.relativeToCenter,g.ellipsoid,g.rectangle,g.nativeRectangle,g.exaggeration,g.skirtHeight,g.includeWebMercatorT,g.negativeAltitudeExponentBias,g.negativeElevationThreshold),v=p.vertices;m.push(v.buffer);var E=p.indices;return m.push(E.buffer),{vertices:v.buffer,indices:E.buffer,numberOfAttributes:p.encoding.getStride(),minimumHeight:p.minimumHeight,maximumHeight:p.maximumHeight,boundingSphere3D:p.boundingSphere3D,orientedBoundingBox:p.orientedBoundingBox,occludeePointInScaledSpace:p.occludeePointInScaledSpace,encoding:p.encoding,vertexCountWithoutSkirts:p.vertexCountWithoutSkirts,indexCountWithoutSkirts:p.indexCountWithoutSkirts,westIndicesSouthToNorth:p.westIndicesSouthToNorth,southIndicesEastToWest:p.southIndicesEastToWest,eastIndicesNorthToSouth:p.eastIndicesNorthToSouth,northIndicesWestToEast:p.northIndicesWestToEast}}))}));
