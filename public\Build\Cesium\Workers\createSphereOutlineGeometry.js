define(["./when-b60132fc","./Cartographic-3309dd0d","./Check-7b2a090c","./EllipsoidOutlineGeometry-2cff2e8c","./Math-119be1a3","./arrayFill-4513d7ad","./buildModuleUrl-9085faaa","./Rectangle-dee65d21","./FeatureDetection-806b12f0","./Cartesian4-3ca25aab","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Event-16a2dfbf","./ComponentDatatype-c140a87d","./GeometryAttribute-c65394ac","./Cartesian2-db21342c","./GeometryAttributes-252e9929","./GeometryOffsetAttribute-fbeb6f1a","./IndexDatatype-8a5eead4"],(function(e,i,t,a,r,n,o,s,d,l,c,u,p,m,y,f,b,G,k){"use strict";function v(t){var r=e.defaultValue(t.radius,1),n={radii:new i.Cartesian3(r,r,r),stackPartitions:t.stackPartitions,slicePartitions:t.slicePartitions,subdivisions:t.subdivisions};this._ellipsoidGeometry=new a.EllipsoidOutlineGeometry(n),this._workerName="createSphereOutlineGeometry"}v.packedLength=a.EllipsoidOutlineGeometry.packedLength,v.pack=function(e,i,t){return a.EllipsoidOutlineGeometry.pack(e._ellipsoidGeometry,i,t)};var E=new a.EllipsoidOutlineGeometry,O={radius:void 0,radii:new i.Cartesian3,stackPartitions:void 0,slicePartitions:void 0,subdivisions:void 0};return v.unpack=function(t,r,n){var o=a.EllipsoidOutlineGeometry.unpack(t,r,E);return O.stackPartitions=o._stackPartitions,O.slicePartitions=o._slicePartitions,O.subdivisions=o._subdivisions,e.defined(n)?(i.Cartesian3.clone(o._radii,O.radii),n._ellipsoidGeometry=new a.EllipsoidOutlineGeometry(O),n):(O.radius=o._radii.x,new v(O))},v.createGeometry=function(e){return a.EllipsoidOutlineGeometry.createGeometry(e._ellipsoidGeometry)},function(i,t){return e.defined(t)&&(i=v.unpack(i,t)),v.createGeometry(i)}}));
