import request from "@/utils/request";

// 飞行日志
export const statisticFlightLog = (query) => {
    return request({
        url: "/uav/flighthub2/statistic/flight_log",
        method: "get",
        params: query
    });
};

//执行飞行次数统计
export const flightNumMonthOfYear = (query) => {
    return request({
        url: "/uav/flighthub2/statistic/flight_num/month_of_year",
        method: "get",
        params: query
    });
};

//首页
export const statisticHome = (query) => {
    return request({
        url: "/uav/flighthub2/statistic/home",
        method: "get",
        params: query
    });
};
