<template>
  <div class="main-content">
    <transition>
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="调用方api" prop="callApi">
              <el-input v-model="queryParams.callApi" placeholder="请输入调用方api" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="调用方ip" prop="callIp">
              <el-input v-model="queryParams.callIp" placeholder="请输入调用方ip" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="调用方ip地址名称" prop="callIpName">
              <el-input v-model="queryParams.callIpName" placeholder="请输入调用方ip地址名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card class="result-wrap">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['uav:partApiCall:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['uav:partApiCall:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['uav:partApiCall:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['uav:partApiCall:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="partApiCallListFly" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="调用方api" align="center" prop="callApi" />
        <el-table-column label="调用方ip" align="center" prop="callIp" />
        <el-table-column label="调用方ip地址名称" align="center" prop="callIpName" />
        <el-table-column label="申请人" align="center" prop="createBy" />
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['uav:partApiCall:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['uav:partApiCall:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改第三方调用记录对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="partApiCallFormRef" :model="form" :rules="rules" label-width="140px">
        <el-form-item label="私钥" prop="privateKey">
          <el-input v-model="form.privateKey" placeholder="请输入私钥" />
        </el-form-item>
        <el-form-item label="调用方api" prop="callApi">
          <el-input v-model="form.callApi" placeholder="请输入调用方api" />
        </el-form-item>
        <el-form-item label="调用方ip" prop="callIp">
          <el-input v-model="form.callIp" placeholder="请输入调用方ip" />
        </el-form-item>
        <el-form-item label="调用方ip地址名称" prop="callIpName">
          <el-input v-model="form.callIpName" placeholder="请输入调用方ip地址名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="PartApiCall">
import { partApiCallList, getPartApiCallById, deleteApiCallList, addPartApiCall, putPartApiCall } from '@/api/uav/partApiApply.js';

const { proxy } = getCurrentInstance();

const partApiCallListFly = ref([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref();
const partApiCallFormRef = ref();

const dialog = reactive({
  visible: false,
  title: ''
});

const initFormData = {
  id: undefined,
  privateKey: undefined,
  callApi: undefined,
  callIp: undefined,
  callIpName: undefined,
}
const data = reactive({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    privateKey: undefined,
    callApi: undefined,
    callIp: undefined,
    callIpName: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "主键，唯一标识不能为空", trigger: "blur" }
    ],
    privateKey: [
      { required: true, message: "私钥不能为空", trigger: "blur" }
    ],
    callApi: [
      { required: true, message: "调用方api不能为空", trigger: "blur" }
    ],
    callIp: [
      { required: true, message: "调用方ip不能为空", trigger: "blur" }
    ],
    callIpName: [
      { required: true, message: "调用方ip地址名称不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询第三方调用记录列表 */
const getList = async () => {
  loading.value = true;
  const res = await partApiCallList(queryParams.value);
  partApiCallListFly.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  partApiCallFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加第三方调用记录";
}

/** 修改按钮操作 */
const handleUpdate = async (row) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getPartApiCallById(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改第三方调用记录";
}

/** 提交按钮 */
const submitForm = () => {
  partApiCallFormRef.value?.validate(async (valid) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await putPartApiCall(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addPartApiCall(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除第三方调用记录编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await deleteApiCallList(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('uav/partApiCall/export', {
    ...queryParams.value
  }, `partApiCall_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
<style lang="scss" scoped>
.main-content{
    padding: 10px;
}
.result-wrap{
    margin-top: 10px;
}
</style>
