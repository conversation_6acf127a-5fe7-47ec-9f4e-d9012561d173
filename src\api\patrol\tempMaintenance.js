import request from "@/utils/request";

/**
 * 查询临时管护列表
 * @param query
 * @returns {*}
 */

export const listTemporaryManage = (query) => {
  return request({
    url: "/patrol/temporaryManage/list",
    method: "get",
    params: query,
  });
};

/**
 * 查询临时管护列表
 * @param query
 * @returns {*}
 */

export const listTemporaryManageAll = (query) => {
  return request({
    url: "/patrol/temporaryManage/listAll",
    method: "get",
    params: query,
  });
};

/**
 * 查询临时管护详细
 * @param id
 */
export const getTemporaryManage = (id) => {
  return request({
    url: "/patrol/temporaryManage/" + id,
    method: "get",
  });
};

/**
 * 新增临时管护
 * @param data
 */
export const addTemporaryManage = (data) => {
  return request({
    url: "/patrol/temporaryManage",
    method: "post",
    data: data,
  });
};

/**
 * 修改临时管护
 * @param data
 */
export const updateTemporaryManage = (data) => {
  return request({
    url: "/patrol/temporaryManage",
    method: "put",
    data: data,
  });
};

/**
 * 删除临时管护
 * @param id
 */
export const delTemporaryManage = (id) => {
  return request({
    url: "/patrol/temporaryManage/" + id,
    method: "delete",
  });
};

export const getLandDetail = (id) => {
  return request({
    url: "/patrol/land/" + id,
    method: "get",
  })
}

export const updateLand = (data) => {
  return request({
    url: "/patrol/land",
    method: "put",
    data: data,
  })
}
export const addLand = (data) => {
  return request({
    url: "/patrol/land",
    method: "post",
    data: data,
  })
}

