<template>
    <div class="container">
        <div class="btn-submit">
            <el-button>打印</el-button>
            <el-button type="primary" v-loading="showLoding" @click="onSubmit">发送</el-button>
        </div>
        <el-row :gutter="20">
            <el-col :span="18" class="card form" v-loading="showLoding">
                <div class="form-content">
                    <h1 style="text-align: center">昆明市土地矿产储备中心收文处理笺</h1>
                    <el-form ref="receiptFormRef" :model="form" :rules="rules" label-width="80px">
                        <div class="flx-justify-between">
                            <el-form-item label="案卷号" prop="num">
                                <el-input v-model="form.num" placeholder="请输入案卷号" />
                            </el-form-item>
                            <el-form-item label="收文日期" prop="receiptTime">
                                <el-date-picker clearable v-model="form.receiptTime" type="datetime"
                                    value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择收文日期">
                                </el-date-picker>
                            </el-form-item>
                        </div>
                        <table>
                            <tr>
                                <td colspan="4">
                                    <h2 style="text-align: center">来文基本情况</h2>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <el-form-item label="来文类别" prop="category" label-width="150px">
                                        <el-select v-model="form.category" placeholder="来文类别" clearable>
                                            <el-option v-for="dict in dicts.doc_category" :key="dict.dictValue"
                                                :label="dict.dictLabel" :value="dict.dictValue" />
                                        </el-select>
                                    </el-form-item>
                                </td>
                                <td>
                                    <el-form-item label="密 级" prop="secretLevel" label-width="150px">
                                        <el-select v-model="form.secretLevel" placeholder="密 级" clearable>
                                            <el-option v-for="dict in dicts.doc_secret" :key="dict.dictValue"
                                                :label="dict.dictLabel" :value="dict.dictValue" />
                                        </el-select>
                                    </el-form-item>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <el-form-item label="急 缓" prop="urgentDegree" label-width="150px">
                                        <el-select v-model="form.urgentDegree" placeholder="急 缓" clearable>
                                            <el-option v-for="dict in dicts.doc_urgency" :key="dict.dictValue"
                                                :label="dict.dictLabel" :value="dict.dictValue" />
                                        </el-select>
                                    </el-form-item>
                                </td>
                                <td>
                                    <el-form-item label="发布方式" prop="releaseMethod" label-width="150px">
                                        <el-select v-model="form.releaseMethod" placeholder="发布方式" clearable>
                                            <el-option v-for="dict in dicts.doc_release" :key="dict.dictValue"
                                                :label="dict.dictLabel" :value="dict.dictValue" />
                                        </el-select>
                                    </el-form-item>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="4">
                                    <el-form-item label="阅办类型" prop="readType" label-width="150px">
                                        <el-select v-model="form.readType" placeholder="阅办类型" clearable>
                                            <el-option v-for="dict in readTypeList" :key="dict.dictValue"
                                                :label="dict.label" :value="dict.value" />
                                        </el-select>
                                    </el-form-item>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="4">
                                    <el-form-item label="来文单位" prop="unitName" label-width="150px">
                                        <el-input v-model="form.unitName" placeholder="请输入来文单位" />
                                    </el-form-item>
                                    <!-- <el-form-item label="来文单位" prop="unitName" label-width="150px">
                                        <el-select v-model="form.unitName" placeholder="来文单位" clearable>
                                            <el-option v-for="dict in dicts.doc_unit_name" :key="dict.dictValue"
                                                :label="dict.dictLabel" :value="dict.dictValue" />
                                        </el-select>
                                    </el-form-item> -->
                                </td>
                            </tr>
                            <tr>
                                <td colspan="4">
                                    <el-form-item label="来文标题" prop="title" label-width="150px">
                                        <el-input v-model="form.title" placeholder="请输入来文标题" />
                                    </el-form-item>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="4">
                                    <el-form-item label="收文备注" prop="remark" label-width="150px">
                                        <el-input type="textarea" v-model="form.remark" placeholder="请输入备注" />
                                    </el-form-item>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="4">
                                    <h2 style="text-align: center">来文审批意见</h2>
                                </td>
                            </tr>
                            <tr v-for="(item, index) in tmpJson" :key="index">
                                <td v-if="item.docData === '1'" colspan="4">
                                    <el-form-item :label="item.nodeName" label-width="150px">
                                        <el-input :disabled="true" type="textarea" v-model="item.data[0].auditIdea"
                                            placeholder="请输入意见" />
                                        <el-form-item>
                                            <el-form-item label="签字">
                                                <el-input :disabled="true" type="textarea"
                                                    v-model="item.data[0].auditSign" placeholder="请输入备注" />
                                            </el-form-item>
                                            <el-form-item label="日期">
                                                <el-date-picker :disabled="true" clearable
                                                    v-model="item.data[0].auditTime" type="datetime"
                                                    value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择日期">
                                                </el-date-picker>
                                            </el-form-item>
                                        </el-form-item>
                                    </el-form-item>
                                </td>
                                <td v-if="item.docData === '2'" colspan="4">
                                    <div class="el-form-item__label" style="width: 150px;border: 0px;">{{ item.nodeName
                                        }}
                                    </div>
                                    <el-table :data="item.data" border style="width: 100%" height="200">
                                        <el-table-column prop="auditIdea" label="意见" />
                                        <el-table-column prop="auditSign" label="签字" width="180" />
                                        <el-table-column prop="auditTime" label="日期" width="180" />
                                    </el-table>
                                </td>
                                <td v-if="item.custom === '回文'">
                                    <el-form-item label="回文办理状态" prop="sendStatus" label-width="150px">
                                        <el-select style="width: 250px;" disabled v-model="form.sendStatus"
                                            placeholder="回文办理状态" clearable>
                                            <el-option v-for="dict in dicts.reply_handle_status" :key="dict.dictValue"
                                                :label="dict.dictLabel" :value="dict.dictValue" />
                                        </el-select>
                                    </el-form-item>
                                </td>
                                <td v-if="item.custom === '回文'">
                                    <el-form-item label="办理截止时间" prop="sendEndTime" label-width="150px">
                                        <el-date-picker clearable v-model="form.sendEndTime" type="datetime"
                                            value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择日期">
                                        </el-date-picker>
                                    </el-form-item>
                                </td>
                                <td v-if="item.custom === '督办'" colspan="4">
                                    <el-form-item label="督办批示件" label-width="150px">
                                        <el-form-item>
                                            <el-form-item label="领导批示件" label-width="100px" prop="instructions">
                                                <el-select style="width: 150px;" v-model="form.instructions"
                                                    placeholder="请选择" clearable>
                                                    <el-option v-for="dict in boList" :key="dict.label"
                                                        :label="dict.label" :value="dict.label" />
                                                </el-select>
                                            </el-form-item>
                                            <el-form-item style="border-left: 1px solid #e2e2e2;margin-left: 1px;"
                                                label="批示领导" prop="insLeader" label-width="150px">
                                                <el-input v-model="form.insLeader" placeholder="请输入批示领导" />
                                            </el-form-item>
                                        </el-form-item>
                                        <el-form-item style="border-top: 1px solid #e2e2e2;width: 100%;">
                                            <el-form-item label="督办件" label-width="100px" prop="handleDoc">
                                                <el-select style="width: 150px;" v-model="form.handleDoc"
                                                    placeholder="请选择" clearable>
                                                    <el-option v-for="dict in boList" :key="dict.label"
                                                        :label="dict.label" :value="dict.label" />
                                                </el-select>
                                            </el-form-item>
                                            <el-form-item style="border-left: 1px solid #e2e2e2;margin-left: 1px;"
                                                label="督办部门" prop="handleDept" label-width="150px">
                                                <el-input v-model="form.handleDept" placeholder="请输入督办部门" />
                                            </el-form-item>
                                        </el-form-item>
                                        <el-form-item style="border-top: 1px solid #e2e2e2;width: 100%;">
                                            <el-input type="textarea" v-model="form.insContent" placeholder="请输入意见" />
                                        </el-form-item>

                                    </el-form-item>
                                </td>
                            </tr>
                        </table>


                    </el-form>
                </div>
            </el-col>
            <el-col :span="6" v-loading="showLoding">
                <div class="file-content card">
                    <FileTree v-if="dicts.doc_receipt_file_type.length > 0" :key="formKey"
                        :data="dicts.doc_receipt_file_type" :upload="true" @onSuccess="onUploadSuccess"
                        @onRemove="onUploadRemove" />
                </div>
            </el-col>
        </el-row>
    </div>
</template>

<script setup>
import FileTree from '@/components/FileTree'
import { onMounted, ref } from 'vue';
import { getDicts } from '@/api/system/dict/data';
import { getTmpJson } from '@/api/document/common';
import { addReceipt, getDetail } from '@/api/document/receipt';
import { startWorkFlow } from '@/api/document/common';
const { proxy } = getCurrentInstance();

const formKey = ref(new Date().getTime())
const showLoding = ref(false)

const readTypeList = [
    { label: '办理件', value: '1' },
    { label: '传阅件', value: '2' },
]
const boList = [
    { label: '是' },
    { label: '否' },
]
//需要请求的字典类型
const dictsList = ['doc_category', 'doc_unit_name', 'doc_secret', 'doc_release', 'doc_urgency', 'doc_receipt_file_type', 'reply_handle_status']
const dicts = ref({})
dictsList.map(item => dicts.value[item] = [])

const receiptFormRef = ref(null)
const initFormData = {
    num: undefined,
    category: undefined,
    secretLevel: '0',
    urgentDegree: '0',
    releaseMethod: undefined,
    unitName: undefined,
    title: undefined,
    receiptTime: undefined,
    readType: undefined,
    affixList: [],
}
const data = reactive({
    form: { ...initFormData },
    rules: {
        category: [
            { required: true, message: "来文类别不能为空", trigger: "blur" }
        ],
        secretLevel: [
            { required: true, message: "密级不能为空", trigger: "blur" }
        ],
        releaseMethod: [
            { required: true, message: "发布方式不能为空", trigger: "blur" }
        ],
        unitName: [
            { required: true, message: "来文单位不能为空", trigger: "blur" }
        ],
        title: [
            { required: true, message: "来文标题不能为空", trigger: "blur" }
        ],
        receiptTime: [
            { required: true, message: "收文日期不能为空", trigger: "blur" }
        ],
        readType: [
            { required: true, message: "阅办类型不能为空", trigger: "change" }
        ],
    }
});

const { form, rules } = toRefs(data);
const tmpJson = ref([])

//上传成功
function onUploadSuccess(fileList) {
    form.value.affixList.push(fileList)
}
//删除文件
function onUploadRemove(file) {
    form.value.affixList = form.value.affixList.filter(item => item.name !== file.response.data.url)
}
function onSubmit() {
    if (!form.value.affixList.length) {
        proxy.$modal.msgError("请上传附件");
        return
    }
    receiptFormRef.value.validate(valid => {
        if (valid) {
            showLoding.value = true
            addReceipt(form.value).then(res => {
                let data = {
                    businessKey: res.data.id,
                    tableName: 'doc_receipt',
                    variables: {
                        entity: res.data
                    }
                }
                startWorkFlow(data).then(() => {
                    proxy.$modal.msgSuccess('提交成功');
                    receiptFormRef.value.resetFields()
                    formKey.value = new Date().getTime()

                    showLoding.value = false
                })
            })
        }
    })
}
onMounted(() => {
    dictsList.map(item => {
        getDicts(item).then(res => {
            dicts.value[item] = res.data
        })
    })
    getTmpJson({ type: '1' }).then(res => {
        tmpJson.value = res.data
        //在经办人办理下方插入表单
        res.data.map((item, index) => {
            if (item.nodeId === 'Activity_1g2spl2') {
                tmpJson.value.splice(index + 1, 0, { custom: '回文' });
                tmpJson.value.splice(index + 2, 0, { custom: '督办' });
            }
        })
    })
})
</script>

<style scoped lang="scss">
@import "@/styles/variables.module.scss";;

.container {
    padding: 10px 20px;
    background-color: #E9EEF3;
    height: $contentHeight;
    overflow-x: auto;
}

.form {
    display: flex;
    justify-content: center;
    background: #dce2f1;
}

.form-content {
    width: 770px;
}

table {
    width: 100%;
    border: 1px solid #e2e2e2;
    border-collapse: collapse;

    :deep(.el-form-item) {
        margin-bottom: 0;
    }

    :deep(.el-form-item__label) {
        justify-content: center;
        border-right: 1px solid #e2e2e2;
        margin-right: 1px;
        height: auto;
        padding: 10px 10px;
    }

    :deep(.el-form-item__error) {
        position: static;
    }

    td {
        border: 1px solid #e2e2e2;
        background-color: rgb(246, 246, 246);
    }
}

.btn-submit {
    width: 100%;
    display: flex;
    margin-bottom: 5px;
}
</style>
