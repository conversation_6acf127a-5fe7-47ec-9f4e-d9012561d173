<template>
  <div class="main-content">
    <div v-if="!showDetail">
      <transition>
        <div
          v-show="showSearch"
          class="mb-[10px]"
        >
          <el-card shadow="hover">
            <el-form
              ref="queryFormRef"
              :model="queryParams" :inline="true"
              class="query-form"
            >
              <el-form-item
                label="项目名称"
                prop="xmmc"
              >
                <el-input
                  v-model="queryParams.xmmc"
                  placeholder="请输入项目名称" clearable @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item
                label="项目坐落"
                prop="xmzl"
              >
                <el-input
                  v-model="queryParams.xmzl"
                  placeholder="请输入批准文号" clearable @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item class="form-search-item">
                <el-button
                  type="primary"
                  icon="Search" @click="handleQuery"
                >搜索</el-button>
                <el-button
                  icon="Refresh"
                  @click="resetQuery"
                >重置</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </div>
      </transition>
      <el-row
        :gutter="10"
        class="mb8 new"
      >
        <el-col :span="1.5">
          <el-button
            type="primary" plain icon="Plus"
            @click="handleAdd"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success" plain icon="Upload"
            @click="handleImport"
          >
            批量导入（Shape）
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning" plain icon="Download"
            @click="handleExport"
          >导出</el-button>
        </el-col>
        <right-toolbar
          v-model:showSearch="showSearch"
          @queryTable="getList"
        />
      </el-row>
      <el-card class="result-wrap">
        <el-table
          v-loading="loading"
          :data="planReserveProjectList" @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            width="55" align="center"
          />
          <el-table-column
            label="项目名称"
            width="250"
            align="center" prop="xmmc"
          />
          <el-table-column
            label="项目坐落"
            align="center" prop="xmzl"
          />
          <el-table-column
            label="收储面积（公顷）"
            align="center" prop="scmj"
          />
          <el-table-column
            label="收储日期"
            align="center" prop="scrq" width="180"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.scrq, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="性质"
            align="center" prop="xz"
          />
          <el-table-column
            label="操作"
            width="280"
            align="center"
            class-name="small-padding"
          >
            <template #default="scope">
              <el-button
                plain
                type="primary" size="small" @click="handlePreview(scope.row)"
              >查看</el-button>
              <el-button
                plain
                type="warning" size="small" @click="handleUpdate(scope.row)"
              >修改</el-button>
              <el-button
                plain
                type="danger" size="small" @click="handleDelete(scope.row)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>

    </div>
    <!-- 添加或修改拆迁地块信息对话框 -->
    <div
      v-else
      class="content"
    >
      <div class="add-header-title">
        <div class="add-title">{{ planStorageTitle }}</div>
        <div
          class="add-title-return"
          @click="cancel"
        >
          <img
            src="@/assets/images/img-return.png"
            class="back"
          >
          <div class="backlist">返回列表</div>
        </div>
      </div>
      <div class="add-content-temp">
        <el-row :gutter="10">
          <el-col
            :span="14"
            class="contentlist"
          >
            <div class="project-header">
              <h2
                class="project-title is-active"
              >
                项目信息
              </h2>
            </div>
            <el-form
              ref="planReserveProjectFormRef"
              label-width="150px"
              :model="form"
              :rules="rules"
            >

              <el-row>
                <el-col :span="12">
                  <el-form-item
                    label="项目名称"
                    prop="xmmc"
                  >
                    <el-input
                      v-model="form.xmmc"
                      placeholder="请输入项目名称"
                    />
                  </el-form-item>
                  <el-form-item
                    label="项目编号"
                    prop="xmbh"
                  >
                    <el-input
                      v-model="form.xmbh"
                      placeholder="请输入项目编号"
                    />
                  </el-form-item>
                  <el-form-item
                    label="行政区域"
                    prop="xzqmc"
                  >
                    <el-tree-select
                      v-model="form.xzqmc"
                      check-strictly
                      filterable
                      node-key="id"
                      :props="props"
                      :data="region"
                      @node-click="selectRegion"
                    />
                  </el-form-item>
                  <el-form-item
                    label="项目坐落"
                    prop="xmzl"
                  >
                    <el-input
                      v-model="form.xmzl"
                      placeholder="请输入项目坐落"
                    />
                  </el-form-item>
                  <!--                  <el-form-item-->
                  <!--                    label="项目涉及地块数"-->
                  <!--                    prop="xmsjdks"-->
                  <!--                  >-->
                  <!--                    <el-input-->
                  <!--                      v-model="form.xmsjdks"-->
                  <!--                      placeholder="请输入项目涉及地块数"-->
                  <!--                    />-->
                  <!--                  </el-form-item>-->
                  <el-form-item
                    label="收储面积（公顷）"
                    prop="scmj"
                  >
                    <el-input
                      v-model="form.scmj"
                      placeholder="请输入收储面积"
                    />
                  </el-form-item>
                  <!--                  <el-form-item-->
                  <!--                    label="(0未入库1已入库)"-->
                  <!--                    prop="tdzt"-->
                  <!--                  >-->
                  <!--                    <el-input-->
                  <!--                      v-model="form.tdzt"-->
                  <!--                      placeholder="请输入(0未入库1已入库)"-->
                  <!--                    />-->
                  <!--                  </el-form-item>-->
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="收储日期"
                    prop="scrq"
                  >
                    <el-date-picker
                      clearable
                      v-model="form.scrq"
                      type="datetime"
                      value-format="YYYY-MM-DD HH:mm:ss"
                      placeholder="请选择收储日期"
                    />
                  </el-form-item>
                  <el-form-item
                    label="批准日期"
                    prop="pzrq"
                  >
                    <el-date-picker
                      clearable
                      v-model="form.pzrq"
                      type="datetime"
                      value-format="YYYY-MM-DD HH:mm:ss"
                      placeholder="请选择批准日期"
                    />
                  </el-form-item>
                  <!--                  <el-form-item-->
                  <!--                    label="批准文号"-->
                  <!--                    prop="pzwh"-->
                  <!--                  >-->
                  <!--                    <el-input-->
                  <!--                      v-model="form.pzwh"-->
                  <!--                      placeholder="请输入批准文号"-->
                  <!--                    />-->
                  <!--                  </el-form-item>-->
                  <!--                  <el-form-item-->
                  <!--                    label="批准文件名"-->
                  <!--                    prop="pzwjm"-->
                  <!--                  >-->
                  <!--                    <el-input-->
                  <!--                      v-model="form.pzwjm"-->
                  <!--                      placeholder="请输入批准文件名"-->
                  <!--                    />-->
                  <!--                  </el-form-item>-->
                  <!--                  <el-form-item-->
                  <!--                    label="是否跨机构联动收储"-->
                  <!--                    prop="sfkjgldsc"-->
                  <!--                  >-->
                  <!--                    <el-input-->
                  <!--                      v-model="form.sfkjgldsc"-->
                  <!--                      placeholder="请输入是否跨机构联动收储"-->
                  <!--                    />-->
                  <!--                  </el-form-item>-->
                  <el-form-item
                    label="项目阶段代码"
                    prop="xmjddm"
                  >
                    <el-input
                      v-model="form.xmjddm"
                      placeholder="请输入项目阶段代码"
                    />
                  </el-form-item>
                  <el-form-item
                    label="备注"
                    prop="bz"
                  >
                    <el-input
                      v-model="form.bz"
                      placeholder="请输入备注"
                    />
                  </el-form-item>

                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-upload
                    ref="projectUploadRef"
                    class="upload-demo"
                    drag
                    multiple
                    accept=".zip"
                    :http-request="customHttpRequest"
                  >
                    <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                    <div class="el-upload__text">
                      拖动文件 或者<em>点击上传</em>项目范围文件
                    </div>
                    <template #tip>
                      <div class="el-upload__tip">
                        请上传 shp 压缩文件
                      </div>
                    </template>
                  </el-upload>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
          <el-col
            :span="10"
          >
            <div class="attachment">
              <div>
                <div class="related_accessories"/>
                <div class="related">相关附件</div>
                <div
                  class="attachment-content"
                  v-loading="isFileLoading"
                  element-loading-text="正在上传..."
                  :element-loading-spinner="svg"
                  element-loading-svg-view-box="-10, -10, 50, 50"
                  element-loading-background="rgba(122, 122, 122, 0.25)"
                >
                  <ul>
                    <li
                      v-for="(item,index) in dictList"
                      :key="index"
                      class="attachment-item"
                    >
                      <div class="attachment-item-header">
                        <span
                          class="arrow-icon"
                          @click="getFiles(item,false)"
                          v-if="isEdit"
                        >
                          <el-icon v-if="item.isArrowUp"><ArrowDownBold /></el-icon>
                          <el-icon v-else><ArrowUpBold /></el-icon>
                        </span>
                        <span>{{ item.dictLabel }}</span>
                        <el-upload
                          class="upload-icon"
                          accept=".pdf,.ppt,.doc,.docx"
                          :action="actionUrl"
                          :headers="headers"
                          :data="{'directory':'','dictCode':item.dictCode}"
                          :show-file-list="false"
                          :before-upload="handleBefore"
                          :on-success="(response,file,fileList)=>handleSuccess(response,file,fileList,item)"
                        >
                          <el-icon><UploadFilled /></el-icon>
                        </el-upload>
                      </div>
                      <div
                        v-if="item.isArrowUp"
                        class="attachment-item-content"
                      >
                        <ul>
                          <li
                            class="file-item clearfix"
                            v-for="(file,i) in allFiles[item.dictValue]" :key="i"
                          >
                            <span v-if="file.wjmc.length<20">{{file.wjmc}}</span>
                            <el-popover
                              v-else
                              placement="top-start"
                              width="500"
                              trigger="hover"
                              :content="file.wjmc"
                            >
                              <template #reference>
                                <span>{{file.wjmc.substring(0,20)+"···"}}</span>
                              </template>
                            </el-popover>
                            <span class="action-btn">
                              <el-button
                                type="primary"
                                plain
                                @click="previewFile(file)"
                              >预览</el-button>
                              <el-button
                                type="success"
                                plain
                                @click="downloadFile(file)"
                              >下载</el-button>
                              <el-button
                                v-if="isEdit"
                                type="danger"
                                plain
                                @click="deleteFile(file,item)"
                              >删除</el-button>
                            </span>
                          </li>
                        </ul>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="add-content-wrap">
        <div class="footer-button">
          <el-button
            :loading="buttonLoading"
            type="primary" @click="submitForm" plain
          >
            <el-icon>
              <CircleCheckFilled/>
            </el-icon>
            提交项目信息
          </el-button>
          <el-button
            @click="cancel"
            plain
          >
            <el-icon>
              <CircleCloseFilled/>
            </el-icon>
            取 消
          </el-button>
        </div>
      </div>
    </div>
    <!--  项目模板数据导入对话框 -->
    <el-dialog
      ref="projectUploadRef"
      v-model="dialogVisible"
      title="导入项目范围数据"
      width="500"
      :before-close="handleDialogClose"
    >
      <el-upload
        ref="projectUploadRef"
        class="upload-demo"
        drag
        multiple
        accept=".zip"
        :action="uploadModelActionUrl"
        :headers="headers"
        :before-upload="beforeUploadProject"
        :on-success="handleProjectSuccess"
        :on-error="handleProjectError"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          拖动文件 或者<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            请上传 Shapefile 【.zip】 压缩文件 <span class="upload-tip">（坐标系：CGCS2000）</span>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="submitProjectModel"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="PlanReserveProject">
import {
  listPlanReserveProject,
  getPlanReserveProject,
  delPlanReserveProject,
  addPlanReserveProject,
  updatePlanReserveProject, downloadPlanReserveProject
} from "@/api/planReserveProject/planReserveProject"
import { getRegionTreeList } from "@/api/gis/layerTree.js"
import { downloadFileByArrayBuffer } from "@/utils/common.js"
import { getDicts } from "@/api/system/dict/data.js"
import { deleteFileById, getAttachments } from "@/api/relocationPatrol/relocationLand.js"
import { ElMessage } from "element-plus"
import { fetchAndDownload, filePreview } from "@/utils/index.js"
import { useRouter } from "vue-router"
import { getToken } from "@/utils/auth.js"

const { proxy } = getCurrentInstance();
const router = useRouter()


const planReserveProjectList = ref([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const showDetail = ref(false);
const planStorageTitle = ref("添加拟收储项目")


// 附件数据
const isEdit = ref(true);
const dictList = ref([])
const isFileLoading = ref(false)
const allFiles = reactive({
  province: [],
  approve: [],
  other: []
})
const attachments = ref([])


const dialogVisible = ref(false)
const projectUploadRef = ref()

// 附件上传参数
const headers = {
  Authorization: "Bearer " + getToken(),
  clientid: import.meta.env.VITE_APP_CLIENT_ID,
  dataType: "planStorage"
}
// 上传征拆项目巡查附件数据接口
const actionUrl = import.meta.env.VITE_APP_BASE_API + "/patrol/attachmentFile/upload"
// 批量上传拟收储项目范围数据接口
const uploadModelActionUrl = import.meta.env.VITE_APP_BASE_API + "/patrol/planReserveProject/importShape"

const props = {
  value: 'areaCode',
  children: 'children',
  label: 'name'
}
const region = ref([]);
const regionTree = async () => {
  const tree = await getRegionTreeList();
  region.value = tree.data;
}


const queryFormRef = ref();
const planReserveProjectFormRef = ref();

const dialog = reactive({
  visible: false,
  title: ''
});

const initFormData = {
  xmmc: undefined,
  xmbh: undefined,
  xmzmj: undefined,
  xmzl: undefined,
  xzqdm: undefined,
  xzqmc: undefined,
  xmsjdks: undefined,
  scmj: undefined,
  scrq: undefined,
  pzrq: undefined,
  pzwh: undefined,
  pzwjm: undefined,
  sfkjgldsc: undefined,
  xmjddm: undefined,
  bz: undefined,
  tdzt: undefined,
  nf: undefined
}
const data = reactive({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    xmmc: undefined,
    xmbh: undefined,
    xmzmj: undefined,
    xmzl: undefined,
    xzqdm: undefined,
    xzqmc: undefined,
    xmsjdks: undefined,
    scmj: undefined,
    scrq: undefined,
    pzrq: undefined,
    pzwh: undefined,
    pzwjm: undefined,
    sfkjgldsc: undefined,
    xmjddm: undefined,
    bz: undefined,
    tdzt: undefined,
    nf: undefined
  },
  rules: {

  }
});

const { queryParams, form, rules } = toRefs(data);


const regionInfo = reactive({
  xzqmc: "",
  xzqdm: ""
})
const selectRegion = (region)=>{
  regionInfo.xzqmc = region.name
  regionInfo.xzqdm = region.areaCode
}

// 获取字典数据
const getDictData = ()=>{
  getDicts('tdcb_file_type').then((res) => {
    dictList.value = res.data.map(item=>{
      item.isArrowUp = false
      return item
    })
  })
}

/**
 * 文件上传前处理事件
 */
const handleBefore = ()=>{
  isFileLoading.value = true
}


/**
 * 文件上传成功处理事件
 */
const handleSuccess = (response,file,fileList,item)=>{
  const data = response.data
  const fileObj = {
    wjmc: data.fileName,
    wjlj: data.url
  }
  item.isArrowUp = true
  allFiles[item.dictValue].push(fileObj)
  attachments.value.push(response.data)
  isFileLoading.value = false
}


/** 查询拟储备项目列表 */
const getList = async () => {
  loading.value = true;
  const res = await listPlanReserveProject(queryParams.value);
  planReserveProjectList.value = res.rows.map(item=>{
    // 转换面积单位：平方米->公顷
    item.scmj = ((item.scmj) * 1e-4).toFixed(4);
    return item;
  });
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  showDetail.value = false;
}

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  planReserveProjectFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  showDetail.value = true;
  planStorageTitle.value = "添加拟收储项目";
  dialog.title = "添加拟储备项目";
}

/**
 * 项目信息查看
 */
const handlePreview = (row)=>{
  router.push({ path: "/planStorage/planStorageDetail",query: { id: row.id } })
}

/** 修改按钮操作 */
const handleUpdate = async (row) => {
  reset();
  const _id = row?.id || ids.value[0]
  const response = await getPlanReserveProject(_id);

  const transformData = {}
  Object.keys(response.data).forEach(key=>{
    if(key === "scmj" || key === "xmzmj"){
      // 转换面积单位：平方米->公顷
      transformData[key] = ((response.data[key]) * 1e-4).toFixed(4);
      return
    }
    transformData[key] = response.data[key]
  })
  Object.assign(form.value, transformData);
  showDetail.value = true;
  planStorageTitle.value = "修改拟收储项目";
  dialog.title = "修改拟储备项目";
}

const customHttpRequest = (response)=>{
  form.value.file = response.file
}

/** 提交按钮 */
const submitForm = () => {
  planReserveProjectFormRef.value?.validate(async (valid) => {
    if (valid) {
      buttonLoading.value = true;

      const formData = new FormData()
      form.value.xzqmc = regionInfo.xzqmc
      form.value.xzqdm = regionInfo.xzqdm
      form.value.files = attachments

      const bussRelProjectData = {
        xzqmc: regionInfo.xzqmc,
        xzqdm: regionInfo.xzqdm,
        files: attachments
      }
      Object.keys(form.value).forEach((key) => {
        if(key === "file"){
          return
        }
        bussRelProjectData[key] = form.value[key]
      })
      formData.append("project",JSON.stringify(bussRelProjectData));
      formData.append("file", form.value.file);


      if (form.value?.id) {
        await updatePlanReserveProject(form.value).finally(() => buttonLoading.value = false);
      } else {
        await addPlanReserveProject(formData).finally(() => buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      showDetail.value = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除拟储备项目名称为"' + row.xmmc + '"的数据项？').finally(() => loading.value = false);
  await delPlanReserveProject(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  downloadPlanReserveProject().then(res=>{
    const mineType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    downloadFileByArrayBuffer(res,"拟收储项目数据" + new Date().getTime() + ".xlsx",mineType)
  })
};

/**
 * 获取附件数据
 */
const getFiles = (item,isArrowUp)=>{
  // isFileLoading.value = true
  item.isArrowUp = !item.isArrowUp
  if(isArrowUp){
    item.isArrowUp = true
  }
  item.wjflId = item.dictCode
  item.createTime = new Date(item.createTime)
  item.zbId = form.value.id
  getAttachments(item).then(res=>{
    allFiles[item.dictValue] = res.rows
    console.log(allFiles)
    isFileLoading.value = false
  })
}

/**
 * 文件预览
 */
const previewFile = (file)=>{
  filePreview(file.wjlj,"_blank")
}

/**
 * 文件下载
 */
const downloadFile = (file)=>{
  fetchAndDownload(file.wjlj, file.wjmc);
}

/**
 * 文件删除
 */
const deleteFile = (file,item)=>{
  deleteFileById(file.id).then(res=>{
    ElMessage.success("【" + file.name + "】文件删除成功！")
    getFiles(item,true)
  }).catch(error=>{
    ElMessage.warning("【" + file.name + "】文件删除失败！" + error)
  })
}


const beforeUploadProject = (file)=>{
  return true;
}

const handleProjectSuccess = (response,uploadFile)=>{
  projectUploadRef.value.clearFiles()
  ElMessage.success("项目数据上传城功")
  getList()
}

const handleProjectError = ()=>{
  projectUploadRef.value.clearFiles()
  ElMessage.error("项目数据上传失败")
}

/**
 * 项目数据导入
 */
const handleImport = ()=>{
  dialogVisible.value = true
}

const submitProjectModel = ()=>{
  dialogVisible.value = false
}
// 关闭导入对话框
const handleDialogClose = ()=>{
  projectUploadRef.value.clearFiles()
  dialogVisible.value = false
}


onMounted(() => {
  getList();
  regionTree();
  getDictData()
});
</script>
<style lang="scss" scoped>
.main-content{
  padding: 10px;
}
.result-wrap{
  margin-top: 10px;
}
.new {
  margin-top: 10px;
}

.el-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.content {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #dadada;

  .add-header-title {
    padding: 10px;
    display: flex;
    justify-content: space-between;
    height: 50px;
    background-color: rgb(222, 239, 255);
    box-sizing: border-box;
    border-bottom: 1px solid rgb(233, 233, 233);
    font-weight: 700;
    font-size: 14px;
    line-height: 28px;
  }
  .add-title-return {
    display: flex;
    align-content: center;
    color: rgb(32, 119, 255);
    cursor: pointer;
    font-weight: normal;
    &:hover{
      cursor: pointer;
      font-size: 16px;
      transform: scale(1.15);
      transition: all ease-in .25s;
    }
    .backlist {
      padding-left: 6px;
      font-size: 14px;
    }
    .back{
      height: 18px;
      width: 18px;
      margin-top: 5px;
    }
  }
  .back-icon{
    margin-right: 5px;
  }
}
.add-content-temp{
  padding: 10px 20px;
}
.project-header{
  display: flex;
  flex-direction: row;
  margin-bottom: 20px;
  background: rgb(235 245 255);
  border-radius: 2.5px;
  .project-title{
    padding: 10px;
    margin-right: 10px;
    margin-left: 10px;
    font-size: 16px;
    background: #03a9f442;
    border-radius: 2.5px;
    &:hover{
      cursor: pointer;
      background: rgba(3, 169, 244, 0.9);
      color: #fff;
      transition: all ease-in .25s;
    }
  }
  .project-land{

  }
  .is-active{
    // background: rgba(3, 169, 244, 0.9);
    background: rgb(32 119 255);
    border-radius: 2.5px;
    color: #fff;
  }
}
.footer-button {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  border-top: 1px solid #e0e0e0;
  padding: 20px 0;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
.attachment {
  border: 1px solid #e6e6e6;
  border-radius: 4px;
}
.attachment-content {
  min-height: 300px;
  max-height: 350px;
  overflow: auto;
}
.related_accessories {
  height: 8px;
  background-color: rgba(33, 120, 255, 1);
  border-radius: 4px 4px 0 0;
}

.related {
  border-bottom: 1px solid rgb(230, 230, 230);
  height: 40px;
  line-height: 40px;
  align-content: center;
  padding-left: 10px;
  font-size: small;
  font-weight: bold;
}
.region-select{
  margin-bottom: 0!important;
  width: 200px;
}
.arrow-icon{
  margin-right: 5px;
  &:hover{
    cursor: pointer;
  }
}
.upload-icon{
  display: inline;
  margin-left: 5px;
  .el-icon:hover{
    cursor: pointer;
    color: #0d84ff;
    transform: scale(1.5);
    margin-left: 5px;
    transition: all ease-in .25s;
  }
}
.attachment-item{
  font-weight: bold;
  margin: 5px 0;
  color: #444444bd;
}
.attachment-item-content{
  font-weight: normal;
  .file-item{
    padding-bottom: 2.5px;
    margin: 5px 0;
    border-bottom: 1px solid #eee;
  }
}
.action-btn{
  float: right;
  margin-right: 5px;
}
.download-btn{
  margin-bottom: 10px;
}

.upload-tip{
  color:red;
}

:deep(.el-select){
  width: 100%!important;
}
:deep(.el-dialog .el-dialog__header) {
  border-bottom: 1px solid #2077ff !important;
  background: #2077ff !important;
}
:deep(.el-dialog__title) {
  color: #ffffff;
}
:deep(.el-dialog__headerbtn) {
  height: 55px;
}
:deep(.el-dialog__headerbtn .el-dialog__close) {
  color: #ffffff;
}
:deep(.el-dialog .el-dialog__footer) {
  border-top: 1px solid #ececec;
}

.query-form{
  /**防止输入框出现清除按钮时输入框产生宽度变化**/
  :deep(.el-input--suffix) {
    // 固定宽度
    width: 200px !important;
  }
}
@media(max-width: 1000px){
  .query-form{
    :deep(.el-form-item) {
      margin-bottom: 18px !important;
    }
  }
}
@media(min-width: 1000px){
  .query-form{
    :deep(.el-form-item) {
      margin-bottom: 0 !important;
    }
  }
}
</style>
