define(["./arrayRemoveDuplicates-d2f048c5","./BoundingRectangle-143a34da","./buildModuleUrl-9085faaa","./Cartesian2-db21342c","./Cartographic-3309dd0d","./Check-7b2a090c","./ComponentDatatype-c140a87d","./CoplanarPolygonGeometryLibrary-e6863e11","./when-b60132fc","./Rectangle-dee65d21","./GeometryAttribute-c65394ac","./GeometryAttributes-252e9929","./GeometryInstance-6bd4503d","./GeometryPipeline-7a733318","./IndexDatatype-8a5eead4","./Math-119be1a3","./FeatureDetection-806b12f0","./PolygonGeometryLibrary-8b220fb0","./PolygonPipeline-d83979ed","./VertexFormat-6446fca0","./Event-16a2dfbf","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./OrientedBoundingBox-3b145304","./Cartesian4-3ca25aab","./EllipsoidTangentPlane-1dfa0a87","./IntersectionTests-0d6905a3","./Plane-a3d8b3d2","./AttributeCompression-0a087f75","./EncodedCartesian3-f1396b05","./ArcType-29cf2197","./EllipsoidRhumbLine-30b5229b","./earcut-2.2.1-20c8012f"],(function(e,t,a,n,r,o,i,l,s,p,c,y,u,m,d,g,b,v,h,f,C,x,P,w,A,F,G,L,E,T,D,_,k){"use strict";var V=new r.Cartesian3,R=new t.BoundingRectangle,M=new n.Cartesian2,I=new n.Cartesian2,H=new r.Cartesian3,B=new r.Cartesian3,O=new r.Cartesian3,z=new r.Cartesian3,S=new r.Cartesian3,N=new r.Cartesian3,Q=new c.Quaternion,U=new b.Matrix3,j=new b.Matrix3,Y=new r.Cartesian3;function q(e,t,a,o,l,s,p,u){var m=e.positions,v=h.PolygonPipeline.triangulate(e.positions2D,e.holes);v.length<3&&(v=[0,1,2]);var f=d.IndexDatatype.createTypedArray(m.length,v.length);f.set(v);var C=U;if(0!==o){var x=c.Quaternion.fromAxisAngle(s,o,Q);if(C=b.Matrix3.fromQuaternion(x,C),t.tangent||t.bitangent){x=c.Quaternion.fromAxisAngle(s,-o,Q);var P=b.Matrix3.fromQuaternion(x,j);p=r.Cartesian3.normalize(b.Matrix3.multiplyByVector(P,p,p),p),t.bitangent&&(u=r.Cartesian3.normalize(r.Cartesian3.cross(s,p,u),u))}}else C=b.Matrix3.clone(b.Matrix3.IDENTITY,C);var w=I;t.st&&(w.x=a.x,w.y=a.y);for(var A=m.length,F=3*A,G=new Float64Array(F),L=t.normal?new Float32Array(F):void 0,E=t.tangent?new Float32Array(F):void 0,T=t.bitangent?new Float32Array(F):void 0,D=t.st?new Float32Array(2*A):void 0,_=0,k=0,R=0,H=0,B=0,O=0;O<A;O++){var z=m[O];if(G[_++]=z.x,G[_++]=z.y,G[_++]=z.z,t.st){var S=l(b.Matrix3.multiplyByVector(C,z,V),M);n.Cartesian2.subtract(S,w,S);var N=g.CesiumMath.clamp(S.x/a.width,0,1),Y=g.CesiumMath.clamp(S.y/a.height,0,1);D[B++]=N,D[B++]=Y}t.normal&&(L[k++]=s.x,L[k++]=s.y,L[k++]=s.z),t.tangent&&(E[H++]=p.x,E[H++]=p.y,E[H++]=p.z),t.bitangent&&(T[R++]=u.x,T[R++]=u.y,T[R++]=u.z)}var q=new y.GeometryAttributes;return t.position&&(q.position=new c.GeometryAttribute({componentDatatype:i.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:G})),t.normal&&(q.normal=new c.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:L})),t.tangent&&(q.tangent=new c.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:E})),t.bitangent&&(q.bitangent=new c.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:T})),t.st&&(q.st=new c.GeometryAttribute({componentDatatype:i.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:D})),new c.Geometry({attributes:q,indices:f,primitiveType:b.PrimitiveType.TRIANGLES})}function J(e){var t=(e=s.defaultValue(e,s.defaultValue.EMPTY_OBJECT)).polygonHierarchy,a=s.defaultValue(e.vertexFormat,f.VertexFormat.DEFAULT);this._vertexFormat=f.VertexFormat.clone(a),this._polygonHierarchy=t,this._stRotation=s.defaultValue(e.stRotation,0),this._ellipsoid=p.Ellipsoid.clone(s.defaultValue(e.ellipsoid,p.Ellipsoid.WGS84)),this._workerName="createCoplanarPolygonGeometry",this.packedLength=v.PolygonGeometryLibrary.computeHierarchyPackedLength(t)+f.VertexFormat.packedLength+p.Ellipsoid.packedLength+2}J.fromPositions=function(e){return new J({polygonHierarchy:{positions:(e=s.defaultValue(e,s.defaultValue.EMPTY_OBJECT)).positions},vertexFormat:e.vertexFormat,stRotation:e.stRotation,ellipsoid:e.ellipsoid})},J.pack=function(e,t,a){return a=s.defaultValue(a,0),a=v.PolygonGeometryLibrary.packPolygonHierarchy(e._polygonHierarchy,t,a),p.Ellipsoid.pack(e._ellipsoid,t,a),a+=p.Ellipsoid.packedLength,f.VertexFormat.pack(e._vertexFormat,t,a),a+=f.VertexFormat.packedLength,t[a++]=e._stRotation,t[a]=e.packedLength,t};var W=p.Ellipsoid.clone(p.Ellipsoid.UNIT_SPHERE),Z=new f.VertexFormat,K={polygonHierarchy:{}};return J.unpack=function(e,t,a){t=s.defaultValue(t,0);var n=v.PolygonGeometryLibrary.unpackPolygonHierarchy(e,t);t=n.startingIndex,delete n.startingIndex;var r=p.Ellipsoid.unpack(e,t,W);t+=p.Ellipsoid.packedLength;var o=f.VertexFormat.unpack(e,t,Z);t+=f.VertexFormat.packedLength;var i=e[t++],l=e[t];return s.defined(a)||(a=new J(K)),a._polygonHierarchy=n,a._ellipsoid=p.Ellipsoid.clone(r,a._ellipsoid),a._vertexFormat=f.VertexFormat.clone(o,a._vertexFormat),a._stRotation=i,a.packedLength=l,a},J.createGeometry=function(t){var n=t._vertexFormat,o=t._polygonHierarchy,i=t._stRotation,s=o.positions;if(!((s=e.arrayRemoveDuplicates(s,r.Cartesian3.equalsEpsilon,!0)).length<3)){var p=H,y=B,b=O,h=S,f=N;if(l.CoplanarPolygonGeometryLibrary.computeProjectTo2DArguments(s,z,h,f)){if(p=r.Cartesian3.cross(h,f,p),p=r.Cartesian3.normalize(p,p),!r.Cartesian3.equalsEpsilon(z,r.Cartesian3.ZERO,g.CesiumMath.EPSILON6)){var C=t._ellipsoid.geodeticSurfaceNormal(z,Y);r.Cartesian3.dot(p,C)<0&&(p=r.Cartesian3.negate(p,p),h=r.Cartesian3.negate(h,h))}var x=l.CoplanarPolygonGeometryLibrary.createProjectPointsTo2DFunction(z,h,f),P=l.CoplanarPolygonGeometryLibrary.createProjectPointTo2DFunction(z,h,f);n.tangent&&(y=r.Cartesian3.clone(h,y)),n.bitangent&&(b=r.Cartesian3.clone(f,b));var w=v.PolygonGeometryLibrary.polygonsFromHierarchy(o,x,!1),A=w.hierarchy,F=w.polygons;if(0!==A.length){s=A[0].outerRing;for(var G=a.BoundingSphere.fromPoints(s),L=v.PolygonGeometryLibrary.computeBoundingRectangle(p,P,s,i,R),E=[],T=0;T<F.length;T++){var D=new u.GeometryInstance({geometry:q(F[T],n,L,i,P,p,y,b)});E.push(D)}var _=m.GeometryPipeline.combineInstances(E)[0];_.attributes.position.values=new Float64Array(_.attributes.position.values),_.indices=d.IndexDatatype.createTypedArray(_.attributes.position.values.length/3,_.indices);var k=_.attributes;return n.position||delete k.position,new c.Geometry({attributes:k,indices:_.indices,primitiveType:_.primitiveType,boundingSphere:G})}}}},function(e,t){return s.defined(t)&&(e=J.unpack(e,t)),J.createGeometry(e)}}));
