/**
 * @name: <PERSON>Map3DLayer.js
 * @description: SuperMap3D 加载切片图层(geoserver 发布 GeoWebCache WMTS切片图层)
 * @author: zyc
 * @time: 2024-03-22
 **/
import { flyToS3MLayerBounds, flyToS3MLayerCenter } from "@/utils/Cesium/CesiumTool"
import eventBus from "@/utils/eventBus.js"
const resolutions = [
  0.7039144156840451, 0.35195720784202256,
  0.17597860392101103, 0.08798930196050551,
  0.043994650980252646, 0.02199732549012637,
  0.010998662745063196, 0.005499331372531586,
  0.002749665686265805, 0.0013748328431328976,
  6.874164215664488E-4, 3.4370821078322564E-4,
  1.7185410539161233E-4, 8.592705269580617E-5,
  4.296352634790308E-5, 2.148176317395154E-5,
  1.074088158697577E-5, 5.370440793487897E-6, 2.6852203967439486E-6
]

/**
 * 设置超图3d图层参数配置
 * @param options
 * @returns {{tileMatrixLabels: *[], maximumLevel: number, format: string, tileWidth: number, url: string, layer, ellipsoid: *, tilingScheme, tileMatrixSetID: string, style: string, credit: *, minimumLevel: number, tileHeight: number}}
 */
export function getSuperMap3DOption(options){
  const gridsetName = "EPSG:4490_" + options.layerName
  // const gridsetName = "Custom_图层"
  // const gridsetName = "EPSG:4490_JNYX"
  // const gridsetName = "EPSG:4326"
  const matrixIds = []
  const level = options.level || 18
  for (let i = 1; i <= level; i++){
    matrixIds[i] = gridsetName + ":" + i
    // matrixIds[i] = String(i)
  }
  return {
    // KVP模式
    url: import.meta.env["VITE_APP_GEOSERVER_URL"],
    // url: "http://*************:8090/iserver/services/map-agscache-Conf/wmts100?",
    // url: "http://*************:8090/iserver/services/map-agscache-Conf/wmts100?",
    layer: options.layerName, // 'KM_YX202212_GCS2000',
    style: 'default',
    // format: options.style || 'image/jpeg',
    format: "image/png",
    tileMatrixSetID: gridsetName, // 'EPSG:4490_KM_YX202212_GCS2000',
    tileMatrixLabels: matrixIds, // 虽然是可选，但却是必传
    tileWidth: 256,
    tileHeight: 256,
    minimumLevel: 0,
    maximumLevel: 18,
    // 切片范围，设置了之后并不能正确显示，和切片方案的范围一致，只需要传递一个即可。
    // rectangle: SuperMap3D.Rectangle.fromDegrees(
    //   -180,-90,
    //   180,90),
    ellipsoid: SuperMap3D.Ellipsoid.CGCS2000,
    // ellipsoid: SuperMap3D.Ellipsoid.WGS84,
    // dimensions: resolutions,
    // tilingScheme: new SuperMap3D.GeographicTilingScheme({
    //   ellipsoid: SuperMap3D.Ellipsoid.CGCS2,
    //   // ellipsoid: SuperMap3D.Ellipsoid.WGS84,
    //   // rectangle: SuperMap3D.Rectangle.fromDegrees(
    //   //   102.16800137157443,24.38888826938492,
    //   //   103.6690014064149,26.545364279423275),
    //   // 设置切片范围
    //   // rectangle: SuperMap3D.Rectangle.fromDegrees(
    //   //   102.16663524839002,24.386031553489815,
    //   //   103.67307702180811,26.547933351271894),
    //   numberOfLevelZeroTilesX: 2,
    //   numberOfLevelZeroTilesY: 1
    // }),
    // tilingScheme: new ArcGISTilingScheme(),
    tilingScheme: new Cesium.WebMercatorTilingScheme(),
    credit: new Cesium.Credit(options.layerName)
  }
}

/**
 * 添加 GeoServer WMTS 切片地图服务
 * @param viewer3d：3d视图对象
 * @param layerOption：图层配置
 */
export function addWMTSLayer(viewer3d,layerOption){
  const wmtsOption = getSuperMap3DOption(layerOption)
  const wmtsLayer = new Cesium.WebMapTileServiceImageryProvider(wmtsOption)
  const layer = viewer3d.imageryLayers.addImageryProvider(wmtsLayer)
  // 不透明
  layer.alpha = 1
}

/**
 * 加载Iserver rest服务
 * @param viewer3d：3d视图对象
 * @param options：iserver 图层参数
 */
export function addIserverLayer(viewer3d,options){
  const provider = new Cesium.SuperMapImageryProvider({
    url: import.meta.env.VITE_APP_ISERVER_URL + options.url,
    name: options.layerName,
    // 设置最大缩放层级后，到了最大缩放层级，不会去后端请求数据，但是地图还可以继续放大
    maximumLevel: 18,
    minimumLevel: 0
  })
  const layer = viewer3d.imageryLayers.addImageryProvider(provider)
  // 半透明
  // layer.alpha = 0.85;
}

/**
 * 加载3d-tiles切片服务
 * @param layerName：图层名称（来自于【nginx】代理名称）
 * @param viewer3d：3d视图对象
 */
export function add3dTilesLayer(layerName,viewer3d){
  const tileSet = new Cesium.Cesium3DTileset({
    url: import.meta.env.VITE_APP_3DTILES_URL + layerName + "/tileset.json",
    name: layerName,
    skipLevel0fDetail: true,
    basescreenspaceError: 1024,
    maximumscreenspaceError: 256,//数值加大，能让最终成像变模糊skipscreenspaceErrorFactor:16
    immediatelyLoadDesiredLevelofDetail: false,
    loadsiblings: true,//如果为true则不会在已加载完概况房屋后，自动从中心开始超清化房屋
    cullWithchildrenBounds: true,
    cullRequestswhileMoving: true,
    cullRequestswhileMovingMultiplier: 10,//值越小能够更快的剔除
    preloadwhenHidden: true,
    preferLeaves: true,
    maximumMemoryusage: 128 // 内存分配变小有利于倾斜摄影数据回收，提升性能体验progressiveResolutionHeightFraction:0.5，//数值偏于8能够让初始加载变得模糊dynamicscreenspaceErrorDensity:0.1，//数值加大，能让周边加载变快dynamicscreenspaceErrorFactor:1.
    // dynamic5creenspaceError: true // 有了这个后，会在真正的全屏加载完之后才清晰化房屋
  })
  tileSet.name = layerName
  viewer3d.scene.primitives.add(tileSet)
  tileSet.readyPromise.then((tarTileSet) => {
    viewer3d.zoomTo(tarTileSet)
  })
}

/**
 * 天地图影像服务
 * @param viewer3d：3d视图对象
 */
export function addTdtImageLayer(viewer3d){
  const tdtLayer = viewer3d.imageryLayers.addImageryProvider(new Cesium.TiandituImageryProvider({
    mapStyle: Cesium.TiandituMapsStyle.IMG_C, // 经纬度投影（影像）
    credit: new Cesium.Credit('天地图全球影像服务 数据来源：国家地理信息公共服务平台'),
    token: import.meta.env.VITE_APP_TDT_TOKEN,
    name: 'img_c'
  }))
  tdtLayer.name = 'img_c'
}

/**
 * 天地图矢量服务
 * @param viewer3d：3d视图对象
 */
export function addTdtVevLayer(viewer3d){
  const tdtLayer = viewer3d.imageryLayers.addImageryProvider(new Cesium.TiandituImageryProvider({
    mapStyle: Cesium.TiandituMapsStyle.VEC_C, // 经纬度投影（矢量）
    credit: new Cesium.Credit('天地图全球矢量服务 数据来源：国家地理信息公共服务平台'),
    token: import.meta.env.VITE_APP_TDT_TOKEN,
    name: 'vec_c'
  }))
  tdtLayer.name = 'vec_c'
}

/**
 * 天地图影像图注记服务
 * @param viewer3d：3d视图对象
 */
export function addTdtImageLabelLayer(viewer3d){
  // 添加天地图注记服务
  const tdtLayer = viewer3d.imageryLayers.addImageryProvider(new Cesium.TiandituImageryProvider({
    mapStyle: Cesium.TiandituMapsStyle.CIA_C, // 经纬度投影（中文注记）
    credit: new Cesium.Credit('天地图全球影像注记服务'),
    token: import.meta.env.VITE_APP_TDT_TOKEN,
    name: 'cva_c'
  }))
  tdtLayer.name = 'cva_c'
}
/**
 * 天地图地形服务
 * @param viewer3d：3d视图对象
 */
export function addTDTTerrainLayer(viewer3d){
  // 服务域名
  // const tdtUrl = 'https://t{s}.tianditu.gov.cn/';
  const tdtUrl = "https://t{s}.tianditu.gov.cn/mapservice/swdx"
  // 服务负载子域
  const subdomains = ['0','1','2','3','4','5','6','7'];

  const terrainProvider = new Cesium.TiandituTerrainProvider({
    urls: tdtUrl,
    subdomains: subdomains,
    token: import.meta.env.VITE_APP_TDT_TOKEN
  })
  viewer3d.terrainProvider = terrainProvider;
}

/**
 * 添加SuperMap iServer发布的影像服务
 * @param url：服务图层地址
 * @param name：服务图层名称
 * @param viewer3d：3d视图对象
 */
export function addImage2Map(url, name,viewer3d) {
  const imageryProvider = new Cesium.SuperMapImageryProvider({
    url: import.meta.env.VITE_APP_ISERVER_URL + url,
    name: name
  })
  viewer3d.imageryLayers.addImageryProvider(imageryProvider)
}

/**
 * 添加SuperMap iServer地形图层
 * @param url：地形服务地址
 * @param viewer3d：3d视图对象
 */
export function addTerrain2Map(url,viewer3d) {
  console.log("地形服务地址：",import.meta.env.VITE_APP_ISERVER_URL + url)
  const terrainProvider = new Cesium.CesiumTerrainProvider({
    url: import.meta.env.VITE_APP_ISERVER_URL + url,
    isSct: true, // 是否为iServer发布的TIN地形服务
    invisibility: true // 为true时控制地形visible才有效
  })
  viewer3d.terrainProvider = terrainProvider
}

/**
 * 添加SuperMap iServer三维切片缓存图层
 * @param url：三维切片缓存服务地址
 * @param name：三维切片缓存服务名称
 * @param viewer3d：3d视图对象
 */
export function addS3MTilesLayerByScp(layerObj,viewer3d) {
  const promise = viewer3d.scene.addS3MTilesLayerByScp(import.meta.env.VITE_APP_ISERVER_URL + layerObj.url,
    { name: layerObj.datasetName }
  )
  promise.then(layer => {
    // console.log("添加s3mLayer:",layer);
    if (layerObj.datasetName === "fengceng" || layerObj.datasetName === "fenghu"){
      flyToS3MLayerCenter(viewer3d,layer)
      // 对BIM模型进行查询
      eventBus.emit("s3mLayerQuery",layerObj)
    } else {
      flyToS3MLayerBounds(viewer3d,layer)
    }
  })
}

/**
 * 添加SuperMap iServer三维场景
 * @param url：场景服务地址
 * @param viewer3d：3d视图对象
 */
export function addSceneLayers(url,viewer3d) {
  const promise = viewer3d.scene.open(import.meta.env.VITE_APP_ISERVER_URL + url)
  Cesium.when(promise,layers => {
    layers.forEach(layer => {
      console.log("场景图层：",layer)
    })
  })
}

/**
 * 根据服务类型添加图层
 * @param layer：服务图层
 * @param viewer3d：3d视图对象
 */
export function addLayerByServiceType(layer,viewer3d) {
  switch (layer.serviceValue) {
    case 'map':
      addImage2Map(layer.url, layer.name,viewer3d)
      break
    case 'terrain':
      addTerrain2Map(layer.url,viewer3d)
      break
    case 's3m':
      addS3MTilesLayerByScp(layer,viewer3d)
      break
    case 'scene':
      addSceneLayers(layer.url,viewer3d)
      break
    case '3d-tiles':
      add3dTilesLayer(layer.value,viewer3d)
      break
  }
}

/**
 * 根据图层名进行移除
 * @param serviceType：图层类型
 * @param layerName：图层名称
 * @param viewer3d：3d视图对象
 */
export function removeLayerByName(serviceType,layer,viewer3d){
  switch (serviceType) {
    case 'map' : {
      const mapLayer = viewer3d.imageryLayers._layers.find(
        item => {
          return item.imageryProvider._name === layer.name
        }
      )
      viewer3d.imageryLayers.remove(mapLayer)
      break
    }
    case 'terrain':
      viewer3d.terrainProvider._visible = false
      break
    case 's3m': {
      const s3mLayer = viewer3d.scene.layers.find(layer.datasetName)
      // console.log("s3mLayers:",s3mLayer);
      viewer3d.scene.layers.remove(layer.datasetName)
      break
    }
    case 'scene': {
      viewer3d.scene.layers.removeAll()
      break
    }
    case '3d-tiles':{
      const primitives = viewer3d.scene.primitives._primitives
      const tileSet = primitives.find(p => p.name === layer.value)
      viewer3d.scene.primitives.remove(tileSet)
      break
    }
  }
}
