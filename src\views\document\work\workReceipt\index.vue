<template>
  <div class="box">
    <div
      v-show="showObj.table"
      class="container"
    >
      <div style="display: flex;gap: 5px;margin-left: 5px;margin-bottom: 5px;">
        <el-button
          color="#4387D6"
          size="default" @click="gotoDetail(selectRow)"
        >打开任务</el-button>
        <el-button
          color="#4387D6"
          size="default" @click="gotoHistory(selectRow)"
        >查询办理过程</el-button>

      </div>
      <div class="card">
        <div
          class="drag-box"
          ref="dragArea" @dragover.prevent @drop="handleDrop"
        >
          <div v-if="dragBox.length === 0">将列拖拽到这里进行分组</div>
          <div class="drag-view">
            <div
              v-for="tag in dragBox"
              :key="tag.name" class="drag-item"
            >
              <el-icon size="18">
                <Menu />
              </el-icon>
              <div>{{ tag.name }}</div>
              <el-icon
                @click="dragClose(tag)"
                size="14"
              >
                <Close />
              </el-icon>
            </div>
          </div>
        </div>
        <el-input
          @input="search" v-model="searchKey" style="width: 100%;margin-bottom: 10px;"
          placeholder="请输入内容"
        >
          <template #prepend>全局搜索</template>
        </el-input>
        <el-table
          @row-dblclick="gotoDetail" @cell-click="tableClick" size="large"
          height="650"
          :span-method="arraySpanMethod" row-key="businessId" default-expand-all
          :row-style="cellStyle" border
          v-loading="loading" :data="receiptList" style="width: 100%"
        >
          <el-table-column
            fixed show-overflow-tooltip v-for="(item, index) in tableHeader"
            :key="index"
            :prop="item.prop" :label="item.label" :width="item.width"
            :render-header="renderHeader"
          />
          <template #empty>
            <div class="table-empty">
              <slot name="empty">
                <img
                  src="@/assets/images/notData.png"
                  alt="notData"
                >
                <div>暂无数据</div>
              </slot>
            </div>
          </template>
        </el-table>

        <!-- <el-empty v-else description="暂无数据" />  -->
      </div>
    </div>
    <formDetail
      :processInstanceId="componentsData.formDetail.processInstanceId"
      :nodeId="componentsData.formDetail.nodeId" :businessId="componentsData.formDetail.businessId"
      :taskId="componentsData.formDetail.taskId" @back="goBack('showForm')" v-if="showObj.showForm"
    />
    <History
      activeName="0" :businessId="componentsData.history.businessId" @back="goBack('showHistory')"
      v-if="showObj.showHistory"
    />
    <FilterBox
      @clean="filterBoxClean" @apply="filterBoxApply" @cancel="filterBoxCancel"
      :checkListBox="dialogProps.checkList" :listBox="dialogProps.list" v-if="dialogProps.show"
      :key="dialogProps.key" :top="dialogProps.top" :left="dialogProps.left"
      :width="dialogProps.width"
    />
  </div>
</template>

<script setup>
import { getDocWorkType } from '@/api/document/common'
import { getDicts } from '@/api/system/dict/data';
import formDetail from './components/detail.vue'
import History from "@/components/History/index.vue";
import Sortable from "sortablejs";


import useUserStore from '@/store/modules/user'
import { computed, h, ref, nextTick } from 'vue';

const { proxy } = getCurrentInstance();

//需要请求的字典类型
const dictsList = ['doc_category', 'doc_unit_name', 'doc_secret', 'doc_release', 'doc_urgency']
const dicts = ref({})
dictsList.map(item => dicts.value[item] = [])

const showObj = reactive({
  table: true,
  showForm: false,
  showHistory: false
})
const componentsData = reactive({
  formDetail: {
    businessId: '',
    processInstanceId: '',
    taskId: '',
    nodeId: ''
  },
  history: {
    businessId: ''
  }
})
const goBack = (e) => {
  showObj[e] = false
  showObj.table = true
  getList()
}

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    readType: '1',
    nodeId: undefined,
    title: undefined,
    number: undefined,//案卷号
    receiptTime: undefined,//收文日期
    receiveUnit: undefined,//来文单位
    category: undefined,//来文类别
    releaseMethod: undefined,//发布方式
    urgent: undefined,//	急缓
    secretLevel: undefined//	秘级
  }
});
const total = ref(0);
const { queryParams } = toRefs(data);

const receiptList = ref([]);
const nodeList = ref([]);
const activeNames = ref(0);
const loading = ref(true);

const searchKey = ref('')
const search = (e) => {
  const keyword = trimSpace(e).toLowerCase();
  let list = filterBox(backupList.value, filterList.value)

  let result = list.filter(item => {
    return Object.keys(item).some(key => {
      return trimSpace(item[key]?.toString()).toLowerCase().includes(keyword)
    })
  })
  //分组
  receiptList.value = propsGroup(result, dragBox.value)

}
const trimSpace = (str) => {
  str || (str = '');
  let string = str.replace(/\ |\　/g, "")
  return string.replace(/&nbsp;/g, "")
}
function cellStyle({ row, column, rowIndex, columnIndex }) {
  let color = ''
  let fontWeight = ''
  //分组列表
  if (!row.taskId) return 'background-color:#E1E1E1'
  if (row.businessId === selectRow.value.businessId) return 'background-color:#80ADBF;color: #fff;'

  if (row.stageStatus === '0') {
    color = 'color: #515a6e;'
    fontWeight = 'font-weight: 600;'
  }
  if (row.urgent !== '0') {
    color = 'color: #DC143C;'
  }
  return color + fontWeight

}
function arraySpanMethod({ row, column, rowIndex, columnIndex }) {
  // 检查当前行是否有 taskid 字段
  if (!row.taskId) {
    return [1, tableHeader.value.length]
  } else {
    // 如果有 taskid 字段，则不合并
    return [1, 1];
  }
}

import { ElIcon } from 'element-plus';
import { Filter } from '@element-plus/icons-vue';
import FilterBox from "@/components/FilterBox/index.vue"
const dialogProps = ref({
  show: false,
  top: '',
  left: '',
  width: '',
  key: '',
  list: [],
  checkList: []
})
//筛选
function renderHeader({ column }) {

  return h('div', { style: ' display: flex;justify-content: space-between;align-items: center;' }, [
    h('span', column.label),
    h(ElIcon, {
      style: 'cursor: pointer;', onClick: ($event) => {
        const headerCell = $event.target.closest('.el-table__header-wrapper th')

        const rect = headerCell.getBoundingClientRect();

        //第二次点击关闭
        if (dialogProps.value.left === rect.left && dialogProps.value.show) {
          dialogProps.value.show = false
          return
        }
        const data = restoreGroup(receiptList.value)

        const list = new Set();
        data.forEach(item => {
          if (item.hasOwnProperty(column.property)) {
            list.add(item[column.property]);
          }
        });
        const backup = new Set();

        backupList.value.forEach(item => {
          if (item.hasOwnProperty(column.property)) {
            backup.add(item[column.property]);
          }
        });
        dialogProps.value.list = [...backup]
        dialogProps.value.checkList = [...list]
        dialogProps.value.top = rect.top
        dialogProps.value.left = rect.left
        dialogProps.value.width = rect.width
        dialogProps.value.key = new Date().getTime()

        dialogProps.value.property = column.property

        dialogProps.value.show = true

      }
    }, { default: () => h(Filter) })
  ])
}

const backupList = ref([])
const filterBoxCancel = () => {
  dialogProps.value.show = false
}
//筛选条件
const filterList = ref({})
//筛选方法
const filterBox = (data, filterList, sort) => {
  const property = sort ? dialogProps.value.property : tableHeader.value[0].prop

  sort = sort || 'down'
  let temp = []
  temp = data.filter(item => {
    return Object.keys(filterList).every(key => {
      return filterList[key].includes(item[key])
    })
  })
  temp.sort((a, b) => {
    let A = a[property]
    let B = b[property]
    if (typeof A === 'number' && typeof B === 'number') {
      if (sort === 'up') {
        return A - B
      } else {
        return B - A
      }
    } else {
      if (sort === 'up') {
        return A.localeCompare(B);
      } else {
        return B.localeCompare(A);
      }
    }

  })
  return temp
}
const filterBoxApply = (data) => {
  filterList.value[dialogProps.value.property] = data.checkList
  const result = filterBox(backupList.value, filterList.value, data.sort)
  //分组
  receiptList.value = propsGroup(result, dragBox.value)
  filterBoxCancel()
}
const filterBoxClean = () => {
  delete filterList.value[dialogProps.value.property];
  const result = filterBox(backupList.value, filterList.value, data.sort)
  //分组
  receiptList.value = propsGroup(result, dragBox.value)
  filterBoxCancel()
}
const tableHeader = ref(
  [
    {
      label: "业务类型",
      prop: "typeName",
      width: undefined
    },
    {
      label: "案卷号",
      prop: "number",
      width: undefined
    },
    {
      label: "急 缓",
      prop: "urgentName",
      width: "110px"

    },
    {
      label: "来文标题",
      prop: "title",
      width: "400px"
    },
    {
      label: "日期",
      prop: "receivingTime",
      width: undefined
    },
    {
      label: "来文单位",
      prop: "receiveUnit",
      width: undefined
    },
    {
      label: "当前阶段",
      prop: "currentStage",
      width: undefined
    },
    {
      label: "上阶段处理人",
      prop: "preHandler",
      width: undefined
    },
    {
      label: "收件人",
      prop: "addresseeName",
      width: '120px'
    }
  ]
)
//表头交换
const initSortTable = () => {
  const wrapperColumn = document.querySelector(
    ".el-table__header-wrapper tr"
  );
    // 第二步，给列容器指定对应拖拽规则
  Sortable.create(wrapperColumn, {
    animation: 500,
    delay: 0,
    onStart: function (evt) {
      dragItem.value = tableHeader.value[evt.oldIndex];
    },

    onEnd: (event) => {
      // 接下来做索引的交换
      let tempHableHeader = [...tableHeader.value]; // 先存一份临时变量表头数据
      let temp; // 临时变量用于交换
      temp = tempHableHeader[event.oldIndex];

      tempHableHeader[event.oldIndex] = tempHableHeader[event.newIndex];
      tempHableHeader[event.newIndex] = temp;

      tableHeader.value = [];

      nextTick(() => {
        tableHeader.value = tempHableHeader;
        if (event.newIndex === 0 && dragBox.value.length > 0) {
          //第一项重组分组
          const result = propsGroup(dragBackupList.value, dragBox.value)
          receiptList.value = result
        }

      });
    }
  });
}
const dragItem = ref(0)
const dragBox = ref([])
const dragBackupList = ref([])
//分组
const handleDrop = (event) => {
  const dragArea = proxy.$refs.dragArea;
  //如果是drag内部拖拽，不执行
  if (dragFlag.value) return

  if (dragArea.contains(event.target)) {
    dragBackupList.value = restoreGroup(receiptList.value)
    dragBox.value.push({ name: dragItem.value.label, ...dragItem.value })
    dragBox.value.map(drag => {

      tableHeader.value = tableHeader.value.filter((item, index) => {
        if (item.prop === drag.prop) {
          drag.headerIndex = index
        }
        return item.prop !== drag.prop
      })

    })
    const result = propsGroup(dragBackupList.value, dragBox.value)
    receiptList.value = result

  }
}
//分组方法
const propsGroup = (data, groupKays) => {
  if (!groupKays.length) return data
  const currentKey = groupKays[0].prop
  const groupedData = {}
  data.forEach(item => {
    const key = item[currentKey]
    if (!groupedData[key]) {
      groupedData[key] = []
    }
    groupedData[key].push(item)
  })

  return Object.keys(groupedData).map(key => ({
    [tableHeader.value[0].prop]: `${groupKays[0].name}：${key}（${groupedData[key].length}项目）`,
    businessId: new Date().getTime() + key,
    children: propsGroup(groupedData[key], groupKays.slice(1))
  }))
}
//分组复原
function restoreGroup(data) {
  let result = [];

  function traverse(items) {
    for (let item of items) {
      if (item.children && item.children.length > 0) {
        traverse(item.children);
      } else {
        result.push(item);
      }
    }
  }

  traverse(data);
  return result;
}
//交换drag
const dragFlag = ref(false)
const initDragSortTable = () => {
  const wrapper = document.querySelector(
    ".drag-box .drag-view"
  );
    // 第二步，给列容器指定对应拖拽规则
  Sortable.create(wrapper, {
    animation: 500,
    delay: 0,
    onStart: () => {
      dragFlag.value = true
    },
    onEnd: (event) => {

      // 接下来做索引的交换
      let tempDragBox = [...dragBox.value]; // 先存一份临时变量表头数据
      let temp; // 临时变量用于交换
      temp = tempDragBox[event.oldIndex];

      tempDragBox[event.oldIndex] = tempDragBox[event.newIndex];

      tempDragBox[event.newIndex] = temp;

      dragBox.value = [];

      nextTick(() => {
        dragBox.value = tempDragBox;
        const result = propsGroup(dragBackupList.value, dragBox.value)
        receiptList.value = result
        dragFlag.value = false

      });
    }
  });
}
const dragClose = (tag) => {

  dragBox.value = dragBox.value.filter(item => tag.prop !== item.prop)
  tableHeader.value.splice(tag.headerIndex, 0, tag)
  const result = propsGroup(dragBackupList.value, dragBox.value)
  receiptList.value = result
}

function getList() {
  loading.value = true;
  getDocWorkType({ type: '1' }).then(response => {
    backupList.value = response.data;
    const result = propsGroup(backupList.value, dragBox.value)
    receiptList.value = result
    loading.value = false;

  });
}

const selectRow = ref({})
const tableClick = (row) => {
  if (!row.taskId) return
  selectRow.value = row
}
function gotoDetail(row) {

  if (!row.taskId) {
    proxy.$modal.msgWarning('请先单击选择任务,或者双击直接打开任务');
    return
  }
  showObj.table = false
  showObj.showForm = true
  componentsData.formDetail.businessId = row.businessId
  componentsData.formDetail.processInstanceId = row.processInstanceId
  componentsData.formDetail.taskId = row.taskId
  componentsData.formDetail.nodeId = row.nodeId
}
function gotoHistory(row) {
  if (!row.taskId) {
    proxy.$modal.msgWarning('请先单击选择任务');
    return
  }
  showObj.showHistory = true
  componentsData.history.businessId = row.businessId
}


getList()
onMounted(() => {
  dictsList.map(item => {
    getDicts(item).then(res => {
      dicts.value[item] = res.data
    })
  })
  initSortTable()
  initDragSortTable()

})

</script>
<style scoped lang="scss">
@import "@/styles/variables.module.scss";;

.box {
    height: $contentHeight;
    background-color: #E9EEF3;
    overflow-y: scroll;
    overflow-x: hidden;

    :deep(highlight-row) {
        background-color: #80ADBF;
    }

}

.container {
    padding: 10px 20px;
}

.search-content {
    background-color: #fff;
    padding: 10px 10px;
    border-radius: 10px;
}

.drag-box {
    padding: 10px 0;

    .drag-view {
        display: flex;
        gap: 10px;
        align-items: center;

        .drag-item {
            cursor: pointer;
            display: flex;
            gap: 10px;
            align-items: center;
            border: 1px solid rgba(0, 0, 0, .2);
            color: #606266bf;
            padding: 5px 5px;
            font-weight: 600;
        }
    }

}

:deep(.el-table__body tr:hover > td) {
  background: #80ADBF!important;
  color: #fff;
}
</style>
