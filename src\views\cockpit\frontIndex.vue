<template>
  <div id="app" class="drone-dashboard">
    <!-- 顶部标题 -->
    <header class="header" :style="{ backgroundImage: `url(${imagePath})` }">
      <div class="status-bar">
        <el-button class="custom-button" type="primary" @click="handleClick" plain>{{handleName}}</el-button>
      </div>
    </header>

    <!-- 主体内容 -->
    <main v-if="drones.isConnected" class="main-content">
      <!-- 左侧区域 -->
      <div class="left-panel">
        <!-- 储备空间 -->
        <div class="storage-info card">
          <div class="card-header">
            <h2>储备空间</h2>
          </div>
          <div class="storage">
            <div class="storage-item">
              <div class="storage-label">
                <span>存储已用</span>
                <span class="value">5.96GB / 10GB</span>
              </div>
              <progress value="5.96" max="10" class="progress-bar danger"></progress>
            </div>
            <div class="storage-item">
              <div class="storage-label">
                <span>本地服务器存储已用</span>
                <span class="value">4.33GB / 15GB</span>
              </div>
              <progress value="4.33" max="15" class="progress-bar warning"></progress>
            </div>
            <div class="storage-item">
              <div class="storage-label">
                <span>建图已用</span>
                <span class="value">1988张 / 3000张</span>
              </div>
              <progress value="1988" max="3000" class="progress-bar success"></progress>
            </div>
            <div class="storage-item">
              <div class="storage-label">
                <span>视频直播已用</span>
                <span class="value">4120分钟 / 5000分钟</span>
              </div>
              <progress value="4120" max="5000" class="progress-bar warning"></progress>
            </div>
          </div>
        </div>

        <!-- 无人机状态信息 -->
        <div class="drone-status card">
          <div class="card-header">
            <h2>无人机状态信息</h2>
          </div>
          <div class="status-grid">
            <div class="status-item">
              <div class="left-image">
                <img src="@/assets/images/drone/status/fjxx1.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value">{{StatusUtils.flightTaskStep(deviceState[1]?.device_state?.flighttask_step_code) }}</span>
                <span class="label">任务状态</span>
              </div>
            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="@/assets/images/drone/status/fjxx2.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value">{{deviceState[0]?.device_state?.battery?.capacity_percent}}%</span>
                <span class="label">飞行器剩余电量</span>
              </div>
            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="@/assets/images/drone/Information/9.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value">{{deviceState[0]?.device_state?.wind_speed}}</span>
                <span class="label">飞机端风速</span>
              </div>
            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="@/assets/images/drone/status/fjxx3.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value">{{ StatusUtils.kbToMb(deviceState[0]?.device_state?.storage?.used) }}/{{ StatusUtils.kbToMb(deviceState[0]?.device_state?.storage?.total) }} MB</span>
                <span class="label">已使用容量/总容量</span>
              </div>
            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="@/assets/images/drone/status/fjxx4.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value">{{StatusUtils.rtkStatus(deviceState[0]?.device_state?.position_state?.is_fixed)}}</span>
                <span class="label">RTK是否收敛</span>
              </div>
            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="@/assets/images/drone/tc4.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value">{{deviceState[0]?.device_state?.vertical_speed}}</span>
                <span class="label">垂直速度</span>
              </div>
            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="@/assets/images/drone/status/fjxx6.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value">{{deviceState[0]?.device_state?.position_state?.rtk_number}}</span>
                <span class="label">RTK搜星数量</span>
              </div>
            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="@/assets/images/drone/tc4.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value">{{deviceState[0]?.device_state?.position_state?.gps_number}}</span>
                <span class="label">GPS搜星数量</span>
              </div>
            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="@/assets/images/drone/tc4.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value">{{deviceState[0]?.device_state?.horizontal_speed}}</span>
                <span class="label">水平速度</span>
              </div>
            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="@/assets/images/drone/tc4.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value">{{deviceState[0]?.device_state?.height}}</span>
                <span class="label">绝对高度</span>
              </div>
            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="@/assets/images/drone/tc4.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value">{{deviceState[0]?.device_state?.elevation}}</span>
                <span class="label">相对起飞点高度</span>
              </div>
            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="@/assets/images/drone/status/fjxx10.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value">{{deviceState[0]?.device_state?.home_distance}}</span>
                <span class="label">距离Home点的距离</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧区域 -->
      <div class="right-panel">
        <!-- 飞行统计图表 -->
        <div class="flight-statistics card">
          <div class="card-header">
            <h2>飞行统计</h2>
          </div>
          <div class="chart-container">
            <div class="echarts-container" ref="flightChart"></div>
          </div>
        </div>

        <!-- 无人机机场状态信息 -->
        <div class="airport-status card">
          <div class="card-header">
            <h2>无人机机场状态信息</h2>
          </div>
          <div class="airport-status-grid">
            <div class="status-item">
              <div class="left-image">
                <img src="@/assets/images/drone/Information/1.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value highlight">{{StatusUtils.secondsToHours(deviceState[1]?.device_state?.acc_time)}}h</span>
                <span class="label">累计运行时长</span>
              </div>
            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="@/assets/images/drone/Information/2.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value">{{StatusUtils.timestampToDate(deviceState[1]?.device_state?.activation_time)}}</span>
                <span class="label">激活时间</span>
              </div>

            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="@/assets/images/drone/Information/13.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value">{{deviceState[1]?.device_state?.humidity }}%RH</span>
                <span class="label">舱内湿度</span>
              </div>
            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="@/assets/images/drone/Information/10.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value safe">{{StatusUtils.rainfallStatus(deviceState[1]?.device_state?.rainfall) }}</span>
                <span class="label">阵雨情况</span>
              </div>

            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="@/assets/images/drone/Information/3.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value highlight"> {{deviceState[1]?.device_state?.job_number }}次</span>
                <span class="label">累计作业次数</span>
              </div>
            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="@/assets/images/drone/Information/4.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value"> {{deviceState[1]?.device_state?.network_state?.rate }} KB/s</span>
                <span class="label">网络状态</span>
              </div>
            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="@/assets/images/drone/Information/9.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value safe">{{deviceState[1]?.device_state?.wind_speed }} m/s</span>
                <span class="label">风速</span>
              </div>
            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="@/assets/images/drone/Information/16.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value">{{deviceState[1]?.device_state?.working_current }} mA</span>
                <span class="label">工作电流</span>
              </div>

            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="@/assets/images/drone/Information/7.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value highlight">{{deviceState[1]?.device_state?.drone_charge_state?.capacity_percent }}%</span>
                <span class="label">飞行器电量</span>
              </div>

            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="@/assets/images/drone/Information/8.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value">{{StatusUtils.chargeState(deviceState[1]?.device_state?.drone_charge_state?.state) }}</span>
                <span class="label">飞行器充电状态</span>
              </div>

            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="@/assets/images/drone/Information/15.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value">{{ deviceState[1]?.device_state?.working_voltage }} mV</span>
                <span class="label">工作电压</span>
              </div>
            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="@/assets/images/drone/Information/6.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value">{{ StatusUtils.kbToMb(deviceState[1]?.device_state?.storage?.used) }}/{{ StatusUtils.kbToMb(deviceState[1]?.device_state?.storage?.total) }} MB</span>
                <span class="label">已使用容量/总容量</span>
              </div>
            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="@/assets/images/drone/Information/14.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value">{{StatusUtils.droneInDockStatus(deviceState[1]?.device_state?.drone_in_dock) }}</span>
                <span class="label">是否在仓</span>
              </div>
            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="@/assets/images/drone/Information/11.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value">{{ deviceState[1]?.device_state?.temperature}}°C</span>
                <span class="label">环境温度</span>
              </div>
            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="@/assets/images/drone/Information/12.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value warning">29.6°C</span>
                <span class="label">舱内温度</span>
              </div>
            </div>
            <div class="status-item">
              <div class="left-image">
                <img src="@/assets/images/drone/Information/5.png" alt="" />
              </div>
              <div class="right-content">
                <span class="value safe">{{ deviceState[1]?.device_state?.media_file_detail?.remain_upload }}个</span>
                <span class="label">待上传文件</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- 主体内容 -->
    <main v-if="!drones.isConnected" class="main-live">
      <!-- 上方区域区域 -->
      <div class="header-panel">
        <div class="viewer-container">
          <Viewer3d ref="viewer3d" @onCreatedViewer3d="createViewer3d"/>
        </div>
      </div>

      <!-- 下方区域 -->
      <div class="bottom-panel">
        <div class="flight-lift">
          <el-header style="height: 3%;" >
            <h3>无人机直播窗口</h3>
          </el-header>
          <el-main style="height: 85%;margin-top: 2%;margin-bottom: 1%">
            <div ref="dronePlayer" id="drone-player"  class="video-player"></div>
          </el-main>
          <el-footer style="height: 5%;">
            <div class="controls">
              <button @click="joinHandler(true)" :disabled="!(isDroneInRoom && droneIsTakeOff)">开始直播</button>
              <button @click="leaveHandler(true)" :disabled="!isDroneInRoom">停止直播</button>
            </div>
          </el-footer>
        </div>

        <div class="airport-right">
          <el-header style="height: 3%;" >
            <h3>机场直播窗口</h3>
          </el-header>
          <el-main style="height: 85%;margin-top: 2%;margin-bottom: 1%">
            <div ref="remotePlayer" id="remote-player" class="video-player"></div>
          </el-main>
          <el-footer style="height: 5%;">
            <div class="controls">
              <button @click="joinHandler(false)" :disabled="isAirportInRoom" >开始直播</button>
              <button @click="leaveHandler(false)" :disabled="!isAirportInRoom">停止直播</button>
            </div>
          </el-footer>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
// ==================== 模块导入 ====================
// Vue相关
import { ref,Ref, onMounted, onBeforeUnmount, reactive, provide, watch } from 'vue'
import Viewer3d from "@/components/GISTools/Viewer/Viewer3d.vue"
// 实时音视频SDK
import VERTC, { IRTCEngine,  StreamIndex } from '@volcengine/rtc'
import AgoraRTC, { IAgoraRTCClient } from 'agora-rtc-sdk-ng'
// 第三方库
import * as echarts from 'echarts';  // 图表库
// MQTT通信
import mqtt from 'mqtt'
// Cesium 3D地图
import UseViewer3d from "@/components/GISTools/Viewer/UseViewer3d.js"
// 状态管理
import { useDroneStore } from '@/store/modules/drone.js'
// 工具函数
import { StatusUtils } from '@/constants/flightTask.js'  // 状态转换工具
// API接口
import { liveBroadcast } from "@/api/uav/liveBroadcast"
import { deviceModel, monthOfYear } from "@/api/uav/flightHub2/way_line.js"
// 资源文件
import cgztImg from '@/assets/images/drone/cgzt_02.png' // 常规状态图片
import wrjdpImg from '@/assets/images/drone/wrjdp_02.png' // 飞行状态图片
// ==================== 状态初始化 ====================
const drones = useDroneStore() // 无人机状态管理实例
const boundUpdate = drones.updateIsConnected // 更新连接状态的方法
// 组件状态
const handleName = ref('常规模式') // 当前模式显示名称
const imagePath = ref(cgztImg) // 动态背景图片路径
// 图表相关
const flightChart = ref(null) // ECharts图表DOM引用
let chartData = { // 图表数据结构
  months: [], // 月份数据 ['1','2'...'12']
  missions: [], // 任务次数数据
  data: [] // 采集数据量
}
// 设备状态
const deviceState = ref([]) // 设备状态数据数组
// MQTT相关
const mqttClient = ref(null) // MQTT客户端实例
const mqttStatus = ref('disconnected') // MQTT连接状态
// 3D地图相关
const viewer3d = ref(null) // Cesium 3D视图实例
const droneEntity = ref(null) // 无人机实体对象
let initialPosition // 存储初始位置
let flightPathPositions = [] // 存储飞行路径点
let flightPathEntity // 飞行路径实体
// 视频监控相关
const droneIsTakeOff = ref(false) // 无人机是否起飞状态
// RTC引擎实例（区分机场和无人机）
const airportEngine = ref<IRTCEngine | null>(null) // 机场视频引擎
const droneEngine = ref<IRTCEngine | null>(null) // 无人机视频引擎
const isAirportInRoom = ref(false) // 机场视频是否在房间中
const isDroneInRoom = ref(false) // 机场视频是否在房间中
// Agora相关实例
const airportAgoraClient = ref<IAgoraRTCClient | null>(null) // 机场Agora客户端
const droneAgoraClient = ref<IAgoraRTCClient | null>(null) // 无人机Agora客户端
const isUsingAgora = ref(false) // 是否使用Agora服务
// 3D视图状态管理
const viewer3dProp = reactive({}) // 3D视图属性
const isShowResource = ref(false) // 是否显示资源组件

// ==================== 核心功能 ====================
/**
 * 初始化ECharts图表
 * 创建图表实例并设置现代科技感样式
 */
const initChart = () => {
  if (!flightChart.value) return;
  // 检查是否已存在图表实例，如果存在则先销毁
  const existingInstance = echarts.getInstanceByDom(flightChart.value);
  if (existingInstance) {
    existingInstance.dispose();
  }

  const chartInstance = echarts.init(flightChart.value);

  // 现代科技感的配色方案
  const colorPalette = {
    blueGradient: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: '#36D1DC' },  // 亮蓝
      { offset: 1, color: '#5B86E5' }   // 深蓝
    ]),
    redGradient: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: '#FF6B6B' },  // 珊瑚红
      { offset: 1, color: '#FF3F3F' }   // 亮红
    ]),
    axisLine: '#2D3E50',                // 深蓝灰轴
    splitLine: 'rgba(45, 62, 80, 0.3)', // 半透明分割线
    text: '#7D8B9C',                    // 中灰文字
    highlight: '#E0E6ED'                // 亮灰高亮
  };

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
        shadowStyle: {
          color: 'rgba(0, 0, 0, 0.1)'
        }
      },
      backgroundColor: 'rgba(25, 35, 50, 0.9)',
      borderColor: '#2D3E50',
      borderWidth: 1,
      padding: [10, 15],
      textStyle: {
        color: '#E0E6ED',
        fontSize: 12
      },
      formatter: params => {
        let tip = `<div style="font-size: 14px; font-weight: bold; margin-bottom: 8px; color: ${params[0].color}">${params[0].axisValue}</div>`;
        params.forEach(item => {
          tip += `
            <div style="display: flex; align-items: center; margin: 6px 0;">
              <div style="
                width: 10px;
                height: 10px;
                border-radius: 2px;
                background: ${item.color};
                margin-right: 8px;
              "></div>
              <span style="flex: 1;">${item.seriesName}</span>
              <strong style="margin-left: 10px; color: ${item.color}">${item.value}${item.seriesIndex === 0 ? '次' : '张'}</strong>
            </div>
          `;
        });
        return tip;
      }
    },
    legend: {
      data: ['执飞任务次数', '飞行采集数据数量'],
      right: 320,
      top: 0,
      textStyle: {
        color: colorPalette.text,
        fontSize: 12,
        fontWeight: 'normal'
      },
      itemWidth: 14,
      itemHeight: 8,
      itemGap: 16,
      itemStyle: {
        borderRadius: 2
      }
    },
    grid: {
      left: '3%',
      right: '3%',
      bottom: '3%',
      top: '18%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: chartData.months,
      axisLine: {
        lineStyle: {
          color: colorPalette.axisLine,
          width: 1
        }
      },
      axisLabel: {
        color: colorPalette.text,
        fontSize: 11,
        margin: 10,
        interval: 0 // 强制显示所有标签
      },
      axisTick: {
        alignWithLabel: true,
        lineStyle: {
          color: colorPalette.axisLine
        }
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '任务次数',
        nameTextStyle: {
          color: colorPalette.text,
          fontSize: 12,
          padding: [0, 0, 0, 40]
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: colorPalette.axisLine
          }
        },
        axisLabel: {
          color: colorPalette.text,
          fontSize: 11
        },
        splitLine: {
          lineStyle: {
            color: colorPalette.splitLine,
            type: 'dashed'
          }
        },
        splitNumber: 5
      },
      {
        type: 'value',
        name: '数据量(张)',
        nameTextStyle: {
          color: colorPalette.text,
          fontSize: 12,
          padding: [0, 0, 0, 40]
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: colorPalette.axisLine
          }
        },
        axisLabel: {
          color: colorPalette.text,
          fontSize: 11
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: '执飞任务次数',
        type: 'bar',
        barWidth: '18%',
        barGap: '100%', // 调整柱子之间的间距
        barCategoryGap: '60%', // 调整类目之间的间距
        data: chartData.missions,
        itemStyle: {
          color: colorPalette.blueGradient,
          borderRadius: [4, 4, 0, 0],
          shadowColor: 'rgba(54, 209, 220, 0.3)',
          shadowBlur: 6,
          shadowOffsetY: 3
        },
        label: {
          show: true,
          position: 'top',
          color: colorPalette.highlight,
          fontSize: 11,
          fontWeight: 'bold',
          formatter: '{c}次',
          distance: 5
        },
        emphasis: {
          itemStyle: {
            shadowColor: 'rgba(54, 209, 220, 0.5)',
            shadowBlur: 10
          }
        },
        animationDelay: function (idx) {
          return idx * 100;
        }
      },
      {
        name: '飞行采集数据数量',
        type: 'bar',
        barWidth: '18%',
        yAxisIndex: 1,
        barGap: '100%', // 调整柱子之间的间距
        barCategoryGap: '10%', // 调整类目之间的间距
        data: chartData.data,
        itemStyle: {
          color: colorPalette.redGradient,
          borderRadius: [4, 4, 0, 0],
          shadowColor: 'rgba(255, 107, 107, 0.3)',
          shadowBlur: 6,
          shadowOffsetY: 3
        },
        label: {
          show: true,
          position: 'top',
          color: colorPalette.highlight,
          fontSize: 11,
          fontWeight: 'bold',
          formatter: '{c}张',
          distance: 5
        },
        emphasis: {
          itemStyle: {
            shadowColor: 'rgba(255, 107, 107, 0.5)',
            shadowBlur: 10
          }
        },
        animationDelay: function (idx) {
          return idx * 100 + 100;
        }
      }
    ],
    animationDuration: 1200,
    animationEasing: 'elasticOut'
  };

  // 响应式调整
  window.addEventListener('resize', function() {
    chartInstance.resize();
  });

  chartInstance.setOption(option);
  return chartInstance;
};

/**
 * 模式切换处理
 * 在常规模式和飞行模式之间切换
 */
const handleClick = () => {
  boundUpdate(!drones.isConnected)
  handleName.value = drones.isConnected ? '常规模式' : '飞行模式';
  imagePath.value = drones.isConnected ? cgztImg : wrjdpImg;

  // 根据模式加载不同数据
  drones.isConnected ? getList() : getLive();
};

/**
 * 获取并处理列表数据
 * 包括月度统计数据和设备状态数据
 */
const getList = async () => {
  // 获取月度数据
  const resMonth = await monthOfYear();

  // 初始化图表数据结构
  chartData = {
    months: [], missions: [], data: []
  };

  // 填充12个月数据（自动补全缺失月份）
  for (let month = 1; month <= 12; month++) {
    const monthKey = month.toString();
    chartData.months.push(monthKey);
    const currentData = resMonth.data[monthKey] || {taskCount: 0, picCount: 0};
    chartData.missions.push(currentData.taskCount);
    chartData.data.push(currentData.picCount);
  }

  // 初始化图表
  initChart();

  // 获取设备状态数据
  const resDevice = await deviceModel();
  deviceState.value = resDevice.data;
};

/**
 * 获取并处理直播数据
 */
const getLive = async () => {
  // 初始化MQTT连接
  await initMqttConnection();

  // 订阅无人机状态主题
  subscribeMqttTopic('thing/product/1581F6QAD241800B73B2/osd');
};

// ==================== MQTT通信 ====================
/**
 * 初始化MQTT连接
 * 配置连接选项并建立连接
 */
const initMqttConnection = async () => {
  try {
    const options = {
      clean: true,
      connectTimeout: 4000,
      clientId: `web_${Math.random().toString(16).substr(2, 8)}`,
      username: 'dji_flighthub_2',
      password: 'Zlzx@123',
      reconnectPeriod: 5000  // 5秒重连间隔
    };

    const brokerUrl = 'ws://121.43.136.41:8083/mqtt';
    mqttClient.value = mqtt.connect(brokerUrl, options);

    // 设置事件监听
    setupMqttEventListeners();

    // 等待连接成功
    await new Promise((resolve, reject) => {
      const timeout = setTimeout(() => reject(new Error('连接超时')), 5000);

      mqttClient.value.on('connect', () => {
        clearTimeout(timeout);
        mqttStatus.value = 'connected';
        resolve(true);
      });

      mqttClient.value.on('error', reject);
    });

  } catch (error) {
    console.error('MQTT初始化失败:', error);
    mqttStatus.value = 'error';
  }
}

/**
 * 订阅MQTT主题
 */
const subscribeMqttTopic = (topic) => {
  if (!mqttClient.value || mqttStatus.value !== 'connected') {
    console.warn('MQTT未连接，无法订阅主题')
    return
  }

  mqttClient.value.subscribe(topic, { qos: 1 }, (err) => {
    if (err) {
      console.error('订阅MQTT主题失败:', err)
    } else {
      console.log('订阅MQTT主题成功:', topic)
    }
  })
}

/**
 * 处理MQTT消息
 * 更新无人机位置信息
 */
const setupMqttEventListeners = () => {
  if (!mqttClient.value) return;

  // 基本事件监听
  mqttClient.value.on('reconnect', () => console.log('MQTT正在重新连接...'));
  mqttClient.value.on('close', () => console.log('MQTT连接关闭'));
  mqttClient.value.on('offline', () => console.log('MQTT离线'));

  // 消息处理
  mqttClient.value.on('message', (topic, message) => {
    try {
      droneIsTakeOff.value = true

      const payload = JSON.parse(message.toString());
      const { latitude, longitude, height } = payload.data.host;

      // 更新3D视图中的无人机位置
      if (droneEntity.value && viewer3d.value) {
        updateDronePosition(longitude, latitude, height);
      }
    } catch (err) {
      console.error('MQTT消息处理错误:', err);
    }
  });
}
// ==================== 3D地图功能 ====================
/**
 * 监听数据，监听视图对象值：当viewer3d值为真时，显示资源组件
 * 目前使用父组件传递属性不成功，只能使用provide,inject进行传递
 */
watch(()=>viewer3dProp.viewer3d,(newValue,oldValue) => {
  if (Object.keys(newValue).length) {
    isShowResource.value = true
  }
})
provide('viewer3d', viewer3dProp);
const createViewer3d = async (viewerId, mapInitStore, mapViewStore) => {
  const useViewer3d = new UseViewer3d(viewerId, mapInitStore, mapViewStore);
  await useViewer3d.createViewer3d();
  const viewer3d = useViewer3d.viewer3d;

  // 设置初始视角位置（无人机） - 高度是相对于地面的
  initialPosition = Cesium.Cartesian3.fromDegrees(101.699636959, 21.207881294, 150);
  // 创建磨憨管委会固定点（蓝色） - 高度是相对于地面的
  const managementPosition = Cesium.Cartesian3.fromDegrees(101.699636959, 21.207881294, 10);

  viewer3d.entities.add({
    name: '磨憨管委会',
    position: managementPosition,
    point: {
      pixelSize: 15,
      color: Cesium.Color.BLUE,
      outlineColor: Cesium.Color.WHITE,
      outlineWidth: 2,
      heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND // 相对于地面高度
    },
    label: {
      text: '磨憨管委会',
      font: '14px sans-serif',
      fillColor: Cesium.Color.WHITE,
      heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND, // 标签也相对于地面
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      pixelOffset: new Cesium.Cartesian2(0, -10)
    }
  });

  // 创建无人机初始位置点（红色） - 高度是相对于地面的
  droneEntity.value = viewer3d.entities.add({
    name: '无人机当前位置',
    position: initialPosition,
    point: {
      pixelSize: 15,
      color: Cesium.Color.RED,
      outlineColor: Cesium.Color.WHITE,
      outlineWidth: 2,
      heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND // 相对于地面高度
    },
    label: {
      text: '无人机',
      font: '14px sans-serif',
      fillColor: Cesium.Color.WHITE,
      heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND, // 标签也相对于地面
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      pixelOffset: new Cesium.Cartesian2(0, -30)
    }
  });

  // 创建飞行路径
  flightPathEntity= viewer3d.entities.add({
    name: '无人机飞行路径',
    polyline: {
      positions: new Cesium.CallbackProperty(() => flightPathPositions, false),
      width: 3,
      material: new Cesium.PolylineGlowMaterialProperty({
        glowPower: 0.2,
        color: Cesium.Color.YELLOW
      }),
      clampToGround: true // 路径贴地
    }
  });

  Object.assign(viewer3dProp, { viewer3d });
};

/**
 * 更新无人机位置
 * @param {number} longitude 经度
 * @param {number} latitude 纬度
 * @param {number} height 高度
 */
function updateDronePosition(longitude, latitude, height) {
  // 创建新位置点（高度设为贴地10米）
  const position = Cesium.Cartesian3.fromDegrees(longitude, latitude, 80);

  // 更新无人机位置
  droneEntity.value.position = position;

  // 添加到飞行路径
  flightPathPositions.push(position);

  // 限制路径点数量避免性能问题
  if (flightPathPositions.length > 1000) {
    flightPathPositions = flightPathPositions.slice(-500);
  }
}

// ==================== 通用 RTC 管理器 ====================
interface RTCManagerConfig {
  /** 场景名称（用于日志） */
  sceneName: string;
  /** 场景(true代表无人机false机场)*/
  scene: boolean;
  /** RTC 引擎实例（火山引擎） */
  engine: Ref<any>;
  /** Agora 客户端实例 */
  agoraClient: Ref<any>;
  /** 房间状态标志 */
  isInRoom: Ref<boolean>;
  /** 是否使用 Agora 的标志 */
  isUsingAgora: Ref<boolean>;
}

function createRTCManager(config: RTCManagerConfig) {
  // ==================== 通用工具函数 ====================
  const parseQueryString = (queryString: string): Record<string, string> => {
    return Object.fromEntries(
        queryString.split('&').map(pair => {
          const [key, value] = pair.split('=');
          return [decodeURIComponent(key), decodeURIComponent(value || '')];
        })
    );
  };

  // ==================== 事件处理器 ====================
  // 火山引擎事件处理
  const setupVolcEventListeners = () => {
    if (!config.engine.value) return;

    config.engine.value.on(VERTC.events.onUserPublishStream, (e: { userId: string; mediaType: any; }) => {
      handleVolcStreamPublish(e.userId, e.mediaType);
    });
  };

  // Agora 事件处理
  const setupAgoraEventListeners = () => {
    if (!config.agoraClient.value) return;

    // 用户离开事件
    config.agoraClient.value.on("user-left", (user: any) => {
      console.log(`${config.sceneName} 用户离开:`, user.uid);
    });

    // 用户发布流事件
    config.agoraClient.value.on("user-published", async (user: any, mediaType: any) => {
      setTimeout(async () => {
        if (!config.agoraClient.value.remoteUsers.some((u: any) => u.uid === user.uid)) {
          return;
        }

        try {
          await config.agoraClient.value.subscribe(user, mediaType);
          if (mediaType === "video" && user.videoTrack) {
            if (config.scene) {
              await user.videoTrack.play("drone-player");
            } else {
              await user.videoTrack.play("remote-player");
            }
          }
          if (mediaType === "audio" && user.audioTrack) {
            user.audioTrack.play();
          }
        } catch (error) {
          console.error(`${config.sceneName} 订阅失败:`, error);
        }
      }, 500);
    });
  };

  // ==================== 核心功能 ====================
  const handleVolcStreamPublish = async (userId: string, mediaType: string) => {
    try {
      const player = config.scene
          ? document.querySelector('#drone-player')
          : document.querySelector('#remote-player');

      if (!player) {
        console.error('Player element not found');
        // 处理找不到元素的情况，比如显示错误信息或采取其他补救措施
      }

      await config.engine.value.setRemoteVideoPlayer(StreamIndex.STREAM_INDEX_MAIN, {
        userId,
        renderDom: player,
      });
    } catch (error) {
      console.error(`${config.sceneName} 火山引擎视频渲染失败:`, error);
    }
  };

  const initVolcEngine = async (appId: string) => {
    try {
      config.engine.value = VERTC.createEngine(appId);
      setupVolcEventListeners();
      return true;
    } catch (error) {
      console.error(`${config.sceneName} 火山引擎初始化失败:`, error);
      return false;
    }
  };

  const initAgoraEngine = async () => {
    try {
      config.agoraClient.value = AgoraRTC.createClient({
        mode: "live",
        codec: "vp8"
      });
      setupAgoraEventListeners();
      return true;
    } catch (error) {
      console.error(`${config.sceneName} Agora 初始化失败:`, error);
      return false;
    }
  };

  const joinRoom = async (data: any) => {
    try {
      const parsedParams = parseQueryString(data.url);
      if (data.urlType === 'volc') {
        if (!(await initVolcEngine(parsedParams.app_id))) return;
        await config.engine.value.joinRoom(
            parsedParams.token,
            parsedParams.room_id,
            { userId: parsedParams.user_id },
            { isAutoPublish: false, isAutoSubscribeAudio: true, isAutoSubscribeVideo: true }
        );
      }
      else if (data.urlType === 'agora') {
        if (!(await initAgoraEngine())) return;
        await config.agoraClient.value.setClientRole("host");
        await config.agoraClient.value.join(
            parsedParams.app_id,
            parsedParams.channel,
            parsedParams.token,
            parseInt(parsedParams.uid, 10)
        );
      }

      config.isInRoom.value = true;
      console.log(`${config.sceneName} 加入房间成功`);
    } catch (error) {
      console.error(`${config.sceneName} 加入房间失败:`, error);
    }
  };

  const leaveRoom = async () => {
    try {
      if (config.isUsingAgora.value && config.agoraClient.value) {
        await config.agoraClient.value.leave();
      } else if (config.engine.value) {
        await config.engine.value.leaveRoom();
      }

      config.isInRoom.value = false;
      config.isUsingAgora.value = false;
      console.log(`${config.sceneName} 离开房间成功`);
    } catch (error) {
      console.error(`${config.sceneName} 离开房间失败:`, error);
    }
  };

  return {
    joinRoom,
    leaveRoom,
    parseQueryString,
  };
}

// ==================== 使用示例 ====================
// 机场场景
const airportRTCManager = createRTCManager({
  sceneName: "机场直播",
  scene: false,
  engine: airportEngine,
  agoraClient: airportAgoraClient,
  isInRoom: isAirportInRoom,
  isUsingAgora: isUsingAgora
});

// 无人机场景
const droneRTCManager = createRTCManager({
  sceneName: "无人机直播",
  scene: true,
  engine: droneEngine,
  agoraClient: droneAgoraClient,
  isInRoom: isDroneInRoom,
  isUsingAgora: isUsingAgora
});

// 加入房间示例
const joinHandler = async (isDrone: boolean) => {
  console.log('droneIsTakeOff', droneIsTakeOff.value)
  const res = await liveBroadcast({
    camera_index: isDrone ? '80-0-0' : '165-0-7',
    quality_type: 'adaptive',
    sn: isDrone ? '1581F6QAD241800B73B2' : '7CTXMCX00B065A',
    video_expire: 7200
  });
  const manager = isDrone ? droneRTCManager : airportRTCManager;

  await manager.joinRoom(res.data);
};
const leaveHandler = async (isDrone: boolean) => {
  const manager = isDrone ? droneRTCManager : airportRTCManager;
  await manager.leaveRoom();
};

// ==================== 生命周期钩子 ====================
onMounted(async () => {
  // 根据当前模式加载数据
  if (drones.isConnected) {
    await getList();
  } else {
    // 初始化MQTT连接
    await initMqttConnection();

    // 订阅无人机状态主题
    subscribeMqttTopic('thing/product/1581F6QAD241800B73B2/osd');
  }
});

onBeforeUnmount(() => {
  // 断开MQTT连接
  if (mqttClient.value) {
    mqttClient.value.end();
    console.log('MQTT连接已断开')
  }

  // 清理图表实例
  if (flightChart.value) {
    const instance = echarts.getInstanceByDom(flightChart.value);
    instance && instance.dispose();
    console.log('图表实例已销毁')
  }

  // 清理3D视图
  if (viewer3d.value) {
    viewer3d.value.destroy();
    console.log('3D视图已销毁')
  }
});
</script>

<style scoped>
/* 基础重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 整体布局 */
.drone-dashboard {
  min-height: 100vh;
  background-image: url("@/assets/images/drone/background.png");
  color: #ffffff;
  font-family: 'Roboto', 'PingFang SC', 'Microsoft YaHei', sans-serif;
  display: flex;
  flex-direction: column;
}

.status-bar {
  position: absolute; /* 或 relative/fixed，取决于你的布局需求 */
  right: 2%; /* 靠右对齐 */
  left: auto; /* 确保不与 left 冲突 */
}

/* 顶部标题 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 10vh;
  min-height: 10vh; /* 防止被压缩 */
  max-height: 10vh; /* 防止被撑大 */
  background-size: cover; /* 让背景图片覆盖整个容器，保持图片比例的同时填满容器 */
  background-position: center; /* 将背景图片居中显示 */
  background-repeat: no-repeat; /* 防止背景图片重复 */
}

/* 自定义按钮样式 */
.custom-button {
  background-color: #1c41bc; /* 修改背景颜色 */
  border-color: #0a2568; /* 修改边框颜色 */
  color: white; /* 修改文字颜色 */
  font-size: 16px; /* 修改字体大小 */
  padding: 10px 20px; /* 修改内边距 */
  border-radius: 20px; /* 修改圆角 */
}

/* 鼠标悬停时的样式 */
.custom-button:hover {
  background-color: #1899aa; /* 悬停时的背景颜色 */
  border-color: #279baa; /* 悬停时的边框颜色 */
}

/* 整体布局 */
.main-content {
  height: 90vh; /* 占据整个视口高度 */
  min-height: 90vh; /* 防止被压缩 */
  max-height: 90vh; /* 防止被撑大 */
  box-sizing: border-box; /* 确保 padding 不影响高度 */
  display: grid; /* 使用 CSS Grid 布局 */
  grid-template-columns: 1fr 1fr; /* 分为两列，每列宽度相等 */
  gap: 1%; /* 模块之间的间距 */
  padding: 1%;
}

.left-panel,
.right-panel {
  display: flex;
  flex-direction: column;
  gap: 20px; /* 子模块之间的间距 */
  height: 100%; /* 确保面板高度与父容器一致 */
}

/* 卡片样式 */
.card {
  background-color: transparent;
  background-image: url("@/assets/images/drone/cgzt_05.png") !important;
  background-size: cover;
  background-repeat: no-repeat;
  border-radius: 10px; /* 圆角效果 */
  padding: 10px;
  border: none;
  flex: 1; /* 自动填充剩余空间 */
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止内容溢出 */
  min-height: 0; /* 确保卡片可以缩小到父容器的高度 */
  align-self: stretch; /* 确保卡片宽度和高度填满父容器 */
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-left: 5%;
}

.viewer-container {
  /* 关键样式设置 */
  width: 100%;
  height: 100%; /* 给地图指定高度，小于卡片高度 */
  position: relative; /* 确保Cesium能正确渲染 */
  overflow: hidden; /* 防止内容溢出 */
}

/* 直播模块*/
.main-live {
  display: flex;
  flex-direction: column; /* 主轴方向为纵向（上下排列） */
  height: 90vh; /* 占据整个视口高度 */
  min-height: 90vh; /* 防止被压缩 */
  max-height: 90vh; /* 防止被撑大 */
  padding:1% 1%;
  gap: 2%; /* 设置上下模块之间的间距 */
}
.controls {
  margin: 1% 0;
  text-align: center;
}

.controls button {
  margin: 0 5px;
  padding: 4px 8px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.controls button:hover {
  background-color: #45a049;
}

.controls button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

/* 上方区域样式 */
.header-panel {
  background: rgba(255, 255, 255, 0.1); /* 半透明背景 */
  border-radius: 20px; /* 圆角效果 */
  height: 35vh;
  min-height: 35vh; /* 防止被压缩 */
  max-height: 35vh; /* 防止被撑大 */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2); /* 阴影效果 */
  text-align: center; /* 文字居中 */
}

/* 下方区域样式 */
.bottom-panel {
  display: flex; /* 使用 Flexbox 布局 */
  justify-content: space-between; /* 左右模块间距均匀分布 */
  height: 50vh; /* 明确设置高度 */
  min-height: 50vh; /* 防止被压缩 */
  max-height: 50vh; /* 防止被撑大 */
  gap: 20px; /* 设置左右模块之间的间距 */
  flex: 1; /* 自动填充剩余空间 */
}

/* 飞行统计图表样式 */
.flight-lift {
  flex: 1; /* 占据一半宽度 */
  background: rgba(255, 255, 255, 0.1); /* 半透明背景 */
  border-radius: 10px; /* 圆角效果 */
  padding:10px 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2); /* 阴影效果 */
  text-align: center; /* 文字居中 */
  color: #ffffff;
}

/* 无人机机场状态信息样式 */
/* 无人机状态信息 */
.drone-status .status-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  padding-top: 3%;
  gap: 15px;
}
.airport-right {
  flex: 1; /* 占据一半宽度 */
  background: rgba(255, 255, 255, 0.1); /* 半透明背景 */
  border-radius: 10px; /* 圆角效果 */
  padding:10px 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2); /* 阴影效果 */
  text-align: center; /* 文字居中 */
  color: #ffffff;
}

/* 储备空间样式 */
.storage{
  margin-top: 5%;
  width: 95%;
  margin-left: 2%;
}
.storage-info .storage-item {
  margin-bottom: 20px;
}
.storage-label {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.85);
  margin-bottom: 8px;
}
.progress-bar {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  overflow: hidden;
}
.progress-bar::-webkit-progress-bar {
  background: transparent;
}
.progress-bar.danger::-webkit-progress-value {
  background: linear-gradient(90deg, #ff416c 0%, #ff4b2b 100%);
}
.progress-bar.warning::-webkit-progress-value {
  background: linear-gradient(90deg, #ffb347 0%, #ffcc33 100%);
}
.progress-bar.success::-webkit-progress-value {
  background: linear-gradient(90deg, #00e676 0%, #00c853 100%);
}
.value {
  display: block;
  color: #4facfe;
  margin-bottom: 4px;
}

/* 机场状态信息 */
.airport-status-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  padding-top: 3%;
  gap: 15px;
}
.status-item {
  background: #00338299;
  border-radius: 10px;
  padding: 10px;
  display: flex;
  align-items: center;
  transition: transform 0.3s;
}
.status-item::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: 10px;
  padding: 2px;
  background: linear-gradient(45deg, #00c6ff, #0072ff);
  -webkit-mask:
      linear-gradient(#fff, #fff) content-box, /* 第一个遮罩作用于 content-box */
      linear-gradient(#fff, #fff);            /* 第二个遮罩默认作用于 border-box */
  -webkit-mask-composite: xor; /* 控制遮罩叠加方式 */
  mask-composite: exclude;
  pointer-events: none;
}
.status-item:hover {
  transform: translateY(-2px);
}

.left-image img {
  width: 40px;
  margin-right: 14px;
}
.right-content {
  flex: 1;
}
.label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

/* 飞行统计图表 */
.chart-container{
  height: 100%;
}
.flight-statistics {
  height: 100%;
}
.echarts-container {
  padding-top: 10%;
  width: 100%;
  height: 300px;
}

/* 基本播放器样式 */
.video-player {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);

  /* 👇 毛玻璃核心样式 */
  background: rgba(255, 255, 255, 0.01); /* 淡白色半透明背景 */
  border: 1px solid rgba(255, 255, 255, 0.3); /* 柔和的边框 */
  backdrop-filter: blur(5px); /* 背景模糊 */
  -webkit-backdrop-filter: blur(12px); /* Safari 兼容 */
}
</style>
