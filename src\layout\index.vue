<template>
  <div
    :class="classObj"
    class="app-wrapper"
    :style="{ '--current-color': theme }"
    v-loading="showLoading"
  >
    <navbar @setLayout="setLayout" />
    <Sidebar
      v-if="!sidebar.hide && !mapRoute"
      class="sidebar-container"
    />
    <div
      :class="{ hasTagsView: needTagsView, sidebarHide: sidebar.hide,'main-container': !mapRoute}"
    >
      <div
        :class="{ 'fixed-header': fixedHeader }"
        v-if="!sidebar.hide && !mapRoute"
      >
        <tags-view v-if="needTagsView" />
      </div>
      <app-main :class="!mapRoute?'content-has-tags':'content-no-tags'" />
      <settings ref="settingRef" />
    </div>
  </div>
</template>

<script setup>
import { useWindowSize } from "@vueuse/core";
import Sidebar from "./components/Sidebar/index.vue";
import { AppMain, Navbar, Settings, TagsView } from "./components";

import useAppStore from "@/store/modules/app";
import useSettingsStore from "@/store/modules/settings";
import { useRoute } from "vue-router";


const showLoading = ref(false)
const settingsStore = useSettingsStore();
const theme = computed(() => settingsStore.theme);
const sidebar = computed(() => useAppStore().sidebar);
const device = computed(() => useAppStore().device);
const needTagsView = computed(() => settingsStore.tagsView);
const fixedHeader = computed(() => settingsStore.fixedHeader);

// 计算是否是地图路由对象
const route = useRoute();
const mapRoute = computed(() => {
  // console.log("路由信息：",route.path)
  return route.path === "/map"
});

const classObj = computed(() => ({
  hideSidebar: !sidebar.value.opened,
  openSidebar: sidebar.value.opened,
  withoutAnimation: sidebar.value.withoutAnimation,
  mobile: device.value === "mobile"
}));
const { width, height } = useWindowSize();
const WIDTH = 992; // refer to Bootstrap's responsive design

watch(
  () => device.value,
  () => {
    if (device.value === "mobile" && sidebar.value.opened) {
      useAppStore().closeSideBar({ withoutAnimation: false });
    }
  }
);

watchEffect(() => {
  if (width.value - 1 < WIDTH) {
    useAppStore().toggleDevice("mobile");
    useAppStore().closeSideBar({ withoutAnimation: true });
  } else {
    useAppStore().toggleDevice("desktop");
  }
});

function handleClickOutside() {
  useAppStore().closeSideBar({ withoutAnimation: false });
}

const settingRef = ref(null);
function setLayout() {
  settingRef.value.openSetting();
}

onMounted(()=>{
  // 预加载用户可能访问的页面
  // const preloadRoutes = ['/map']
  // preloadRoutes.forEach(path => {
  //   const route = useRouter().resolve(path)
  //   route.matched.forEach(m => m.components?.default().catch(() => {}))
  // })
})
</script>

<style lang="scss" scoped>
@import "@/styles/mixin.scss";
@import "@/styles/variables.module.scss";

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;
  overflow: hidden;
  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$base-sidebar-width});
  transition: width 0.28s;
}
.sidebar-container {
  margin-top: $topBarHeight;
  max-height: $sideBarHeight;
  font-family: "Microsoft YaHei", sans-serif;
  font-weight: 800;
}
.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}

.sidebarHide .fixed-header {
  width: 100%;
}

.mobile .fixed-header {
  width: 100%;
}
.content-no-tags{
  min-height: $content-no-tags-Height;
  font-family: "Microsoft YaHei", sans-serif;
}
.content-has-tags {
  max-height: calc(100vh - 300px);
  overflow-y: scroll;
  font-family: "Microsoft YaHei", sans-serif;

}
@media(max-height: 768px) {
  .content-has-tags {
    max-height: calc(100vh - 200px);
  }
}
@media(min-height: 900px) {
  .content-has-tags {
    max-height: calc(100vh - 100px);
  }
}
</style>
