import request from "@/utils/request";

/**
 * 查询拆迁地块信息列表
 * @param query
 * @returns {*}
 */

export const listRelLand = (query) => {
  return request({
    url: "/patrol/relLand/list",
    method: "get",
    params: query
  });
};

/**
 * 查询拆迁项目信息列表
 * @param query
 * @returns {*}
 */

export const listRelProject = (query) => {
  return request({
    url: "/patrol/relProject/list",
    method: "get",
    params: query
  });
};

/**
 * 查询拆迁地块信息详细
 * @param id
 */
export const getRelLand = (id) => {
  return request({
    url: "/patrol/relLand/" + id,
    method: "get"
  });
};

/**
 * 查询征拆项目信息详细
 * @param id
 */
export const getRelProject = (id) => {
  return request({
    url: "/patrol/relProject/" + id,
    method: "get"
  });
};

/**
 * 根据项目数据类型查询地块信息
 */
export const getRelLandByDataType = (query) => {
  return request({
    url: "/patrol/land/landList",
    method: "get",
    params: query
  });
};

/**
 * 新增拆迁地块信息
 * @param data
 */
export const addRelLand = (data) => {
  return request({
    url: "/patrol/relLand",
    method: "post",
    data: data
  });
};

/**
 * 新增拆迁项目信息
 * @param data
 */
export const addRelProject = (data) => {
  return request({
    url: "/patrol/relProject",
    method: "post",
    data: data,
    headers: {
      "Content-Type": "multipart/form-data" // 必须设置
    }
  });
};

/**
 *
 */
export const addProjectAndShp = (data)=>{
  return request({
    url: "/patrol/relProject/importShape",
    method: "post",
    data: data
  })
}

/**
 * 修改拆迁地块信息
 * @param data
 */
export const updateRelLand = (data) => {
  return request({
    url: "/patrol/relLand",
    method: "put",
    data: data
  });
};

/**
 * 修改拆迁项目信息
 * @param data
 */
export const updateRelProject = (data) => {
  return request({
    url: "/patrol/relProject",
    method: "put",
    data: data
  });
};

/**
 * 删除拆迁地块信息
 * @param id
 */
export const delRelLand = (id) => {
  return request({
    url: "/patrol/relLand/" + id,
    method: "delete"
  });
};

/**
 * 删除拆迁项目信息
 * @param id
 */
export const delRelProject = (id) => {
  return request({
    url: "/patrol/relProject/" + id,
    method: "delete"
  });
};

/**
 * 获取征拆附件数据
 */
export const getAttachments = (query)=>{
  return request({
    url: "/patrol/attachmentFile/list",
    method: "get",
    params: query
  })
}

/**
 * 删除文件信息
 */
export const deleteFileById = (id) => {
  return request({
    url: "/patrol/attachmentFile/" + id,
    method: "delete"
  })
}

/**
 * 导入征拆项目数据
 */
export const importProjectModel = (data) => {
  return request({
    url: "/patrol/relProject/importData",
    method: "post",
    data: data
  })
}

/**
 * 下载征拆项目模板数据
 */
export const downloadProjectModel = () => {
  return request({
    url: "/patrol/relProject/downloadExcel",
    method: "get",
    responseType: 'arraybuffer'
  })
}


/**
 * 下载征拆项目列表数据
 */
export const downloadProjectList = () => {
  return request({
    url: "/patrol/relProject/export",
    method: "POST",
    responseType: "blob"
  })
}

/**
 * 统计征拆项目数量与面积
 */
export const getRelocationStatistics = () => {
  return request({
    url: "/patrol/relProject/relProjectSta",
    method: "get"
  })
}
