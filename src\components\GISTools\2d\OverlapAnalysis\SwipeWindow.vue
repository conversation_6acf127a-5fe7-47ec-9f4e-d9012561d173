<template>
  <div class="swipe-wrap">
    <div :id="mapId" ref="swipeMap" class="new-map">
      <div
        v-if="isSwipeSwitch"
        id="swipeContainer"
        ref="swipeContainer"
        @mouseup="mouseUp"
      >
        <div
          ref="moveBall"
          class="handle"
          @mousedown="mouseDown"
        />
      </div>
    </div>
    <div class="switch">
      <div class="switch-div swipe-switch">
        <span>{{ !isSwipeSwitch?'卷帘关闭':'卷帘打开' }}</span>
        <el-switch
          v-model="isSwipeSwitch"
          active-color="#409eff"
          inactive-color="#ddd"
          @change="handleSwipeSwitch"
        />
      </div>
      <div class="switch-div legend-switch">
        <span>{{ isLegendSwitch?'图例关闭':'图例打开' }}</span>
        <el-switch
          v-model="isLegendSwitch"
          active-color="#409eff"
          inactive-color="#ddd"
          @change="handleLegendSwitch"
        />
      </div>
    </div>
    <div class="title" @click="isCollapse=!isCollapse">
      <i :class="isCollapse?'el-icon-arrow-up':'el-icon-arrow-down'" />
      <span>{{ `${classify.layerName}` }}</span>
    </div>
    <div v-show="isCollapse" class="content-div">
      <div class="collapse">
        <div v-if="classify.areaList" class="data-content">
          <div class="data-top">
            <div class="table-container">
              <el-table
                class="table-content"
                :data="classify.areaList"
              >
                <el-table-column
                  prop="label"
                  label="地类"
                  min-width="65%"
                />
                <el-table-column
                  prop="value"
                  label="面积(公顷)"
                  min-width="35%"
                >
                  <template v-slot="scope">
                    {{ (scope.row.value*1e-4).toFixed(4) }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="data-bottom">
            <ul>
              <li v-for="(item,index) in classify.list" :key="index" class="content">
                <p class="description">
                  <span class="name">{{ item.properties['dlmc']|| item.properties['ghdlmc'] }}</span>占地
                  <span class="area">{{ Number(item.properties['area']*1e-4).toFixed(4) }}</span>公顷
                </p>
                <i
                  class="location el-icon-location"
                  :class="currentIndex===index?'active':''"
                  @click="location(item,index)"
                />
              </li>
            </ul>
          </div>
        </div>
        <div v-else class="no-data-tip">
          <i class="iconfont icon-zanwushuju" />
          <span class="">{{ `未占用${classify.layerName}` }}</span>
        </div>
      </div>
    </div>
    <div class="close-btn" @click="$emit('closeWindow')">
      <i class="el-icon-close" />
    </div>
  </div>
</template>

<script setup name="卷帘窗口">
import { addGeoJSON2Map, addGeoServerLayer, addLayer } from '@/utils/OpenLayers/olLayer'
import { Map, View } from "ol/index";
import Polygon from "ol/geom/Polygon";
import GeoJSON from 'ol/format/GeoJSON';
import { removeLayerByName, removeOverlayByName, setPopup } from "@/utils/OpenLayers/olTool";
import Stroke from "ol/style/Stroke";
import Fill from "ol/style/Fill";
import Style from "ol/style/Style";
import Select from "ol/interaction/Select";
import Vector from "ol/layer/Vector";
import Source from "ol/source/Vector";
import { getRenderPixel } from "ol/render";
import { getCommentList } from "@/utils/mapFunction/globalFunc";
import useMapInitStore from "@/store/modules/map/mapInit.js"


const { proxy } = getCurrentInstance()


const props = defineProps({
  mapId: {
    type: String
  },
  // 叠加分析图层
  overLayer: {
    type: Object,
    required: true,
    default: () => {}
  },
  // 分析几何对象
  geometry: {
    type: Object,
    required: true
  },
  // 分类结果
  classify: {
    type: Object,
    required: true,
    default: () => {}
  }
})
const { geometry,overLayer,classify,mapId } = toRefs(props)
const map = ref(undefined)
const statisticList = ref(1)
const isCollapse = ref(true)
const isSwipeSwitch = ref(true)
const isLegendSwitch = ref(false)
const resultPolygon = ref(undefined)
const columnArr = ref([]) //信息弹窗字段信息
const mapObj = ref({})
const selectEvt = ref({})
const swiperLayer = ref({})
const swiperValue = ref(0)
const serviceUrl = ref()
const currentIndex = ref(-1)


const ballWidthOfHalf = computed(()=>{
  const ballEle = proxy.$refs["moveBall"]
  const rect = ballEle.getBoundingClientRect()
  return rect.width / 2
})

const mapInfo = computed(()=>useMapInitStore().mapInfo)


watch(map,(value)=>{
  if (value){
    const mapSize = map.value.getSize()
    const halfWidth = mapSize[0] / 2
    swiperValue.value = halfWidth
  }
})


const initMap = async ()=> {
  map.value = new Map({
    target: mapId.value,
    view: new View({
      center: mapInfo.value.center,
      zoom: mapInfo.value.zoom,
      maxZoom: 18,
      minZoom: 7,
      projection: mapInfo.value.projection // 定义坐标系
    }),
    controls: []
  })
  serviceUrl.value = mapInfo.value.serviceType === "geoserver" ?
    mapInfo.value.url : import.meta.env.VITE_APP_ISERVER_URL + mapInfo.value.url

  const wmtsOptions = {
    url: serviceUrl.value,
    layerName: mapInfo.value.layerName.toUpperCase(),
    isBaseMap: true,
    layerId: '1'
  }
  addLayer(mapInfo.value.serviceType,wmtsOptions,map.value)

  addTempLayer()
  openPopup()

  handleSwipeSwitch()
}

// 获取字段信息
const getColumnName = async()=>{
  columnArr.value = await getCommentList(overLayer.value)
}


// 添加分析范围
const addTempLayer = ()=> {
  addGeoJSON2Map(geometry.value,undefined,map.value)
  const polygon = new Polygon(geometry.value.coordinates,undefined,undefined)
  map.value.getView().fit(polygon)
  if (!classify.value.list?.length){
    console.warn("未查询到【" + classify.value.layerName + "】数据！")
    return
  }
  let geoJSON
  const feats = classify.value.list
  if (feats.length) {
    for (const feat of feats){
      feat.type = "Feature"
    }
    const featCollection = {
      type: "FeatureCollection",
      features: feats
    }
    geoJSON = featCollection
  } else {
    geoJSON = feats
  }
  // 添加分析结果
  resultPolygon.value = addGeoJSON2Map(geoJSON,undefined,map.value)
  resultPolygon.value.setProperties({ layerName: "tempLayer" })
}


// 跳转定位
const location = (feature, index)=> {
  feature.type = "Feature"
  currentIndex.value = index
  map.value.removeInteraction(selectEvt.value)
  removeLayerByName('locationLayer',map.value)
  removeLayerByName('popupLayer',map.value)
  removeOverlayByName('overLay',map.value)
  const style = new Style({
    // 边线
    stroke: new Stroke({
      color: "yellow",
      lineDash: [4],
      width: 3
    }),
    // 填充色
    fill: new Fill({
      color: 'rgba(0, 0, 255, 0.1)'
    })
  })
  const locationLayer = addGeoJSON2Map(feature,style,map.value)
  locationLayer.setProperties({
    zIndex: 100,
    layerName: "locationLayer"
  })

  const feat = new GeoJSON().readFeature(feature)
  setPopup(feature.properties,columnArr.value, feat.getGeometry(), map.value)
  map.value.getView().fit(feat.getGeometry())
}

// 打开popup信息弹窗
const openPopup = ()=> {
  if (!classify.value.list?.length){
    return
  }
  map.value.on('click',evt => {
    map.value.addInteraction(selectEvt.value)
  })
  const style = new Style({
    stroke: new Stroke({
      color: "yellow",
      lineDash: [4],
      width: 3
    }),
    // 填充色
    fill: new Fill({
      color: 'rgba(0, 0, 255, 0.1)'
    })
  })
  const selectEvent = new Select({
    style: style
  })
  selectEvt.value = selectEvent
  map.value.addInteraction(selectEvt)
  selectEvent.on('select',evt => {
    removeLayerByName('popupLayer',map.value)
    removeLayerByName('locationLayer',map.value)
    removeOverlayByName('overLay',map.value)
    const features = evt.target.getFeatures().getArray()
    const feature = features[0]
    const vectorLayer = new Vector({
      source: new Source({
        features: features
      }),
      style: style
    })

    vectorLayer.setProperties({ zIndex: 100,layerName: "popupLayer" })
    map.value.addLayer(vectorLayer)

    const props = feature.getProperties()
    const geometry = feature.getGeometry()
    setPopup(props,columnArr.value, geometry, map.value)
    map.value.getView().fit(geometry)
  })
}

// 卷帘开关事件
const handleSwipeSwitch = ()=> {
  const serviceUrl = overLayer.value.mapUrl
  const wmtsOptions = {
    url: serviceUrl,
    layerName: serviceUrl,
    isBaseMap: true,
    layerId: overLayer.value.id
  }
  if (isSwipeSwitch.value) {
    swiperLayer.value = addGeoServerLayer(wmtsOptions,map.value)
    layerSwipes()
  } else {
    map.value.removeLayer(swiperLayer.value)
  }
}


// 图例开关事件
const handleLegendSwitch = ()=> {
  console.log('图例暂无！！')
}

const mouseDown = (evt)=>{
  const ballEle = proxy.$refs["moveBall"]
  // 禁用浏览器默认拖放行为
  ballEle.ondragstart = function() {
    return false
  }
  // 计算当前按下时距离地图左侧边缘的距离，注意是地图左侧，不是窗口坐标
  // 此时应该使用相对坐标，相对与父元素也就是地图控件的坐标 offsetLeft
  const target = proxy.$refs['swipeContainer']

  swiperValue.value = target.offsetLeft
  map.value.getTargetElement().addEventListener('mousemove',swipeMove)
}

const swipeMove = (evt)=>{
  const target = proxy.$refs['swipeContainer']
  const mapSize = map.value.getSize()
  const offsetLeft = target.offsetLeft
  swiperValue.value = offsetLeft

  const rect = target.offsetParent.getBoundingClientRect()
  let leftDistance = evt.clientX - rect.x
  if (leftDistance <= ballWidthOfHalf.value){
    leftDistance = ballWidthOfHalf.value
  }
  if (leftDistance >= mapSize[0] - ballWidthOfHalf.value){
    leftDistance = mapSize[0] - ballWidthOfHalf.value
  }
  // 更新要素位置
  target.style.left = leftDistance + "px"
  map.value.render()
}
const mouseUp = (evt)=>{
  map.value.getTargetElement().removeEventListener('mousemove',swipeMove)
}


const layerSwipes = ()=> {
  swiperLayer.value.on("prerender", event => {
    const ctx = event.context;
    const mapSize = map.value.getSize();
    // 绘制裁剪范围，卷帘图层占整个地图的百分比距离
    const width = swiperValue.value;
    const tl = getRenderPixel(event, [width, 0]);
    const tr = getRenderPixel(event, [mapSize[0], 0]);
    const bl = getRenderPixel(event, [width, mapSize[1]]);
    const br = getRenderPixel(event, mapSize);

    ctx.save();
    ctx.beginPath();
    ctx.moveTo(tl[0], tl[1]);
    ctx.lineTo(bl[0], bl[1]);
    ctx.lineTo(br[0], br[1]);
    ctx.lineTo(tr[0], tr[1]);
    ctx.closePath();
    ctx.clip();
  });

  swiperLayer.value.on("postrender", (event) => {
    const ctx = event.context;
    ctx.restore();
  });
}


onBeforeMount(()=>{
  getColumnName()
})
onMounted(()=>{
  initMap()
})


</script>

<style scoped lang="scss">
.swipe-wrap{
  position: relative;
  height: 100%;
  width: 100%;
  .new-map{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #eeeeee;
  }
  .switch{
    position: absolute;
    top: 20px;
    left: 60px;
    z-index: 10000;
    color: #fff;
    font-size: 16px;
    .switch-div{
      background: #031f30d7;
      padding: 5px;
      border: 1px solid #4a96e2ab;
      border-radius: 5px;
    }
    .swipe-switch{
      margin-bottom: 10px;
    }
    span{
      margin-right: 10px;
    }
  }
  .title{
    position: absolute;
    top: 5px;
    right: 0;
    width: 25%;
    z-index: 10000;
    background: #0167ccf2;
    padding: 10px 10px 10px 15px;
    color: #fff;
    font-size: 16px;
    &:hover{
      cursor: pointer;
    }
    i{
      font-size: 20px;
      vertical-align: middle;
    }
    span{
      margin-left: 10px;
    }
  }
  .close-btn{
    z-index: 9999999;
    position: absolute;
    background: #00d2ff;
    right: -4px;
    top: -4px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    color: #fff;
    text-align: center;
    line-height: 30px;
    font-size: 19px;
    &:hover{
      cursor: pointer;
      background: #5aa9fa;
    }
  }
  .content-div{
    z-index: 9999;
    position: absolute;
    background: #012c56d1;
    color: #fff;
    width: 25%;
    top: 45px;
    right: 0;
    padding: 10px 0;
    .collapse{
      font-size: 14px;
      .data-content{
        padding: 0 15px;
        .data-top{
          .table-container{
            padding: 8px 3px 8px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            .table-content{
              max-height: 210px;
              overflow-y: auto;
            }
          }
        }
        .data-bottom{
          margin-top: 10px;
          border: 2px dashed #ddd;
          border-radius: 5px;
          padding-right: 2.5px;
          ul{
            margin: 5px 0;
            max-height: 250px;
            overflow-y: auto;
          }
          .content{
            font-size: 14px;
            padding: 8px 8px 8px 15px;
            display: flex;
            &:hover{
              cursor: pointer;
              background: #0167ccc9;
            }
            .description{
              width: 90%;
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
            }
            span:nth-child(2){
              font-weight: bold;
              margin: 0 2px;
            }
            .location{
              float: right;
              font-size: 18px;
              color: #fff;
              margin-top: -2px;
              &:hover{
                color:  #5cb6ff;
              }
              .active{
                color:  #5cb6ff !important;
              }
            }
          }
        }
      }
      .no-data-tip{
        display: flex;
        justify-content: center;
        flex-direction: column;
        text-align: center;
        align-items: center;
        height: 200px;
        .iconfont{
          font-size: 40px;
          margin: 20px 0;
        }
      }
    }
  }
}
.active{
  color: #02e377 !important;
}
:deep(.el-table){
  background-color: #42b8a600;
}
:deep(.el-table::before) {
  content: none;
}
:deep(.el-table tr){
  color: #fff;
  background-color: #42b8a600;
}
:deep(.el-table .el-table__header-wrapper th){
  color: #fff;
  background-color: #4a96e2;
}
:deep(.el-table__row) {
  background-color: #0167ccc9;
  &:hover{
    cursor: pointer;
    background-color: #0167ccc9;
  }
}
/* 去除默认的hover效果 */
:deep(.el-table--enable-row-hover .el-table__body tr:hover > td) {
  background-color: rgba(0, 0, 0, 0) !important;
}
:deep(.el-table td){
  padding: 5px 0;
}
/**
 * 卷帘滑动块样式
 */
#swipeContainer {
  position: absolute;
  opacity: 0.8;
  width: 2px;
  height: 100%;
  top: 0;
  left: 50%;
  margin-left: -1px;
  background-color: #00A0E9;
  z-index: 2;
}
.handle {
  position: absolute;
  height: 40px;
  width: 40px;
  border-radius: 50%;
  background-color: #cee9ff;
  background-image: url('@/assets/images/drag.png');
  rotate: 90deg;
  background-size: 100% 100%;
  cursor: e-resize;
  margin-left: -19px;
  top: 50%;
  margin-top: -20px;
}
</style>
