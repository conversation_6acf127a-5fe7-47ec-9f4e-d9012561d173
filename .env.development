# 页面标题
VITE_APP_TITLE = "储备项目与地块巡查管理系统"

# 开发环境配置
VITE_APP_ENV = 'development'
# .env.development
VITE_APP_MODE = 'development'

# 开发环境公共API前缀
# VITE_APP_BASE_API = '/dev-api'
VITE_APP_BASE_API = '/land-prod-api'

# 服务器地址
VITE_APP_BASE_URL = "192.168.2.122:8080"

# 应用访问路径 例如使用前缀 /admin/
VITE_APP_CONTEXT_PATH = '/'

# 客户端id
VITE_APP_CLIENT_ID = 'e5cd7e4891bf95d1d19206ce24a7b32e'

# 文件服务器
VITE_APP_FILE_SERVICE_URL = 'http://172.25.18.243:19000/test'

# 文件服务器
VITE_APP_FILE_C_SERVICE_URL = 'http://122.9.202.56:29000/test'


# 文件预览
VITE_APP_FILE_PERVIEW_URL = 'http://121.43.136.41:8012/onlinePreview?url='

# 在线编辑服务器
VITE_APP_OFFICE_URL = 'http://122.9.202.56:8088/'

# 天地图TOKEN：'91fbcfac3bb4a1f211e666ac5fd0f640'
VITE_APP_TDT_TOKEN = "fa7ec9766b2c00747e3dd60ab3d05892"

# ISERVER 服务地址
# VITE_APP_ISERVER_URL = 'http://192.168.1.148:8090/'

# GEOSERVER 服务地址
# VITE_APP_GEOSERVER_URL = "http://192.168.1.148:8080/geoserver/gwc/service/wmts?"

# ISERVER 服务地址
VITE_APP_ISERVER_URL = 'http://172.17.1.62:8090/'

# GEOSERVER 服务地址
VITE_APP_GEOSERVER_URL = "http://192.168.2.122:8050/geoserver/gwc/service/wmts"

# model 服务地址
VITE_APP_MODEL_URL = "http://192.168.1.148:8881"
