import { download, exportWord } from "@/api/common/affix";
import { ElLoading } from 'element-plus';
import { saveAs } from 'file-saver'

export const downloadFileByArrayBuffer = (data, fileName, mineType) => {
  const blob = new Blob([data], { type: mineType });
  const href = URL.createObjectURL(blob);
  const downA = document.createElement('a');
  downA.href = href;
  downA.download = fileName;
  downA.click();
  URL.revokeObjectURL(href);
}

const download1 = {
  excel: (data, fileName) => downloadFileByArrayBuffer(data, fileName, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'),
  word: (data, fileName) => downloadFileByArrayBuffer(data, fileName, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'),
  zip: (data, fileName) => downloadFileByArrayBuffer(data, fileName, 'application/zip'),
  html: (data, fileName) => downloadFileByArrayBuffer(data, fileName, 'text/html'),
  markdown: (data, fileName) => downloadFileByArrayBuffer(data, fileName, 'text/markdown'),
  pdf: (data, fileName) => downloadFileByArrayBuffer(data, fileName, 'application/pdf'),
  txt: (data, fileName) => downloadFileByArrayBuffer(data, fileName, 'text/plain'),
  jpg: (data, fileName) => downloadFileByArrayBuffer(data, fileName, 'image/jpeg'),
  png: (data, fileName) => downloadFileByArrayBuffer(data, fileName, 'image/png'),
  ppt: (data, fileName) => downloadFileByArrayBuffer(data, fileName, 'application/vnd.ms-powerpoint'),
  pptx: (data, fileName) => downloadFileByArrayBuffer(data, fileName, 'application/vnd.openxmlformats-officedocument.presentationml.presentation')
};

function getExtension(fileName) {
  return fileName.slice((fileName.lastIndexOf('.') - 1 >>> 0) + 2);
}

export function downloadFileFromUrl(url, name) {
  const extension = getExtension(name).toLowerCase();

  const mimeTypeMap = {
    'xlsx': 'excel',
    'docx': 'word',
    'doc': 'word',
    'zip': 'zip',
    'html': 'html',
    'md': 'markdown',
    'pdf': 'pdf',
    'txt': 'txt',
    'jpg': 'jpg',
    'jpeg': 'jpg',
    'png': 'png',
    'ppt': 'ppt',
    'pptx': 'pptx'
  };

  const method = mimeTypeMap[extension];

  if (!method) {
    console.error('Unsupported file type:', extension);
    return;
  }

  // 使用 ElLoading 显示下载提示
  const loadingInstance = ElLoading.service({
    lock: true,
    text: '文件下载中，请等待',
    background: 'rgba(0, 0, 0, 0.7)'
  });
  download(url, name)
    .then(response => {
      console.log("response.data", response);
      download1[method](response, name);
    })
    .catch(error => {
      console.error('文件下载失败:', error);
    })
    .finally(() => {
      // 无论成功还是失败，最终都要关闭加载提示
      loadingInstance.close();
    });
}

export function exportWordForId(name,type,id) {
  // 使用 ElLoading 显示下载提示
  const loadingInstance = ElLoading.service({
    lock: true,
    text: '文件下载中，请等待',
    background: 'rgba(0, 0, 0, 0.7)'
  });

  exportWord(type,id)
    .then(response => {
      console.log("response.data", response);
      download1['word'](response,name);
    })
    .catch(error => {
      console.error('文件下载失败:', error);
    })
    .finally(() => {
      // 无论成功还是失败，最终都要关闭加载提示
      loadingInstance.close();
    });
}
