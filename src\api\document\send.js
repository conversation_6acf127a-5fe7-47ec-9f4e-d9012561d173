import request from '@/utils/request'

// 查询待办箱
export function lisTodo(query) {
    return request({
        url: '/document/send/backlog',
        method: 'get',
        params: query
    })
}
// 新增发文
export function addSend(data) {
    return request({
        url: '/document/send',
        method: 'post',
        data: data
    })
}
// 修改发文
export function updateSend(data) {
    return request({
        url: '/document/send/updateDraftByBo',
        method: 'PUT',
        data: data
    })
}
//获取发文详细信息
export function getSendDetail(id) {
    return request({
        url: '/document/send/' + id,
        method: 'get',
    })
}
