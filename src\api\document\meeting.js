import request from '@/utils/request'

// 查询待办箱
export function lisTodo(query) {
    return request({
        url: '/document/notice/backlog',
        method: 'get',
        params: query
    })
}
// 新增会议通知
export function addForm(data) {
    return request({
        url: '/document/notice',
        method: 'post',
        data: data
    })
}
// 修改会议通知
export function updateForm(data) {
    return request({
        url: '/document/notice/updateDraftByBo',
        method: 'PUT',
        data: data
    })
}
//获取会议通知详细信息
export function getDetail(id) {
    return request({
        url: '/document/notice/' + id,
        method: 'get',
    })
}