<template>
  <div class="common-layout">
    <el-container>
      <!-- Header -->
      <el-header class="header">
        <el-card class="header-card">
          <template #header>
            <div class="header-left">
              <img src="@/assets/images/drone/tc3.png" alt="status" class="header-icon">
              <span class="header-text">系统状态</span>
            </div>
          </template>
          <!-- 使用自定义容器替代 el-row -->
          <div class="stats-grid-container">
            <el-card v-for="(item, index) in stats" :key="index" shadow="hover" class="stat-card" :body-style="{ padding: '16px', display: 'flex', alignItems: 'center' }">
              <div class="stat-content">
                <div class="stat-title">{{ item.title }}</div>
                <div class="stat-value">{{ item.value }} <p class="stat-unit">{{ item.unit }}</p></div>
              </div>
              <el-image :src="item.icon" fit="contain" class="stat-icon"/>
            </el-card>
          </div>
        </el-card>
      </el-header>

      <!-- Main -->
      <el-main class="main">
        <div class="main-card">
          <el-card class="main-card-left">
            <template #header>
              <div class="card-header">
                <div class="header-left">
                  <img src="@/assets/images/drone/tc3.png" alt="status" class="header-icon">
                  <span class="header-text">数据量统计</span>
                </div>
              </div>
            </template>

            <div id="flight-chart"  style="height: 450px"></div>
          </el-card>
          <el-card class="main-card-right">
            <template #header>
              <div class="card-header first">
                <div class="header-left">
                  <img src="@/assets/images/drone/tc3.png" alt="status" class="header-icon">
                  <span class="header-text">飞行任务统计</span>
                </div>
                <div class="header-right">
                  <el-button type="text" size="small" @click="refreshLogs">
                    <i class="el-icon-refresh"></i> 刷新
                  </el-button>
                </div>
              </div>
            </template>
            <el-table :data="logs" height="450"  stripe v-loading="loading">
              <el-table-column prop="name" label="任务名称" align="center" width="200"/>
              <el-table-column prop="beginAt" label="开始时间" align="center" width="180" sortable>
                <template #default="{row}">
                  {{ formatDate(row.beginAt) }}
                </template>
              </el-table-column>
              <el-table-column prop="completedAt" label="完成的时间" align="center" width="180" sortable>
                <template #default="{row}">
                  {{ formatDate(row.completedAt) }}
                </template>
              </el-table-column>
              <el-table-column prop="currentWaypointIndex" align="center" label="航点数量" />
              <el-table-column prop="status" label="飞行状态" align="center" width="180"/>
              <el-table-column prop="endAt" label="结束时间" align="center" width="180" >
                <template #default="{row}">
                  {{ formatDate(row.endAt) }}
                </template>
              </el-table-column>
              <el-table-column prop="runAt" label="实际执行的时间" align="center" width="180">
                <template #default="{row}">
                  {{ formatDate(row.runAt) }}
                </template>
              </el-table-column>
              <el-table-column prop="resumableStatus" label="断点续飞状态" align="center" width="180"/>
              <el-table-column prop="taskType" align="center" label="任务类型" />
              <el-table-column prop="totalWaypoints" label="总航点数量" align="center" width="180"/>
            </el-table>
          </el-card>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import {flightNumMonthOfYear, statisticFlightLog, statisticHome} from "@/api/uav/statistic.js";
import {formatDate} from "@/constants/flightUtils.js";

export default {
  name: 'HomePage',
  data() {
    return {
      total: 1,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      stats: [{ title: '系统状态', value: '正常', unit: '', icon: new URL('@/assets/images/drone/tc5.png', import.meta.url).href },
        { title: '设备总数', value: '0', unit: '台', icon: new URL('@/assets/images/drone/tc1.png', import.meta.url).href },
        { title: '项目总数', value: '0', unit: '个', icon: new URL('@/assets/images/drone/tc2.png', import.meta.url).href },
        { title: '航线总数', value: '0', unit: '条', icon: new URL('@/assets/images/drone/tc6.png', import.meta.url).href},
        { title: '飞行任务', value: '0', unit: '次', icon: new URL('@/assets/images/drone/tc4.png', import.meta.url).href }
      ],
      logs: [],
      loading: false,
      chart: null
    };
  },
  mounted() {
    this.initChart();
  },
  created() {
    this.getList();
  },
  methods: {
    formatDate,
    initChart() {
      const chartDom = document.getElementById('flight-chart');
      if (!chartDom) return;

      this.chart = echarts.init(chartDom);

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: 'rgba(150,150,150,0.1)' // 更柔和的阴影
            }
          },
          backgroundColor: 'rgba(255,255,255,0.95)',
          borderWidth: 0,
          textStyle: {
            color: '#666',
            fontSize: 14
          },
          formatter: params => {
            let tip = `<div style="font-weight:600;margin-bottom:5px">${params[0].axisValue}</div>`;
            params.forEach(item => {
              tip += `<div style="display:flex;align-items:center;margin:2px 0">
            <span style="display:inline-block;width:8px;height:8px;border-radius:50%;background:${item.color};margin-right:8px"></span>
            <span style="flex:1">${item.seriesName}：</span>
            <span style="font-weight:500">${item.value} ${item.seriesName === '飞行时长' ? '小时' : '次'}</span>
          </div>`;
            });
            return tip;
          }
        },
        legend: {
          data: ['飞行次数', '飞行时长'],
          top: 10,
          itemGap: 30,
          textStyle: {
            fontSize: 14,
            color: '#666'
          },
          itemStyle: {
            borderWidth: 0
          }
        },
        grid: {
          left: '1%',
          right: '1%',
          bottom: '1%',
          containLabel: true,
          backgroundColor: '#fff' // 增加背景色
        },
        xAxis: {
          type: 'category',
          axisLine: {
            lineStyle: {
              color: '#ddd'
            }
          },
          axisLabel: {
            interval: 0,
            color: '#666',
            fontSize: 12
          },
          splitLine: {
            show: false
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '飞行次数',
            nameTextStyle: {
              color: '#666',
              fontSize: 14
            },
            axisLabel: {
              formatter: '{value} 次',
              color: '#999'
            },
            splitLine: {
              lineStyle: {
                color: '#f5f5f5'
              }
            }
          },
          {
            type: 'value',
            name: '飞行时长',
            nameTextStyle: {
              color: '#666',
              fontSize: 14
            },
            axisLabel: {
              formatter: '{value} 小时',
              color: '#999'
            },
            splitLine: {
              show: false
            }
          }
        ],
        series: [
          {
            name: '飞行次数',
            type: 'bar',
            barWidth: '30%',
            barGap: '50%',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#6B9EFF' },
                { offset: 1, color: '#409EFF' }
              ]),
              borderRadius: [4, 4, 0, 0],
              shadowColor: 'rgba(64,158,255,0.3)',
              shadowBlur: 6
            },
            label: {
              show: true,
              position: 'top',
              color: '#409EFF',
              fontSize: 12,
              distance: 8 // 标签间距
            }
          },
          {
            name: '飞行时长',
            type: 'bar',
            barWidth: '30%',
            barGap: '50%',
            yAxisIndex: 1,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#FFB13A' },
                { offset: 1, color: '#FF9F40' }
              ]),
              borderRadius: [4, 4, 0, 0],
              shadowColor: 'rgba(255,159,64,0.3)',
              shadowBlur: 6
            },
            label: {
              show: true,
              position: 'top',
              color: '#FF9F40',
              fontSize: 12,
              distance: 8
            }
          }
        ]
      };

      this.chart.setOption(option);
    },
    async fetchChartData() {
      try {
        const response = await flightNumMonthOfYear({ year: this.year });

        const months = Array.from({ length: 12 }, (_, i) => i + 1);
        const flightNumData = [];
        const flightDurationData = [];

        months.forEach(month => {
          const item = response.data[month] || {};
          flightNumData.push(item.flightNum || 0);
          flightDurationData.push(item.flightDuration || 0);
        });

        if (this.chart) {
          this.chart.setOption({
            xAxis: { data: months.map(m => `${m}月`) },
            series: [
              { data: flightNumData },
              { data: flightDurationData }
            ]
          });
        }
      } catch (error) {
        console.error('数据加载失败:', error);
        this.chart?.setOption({
          graphic: {
            type: 'text',
            left: 'center',
            top: 'middle',
            style: {
              text: '数据加载失败',
              fontSize: 20,
              fill: '#999'
            }
          }
        });
      }
    },
    async getList(){
      await Promise.all([
        this.fetchChartData(),
        this.fetchLogs(),
        this.fetchStatsData()
      ]);
    },
    async fetchStatsData() {
      try {
        const response = await statisticHome();
        const statsMapping = {
          '设备总数': 'deviceNum',
          '项目总数': 'projectNum',
          '飞行任务': 'taskNum',
          '航线总数': 'waylineNum',
          '系统状态': 'systemStatus'
        };

        this.stats = this.stats.map(statItem => {
          const apiKey = statsMapping[statItem.title];
          return {
            ...statItem,
            value: response.data[apiKey] ?? statItem.value,
            unit: statItem.unit
          };
        });
      } catch (error) {
        console.error('获取统计数据失败:', error);
        this.$message.error('统计数据加载失败');
      }
    },
    async fetchLogs() {
      this.loading = true;
      try {
        const response = await statisticFlightLog(this.queryParams);
        this.logs = response.data;
      } catch (error) {
        console.error('获取日志失败:', error);
        this.$message.error('日志加载失败');
      } finally {
        this.loading = false;
      }
    },
    refreshLogs (){
      this.fetchLogs();
    },
  }
};
</script>

<style scoped lang="scss">
html, body {
  margin: 0;
  padding: 0;
  height: 100%; /* 确保根元素占满整个视口 */
  overflow: hidden; /* 禁止页面滚动 */
}

.common-layout {
  display: flex;
  flex-direction: column;
  height: 88vh; /* 占满整个视口高度 */

  .el-container {
    display: flex;
    flex-direction: column;
    flex: 1; /* 让容器占满父级空间 */
    overflow: hidden; /* 禁止内部滚动 */
  }

  /* Header 样式 */
  .header {
    flex: 0 0 30%; /* 高度为窗口高度的 30% */
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Main 样式 */
  .main {
    flex: 0 0 70%; /* 高度为窗口高度的 70% */
    display: flex;
    flex-direction: row;  /* 关键修改：改为横向排列 */
    align-items: stretch; /* 让子元素撑满高度 */
    overflow: hidden;     /* 防止内容溢出 */
  }
}

.header-card{
  // padding: 20px;
  width: 100%;
  height: 100%;
}

.main-card {
  display: flex;
  flex-direction: row;  /* 明确指定横向排列 */
  gap: 10px;           /* 添加间距 */
  width: 100%;
  height: 100%;
}

.main-card-left,
.main-card-right {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.header-icon {
  width: 16px;
  height: 16px;
  margin-right: 10px;
}

.card-header {
  display: flex;
  justify-content: space-between; /* 标题靠左，时间选择器靠右 */
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
}

.header-text {
  font-size: 16px;
  font-weight: bold;
}

.stat-content {
  flex: 1;
  min-width: 0;
}

.stat-title {
  font-size: 18px;
  color: #333333;
  margin-bottom: 8px;
  font-weight: bold;
  white-space: nowrap; /* 标题不换行 */
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #2077FF;
  margin-bottom: 4px;
  display: flex;
  white-space: nowrap; /* 数值不换行 */
}
.stat-unit {
  font-size: 12px;
  font-weight: 200;
  color: #605F5F;
  margin-left: 5px;
}
.stat-icon {
  width: 105px;
  height: 105px;
  // margin-left: 16px;
  flex-shrink: 0; /* 防止图标被压缩 */
}

/* 主容器样式 */
.stats-grid-container {
  display: grid;
  grid-auto-flow: column; /* 横向排列 */
  grid-auto-columns: 1fr; /* 自动等分宽度 */
  gap: 20px; /* 卡片间距 */
  overflow-x: auto; /* 允许横向滚动 */
  padding-bottom: 10px; /* 为滚动条预留空间 */
  scrollbar-width: thin; /* 更细的滚动条 */
}
/* 卡片样式 */
.stat-card {
  min-width: 0; /* 防止内容溢出 */
  height: 100%; /* 统一高度 */
  border: 1px solid #E1F0FF;
  box-shadow: 3px 3px 5px rgba(176, 215, 251, 0.8);
}
/* 响应式调整 */
@media (max-width: 768px) {
  .stat-card {
    min-width: 180px; /* 移动端最小宽度 */
  }

  .stat-icon {
    width: 40px;
    height: 40px;
  }
}

/* 美化滚动条 */
.stats-grid-container::-webkit-scrollbar {
  height: 6px;
}

.stats-grid-container::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  border-radius: 3px;
}

@media (max-width: 768px) {
  .home-page {
    padding: 10px;

    .stat-icon {
      width: 40px;
      height: 40px;
    }
  }
}
.common-layout .header {
    margin-top: 15px;
}
:deep(.el-card__header) {
  border-bottom: 1px solid #B0D7FB;
  padding: 14px 10px 14px 14px !important;
}
:deep(.el-card__header){
  position: sticky;
  top: 0;
}
</style>
