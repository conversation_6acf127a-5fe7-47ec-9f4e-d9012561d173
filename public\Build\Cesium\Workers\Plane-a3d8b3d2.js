define(["exports","./Cartographic-3309dd0d","./Check-7b2a090c","./when-b60132fc","./Math-119be1a3","./FeatureDetection-806b12f0"],(function(n,e,a,t,r,i){"use strict";function o(n,a){this.normal=e.Car<PERSON>ian3.clone(n),this.distance=a}o.fromPointNormal=function(n,a,r){var i=-e.Cartesian3.dot(a,n);return t.defined(r)?(e.Cartesian3.clone(a,r.normal),r.distance=i,r):new o(a,i)};var s=new e.Cartesian3;o.fromCartesian4=function(n,a){var r=e.Cartesian3.fromCartesian4(n,s),i=n.w;return t.defined(a)?(e.Cartesian3.clone(r,a.normal),a.distance=i,a):new o(r,i)},o.getPointDistance=function(n,a){return e.Cartesian3.dot(n.normal,a)+n.distance};var c=new e.Cartesian3;o.projectPointOntoPlane=function(n,a,r){t.defined(r)||(r=new e.Cartesian3);var i=o.getPointDistance(n,a),s=e.Cartesian3.multiplyByScalar(n.normal,i,c);return e.Cartesian3.subtract(a,s,r)};var l=new e.Cartesian3;o.transform=function(n,a,t){return i.Matrix4.multiplyByPointAsVector(a,n.normal,s),e.Cartesian3.normalize(s,s),e.Cartesian3.multiplyByScalar(n.normal,-n.distance,l),i.Matrix4.multiplyByPoint(a,l,l),o.fromPointNormal(l,s,t)},o.clone=function(n,a){return t.defined(a)?(e.Cartesian3.clone(n.normal,a.normal),a.distance=n.distance,a):new o(n.normal,n.distance)},o.equals=function(n,a){return n.distance===a.distance&&e.Cartesian3.equals(n.normal,a.normal)},o.ORIGIN_XY_PLANE=Object.freeze(new o(e.Cartesian3.UNIT_Z,0)),o.ORIGIN_YZ_PLANE=Object.freeze(new o(e.Cartesian3.UNIT_X,0)),o.ORIGIN_ZX_PLANE=Object.freeze(new o(e.Cartesian3.UNIT_Y,0)),n.Plane=o}));
