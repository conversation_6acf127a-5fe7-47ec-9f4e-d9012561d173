<template>
  <div class="main-content">
    <transition>
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="错误信息" prop="errorMessage">
              <el-input v-model="queryParams.errorMessage" placeholder="请输入错误信息" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card class="result-wrap">
      <el-table v-loading="loading" :data="recordList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="同步时间" align="center" prop="syncTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.syncTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="同步类型" align="center" prop="syncType" />
        <el-table-column label="同步状态" align="center" prop="status" />
        <el-table-column label="错误信息" align="center" prop="errorMessage" />
        <el-table-column label="启动时间" align="center" prop="startTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="结束时间" align="center" prop="endTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="同步" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
  </div>
</template>

<script setup name="Record">
import {
  listRecord,
  getRecord,
  delRecord,
  addRecord,
  updateRecord,
  putRecord,
  syncAll
} from '@/api/uav/syncMaintenance.js';
const { proxy } = getCurrentInstance();
const recordList = ref([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const queryFormRef = ref();
const recordFormRef = ref();
const dialog = reactive({
  visible: false,
  title: ''
});
const initFormData = {
  id: undefined,
  syncTime: undefined,
  syncType: undefined,
  status: undefined,
  recordsSynced: undefined,
  errorMessage: undefined,
  startTime: undefined,
  endTime: undefined,
}
const data = reactive({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    syncTime: undefined,
    syncType: undefined,
    status: undefined,
    recordsSynced: undefined,
    errorMessage: undefined,
    startTime: undefined,
    endTime: undefined,
    params: {
    }
  },
  rules: {
    id: [
      { required: true, message: "主键，自增 ID不能为空", trigger: "blur" }
    ],
    syncTime: [
      { required: true, message: "同步时间不能为空", trigger: "blur" }
    ],
    syncType: [
      { required: true, message: "同步类型不能为空", trigger: "change" }
    ],
    status: [
      { required: true, message: "同步状态不能为空", trigger: "change" }
    ],
  }
});
const { queryParams, form, rules } = toRefs(data);

/** 查询同步记录列表 */
const getList = async () => {
  loading.value = true;
  const res = await listRecord(queryParams.value);
  //await syncAll({startTime: new Date(), endTime: new Date()})
  recordList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  recordFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加同步记录";
}

/** 修改按钮操作 */
const handleUpdate = async (row) => {
  reset();
  console.log('row',row)
  const res = await putRecord(row.id);
  Object.assign(form.value, res.data);
  proxy?.$modal.msgSuccess("操作成功");
}

/** 提交按钮 */
const submitForm = () => {
  recordFormRef.value?.validate(async (valid) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateRecord(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addRecord(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除同步记录编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delRecord(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('uav/record/export', {
    ...queryParams.value
  }, `record_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
});
</script>
<style lang="scss" scoped>
.main-content{
    padding: 10px;
}
.result-wrap{
    margin-top: 10px;
}
</style>
