<template>
  <!-- 列设置 -->
  <el-drawer v-model="drawerVisible" title="列设置" size="450px">
    <div class="table-main">
      <el-table
        :data="colSetting"
        :border="true"
        row-key="prop"
        default-expand-all
        :tree-props="{ children: '_children' }"
      >
        <el-table-column prop="label" align="center" label="列名" />
        <el-table-column
          v-slot="scope"
          prop="isShow"
          align="center"
          label="显示"
        >
          <el-switch v-model="scope.row.isShow"></el-switch>
        </el-table-column>
        <el-table-column
          v-slot="scope"
          prop="sortable"
          align="center"
          label="排序"
        >
          <el-switch v-model="scope.row.sortable"></el-switch>
        </el-table-column>
        <template #empty>
          <div class="table-empty">
            <img src="@/assets/images/notData.png" alt="notData" />
            <div>暂无可配置列</div>
          </div>
        </template>
      </el-table>
    </div>
  </el-drawer>
</template>

<script setup>
import { ref } from "vue";

// 定义输入的属性
const props = defineProps({
  colSetting: {
    type: Array,
    required: true,
  },
});

// 定义响应式状态
const drawerVisible = ref(false);

// 打开列设置的函数
const openColSetting = () => {
  drawerVisible.value = true;
};

// 暴露函数给父组件
defineExpose({
  openColSetting,
});
</script>

<style scoped lang="scss">
.cursor-move {
  cursor: move;
}
</style>