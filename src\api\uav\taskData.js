import request from "@/utils/request";

/**
 * 查询同步记录列表
 * @param query
 * @returns {*}
 */
export const taskDataList = (query) => {
    return request({
        url: '/uav/taskData/list',
        method: 'get',
        params: query
    });
};

/**
 * 获取飞行任务详细信息
 * @param id
 * @returns {*}
 */
export const taskDataById = (id) => {
    return request({
        url: '/uav/taskData/'+id,
        method: 'get',
    });
};

/**
 * 导出飞行任务列表
 * @param query
 * @returns {*}
 */
export const taskDataExport = (query) => {
    return request({
        url: '/uav/taskData/export',
        method: 'post',
        data: query
    });
};
