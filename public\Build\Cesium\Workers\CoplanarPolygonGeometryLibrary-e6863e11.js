define(["exports","./Cartesian2-db21342c","./Cartographic-3309dd0d","./Check-7b2a090c","./FeatureDetection-806b12f0","./OrientedBoundingBox-3b145304"],(function(n,t,e,r,a,i){"use strict";var o={},u=new e.Cartesian3,s=new e.Cartesian3,C=new e.Cartesian3,c=new e.Cartesian3,d=new i.OrientedBoundingBox;function m(n,r,a,i,o){var s=e.Cartesian3.subtract(n,r,u),C=e.Cartesian3.dot(a,s),c=e.Cartesian3.dot(i,s);return t.Cartesian2.fromElements(C,c,o)}o.validOutline=function(n){var t=i.OrientedBoundingBox.fromPoints(n,d).halfAxes,r=a.Matrix3.getColumn(t,0,s),o=a.Matrix3.getColumn(t,1,C),u=a.Matrix3.getColumn(t,2,c),m=e.Cartesian3.magnitude(r),g=e.Cartesian3.magnitude(o),l=e.Cartesian3.magnitude(u);return!(0===m&&(0===g||0===l)||0===g&&0===l)},o.computeProjectTo2DArguments=function(n,t,r,o){var u,m,g=i.OrientedBoundingBox.fromPoints(n,d),l=g.halfAxes,f=a.Matrix3.getColumn(l,0,s),x=a.Matrix3.getColumn(l,1,C),B=a.Matrix3.getColumn(l,2,c),P=e.Cartesian3.magnitude(f),h=e.Cartesian3.magnitude(x),M=e.Cartesian3.magnitude(B),b=Math.min(P,h,M);return(0!==P||0!==h&&0!==M)&&(0!==h||0!==M)&&(b!==h&&b!==M||(u=f),b===P?u=x:b===M&&(m=x),b!==P&&b!==h||(m=B),e.Cartesian3.normalize(u,r),e.Cartesian3.normalize(m,o),e.Cartesian3.clone(g.center,t),!0)},o.createProjectPointsTo2DFunction=function(n,t,e){return function(r){for(var a=new Array(r.length),i=0;i<r.length;i++)a[i]=m(r[i],n,t,e);return a}},o.createProjectPointTo2DFunction=function(n,t,e){return function(r,a){return m(r,n,t,e,a)}},n.CoplanarPolygonGeometryLibrary=o}));
