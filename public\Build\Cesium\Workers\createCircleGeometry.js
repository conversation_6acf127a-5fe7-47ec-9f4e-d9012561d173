define(["./Cartographic-3309dd0d","./Check-7b2a090c","./when-b60132fc","./EllipseGeometry-204f6df4","./Rectangle-dee65d21","./VertexFormat-6446fca0","./Math-119be1a3","./arrayFill-4513d7ad","./buildModuleUrl-9085faaa","./FeatureDetection-806b12f0","./Cartesian4-3ca25aab","./RuntimeError-4a5c8994","./WebGLConstants-4ae0db90","./Event-16a2dfbf","./Cartesian2-db21342c","./ComponentDatatype-c140a87d","./EllipseGeometryLibrary-a39b75ad","./GeometryAttribute-c65394ac","./GeometryAttributes-252e9929","./GeometryInstance-6bd4503d","./GeometryOffsetAttribute-fbeb6f1a","./GeometryPipeline-7a733318","./AttributeCompression-0a087f75","./EncodedCartesian3-f1396b05","./IndexDatatype-8a5eead4","./IntersectionTests-0d6905a3","./Plane-a3d8b3d2"],(function(e,t,i,r,o,a,n,l,s,d,m,c,u,p,y,_,h,G,x,f,g,b,v,E,w,A,C){"use strict";function M(e){var t=(e=i.defaultValue(e,i.defaultValue.EMPTY_OBJECT)).radius,o={center:e.center,semiMajorAxis:t,semiMinorAxis:t,ellipsoid:e.ellipsoid,height:e.height,extrudedHeight:e.extrudedHeight,granularity:e.granularity,vertexFormat:e.vertexFormat,stRotation:e.stRotation,shadowVolume:e.shadowVolume};this._ellipseGeometry=new r.EllipseGeometry(o),this._workerName="createCircleGeometry"}M.packedLength=r.EllipseGeometry.packedLength,M.pack=function(e,t,i){return r.EllipseGeometry.pack(e._ellipseGeometry,t,i)};var F=new r.EllipseGeometry({center:new e.Cartesian3,semiMajorAxis:1,semiMinorAxis:1}),V={center:new e.Cartesian3,radius:void 0,ellipsoid:o.Ellipsoid.clone(o.Ellipsoid.UNIT_SPHERE),height:void 0,extrudedHeight:void 0,granularity:void 0,vertexFormat:new a.VertexFormat,stRotation:void 0,semiMajorAxis:void 0,semiMinorAxis:void 0,shadowVolume:void 0};return M.unpack=function(t,n,l){var s=r.EllipseGeometry.unpack(t,n,F);return V.center=e.Cartesian3.clone(s._center,V.center),V.ellipsoid=o.Ellipsoid.clone(s._ellipsoid,V.ellipsoid),V.height=s._height,V.extrudedHeight=s._extrudedHeight,V.granularity=s._granularity,V.vertexFormat=a.VertexFormat.clone(s._vertexFormat,V.vertexFormat),V.stRotation=s._stRotation,V.shadowVolume=s._shadowVolume,i.defined(l)?(V.semiMajorAxis=s._semiMajorAxis,V.semiMinorAxis=s._semiMinorAxis,l._ellipseGeometry=new r.EllipseGeometry(V),l):(V.radius=s._semiMajorAxis,new M(V))},M.createGeometry=function(e){return r.EllipseGeometry.createGeometry(e._ellipseGeometry)},M.createShadowVolume=function(e,t,i){var r=e._ellipseGeometry._granularity,o=e._ellipseGeometry._ellipsoid,n=t(r,o),l=i(r,o);return new M({center:e._ellipseGeometry._center,radius:e._ellipseGeometry._semiMajorAxis,ellipsoid:o,stRotation:e._ellipseGeometry._stRotation,granularity:r,extrudedHeight:n,height:l,vertexFormat:a.VertexFormat.POSITION_ONLY,shadowVolume:!0})},Object.defineProperties(M.prototype,{rectangle:{get:function(){return this._ellipseGeometry.rectangle}},textureCoordinateRotationPoints:{get:function(){return this._ellipseGeometry.textureCoordinateRotationPoints}}}),function(t,r){return i.defined(r)&&(t=M.unpack(t,r)),t._ellipseGeometry._center=e.Cartesian3.clone(t._ellipseGeometry._center),t._ellipseGeometry._ellipsoid=o.Ellipsoid.clone(t._ellipseGeometry._ellipsoid),M.createGeometry(t)}}));
