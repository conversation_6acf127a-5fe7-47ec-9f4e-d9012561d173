/**
 * 储备土地管护-->巡查任务管理
 */
import request from "@/utils/request";

export const reserveTaskPublish = (data) => {
  return request({
    url: "/patrol/ghTask",
    data: data,
    method: "post"
  })
}

/**
 * 查询巡查任务列表
 * @param id
 */
export const listPatrolTask = (query) => {
  return request({
    url: "/patrol/ghTask/list",
    params: query,
    method: "get"
  })
}


/**
 * 删除巡查任务管理
 * @param id
 */
export const delPatrolTask = (id) => {
  return request({
    url: "/patrol/ghTask/" + id,
    method: "delete"
  });
};

/**
 * 查询储备土地管护巡查任务详情
 * @param id
 */
export const getReserveTask = (id) => {
  return request({
    url: "/patrol/ghTask/" + id,
    method: "get"
  });
};


/**
 * 新增巡查任务管理
 * @param data
 */
export const addPatrolTask = (data) => {
  return request({
    url: "/patrol/ghTask",
    method: "post",
    data: data
  });
};

/**
 * 新增巡查任务记录
 * @param data
 */
export const addPatrolRecord = (data) => {
  return request({
    url: "/patrol/ghRecord",
    method: "post",
    data: data
  });
};


/**
 * 获取巡查记录信息列表
 */
export const listGhRecord = (query)=>{
  return request({
    url: "/patrol/ghRecord/list",
    method: "get",
    params: query
  })
}

/**
 * 修改巡查任务管理
 * @param data
 */
export const updatePatrolTask = (data) => {
  return request({
    url: "/patrol/ghTask",
    method: "put",
    data: data
  });
};

/**
 * 获取储备管护附件数据
 */
export const getAttachments = (query)=>{
  return request({
    url: "/patrol/attachmentFile/list",
    method: "get",
    params: query
  })
}

/**
 * 删除储备管护文件信息
 */
export const deleteFileById = (id) => {
  return request({
    url: "/patrol/attachmentFile/" + id,
    method: "delete"
  })
}

/**
 * 查询巡查记录详细
 * @param id
 */
export const getGhRecord = (id) => {
  return request({
    url: "/patrol/ghRecord/" + id,
    method: "get"
  });
};

