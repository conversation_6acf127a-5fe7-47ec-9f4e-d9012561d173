<template>
  <div class="container">
    <div v-if="!showDetail" class="project-list">
      <transition name="fade">
        <div v-show="showSearch" class="search-container">
          <el-card shadow="hover" class="search-card">
            <el-form ref="queryFormRef" :model="queryParams" :inline="true">
              <el-form-item label="设备类型" prop="xmmc" class="serchtop">
                <el-input
                  v-model="queryParams.xmmc"
                  placeholder="请输入项目名称"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item label="设备SN" prop="xmbh" class="serchtop">
                <el-input
                  v-model="queryParams.xmbh"
                  placeholder="请输入经度"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
<!--              <el-form-item-->
<!--                label="设备状态"-->
<!--                prop="sceneType"-->
<!--                style="width: 250px"-->
<!--                class="serchtop"-->
<!--              >-->
<!--                <el-select-->
<!--                  v-model="form.sceneType"-->
<!--                  placeholder="请选择场景类型"-->
<!--                  clearable-->
<!--                  @keyup.enter="handleQuery"-->
<!--                >-->
<!--                  <el-option-->
<!--                    v-for="item in xzqdms"-->
<!--                    :key="item.value"-->
<!--                    :label="item.label"-->
<!--                    :value="item.value"-->
<!--                  />-->
<!--                </el-select>-->
<!--              </el-form-item>-->

              <el-form-item class="serchtop">
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </div>
      </transition>

      <el-card shadow="never" class="result-card">
        <el-table v-loading="loading" :data="reserveProjectList" @selection-change="handleSelectionChange" style="width: 100%" stripe>
          <el-table-column label="设备类型" align="center" prop="keyName" min-width="100">
            <template #default="scope">
              {{ scope.row.drone.deviceModel.keyName }}
            </template>
          </el-table-column>
          <el-table-column label="设备型号" align="center" prop="name">
            <template #default="scope">
              {{ scope.row.drone.deviceModel.name }}
            </template>
          </el-table-column>
          <el-table-column label="设备SN" align="center" prop="sn">
            <template #default="scope">
              {{ scope.row.drone.sn }}
            </template>
          </el-table-column>
          <!-- <el-table-column label="设备状态" align="center" prop="modeCode" width="180">
            <template #default="scope">
              {{ getFlightStatusLabel(scope.row.drone.modeCode) }}
            </template>
          </el-table-column> -->
          <el-table-column label="设备状态" align="center" prop="modeCode" width="180">
            <template #default="scope">
              <div style="display: flex;align-items: center;justify-content: center;">
                <span class="status-dot" :style="{backgroundColor: getStatusColor(scope.row.drone.modeCode),}"></span>
                {{ getFlightStatusLabel(scope.row.drone.modeCode) }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="200" fixed="right">
            <template #default="scope">
              <el-button plain type="primary" size="small" @click="handleUpdate(scope.row)">查看</el-button>
              <el-button plain type="primary" size="small" @click="handleSelect(scope.row)">跳转驾驶舱</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
          class="pagination-container"
        />
      </el-card>
    </div>

    <div v-else class="project-detail">
      <div class="dashboard-container">
        <div class="add-header-title">
          <div class="add-title">{{ headerTitle }}</div>
          <div class="add-title-return" @click="goBack">
            <img src="@/assets/images/img-return.png" class="back" />
            <div class="backlist">返回列表</div>
          </div>
        </div>
        <!-- 主设备信息区 -->
        <div class="main-content">
          <!-- 左侧设备信息 -->
          <div class="device-profile">
            <div class="status-card">
              <div class="content-title-1">
                <img src="@/assets/images/left.png" />
                <p>设备信息</p>
              </div>
              <!-- <span class="status-title">设备状态</span> -->
              <el-tag type="success" class="status-badge">当前正常</el-tag>
            </div>

            <div class="device-details">
              <img
                src="@/assets/images/wrj.png"
                alt="Drone Image"
                class="device-image"
              />
              <!-- 设备信息部分 -->
              <div class="info-list">
                <div class="info-item">
                  <span class="info-label">设备类型:</span>
                  <span class="info-value">{{ EquipmentFrom.device_model.name }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">设备在线状态:</span>
                  <span class="info-value">{{ EquipmentFrom.device_state.mode_code === 'not_connected' ? '离线' : '在线' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">状态:</span>
                  <span class="info-value">{{ StatusUtils.flightStateToChinese(EquipmentFrom.device_state.mode_code) }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">型号:</span>
                  <span class="info-value">{{ EquipmentFrom.device_model.name }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">飞行器SN:</span>
                  <span class="info-value">{{ EquipmentFrom.device_sn }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">左电池SN:</span>
                  <span class="info-value">{{ EquipmentFrom.device_state.battery.batteries[0]?.sn || '-' }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧数据区 -->
          <div class="data-panel">
            <!-- 飞行数据卡片 -->
            <div class="data-cards">
              <div class="data-card">
                <div class="card-label">累计飞行时长</div>
                <div class="card-value highlight">  {{ (EquipmentFrom.device_state.total_flight_time / 3600).toFixed(1) }} 小时</div>
              </div>
              <div class="data-card">
                <div class="card-label">飞行架次</div>
                <div class="card-value highlight">{{ EquipmentFrom.device_state.total_flight_sorties }} 次</div>
              </div>
              <div class="data-card">
                <div class="card-label">图传</div>
                <div class="card-value">RTK {{ EquipmentFrom.device_state.position_state.rtk_number }}</div>
              </div>
              <div class="data-card">
                <div class="card-label">搜量状态</div>
                <div class="card-value" :class="{
                     'status-good': EquipmentFrom.device_state.position_state.gps_number > 20,
                     'status-warning': EquipmentFrom.device_state.position_state.gps_number <= 20
                  }">
                  {{ EquipmentFrom.device_state.position_state.quality === 'gear_5' ? '良好' : '信号弱' }}
                </div>
              </div>
            </div>

            <!-- 电池信息表格 -->
            <div class="section">
              <h3 class="section-title">电池状态详情</h3>
              <div class="battery-table">
                <div class="table-header">
                  <div class="header-cell">电池</div>
                  <div class="header-cell">循环次数</div>
                  <div class="header-cell">高电量存储</div>
                  <div class="header-cell">电量</div>
                  <div class="header-cell">备降转移高</div>
                  <div class="header-cell">电压</div>
                  <div class="header-cell">温度</div>
                </div>
                <div class="table-row left-battery">
                  <div class="row-cell battery-label">左电池</div>
                  <div class="row-cell">{{ EquipmentFrom.device_state.battery.batteries[0]?.loop_times || 0 }} 次</div>
                  <div class="row-cell">{{ EquipmentFrom.device_state.battery.batteries[0]?.high_voltage_storage_days || 0 }} 天</div>
                  <div class="row-cell highlight">{{ EquipmentFrom.device_state.battery.capacity_percent }}%</div>
                  <div class="row-cell">{{ EquipmentFrom.device_state.battery.landing_power }}m</div>
                  <div class="row-cell">{{ (EquipmentFrom.device_state.battery.batteries[0]?.voltage / 1000).toFixed(1) }}V</div>
                  <div class="row-cell">{{ EquipmentFrom.device_state.battery.batteries[0]?.temperature }}℃</div>
                </div>
              </div>
            </div>

            <!-- 设备状态卡片 -->
            <div class="status-container">
              <h3 class="section-title">设备状态</h3>
              <div class="status-grid">
                <div class="status-row">
                  <div class="status-item">
                    <span class="status-name">飞行器夜航灯</span>
                    <el-tag :type="EquipmentFrom.device_state.night_lights_state === 'close' ? 'info' : 'success'" class="status-tag">
                      {{ EquipmentFrom.device_state.night_lights_state === 'close' ? '关闭' : '开启' }}
                    </el-tag>
                  </div>
                  <div class="status-item">
                    <span class="status-name">限高</span>
                    <span class="status-value">{{ EquipmentFrom.device_state.height_limit }}m</span>
                  </div>
                </div>
                <div class="status-row">
                  <div class="status-item">
                    <span class="status-name">避障</span>
                    <el-tag type="success" class="status-tag">
                      {{ Object.values(EquipmentFrom.device_state.obstacle_avoidance).every(v => v === 'enable') ? '开启' : '部分关闭' }}
                    </el-tag>
                  </div>
                  <div class="status-item">
                    <span class="status-name">运行模式</span>
                    <el-tag class="status-tag">
                      {{ EquipmentFrom.device_state.commander_flight_mode === 'optimal_height_flight' ? '智能高度飞行' : '设定高度飞行' }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 相机信息区 -->
        <div class="section1">
          <div class="content-title-1">
            <img src="@/assets/images/left.png" />
            <p>相机信息</p>
          </div>
          <el-table :data="cameraData" style="width: 100%" class="camera-table">
            <!-- 相机索引 -->
            <el-table-column prop="payload_index" label="相机索引" width="120"></el-table-column>

            <!-- 相机模式 -->
            <el-table-column prop="camera_mode" label="相机模式" width="120">
              <template #default="{ row }">
                <el-tag type="info">{{ row.camera_mode }}</el-tag>
              </template>
            </el-table-column>

            <!-- 广角镜头 -->
            <el-table-column label="广角镜头">
              <template #default="{ row }">
                <div>
                  <p>曝光模式：{{ row.wide_exposure_mode }}</p>
                  <p>ISO：{{ row.wide_iso }}</p>
                  <p>快门速度：{{ row.wide_shutter_speed }}</p>
                </div>
              </template>
            </el-table-column>

            <!-- 变焦镜头 -->
            <el-table-column label="变焦镜头">
              <template #default="{ row }">
                <div>
                  <p>曝光模式：{{ row.zoom_exposure_mode }}</p>
                  <p>ISO：{{ row.zoom_iso }}</p>
                  <p>快门速度：{{ row.zoom_shutter_speed }}</p>
                  <p v-if="row.zoom_focus_mode">对焦模式：{{ row.zoom_focus_mode }}</p>
                  <p v-if="row.zoom_focus_value !== undefined">当前焦距：{{ row.zoom_focus_value }}</p>
                </div>
              </template>
            </el-table-column>

            <!-- 拍照状态 -->
            <el-table-column label="拍照状态" width="120">
              <template #default="{ row }">
                <el-tag :type="row.photo_state === 'idle' ? 'success' : 'warning'">
                  {{ row.photo_state === "idle" ? "空闲" : "拍摄中" }}
                </el-tag>
              </template>
            </el-table-column>

            <!-- 剩余照片数量 -->
            <el-table-column prop="remain_photo_num" label="剩余照片数量" width="150">
              <template #default="{ row }">
                <strong>{{ row.remain_photo_num }}</strong>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="ReserveProject">
import { organizationDeviceList } from "@/api/uav/flightHub2/organization/device.js";
import { FLIGHT_STATUS_LIST, StatusUtils} from "@/constants/flightTask.js";
import {deviceSn} from "@/api/uav/flightHub2/way_line.js";
const { proxy } = getCurrentInstance();
const showDetail = ref(false);

// 方法
const goBack = () => {
  showDetail.value = false;
};
//设备状态颜色
const getStatusColor = (modeCode) => {
  const status = xzqdms.value.find(
    (item) => item.value === getFlightStatusLabel(modeCode)
  );
  if (!status) return "#999"; // 默认灰色

  switch (status.type) {
    case "success":
      return "#67C23A"; // 绿色
    case "info":
      return "#409EFF"; // 蓝色
    case "warning":
      return "#E6A23C"; // 黄色
    case "danger":
      return "#F56C6C"; // 红色
    default:
      return "#999"; // 默认灰色
  }
};

const xzqdms = ref([
  {
    value: "设备空闲中",
    label: "设备空闲中",
    type: "success",
  },
  {
    value: "待机",
    label: "待机",
    type: "warning",
  },

  {
    value: "现场调试",
    label: "现场调试",
    type: "warning",
  },
  {
    value: "自动起飞",
    label: "自动起飞",
    type: "info",
  },
  {
    value: "固件升级中",
    label: "固件升级中",
    type: "warning",
  },
  {
    value: "远程调试",
    label: "远程调试",
    type: "warning",
  },
  {
    value: "航线飞行",
    label: "航线飞行",
    type: "info",
  },
  {
    value: "机场选址中",
    label: "机场选址中",
    type: "info",
  },
  {
    value: "作业中",
    label: "作业中",
    type: "success",
  },
  {
    value: "强制降落",
    label: "强制降落",
    type: "danger",
  },
]);
const cameraData = ref([]);
const EquipmentFrom = ref({});

// 数据表格
const tableDataCQ = ref([]);
const tableDataDK = ref([]);
const reserveProjectList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const buttonLoading = ref(false);
const queryFormRef = ref();
const reserveProjectFormRef = ref();

const initFormData = {
  id: undefined,
  bz: undefined,
  rightBos: undefined,
  landBos: undefined,
};

//详情页动态表头
const headerTitle = computed(() => {
  if (!EquipmentFrom.value.device_model) return "设备信息";
  const deviceType = EquipmentFrom.value.device_model?.name || "";
  return `${deviceType}设备信息`;
});

const data = reactive({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    params: {},
  },
  rules: {
    id: [{ required: true, message: "$comment不能为空", trigger: "blur" }],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询储备项目列表 */
const getList = async () => {
  loading.value = true;
  const res = await organizationDeviceList(queryParams.value);
  reserveProjectList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

// 获取飞行状态标签的辅助函数
const getFlightStatusLabel = (modeCode) => {
  const status = FLIGHT_STATUS_LIST.find((item) => item.value === modeCode);
  return status ? status.label : "未知状态";
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 修改按钮操作 */
const handleUpdate = async (row) => {
  EquipmentFrom.value = row;
  const res = await deviceSn(row.drone.sn)
  Object.assign(EquipmentFrom.value, res.data);
  EquipmentFrom.value = res.data
  cameraData.value = res.data.device_state.cameras
  console.log(cameraData.value)
  showDetail.value = true;
};
const handleSelect = async (row) => {
  const routePath = 'droneHome';
  const fullUrl = window.location.origin +import.meta.env.VITE_APP_CONTEXT_PATH + routePath;
  window.open(fullUrl, '_blank');
};
onMounted(() => {
  getList();
});
</script>

<style scoped>
.container {
  padding: 20px;
  background-color: #f5f7fa;
  overflow-y: auto; /* 当内容超出容器高度时显示滚动条 */
  height: 85vh; /* 需要设置一个固定高度或最大高度 */
}
.search-container {
  margin-bottom: 20px;
  .search-card {
    border-radius: 8px;

    :deep(.el-card__body) {
      padding: 20px;
    }
  }
}
.result-card {
  border-radius: 8px;
  :deep(.el-card__body) {
    padding: 0;
  }
}
.pagination-container {
  padding: 16px;
  text-align: right;
}
.dashboard-container {
  width: 100%;
  margin: 0 auto;
  font-family: "Helvetica Neue", Arial, sans-serif;
  color: #333;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  .dashboard-title {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
    margin: 0;
  }

  .back-button {
    border-radius: 4px;
  }
}

.back-button {
  padding: 10px 15px;
}

.main-content {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  width: 100%;
}

.device-profile {
  flex: 0 0 320px;
  background: #fff;
  border-radius: 8px;
  padding: 0 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.status-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.status-container {
  width: 100%;
  background: #fff;
  border-radius: 8px;
  margin-bottom: 24px;
}
.status-row {
  display: flex;
  gap: 12px;
}

.status-title {
  font-size: 16px;
  font-weight: 500;
}

.status-badge {
  font-size: 14px;
}

.device-details {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.device-image {
  max-width: 250px;
  margin-bottom: 20px;
}

.info-list {
  width: 100%;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px dashed #ebeef5;
}

.info-label {
  color: #909399;
  font-size: 14px;
}

.info-value {
  font-weight: 500;
  color: #303133;
}

.data-panel {
  flex: 1;
}

.data-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr); /* 5列平均分配 */
  gap: 16px;
  margin-bottom: 24px;
  height: 14%;
}

.data-card {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.card-value {
  font-size: 18px;
  font-weight: 600;
}

.highlight {
  color: #409eff;
}

.status-good {
  color: #00ae00;
}
.history-section {
  background: #fff;
  border-radius: 8px;
  padding: 0 20px 15px 20px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  width: 100%;
}
.section {
  background: #fff;
  border-radius: 8px;
  margin-bottom: 24px;
  width: 100%;
}

.section-title {
  font-size: 18px;
  font-weight: 500;
  margin: 0 0 16px 0;
  color: #303133;
}
.section1 {
  background: #fff;
  border-radius: 8px;
  margin-bottom: 24px;
  width: 100%;
  padding: 0 20px 15px 20px;
}
.battery-table {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  border: 1px solid #ebeef5;
  border-radius: 8px;
  overflow: hidden;
}

.table-header {
  display: contents;
}

.header-cell {
  background-color: #f5f7fa;
  padding: 12px 16px;
  font-weight: 600;
  text-align: center;
  color: #303133;
}

.table-row {
  display: contents;
}

.row-cell {
  padding: 12px 16px;
  text-align: center;
  border-top: 1px solid #ebeef5;
}

.left-battery .row-cell {
  background-color: #f0f7ff;
}

.left-battery .battery-label {
  color: #0066cc;
  font-weight: 600;
}

.right-battery .row-cell {
  background-color: #fff0f0;
}

.right-battery .battery-label {
  color: #cc3300;
  font-weight: 600;
}

.status-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.status-item {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f5f7fa;
  border-radius: 4px;
  min-height: 90px;
}

.status-name {
  font-size: 14px;
  color: #606266;
}

.status-value {
  font-weight: 500;
}

.status-tag {
  margin-left: 8px;
}

.lens-tag {
  margin-right: 8px;
}

/* 响应式设计 */
@media (max-width: 992px) {
  .main-content {
    flex-direction: column;
  }

  .device-profile {
    flex: 1;
  }
}

@media (max-width: 768px) {
  .data-cards {
    grid-template-columns: repeat(2, 1fr);
  }

  .status-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .data-cards {
    grid-template-columns: 1fr;
  }

  .status-grid {
    grid-template-columns: 1fr;
  }

  .battery-table {
    display: block;
    overflow-x: auto;
  }
}
.serchtop {
  margin-bottom: 0 !important;
}
.add-header-title {
  padding: 10px;
  display: flex;
  justify-content: space-between;
  height: 50px;
  font-weight: bold;
  background-color: rgb(222, 239, 255);
  box-sizing: border-box;
  border-bottom: 1px solid rgb(233, 233, 233);
  font-weight: 700;
  font-size: 16px;
  line-height: 28px;
}

.add-title-return {
  display: flex;
  align-content: center;
  color: rgb(32, 119, 255);
  cursor: pointer;
  font-weight: normal;
}

.back {
  height: 18px;
  width: 18px;
  margin-top: 5px;
}

.backlist {
  padding-left: 6px;
  font-size: 14px;
}
.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}
.content-title-1 {
  font-weight: bold;
  display: flex;
  align-items: center;
  p {
    margin-left: 8px;
  }
}
</style>
