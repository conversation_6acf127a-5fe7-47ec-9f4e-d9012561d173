<template>
  <div v-if="isShow" class="analyse-wrap">
    <!-- <Header :header-info="headerInfo" @closePanel="closePanel" /> -->
    <div class="tab-container">
      <el-button type="primary" @click="drawViewArea">
        <svg-icon icon-class="viewshed" />
        <span class="sight-btn">绘制可视域</span>
      </el-button>
      <el-button type="primary" @click="drawClipArea">
        <svg-icon icon-class="viewshed" />
        <span class="sight-btn">绘制裁剪面</span>
      </el-button>
      <div class="clip-mode">
        <label for="">裁剪模式</label>
        <el-select v-model="clipMode" placeholder="请选择裁剪模式">
          <el-option
            v-for="item in clipModeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
      <el-button type="primary" @click="clearResult">
        <svg-icon icon-class="clear" />
        <span class="sight-btn">清除</span>
      </el-button>
    </div>

    <div v-if="isShowAction" ref="modelTool" class="model-action">
      <div class="action-title">
        <h5>属性编辑</h5>
      </div>
      <div class="action-panel">
        <div class="action-item clearfix">
          <span class="action-label">方向（度）</span>
          <el-slider
            id="pitch"
            v-model="viewModel.direction"
            class="action-slider"
            :min="0"
            :max="360"
            :step="1"
            @input="handleAction('direction')"
          />
        </div>
        <div class="action-item clearfix">
          <span class="action-label">翻转（度）</span>
          <el-slider
            v-model="viewModel.pitch"
            class="action-slider"
            :min="-90"
            :max="90"
            :step="1"
            @input="handleAction('pitch')"
          />
        </div>
        <div class="action-item clearfix">
          <span class="action-label">距离(米)</span>
          <el-slider
            v-model="viewModel.distance"
            class="action-slider"
            :min="1"
            :max="10000"
            :step="1"
            @input="handleAction('distance')"
          />
        </div>
        <div class="action-item action-move clearfix">
          <span class="action-label">水平视场角(度)</span>
          <el-slider
            v-model="viewModel.horizontalFov"
            class="action-slider"
            :min="1"
            :max="120"
            :step="1"
            @input="handleAction('horizontalFov')"
          />
        </div>
        <div class="action-item action-move clearfix">
          <span class="action-label">垂直视场角(度)</span>
          <el-slider
            v-model="viewModel.verticalFov"
            class="action-slider"
            :min="5"
            :max="90"
            :step="1.0"
            @input="handleAction('verticalFov')"
          />
        </div>
        <div class="action-item action-move clearfix">
          <span class="action-label">可见区域颜色</span>
          <el-color-picker
            v-model="viewModel.visibleAreaColor"
            class="color-picker"
            @change="handleAction('visible')"
          />
        </div>
        <div class="action-item action-move clearfix">
          <span class="action-label">不可见区域颜色</span>
          <el-color-picker
            v-model="viewModel.invisibleAreaColor"
            class="color-picker"
            @change="handleAction('invisible')"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="ViewshedAnalysis" text="视域分析">
import useMapViewStore from "@/store/modules/map/mapView.js"
import { createTooltip, setCursor, setLayerSelectStatus } from "@/utils/Cesium/CesiumTool.js"


defineProps({
  isShow: {
    type: Boolean,
    default: true
  },
  headerInfo: {
    type: Object
  }
})

const handlerPoint = ref(undefined)
const handler = ref(undefined)
const toolTip = ref(undefined)

// 视域裁剪选项
const clipModeOptions = ref([
  {
    label: "保留区域内",
    value: "keep-inside"
  },
  {
    label: "保留区域外",
    value: "keep-outside"
  }
])

// 视域裁剪模式
const clipMode = ref("keep-inside")
// 视域对象
const viewshed3D = ref(undefined)
// 视点
const viewPosition = ref(undefined)
// 可视域样式设置
const viewModel = ref({
  direction: 1.0,
  pitch: 1.0,
  distance: 1.0,
  horizontalFov: 1.0,
  verticalFov: 1.0,
  visibleAreaColor: "",
  invisibleAreaColor: ""
})
const isShowAction = ref(false)

const viewer3d = computed(()=>useMapViewStore().viewer3d)

watch(clipMode,(value)=> {
  const clipMode = value === "keep-inside" ?
    Cesium.ClippingType.KeepInside :
    Cesium.ClippingType.KeepOutside;
  viewshed3D.value.setClipMode(clipMode);
})

const emits = defineEmits(['closePanel'])
const closePanel = ()=>{
  emits('closePanel')
}

/**
 * 可视域初始化
 */
const initViewshed = ()=> {
  toolTip.value = createTooltip(document.body);
  const scene = viewer3d.value.scene;
  if (!scene.pickPositionSupported) {
    alert("不支持深度纹理,可视域分析功能无法使用（无法添加观测）！");
    return;
  }
  // 先将此标记置为true，不激活鼠标移动事件中对可视域分析对象的操作
  scene.viewFlag = true;
  handlerPoint.value = new Cesium.DrawHandler(
    viewer3d.value,
    Cesium.DrawMode.Point
  );
  handler.value = new Cesium.ScreenSpaceEventHandler(scene.canvas);
  // 创建可视域分析对象
  viewshed3D.value = new Cesium.ViewShed3D(scene);

  // 鼠标移动时间回调
  handler.value.setInputAction((e) => {
    // 若此标记为false，则激活对可视域分析对象的操作
    if (!scene.viewFlag) {
      //获取鼠标屏幕坐标,并将其转化成笛卡尔坐标
      const windowPosition = e.endPosition;
      const position = scene.pickPosition(windowPosition)

      // 计算该点与视口位置点坐标的距离
      const distance = Cesium.Cartesian3.distance(
        viewPosition.value,
        position
      );

      if (distance > 0) {
        // 将鼠标当前点坐标转化成经纬度
        const cartographic = Cesium.Cartographic.fromCartesian(position);
        const longitude = Cesium.Math.toDegrees(
          cartographic.longitude
        );
        const latitude = Cesium.Math.toDegrees(cartographic.latitude);
        const height = cartographic.height;
        // 通过该点设置可视域分析对象的距离及方向
        viewshed3D.value.setDistDirByPoint([longitude, latitude, height]);
      }
    }
  }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

  handler.value.setInputAction((e) => {
    //鼠标右键事件回调，不再执行鼠标移动事件中对可视域的操作
    scene.viewFlag = true;
    isShowAction.value = true;
    setCursor(viewer3d.value,'pointer')

    viewModel.value.direction = viewshed3D.value.direction;
    viewModel.value.pitch = viewshed3D.value.pitch;
    viewModel.value.distance = viewshed3D.value.distance;
    viewModel.value.horizontalFov = viewshed3D.value.horizontalFov;
    viewModel.value.verticalFov = viewshed3D.value.verticalFov;

    viewModel.value.visibleAreaColor = viewshed3D.value.visibleAreaColor.toCssColorString();
    viewModel.value.invisibleAreaColor = viewshed3D.value.hiddenAreaColor.toCssColorString();
  }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
}

/**
 * 绘制可视域
 */
const drawViewArea = ()=> {
  setLayerSelectStatus(viewer3d.value,false)
  clearResult();

  const scene = viewer3d.value.scene;

  //先清除之前的可视域分析
  viewer3d.value.entities.removeAll();
  viewshed3D.value.distance = 0.1;
  scene.viewFlag = true;

  setCursor(viewer3d.value,'crosshair')
  handlerPoint.value.activate();
  handlerPoint.value.drawEvt.addEventListener((result) => {
    const position = result.object.position;
    viewPosition.value = position;

    // 将获取的点的位置转化成经纬度
    const cartographic = Cesium.Cartographic.fromCartesian(position);
    const longitude = Cesium.Math.toDegrees(cartographic.longitude);
    const latitude = Cesium.Math.toDegrees(cartographic.latitude);
    const height = cartographic.height + 1.8;
    // point.position = SuperMap3D.Cartesian3.fromDegrees(longitude, latitude, height);

    if (scene.viewFlag) {
      // 设置视口位置
      viewshed3D.value.viewPosition = [longitude, latitude, height];
      viewshed3D.value.build();
      // 将标记置为false以激活鼠标移动回调里面的设置可视域操作
      scene.viewFlag = false;
    }
  });
}

/**
 * 绘制裁剪面
 */
const drawClipArea = ()=> {
  setLayerSelectStatus(viewer3d.value,false)
  // 绘制裁剪面
  const handlerPolygon = new Cesium.DrawHandler(
    viewer3d.value,
    Cesium.DrawMode.Polygon,
    0
  );
  handlerPolygon.activate();
  // 这里为什么设置鼠标指针无效呢
  handlerPolygon.activeEvt.addEventListener((isActive) => {
    if (isActive == true) {
      setCursor(viewer3d.value, "crosshair");
    } else {
      setCursor(viewer3d.value, "pointer");
    }
  });
  handlerPolygon.movingEvt.addEventListener((windowPosition) => {
    setCursor(viewer3d.value, "crosshair");

    if (handlerPolygon.isDraging) {
      toolTip.value.showAt(
        windowPosition,
        "<p>左键绘制相交区域，右键结束</p>"
      );
    }
  });
  handlerPolygon.drawEvt.addEventListener((result) => {
    setCursor(viewer3d.value, "pointer");
    toolTip.value.setVisible(false);
    const array = [].concat(result.object.positions);
    const positions = [];

    for (let i = 0, len = array.length; i < len; i++) {
      const cartographic = Cesium.Cartographic.fromCartesian(array[i]);
      const longitude = Cesium.Math.toDegrees(cartographic.longitude);
      const latitude = Cesium.Math.toDegrees(cartographic.latitude);
      const h = cartographic.height;
      if (
        positions.indexOf(longitude) == -1 &&
        positions.indexOf(latitude) == -1
      ) {
        positions.push(longitude);
        positions.push(latitude);
        positions.push(h);
      }
    }
    handlerPolygon.polygon.show = false;
    handlerPolygon.polyline.show = false;
    viewshed3D.value.addClipRegion({ name: "test", position: positions });
    handlerPolygon.deactivate();
  });
}
/**
 * 视域编辑处理
 * @type：编辑类型
 */
const handleAction = (type)=> {
  switch (type) {
    case "direction":
      if (
        viewshed3D.value.direction !== parseFloat(viewModel.value.direction)
      ) {
        viewshed3D.value.direction = parseFloat(viewModel.value.direction);
        viewshed3D.value.removeClipRegion("test");
      }
      break;
    case "pitch":
      if (viewshed3D.value.pitch !== parseFloat(viewModel.value.pitch)) {
        viewshed3D.value.pitch = parseFloat(viewModel.value.pitch);
        viewshed3D.value.removeClipRegion("test");
      }
      break;
    case "distance":
      if (
        viewshed3D.value.distance !== parseFloat(viewModel.value.distance)
      ) {
        viewshed3D.value.distance = parseFloat(viewModel.value.distance);
        viewshed3D.value.removeClipRegion("test");
      }
      break;
    case "horizontalFov":
      if (
        viewshed3D.value.horizontalFov !==
        parseFloat(viewModel.value.horizontalFov)
      ) {
        viewshed3D.value.horizontalFov = parseFloat(
          viewModel.value.horizontalFov
        );
        viewshed3D.value.removeClipRegion("test");
      }
      break;
    case "verticalFov":
      if (
        viewshed3D.value.verticalFov !==
        parseFloat(viewModel.value.verticalFov)
      ) {
        viewshed3D.value.verticalFov = parseFloat(
          viewModel.value.verticalFov
        );
        viewshed3D.value.removeClipRegion("test");
      }
      break;
    case "visible":
      {
        const color = Cesium.Color.fromCssColorString(
          viewModel.value.visibleAreaColor
        );
        viewshed3D.value.visibleAreaColor = color;
      }
      break;
    case "invisible":
      {
        const color = Cesium.Color.fromCssColorString(
          viewModel.value.invisibleAreaColor
        );
        viewshed3D.value.hiddenAreaColor = color;
      }
      break;
  }
}

/**
 * 清除结果
 */
const clearResult = ()=> {
  isShowAction.value = false;
  viewer3d.value.entities.removeAll();
  // 清除可视对象
  viewshed3D.value.removeAllClipRegion();
  viewshed3D.value.distance = 0.1;
  viewer3d.value.scene.viewFlag = true;

  // 移除绘制图元
  handlerPoint.value.clear();
  handlerPoint.value.deactivate();
}

onBeforeMount(()=>{
  initViewshed()
})

onBeforeUnmount(()=>{
  clearResult();
  setLayerSelectStatus(viewer3d.value,true)
  const toolTipEle = document.querySelector(".twipsy")
  toolTipEle.parentNode.removeChild(toolTipEle);
})
</script>

<style scoped lang="scss">
.analyse-wrap {
  color: #fff;
  background-image: url("@/assets/images/map/tool.png");
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-size: cover;
  box-shadow: 0 0 8px 0 #057595;
}
.tool-tabs {
  display: flex;
  justify-items: center;
  justify-content: space-between;
  background-color: #4f7287;
  span {
    display: inline-block;
    width: 50%;
    padding: 10px 5px;
    text-align: center;
    color: #fff;
    &:hover {
      cursor: pointer;
      background-color: #00baff;
      filter: brightness(110%);
      color: #fff;
      transition: background-color 0.25s;
    }
  }
}
.tab-active {
  color: #fff;
  background-color: #00baff;
}
.tool-tab-content {
  padding-top: 20px;
}
:deep(.el-form-item__label) {
  width: 60px !important;
  color: #b5b5b5;
}
.tab-container {
  display: flex;
  padding: 10px;
  flex-direction: column;
  column-count: 2;
}
.tool-btn {
  margin-top: 20px;
  padding: 10px;
  width: 48%;
  background-color: #00cffa;
  border-color: #fff;
  text-align: center;
  border-radius: 4px;
  color: #ffffff;
  &:hover {
    cursor: pointer;
    color: #fff;
    filter: brightness(110%) opacity(100%);
    transition: all 0.5s ease-in;
    background: linear-gradient(to bottom right, #00baff, #00cffa);
  }
  svg {
    margin-right: 10px;
  }
}
.sight-btn {
  margin-left: 5px;
}
.el-button--primary {
  margin: 10px;
}
.clip-mode {
  margin: 10px;
  label {
    margin-right: 10px;
  }
}

.model-action {
  position: absolute;
  top: 50px;
  left: -270px;
  width: 80%;
  background-color: #001d3bdb;
  border-radius: 5px;
  z-index: 999;
}
.action-title {
  padding: 15px;
  background-image: url("@/assets/images/map/queryResultTitle2.png");
  background-repeat: no-repeat;
  font-size: 14px;
}
.action-panel {
  padding: 10px;
}
.action-item {
  line-height: 40px;
}
.action-slider {
  float: right;
  width: 60%;
}
.action-move {
  margin: 5px 0;
}
.color-picker {
  float: right;
  margin-right: 44px;
}
</style>
