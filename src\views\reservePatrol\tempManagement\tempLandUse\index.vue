<template>
  <div class="main-content">
    <div v-if="!showDetail">
      <transition>
        <div
          v-show="showSearch"
          class="mb-10"
        >
          <el-card shadow="hover">
            <el-form
              ref="queryFormRef"
              :model="queryParams" :inline="true" class="query-form"
            >
              <el-form-item
                label="行政区"
                prop="xzqdm"
              >
                <el-tree-select
                  v-model="queryParams.xzqdm" :data="region"
                  node-key="id"
                  :props="props"
                  check-strictly
                  filterable style="width: 200px"
                />
              </el-form-item>
              <el-form-item
                label="合同编号"
                prop="htbh"
              >
                <el-input
                  v-model="queryParams.htbh"
                  placeholder="请输入合同编号"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item
                label="乙方单位"
                prop="yfdw"
              >
                <el-input
                  v-model="queryParams.yfdw"
                  placeholder="请输入乙方单位"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item
                label="是否已收回"
                prop="sfysh" style="width: 250px"
              >
                <el-select
                  v-model="queryParams.sfysh"
                >
                  <el-option
                    :key="1"
                    label="已收回" :value="true"
                  />
                  <el-option
                    :key="2"
                    label="未收回" :value="false"
                  />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  icon="Search" @click="handleQuery"
                >搜索
                </el-button>
                <el-button
                  icon="Refresh"
                  @click="resetQuery"
                >重置</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </div>
      </transition>
      <el-row
        :gutter="10"
        class="mb8"
      >
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
          >新增
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="Download"
            @click="handleUpdate()"
          >导入
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="Upload"
            @click="handleExport"
          >导出
          </el-button>
        </el-col>
        <right-toolbar
          v-model:showSearch="showSearch"
          @queryTable="getList"
        />
      </el-row>
      <el-card class="result-wrap">
        <el-table
          v-loading="loading"
          :data="temporaryManageList"
        >
          <el-table-column
            label="区域"
            align="center" prop="landVo.xzqmc"
          />
          <el-table-column
            label="地块编号"
            align="center" prop="landVo.dkbh"
          />
          <el-table-column
            label="地块标识码"
            align="center" prop="dkbsm"
          />
          <el-table-column
            label="地块名称"
            align="center" prop="landVo.dkmc"
          />
          <el-table-column
            label="地块利用面积(公顷)"
            align="center" prop="dklymj"
          />
          <el-table-column
            label="合同编号"
            align="center" prop="htbh"
          />
          <el-table-column
            label="乙方单位"
            align="center" prop="yfdw"
          />
          <el-table-column
            label="是否已收回"
            align="center" prop="sfysh"
          >
            <template #default="scope">
              <el-tag
                v-if="scope.row.sfysh"
                type="primary"
              >已收回</el-tag>
              <el-tag
                v-else
                type="success"
              >未收回</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="临时利用期限"
            align="center"
            width="180"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.lslyqxq, "{y}-{m}-{d}") }}起至{{ parseTime(scope.row.lslyqxz, "{y}-{m}-{d}") }}止</span>
            </template>
          </el-table-column>
          <el-table-column
            label="利用方式"
            align="center" prop="lyfs"
          >
            <template #default="scope">
              <el-tag
                v-if="scope.row.lyfs === '1'"
                type="primary"
              >有偿</el-tag>
              <el-tag
                v-else
                type="danger"
              >无偿</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="临时利用用途"
            align="center" prop="lslyyt"
          />
          <el-table-column
            label="操作"
            align="center"
            width="180"
          >
            <template #default="scope">
              <el-button
                plain
                type="primary" size="small" @click="handleUpdate(scope.row)"
              >修改</el-button>
              <el-button
                plain
                type="danger" size="small" @click="handleDelete(scope.row)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>
    <!-- 添加或修改临时管护对话框 -->
    <div
      v-else
      class="content"
    >
      <div class="add-header-title">
        <div class="add-title">{{ ghTitle }}</div>
        <div
          class="add-title-return"
          @click="cancel"
        >
          <img
            src="../../../../assets/images/img-return.png"
            class="back"
          >
          <div class="backlist">返回列表</div>
        </div>
      </div>
      <div class="add-content-temp">
        <div>
          <div class="content-title-1">
            <img src="../../../../assets/images/left.png">
            <p>地块信息</p>
          </div>
          <el-form
            ref="landFormRef"
            :rules="rules"
            label-width="180px"
            style="padding: 10px;"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item
                  label="地块编号"
                  prop="dkbh"
                >
                  <el-autocomplete
                    v-model="land.dkbh"
                    :fetch-suggestions="querySearch"
                    @select="handleSelect"
                  >
                    <template #default="{ item }">
                      <div class="value">地块编号：{{ item.dkbh }}，项目名称：{{item.xmmc}}，位置：{{ item.dz }}</div>
                    </template>
                  </el-autocomplete>
                </el-form-item>
                <el-form-item
                  label="地块名称"
                  prop="dkmc"
                >
                  <el-input
                    v-model="land.dkmc"
                    disabled
                  />
                </el-form-item>
                <el-form-item
                  label="地块面积"
                  prop="dkmj"
                >
                  <el-input
                    v-model="land.dkmj"
                    disabled
                  >
                    <template #append>公顷</template>
                  </el-input>
                </el-form-item>
                <el-form-item
                  label="储备机构"
                  prop="cbjg"
                >
                  <el-input
                    v-model="land.cbjg"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="地块标识码"
                  prop="landDkbsm"
                >
                  <el-input
                    v-model="land.dkbsm"
                    disabled
                  />
                </el-form-item>
                <el-form-item
                  label="项目名称"
                  prop="xmmc"
                >
                  <el-input
                    v-model="land.xmmc"
                    disabled
                  />
                </el-form-item>
                <el-form-item
                  label="行政区域"
                  prop="xzqmc"
                >
                  <el-input
                    v-model="land.xzqmc"
                    disabled
                  />
                </el-form-item>
                <el-form-item
                  label="来源"
                  prop="cbjg"
                >
                  <el-input
                    v-model="land.cbjg"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="详细地址"
                  prop="bz"
                >
                  <el-input
                    v-model="land.dz"
                    :rows="2" type="textarea" disabled
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div>
          <div class="content-title-1">
            <img src="../../../../assets/images/left.png">
            <p>管护信息</p>
          </div>
          <el-form
            ref="temporaryManageFormRef"
            :model="form"
            :rules="rules"
            label-width="180px"
            style="padding: 10px;"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item
                  label="合同编号"
                  prop="htbh"
                >
                  <el-input
                    v-model="form.htbh"
                    placeholder="请输入合同编号"
                  />
                </el-form-item>
                <el-form-item
                  label="合同金额"
                  prop="htje"
                >
                  <el-input
                    v-model="form.htje"
                    placeholder="请输入合同金额"
                  >
                    <template #append>万元</template>
                  </el-input>
                </el-form-item>
                <el-form-item
                  label="临时利用期限起"
                  prop="lslyqxq"
                >
                  <el-date-picker
                    clearable
                    v-model="form.lslyqxq"
                    type="date"
                    value-format="YYYY-MM-DD"
                    placeholder="请选择临时利用期限起"
                    style="width: 100%;"
                  />
                </el-form-item>
                <el-form-item
                  label="联系人"
                  prop="lxrmc"
                >
                  <el-input
                    v-model="form.lxrmc"
                    placeholder="请输入联系人"
                  />
                </el-form-item>
                <el-form-item
                  label="临时利用用途"
                  prop="lslyyt"
                >
                  <el-input
                    v-model="form.lslyyt"
                    placeholder="请输入临时利用用途"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="乙方单位"
                  prop="yfdw"
                >
                  <el-input
                    v-model="form.yfdw"
                    placeholder="请输入乙方单位"
                  />
                </el-form-item>
                <el-form-item
                  label="地块利用面积"
                  prop="dklymj"
                >
                  <el-input
                    v-model="form.dklymj"
                    placeholder="请输入地块利用面积"
                  >
                    <template #append>公顷</template>
                  </el-input>
                </el-form-item>
                <el-form-item
                  label="临时利用期限止"
                  prop="lslyqxz"
                >
                  <el-date-picker
                    clearable
                    v-model="form.lslyqxz"
                    type="date"
                    value-format="YYYY-MM-DD"
                    placeholder="请选择临时利用期限止"
                    style="width: 100%;"
                  />
                </el-form-item>
                <el-form-item
                  label="联系电话"
                  prop="lxdh"
                >
                  <el-input
                    v-model="form.lxdh"
                    placeholder="请输入联系电话"
                  />
                </el-form-item>
                <el-form-item
                  label="利用方式"
                  prop="lyfs"
                >
                  <el-select
                    v-model="form.lyfs"
                  >
                    <el-option
                      :key="1"
                      label="有偿" value="1"
                    />
                    <el-option
                      :key="2"
                      label="无偿" value="2"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="备注"
                  prop="bz"
                >
                  <el-input
                    v-model="form.bz"
                    :rows="2" type="textarea" placeholder="请输入备注"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div>
          <div class="content-title-2">
            <div class="fujian"><img src="../../../../assets/images/left.png">
              <p>附件资料</p>
            </div>
            <el-upload
              ref="uploadRef"
              multiple
              :auto-upload="false"
              action="none"
              :show-file-list="false"
              :on-change="handleUploadSuccess"
            >
              <el-icon
                color="rgb(32, 119, 255)"
                size="22" class="el-icon--upload"
              >
                <upload-filled/>
              </el-icon>
              <div class="el-upload__text">
                <em>点击上传文件</em>
              </div>
            </el-upload>
          </div>
          <el-table
            :data="form.files"
            style="width: 100%"
          >
            <el-table-column
              label="文件名称"
              align="center" prop="wjmc"
            />
            <el-table-column
              label="文件大小"
              align="center" prop="size"
            >
              <template #default="scope">
                <span>{{
                  (scope.row.size / 1000000).toFixed(2) < 0.01 ?
                    (scope.row.size / 1000).toFixed(2) + " KB" :
                    (scope.row.size / 1000000).toFixed(2) + " MB"
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="上传人"
              align="center" prop="scrMc"
            />
            <el-table-column
              label="上传时间"
              align="center" prop="scsj"
            >
              <template #default="scope">
                <span>{{ parseTime(scope.row.scsj, "{y}-{m}-{d}") }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              align="center" width="120"
            >
              <template #default="scope">
                <el-button
                  plain
                  type="danger" size="small" @click="removeFile(scope.row)"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div class="add-content-button">
        <div class="footer-m-button">
          <el-button
            :loading="buttonLoading"
            type="primary" @click="submitForm" plain
          >
            <el-icon>
              <CircleCheckFilled/>
            </el-icon>
            提交项目信息
          </el-button>
          <el-button
            @click="cancel"
            plain
          >
            <el-icon>
              <CircleCloseFilled/>
            </el-icon>
            取 消
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="TemporaryManage">
import {
  listTemporaryManage,
  getTemporaryManage,
  delTemporaryManage,
  addTemporaryManage,
  updateTemporaryManage, getLandDetail
} from "@/api/patrol/tempMaintenance.js";
import { reserveLandlist } from "@/api/patrol/reserveProject.js";
import useUserStore from "@/store/modules/user.js";
import { parseTime } from "@/utils/ruoyi.js";
import { getRegionTreeList } from "@/api/gis/layerTree.js";
import { getGhdldm } from "@/api/system/dict/data.js";

const { proxy } = getCurrentInstance();
const userStore = useUserStore();

const temporaryManageList = ref([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const uploadRef = ref();
const total = ref(0);

const queryFormRef = ref();
const temporaryManageFormRef = ref();

const showDetail = ref(false);

const initFormData = {
  id: undefined,
  dkId: undefined,
  htbh: undefined,
  yfdw: undefined,
  lslyqxq: undefined,
  lslyqxz: undefined,
  htje: undefined,
  lslyyt: undefined,
  sfysh: undefined,
  tdlslycb: undefined,
  bz: undefined,
  files: []
};
const data = reactive({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    dkId: undefined,
    htbh: undefined,
    yfdw: undefined,
    lslyqxq: undefined,
    lslyqxz: undefined,
    htje: undefined,
    lslyyt: undefined,
    sfysh: undefined,
    tdlslycb: undefined,
    bz: undefined,
    params: {},
    xzqdm: undefined
  },
  rules: {
    htbh: [{ required: true, message: "合同编号不能为空", trigger: "blur" }],
    htje: [{ required: true, message: "合同金额不能为空", trigger: "blur" }],
    yfdw: [{ required: true, message: "乙方单位不能为空", trigger: "blur" }],
    lslyqxq: [{ required: true, message: "起始临时利用期不能为空", trigger: "blur" }],
    lslyqxz: [{ required: true, message: "结束临时利用期不能为空", trigger: "blur" }]
  }
});

const { queryParams, form, rules } = toRefs(data);

const ghdldm = ref([]);
getGhdldm().then((res) => {
  ghdldm.value = res.data;
});
/** 查询临时管护列表 */
const getList = async () => {
  loading.value = true;
  const res = await listTemporaryManage(queryParams.value);
  temporaryManageList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  showDetail.value = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  land.value = {};
  temporaryManageFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

//标题变量
const ghTitle = ref('新增临时管护信息')
/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  ghTitle.value = '新增临时管护信息'
  showDetail.value = true;
};

/** 修改按钮操作 */
const handleUpdate = async (row) => {
  reset();
  ghTitle.value = '修改临时管护信息'
  const _id = row?.id || ids.value[0];
  const res = await getTemporaryManage(_id);
  Object.assign(form.value, res.data);
  const landRes = await getLandDetail(res.data.dkId)
  land.value = landRes.data;
  showDetail.value = true;
};

/** 提交按钮 */
const submitForm = () => {
  temporaryManageFormRef.value?.validate(async (valid) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateTemporaryManage(form.value).finally(
          () => (buttonLoading.value = false)
        );
      } else {
        await addTemporaryManage(form.value).finally(
          () => (buttonLoading.value = false)
        );
      }
      proxy?.$modal.msgSuccess("操作成功");
      showDetail.value = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal
    .confirm('是否确认删除临时管护编号为"' + _ids + '"的数据项？')
    .finally(() => (loading.value = false));
  await delTemporaryManage(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    "patrol/temporaryManage/export",
    {
      ...queryParams.value
    },
    `temporaryManage_${new Date().getTime()}.xlsx`
  );
};

const handleUploadSuccess = (file, files) => {
  files.forEach((singlefile) => {
    singlefile.scrMc = userStore.user.nickName;
    singlefile.scrId = userStore.user.userId;
    singlefile.scsj = parseTime(new Date(), "{y}-{m}-{d} {h}:{i}:{s}");
    singlefile.wjmc = singlefile.name;
  })
  form.value.files = files;
}

const removeFile = (row) => {
  uploadRef.value.handleRemove(row);
}

const querySearch = async (queryString, callback) => {
  const results = await createFilter(queryString)
  callback(results.rows);
}
const createFilter = async (queryString) => {
  return await reserveLandlist({
    dkbh: queryString,
    pageNum: 1,
    pageSize: 10
  });
}

const land = ref({});
const handleSelect = async (row) => {
  const landRes = await getLandDetail(row.id);
  land.value = landRes.data;
  form.value.dkId = landRes.data.id;
  form.value.dkbsm = landRes.data.dkbsm;
}

const props = {
  value: 'areaCode',
  children: 'children',
  label: 'name'
}
const region = ref([]);
const regionTree = async () => {
  const tree = await getRegionTreeList();
  region.value = tree.data;
}

onMounted(() => {
  getList();
  regionTree();
});
</script>
<style lang="scss" scoped>
.main-content {
  padding: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}

.content {
  border: 1px solid rgb(233, 233, 233);
  border-radius: 4px;
}

.add-header-title {
  padding: 10px;
  display: flex;
  justify-content: space-between;
  height: 50px;
  background-color: rgb(222, 239, 255);
  box-sizing: border-box;
  border-bottom: 1px solid rgb(233, 233, 233);
  font-weight: 700;
  font-size: 14px;
  line-height: 28px;
}

.add-title-return {
  display: flex;
  align-content: center;
  color: rgb(32, 119, 255);
  cursor: pointer;
  font-weight: normal;
}

.add-content-button {
  width: 100%;
  height: 70px;
  line-height: 70px;
  text-align: center;
  border-top: 1px solid #e0e0e0;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.add-content-temp {
  overflow-y: auto;
  padding: 0px 10px;
}

.content-title-1 {
  font-weight: bold;
  display: flex;
  align-items: center;

  p {
    margin-left: 8px;
  }
}

.content-title-2 {
  height: 60px;
  line-height: 70px;
  font-weight: bold;
  display: flex;
  justify-content: space-between;
}

.el-upload__text {
  color: rgb(32, 119, 255);
}

.fujian {
  display: flex;
  align-items: center;

  p {
    margin-left: 8px;
  }
}

.el-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.back {
  height: 18px;
  width: 18px;
  margin-top: 5px;
}

.backlist {
  padding-left: 6px;
  font-size: 14px;
}

.footer-m-button {
  button {
    height: 40px;
  }
}

.container2 {
  margin: 0 auto;
  background: #fff;
}

.el-radio-group {
  display: flow;
}

.land-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  border-bottom: 1px solid #ebeef5;
  transition: background-color 0.3s;
}

.land-item:hover {
  background-color: #f5f7fa;
}

.land-info {
  flex: 1;
}

.land-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 8px;
  color: #303133;
}

.land-details {
  font-size: 14px;
  color: #606266;
}

.land-detail {
  margin-right: 15px;
  display: inline-block;
}

.next-button {
  margin-top: 20px;
  text-align: right;
}

.page-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #303133;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}
@media(max-width: 1000px){
  .query-form{
    :deep(.el-form-item) {
      margin-bottom: 18px !important;
    }
  }
}
@media(min-width: 1500px){
  .query-form{
    :deep(.el-form-item) {
      margin-bottom: 0 !important;
    }
  }
}
</style>
