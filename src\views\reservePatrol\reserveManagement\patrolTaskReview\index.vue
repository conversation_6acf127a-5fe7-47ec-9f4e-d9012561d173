<template>
  <div class="main-content">
    <transition>
      <div
        v-show="showSearch"
        class="mb-[10px] top"
      >
        <el-card shadow="hover">
          <el-form
            ref="queryFormRef"
            :model="queryParams" :inline="true"
            class="query-form"
          >
            <el-form-item
              label="巡查人员"
              prop="xcry" class="nameone"
            >
              <el-input
                v-model="queryParams.xcry"
                placeholder="请输入巡查人员"
                @keyup.enter="handleQuery"
                clearable
              />
            </el-form-item>
            <!--            <el-form-item-->
            <!--              label="巡查时间"-->
            <!--              prop="xcsj" class="nameone"-->
            <!--            >-->
            <!--              <el-date-picker-->
            <!--                v-model="queryDate"-->
            <!--                type="date"-->
            <!--                value-format="YYYY-MM-DD"-->
            <!--                placeholder="请选择巡查时间"-->
            <!--              />-->
            <!--            </el-form-item>-->
            <el-form-item class="nameone">
              <el-button
                type="primary"
                icon="Search" @click="handleQuery"
              >搜索
              </el-button>
              <el-button
                icon="Refresh"
                @click="resetQuery"
              >重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>
    <el-card class="result-wrap">
      <el-table
        v-loading="loading"
        :data="patrolRecordList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          label="巡查人员"
          align="center" prop="xcry"
        />
        <el-table-column
          label="巡查时间"
          align="center" prop="xcsj"
        />
        <el-table-column
          label="是否改变用途"
          align="center" prop="sfgbyt"
        >
          <template #default="scope">
            <el-button
              plain
              :type="scope.row.sfgbyt === '1'?'success':'primary'"
              size="small"
            >{{scope.row.sfgbyt === "1"? "是" : "否"}}</el-button>
          </template>
        </el-table-column>
        <el-table-column
          label="是否永久性建筑物"
          align="center" prop="sfjsyjxjz"
        >
          <template #default="scope">
            <el-button
              plain
              :type="scope.row.sfjsyjxjz === '1'?'success':'primary'"
              size="small"
            >{{scope.row.sfjsyjxjz === "1"? "是" : "否"}}</el-button>
          </template>
        </el-table-column>
        <el-table-column
          label="情况说明"
          align="center" prop="qtqksm"
        />
        <el-table-column
          label="操作"
          align="center"
          width="180"
        >
          <template #default="scope">
            <el-button
              plain
              type="primary" size="small" @click="showDetail(scope.row)"
            >查看</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script setup name="ReservePatrolTaskInfo">
/**
 * CHN：储备土地管护巡查任务信息
 */
import { listGhRecord } from "@/api/reserve/reserveManage.js";
import { useRouter } from "vue-router"

const router = useRouter()

const patrolRecordList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryDate = ref("")
const queryFormRef = ref();
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    xcsj: undefined,
    tjsj: undefined,
    xcry: undefined,
    sfgbyt: undefined,
    sfjsyjxjz: undefined,
    qtqksm: undefined,
    xcjlxh: undefined,
    shzt: undefined,
    shsm: undefined,
    shsj: undefined,
    shry: undefined,
    bz: undefined
  }
});

const { queryParams } = toRefs(data);

/** 查询巡查记录列表 */
const getList = async () => {
  loading.value = true;
  const res = await listGhRecord(queryParams.value);
  patrolRecordList.value = res.rows
  total.value = res.total;
  loading.value = false;
};


/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.xcsj = new Date(queryDate.value)
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};


/** 修改按钮操作 */
const showDetail = (row) => {
  console.log("数据详情：",row);
  router.push({ "path": "/reserveProject/reserveLand/reservePatrolTaskReviewDetail",query: { id: row.id } })
};

onMounted(() => {
  getList();
});
</script>
<style lang="scss" scoped>
@import "@/styles/variables.module.scss";

.main-content {
  padding: 10px;
}

.result-wrap {
  margin-top: 10px;
}

.nameone {
  margin-bottom: 0 !important;
}

.top {
  margin-bottom: 15px;
}


.el-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
:deep(.el-select__wrapper) {
  width: 150px;
}

.query-form{
  /**防止输入框出现清除按钮时输入框产生宽度变化**/
  :deep(.el-input--suffix) {
    // 固定宽度
    width: 200px !important;
  }
}
</style>
