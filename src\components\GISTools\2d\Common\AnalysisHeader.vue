<template>
  <header class="header">
    <span>{{ title }}</span>
    <el-icon v-show="showIcon" @click="$emit('headerClick')" class="header-icon">
      <ArrowDown v-if="headerClicked"/>
      <ArrowUp v-else/>
    </el-icon>
  </header>
</template>

<script setup name="这是默认标题">
defineProps({
  title: {
    type: String,
    require: true
  },
  headerClicked: {
    type: Boolean
  },
  showIcon: {
    type: Boolean,
    default: true
  }
})
</script>

<style scoped lang="scss">
.header{
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  height: 40px;
  color: #ffffff;
  font-size: 14px;
  font-weight: bold;
  padding-left: 10px;
  font-family: '微软雅黑';
  &:hover{
    // cursor: pointer;
    // background: #0167ccd4;
    // color: #fff;
  }
}
.header-icon{
  position: absolute;
  right: 15px;
}
</style>
