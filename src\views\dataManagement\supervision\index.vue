<template>
  <div class="main-content">
    <div v-if="!showDetail">
      <transition>
        <div
          v-show="showSearch"
          class="mb-10"
        >
          <el-card shadow="hover">
            <el-form
              ref="queryFormRef"
              :model="queryParams" :inline="true"
            >
              <el-form-item
                label="项目名称"
                prop="projectName" class="nameone"
              >
                <el-input
                  v-model="queryParams.projectName"
                  placeholder="项目名称" clearable @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item
                label="项目编号"
                prop="projectId" class="nameone"
              >
                <el-input
                  v-model="queryParams.projectId"
                  placeholder="项目编号" clearable @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item class="nameone">
                <el-button
                  type="primary"
                  icon="Search" @click="handleQuery"
                >搜索</el-button>
                <el-upload
                  class="upload-demo"
                  :show-file-list="false" :limit="1" :before-upload="beforeUpload"
                  :http-request="customUpload" accept=".zip,.rar,.7z" style="margin-left: 10px"
                >
                  <el-tooltip
                    content="请上传 .zip/.rar/.7z 格式的压缩包，大小不超过50MB"
                    placement="top"
                  >
                    <el-button type="primary">上传shape文件</el-button>
                  </el-tooltip>
                </el-upload>
                <el-button
                  icon="Refresh"
                  @click="resetQuery"
                >重置</el-button>
                <el-button
                  icon="Refresh"
                  @click="getList"
                >刷新</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </div>
      </transition>
      <el-card class="result-wrap">
        <el-table
          v-loading="loading"
          :data="reserveProjectList" @selection-change="handleSelectionChange"
        >
          <el-table-column
            label="项目名称"
            align="center" prop="projectName"
          />
          <el-table-column
            label="项目编号"
            align="center" prop="projectId" width="100px"
          />
          <el-table-column
            label="项目说明"
            align="center" prop="projectDescription"
          />
          <el-table-column
            label="操作"
            align="center" class-name="small-padding fixed-width" width="300px"
          >
            <template #default="scope">
              <el-button
                plain
                type="success" size="small" @click="handleView(scope.row)"
                class="see"
              >查看</el-button>
              <el-button
                plain
                type="primary" size="small" @click="handleUpdate(scope.row)"
                class="see"
              >修改</el-button>
              <el-button
                plain
                type="danger" size="small"  @click="handleDelete(scope.row)"
                class="see"
              >删除</el-button>
              <el-button
                plain
                type="warning" size="small" @click="handleExport(scope.row)"
                class="see"
              >导出</el-button>
            </template>
            <!-- <template #default="scope">
              <el-tooltip content="" placement="top">
                <el-button link type="primary" icon="view" @click="handleView(scope.row)">查看</el-button>
              </el-tooltip>
              <el-tooltip content="修改" placement="top">
                <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button link type="primary" icon="delete" @click="handleDelete(scope.row)"></el-button>
              </el-tooltip>
              <el-tooltip content="导出" placement="top">
                <el-button link type="primary" icon="Download" @click="handleExport(scope.row)"></el-button>
              </el-tooltip>
            </template> -->
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>
    <div
      v-else
      class="content"
    >
      <!-- 添加或修改储备项目对话框 -->
      <div class="add-header-title">
        <div class="add-title">监测项目管理详情</div>
        <div
          class="add-title-return"
          @click="cancel"
        >
          <!--          <el-image src="../../../public/images/img-return.png" style="height: 24px;width: 24px;margin-top: 3px"/>-->
          <div
            style="padding-left: 6px;font-size: 14px;"
            @click="showDetail = !showDetail"
          >返回列表</div></div>
      </div>
      <div class="add-content">
        <div class="content-title">飞行任务信息</div>

        <!--项目基本信息-->
        <div class="custom-card-layout">
          <!-- 左侧卡片 -->
          <div
            class="custom-card"
            style="width: 50%;"
          >
            <div class="card-content">
              <el-form
                ref="reserveProjectFormRef"
                :model="form"
                :rules="rules"
                label-width="180px"
              >
                <el-descriptions
                  title="项目信息"
                  :column="1" border
                >
                  <el-descriptions-item label="项目名称">
                    {{ form.projectName }}
                  </el-descriptions-item>
                  <el-descriptions-item label="项目编号">
                    {{ form.projectId }}
                  </el-descriptions-item>
                  <el-descriptions-item
                    label="项目说明"
                    :span="2"
                  >
                    {{ form.projectDescription }}
                  </el-descriptions-item>
                </el-descriptions>
              </el-form>
            </div>
          </div>

          <!-- 右侧卡片 -->
          <div
            class="custom-card"
            style="width: 50%;"
          >
            <div class="card-content">
              <div class="viewer-container">
                <Viewer3d
                  ref="viewer3d"
                  @onCreateViewer3d="createViewer3d"
                />
              </div>
            </div>
          </div>
        </div>

        <!--定位信息-->
        <div>
          <div class="content-title">
            <el-card
              class="detail-card"
              style="width: 100%"
            >
              <div
                class="header-container"
                style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; width: 100%;"
              >
                <el-descriptions
                  title="媒体文件列表"
                  :column="3" border
                />
              </div>
              <el-table
                :data="mediaList"
                v-loading="loading"  :infinite-scroll-distance="100" :infinite-scroll-immediate="false"
                :infinite-scroll-delay="200" class="media-table"
              >
                <el-table-column
                  label="飞行任务名称"
                  align="center" prop="flightTaskName"
                />
                <el-table-column
                  label="航点名称"
                  align="center" prop="routePositionName"
                />
                <el-table-column
                  label="文件后缀"
                  align="center" prop="suffix"
                />
                <el-table-column
                  label="类型"
                  align="center" prop="fileType" width="180"
                />
                <el-table-column
                  label="预览"
                  align="center" width="180"
                >
                  <template #default="{ row }">
                    <!-- 图片预览 -->
                    <el-image
                      style="width: 40px; height: 40px"
                      :src="[getSafePreviewUrl(row.downloadUrl)]"
                      :preview-src-list="[getSafePreviewUrl(row.downloadUrl)]"
                      hide-on-click-modal
                      :preview-teleported="true"
                      referrerpolicy="no-referrer"
                    >
                      <template #error>
                        <div class="image-error">
                          <el-icon><Picture /></el-icon>
                        </div>
                      </template>
                    </el-image>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </div>
        </div>
      </div>
    </div>
    <!-- 添加或修改公司名录对话框 -->
    <el-dialog
      :title="dialog.title"
      v-model="dialog.visible" width="500px" append-to-body
    >
      <el-form
        ref="topicCompanyFormRef"
        :model="form" :rules="rules" label-width="80px"
      >
        <el-form-item
          label="项目名称"
          prop="projectName"
        >
          <el-input
            v-model="form.projectName"
            placeholder="请输入项目名称"
          />
        </el-form-item>
        <el-form-item
          label="项目描述"
          prop="projectDescription"
        >
          <el-input
            v-model="form.projectDescription"
            placeholder="请输入项目描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer" >
          <el-button @click="cancel">取 消</el-button>
          <el-button
            :loading="buttonLoading"
            type="primary" @click="submitForm"
          >确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ReserveProject">
import { deleteProjectById, projectById, projectExport, projectImportShp, ProjectList, ProjectPicList, putProject } from "@/api/uav/project/index.js";
import { setSceneEffect } from "@/utils/Cesium/CesiumTool.js";
import { addTdtImageLayer } from "@/utils/Cesium/CesiumLayer.js";
import useMapViewStore from "@/store/modules/map/mapView.js"
import { addTopicCompany } from "@/api/company/company.js";
import { ref, toRaw } from "vue";
import { Picture } from "@element-plus/icons-vue";
import UseViewer3d from "@/components/GISTools/Viewer/UseViewer3d.js"
import Viewer3d from "@/components/GISTools/Viewer/Viewer3d.vue"
// 用户信息
const store = useMapViewStore()
const { proxy } = getCurrentInstance();
const showDetail = ref(false);
const value1 = ref('')
const reserveProjectList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const buttonLoading = ref(false);
const topicCompanyFormRef = ref();
const queryFormRef = ref();
const reserveProjectFormRef = ref();
const initFormData = {
  id: undefined,
  bz: undefined,
  projectName: undefined,
  projectId: undefined
};
const dialog = reactive({
  visible: false,
  title: ""
});
const data = reactive({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectName: undefined,
    projectId: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: "$comment不能为空", trigger: "blur" }]
  }
});
const mediaList = ref([]);
const projectName = ref('');
const { queryParams, form, rules } = toRefs(data);

// 状态管理
const viewer3d = reactive({}); // 兼容原有代码

/**
 * 响应数据
 */
const isShowResource = ref(false);
const viewer3dProp = reactive({});

/**
 * 监听数据，监听视图对象值：当viewer3d值为真时，显示资源组件
 * 目前使用父组件传递属性不成功，只能使用provide,inject进行传递
 */
watch(()=>viewer3dProp.viewer3d,(newValue,oldValue) => {
  if (Object.keys(newValue).length) {
    isShowResource.value = true
  }
})
provide('viewer3d', viewer3dProp);

const createViewer3d = async (viewerId,mapInitStore,mapViewStore)=>{
  const useViewer3d = new UseViewer3d(viewerId,mapInitStore,mapViewStore)
  await useViewer3d.createViewer3d()
  const viewer3d = useViewer3d.viewer3d
  // 获取表单数据
  const formRow = toRaw(form.value); // 获取表单的原始数据
  const point = {
    latitude: formRow.projectPoint.geom.coordinates[1], // 获取纬度
    longitude: formRow.projectPoint.geom.coordinates[0], // 获取经度
    timestamp: formRow.projectName // 使用项目名称作为时间戳
  };
  showTrackData(viewer3d, point); // 显示轨迹或单点数据
  Object.assign(viewer3dProp,{ viewer3d })
}

/** 显示轨迹或单点数据 */
const showTrackData = (viewer, point) => {
  if (!viewer || !point) return;

  // 转换坐标
  const position = Cesium.Cartesian3.fromDegrees(
    point.longitude,
    point.latitude,
    point.height || 500 // 默认高度设为300米，避免直接贴地
  );

  // 清除之前的实体（如果存在）
  const existingEntity = viewer.entities.getById('single-point');
  if (existingEntity) {
    viewer.entities.remove(existingEntity); // 删除旧的单点标记
  }

  // 添加单点标记
  viewer.entities.add({
    id: 'single-point', // 设置唯一ID
    position: position, // 设置位置
    point: {
      pixelSize: 10, // 点的像素大小
      color: Cesium.Color.RED, // 点的颜色
      outlineColor: Cesium.Color.WHITE, // 边框颜色
      outlineWidth: 2, // 边框宽度
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND // 如果高度为0，则贴地
    },
    label: {
      text: `${point.timestamp}`, // 标签文字内容
      font: '12px sans-serif', // 字体样式
      fillColor: Cesium.Color.WHITE, // 文字填充颜色
      style: Cesium.LabelStyle.FILL_AND_OUTLINE, // 文字样式
      outlineWidth: 2, // 边框宽度
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM, // 垂直对齐方式
      pixelOffset: new Cesium.Cartesian2(0, -10), // 偏移量
      heightReference: point.height ? undefined : Cesium.HeightReference.CLAMP_TO_GROUND // 高度参考
    }
  });
};

/** 文件上传前校验 */
const beforeUpload = (file) => {
  // 检查文件类型是否为允许的格式
  const isAllowedType = ['.zip', '.rar', '.7z'].some(ext =>
    file.name.toLowerCase().endsWith(ext)
  );
  // 检查文件大小是否小于等于50MB
  const isSizeValid = file.size / 1024 / 1024 <= 50;

  if (!isAllowedType) {
    proxy?.$modal.msgError('只能上传ZIP/RAR/7Z格式的压缩包!'); // 提示错误信息
    return false;
  }
  if (!isSizeValid) {
    proxy?.$modal.msgError('文件大小不能超过50MB!'); // 提示错误信息
    return false;
  }
  return true; // 校验通过
};

/** 自定义文件上传逻辑 */
const customUpload = async (file) => {
  const formData = new FormData(); // 创建FormData对象
  formData.append('file', file.file); // 添加文件到FormData

  try {
    const response = await projectImportShp(formData); // 调用接口上传文件
    console.log(response);
    // 判断上传是否成功
    if (response && response.code === 200) {
      proxy?.$modal.msgSuccess("操作成功"); // 提示成功信息
    } else {
      proxy?.$modal.msgError("上传失败"); // 提示失败信息
    }
  } catch (error) {
    proxy?.$modal.msgError("操作失败"); // 捕获异常并提示失败信息
    throw error; // 抛出异常
  }
};

/** 查询储备项目列表 */
const getList = async () => {
  loading.value = true;
  const res = await ProjectList(queryParams.value);
  reserveProjectList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 获取安全预览地址 */
const getSafePreviewUrl = (previewUrl) => {
  return previewUrl
    .replace('response-content-disposition=attachment', '')
    .replace(/;filename.*?(?=&|$)/, '');
}

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  topicCompanyFormRef.value?.resetFields();
  reserveProjectFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  form.value = { ...initFormData };
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 查看项目 */
const handleView = async (row) => {
  reset();
  const _id = row?.projectId || ids.value[0];
  const res = await projectById(_id);
  Object.assign(form.value, res.data);
  showDetail.value = true;
  const ProjectPicListRes = await ProjectPicList({ projectName: row.projectName })
  Object.assign(mediaList.value, ProjectPicListRes.rows);
  projectName.value = row.projectName
};

/** 获取当前日期的函数，格式为 "YYYY-MM-DD" */
const getCurrentDate = () => {
  const today = new Date(); // 获取当前日期和时间
  const year = today.getFullYear(); // 获取年份（4位数字）
  const month = String(today.getMonth() + 1).padStart(2, '0'); // 获取月份（从0开始，需加1），并补零到两位
  const day = String(today.getDate()).padStart(2, '0'); // 获取日期，并补零到两位
  return `${year}-${month}-${day}`; // 返回格式化的日期字符串
};

/** 处理日期变更的异步函数 */
const handleDateChange = async (selectedDate) => {
  try {
    // 调用 ProjectPicList 接口，传入格式化后的日期和项目名称
    const ProjectPicListRes = await ProjectPicList({
      queryTime: formatDate(selectedDate), // 格式化日期为 "YYYY-MM-DD"
      projectName: toRaw(projectName.value) // 获取项目名称的原始值
    });
    // 将接口返回的数据更新到 mediaList 中
    Object.assign(mediaList.value, ProjectPicListRes.rows); // 使用 Object.assign 更新响应式对象的值
  } catch (error) {
    console.error("接口调用失败:", error); // 捕获并打印错误信息
  }
};

/** 格式化日期的函数，将 Date 对象转换为 "YYYY-MM-DD" 格式的字符串 */
const formatDate = (date) => {
  const year = date.getFullYear(); // 获取年份（4位数字）
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 获取月份（从0开始，需加1），并补零到两位
  const day = String(date.getDate()).padStart(2, '0'); // 获取日期，并补零到两位
  return `${year}-${month}-${day}`; // 返回格式化的日期字符串
};

/** 修改按钮操作 */
const handleUpdate = async (row) => {
  reset();
  const _id = row?.projectId || ids.value[0];
  const res = await projectById(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改项目信息"
};

/** 提交按钮 */
const submitForm = () => {
  topicCompanyFormRef.value?.validate(async (valid) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.projectId) {
        await putProject(form.value).finally(
          () => (buttonLoading.value = false)
        );
      } else {
        await addTopicCompany(form.value).finally(
          () => (buttonLoading.value = false)
        );
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });

};

/** 删除按钮操作 */
const handleDelete = async (row) => {
  const _ids = row?.projectId || ids.value;
  await proxy?.$modal
    .confirm('是否确认删除储备项目为"' + _ids + '"的数据项？')
    .finally(() => (loading.value = false));
  await deleteProjectById(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
};

/** 导出按钮操作 */
const handleExport = (row) => {
  projectExport({ projectId: row.projectId,projectName: row.projectName })
};

onMounted(() => {
  getList();
});
</script>
<style lang="scss">
.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  width: 100%;
}

/* 添加上传按钮样式 */
.upload-demo {
  display: inline-block;
  margin-right: 10px;
}

.el-upload__tip {
  font-size: 12px;
  color: #888;
  margin-top: 5px;
}
.main-content {
  padding: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}

.result-wrap {
  margin-top: 10px;
}

.content {
  border: 1px solid #e9e9e9;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin: 10px;
  overflow: hidden;
}

.add-header-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  height: 60px;
  background-color: #deefff;
  border-bottom: 1px solid #e9e9e9;

  .add-title {
    font-weight: 600;
    font-size: 18px;
    color: #333;
  }

  .add-title-return {
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      opacity: 0.8;
    }

    .return-icon {
      height: 24px;
      width: 24px;
    }

    .return-text {
      padding-left: 6px;
      font-size: 14px;
      color: #2077ff;
    }
  }
}

.add-content {
  padding: 20px;
  overflow-y: auto;
  height: calc(100vh - 200px);

  .project-info-section,
  .project-location-section {
    margin-bottom: 30px;
  }
}

.content-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .location-title {
    font-size: 16px;
  }
}

.info-form {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 20px 15px;
  background-color: #fafafa;

  .form-input {
    width: 100%;
  }

  .form-textarea {
    width: 100%;

    :deep(.el-textarea__inner) {
      min-height: 80px !important;
    }
  }
}
.custom-card-layout {
  margin-top: 30px;
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
  align-items: stretch;
}

.custom-card {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  border: 1px solid #ebeef5;
}

.custom-card:hover {
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
}

.card-content {
  padding: 15px;
  height: 100%;
  box-sizing: border-box;
}



.viewer-container {
  width: 100%;
  height: 280px;
  position: relative;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  background-color: #f5f7fa;
  &:before {
    content: '地图加载中...';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #999;
    font-size: 14px;
  }
}

/* 响应式调整 */
@media (max-width: 992px) {
  .add-content {
    padding: 15px;
  }

  .viewer-container {
    height: 400px;
  }
}

@media (max-width: 768px) {
  .add-header-title {
    padding: 10px 15px;
    height: 50px;

    .add-title {
      font-size: 16px;
    }
  }

  .add-content {
    height: calc(100vh - 160px);
  }

  .info-form {
    padding: 15px 10px;
  }

  .viewer-container {
    height: 300px;
  }
}

@media (max-width: 576px) {
  .add-header-title {
    flex-direction: column;
    height: auto;
    padding: 10px;

    .add-title {
      margin-bottom: 5px;
    }
  }

  .content-title {
    flex-direction: column;
    align-items: flex-start;
  }

  .viewer-container {
    height: 250px;
  }
}
.nameone {
  margin-bottom: 0 !important;
}
.el-button.see {
  padding: 10px !important;
  min-width: 30px;
}

</style>
