import Style from 'ol/style/Style';
import { Fill, Stroke } from "ol/style";
import CircleStyle from "ol/style/Circle";

const defaultStyle = new Style({
  // 边线
  stroke: new Stroke({
    color: "blue",
    lineDash: [4],
    width: 3
  }),
  // 填充色
  fill: new Fill({
    color: 'rgba(0, 0, 255, 0.1)'
  })
})

const geometryStyle = {
  MultiPolygon: new Style({
    fill: new Fill({
      color: 'rgba(255, 255, 255, 0)'
    }),
    stroke: new Stroke({
      color: "red",
      width: 2
    })
  }),
  Polygon: new Style({
    fill: new Fill({
      color: 'rgba(255, 255, 255, 0)'
    }),
    stroke: new Stroke({
      color: "red",
      width: 2
    })
  }),
  LineString: new Style({
    fill: new Fill({
      color: 'rgb(255,214,130,0.5)'
    }),
    stroke: new Stroke({
      color: "#fffcc3",
      width: 1.5
    })
  }),
  Point: new Style({
    image: new CircleStyle({
      fill: new Fill({
        color: '#ff9300'
      }),
      radius: 5
    })
  }),
  Circle: new Style({
    fill: new Fill({
      color: 'rgba(255, 255, 255, 0)'
    }),
    stroke: new Stroke({
      color: "red",
      width: 2
    })
  }),
  Box: new Style({
    fill: new Fill({
      color: 'rgba(255, 255, 255, 0)'
    }),
    stroke: new Stroke({
      color: "red",
      width: 2
    })
  })

}

export { geometryStyle }
