<template>

</template>

<script setup name="属性查询">
import { getCommentList } from "@/utils/mapFunction/globalFunc.js"
import { getConfigKey } from "@/api/system/config.js"
import { openPopupOfQueryLayer, removeAllLayer } from "@/utils/OpenLayers/olTool.js"
import eventBus from "@/utils/eventBus.js"
import { ElMessage } from "element-plus"
import { pointQuery } from "@/api/gis/postgisInterface.js"
import { addGeoJSON2Map } from "@/utils/OpenLayers/olLayer.js"
import { getMapQueryList } from "@/api/gis/mapQuery.js"

const queryList = ref([])// 查询服务列表
const resultList = ref([]) // 结果列表
const popupColumnsInfo = ref({}) // 注释字段信息
const activeName = ref("") // 当前点击选项信息

// 获取字段配置 option
const getLayerOption = (layers)=> {
  layers.forEach(async layer => {
    const columnInfos = await getCommentList(layer.dataSet)
    popupColumnsInfo.value[layer.dataSet] = columnInfos;
  })
}


// 获取地图查询服务下拉树列表
const getMapQueryData = async ()=> {
  const sysId = await getConfigKey('sys.id')
  const response = await getMapQueryList(sysId.data)
  const basicList = response.data.find(layer => layer.label === '基础查询')
  if (!basicList){
    console.error("后台未配置基础查询数据！")
    return
  }
  queryList.value = basicList.children;
  getLayerOption(queryList.value)
}

// 鼠标点击进行查询
const getFeatureByClick = ()=> {
  window._map.on('click', getFeatureByGeometry)
}

// 几何查询
const getFeatureByGeometry = (evt)=> {
  // 将屏幕坐标转换为地理坐标
  const trueCoords = window._map.getCoordinateFromPixel(evt.pixel)
  removeAllLayer()
  resultList.value = []
  const pointGeometry = {
    type: "Point",
    crs: { "type": "name","properties": { "name": "EPSG:4490" } },
    coordinates: trueCoords
  }
  eventBus.emit('showLoading',true)
  if (!window._layerList?.length){
    ElMessage.warning("暂无查询图层,请在资源浏览中勾选查询图层！")
    eventBus.emit('showLoading',false)
    return
  }
  // 过滤目标图层
  const dataLayerList = queryList.value.filter(queryLayer => {
    return window._layerList.indexOf(queryLayer.dataSet.toUpperCase()) > -1
  })
  if (!dataLayerList.length) {
    ElMessage({
      message: '当前查询无数据服务，请联系管理员进行配置！',
      type: 'warning'
    })
    eventBus.emit('showLoading',false)
    return
  }
  // 设置默认选中图层
  activeName.value = dataLayerList[0].value
  dataLayerList?.forEach(layer => {
    const queryParams = {
      tableName: layer.dataSet,
      geojson: JSON.stringify(pointGeometry)
    }
    pointQuery(queryParams).then(res => {
      if (!res.data.length){
        ElMessage.warning("该位置未查询到【" + layer.label + "】图层数据，请重新查询！")
        return
      }
      const feature = res.data[0]
      const polygon = addGeoJSON2Map(feature,"",window._map)
      feature.popupColumns = popupColumnsInfo.value[layer.dataSet]
      feature.label = layer.label
      feature.polygon = polygon
      resultList.value.push(feature)
    }).catch(err => {
      console.log("查询错误：",err)
    }).finally(() => {
      eventBus.emit('showLoading',false)
      openPopupOfQueryLayer(resultList.value, window._map)
    })
  })
}

onBeforeMount(()=>{
  getFeatureByClick()
})

onBeforeUnmount(()=>{
  // 移除地图点击事件
  window._map.un("click",getFeatureByGeometry)
})

onUnmounted(()=>{
  // 移除地图点击事件
  window._map.un("click",getFeatureByGeometry)
})


</script>

<style scoped lang="scss">

</style>
