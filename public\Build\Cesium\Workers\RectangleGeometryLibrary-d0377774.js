define(["exports","./Cartographic-3309dd0d","./when-b60132fc","./Check-7b2a090c","./buildModuleUrl-9085faaa","./Math-119be1a3","./GeometryAttribute-c65394ac","./Rectangle-dee65d21"],(function(t,a,n,r,e,o,i,s){"use strict";var g=Math.cos,h=Math.sin,u=Math.sqrt,c={computePosition:function(t,a,r,e,o,i,s){var c=a.radiiSquared,C=t.nwCorner,l=t.boundingRectangle,d=C.latitude-t.granYCos*e+o*t.granXSin,S=g(d),w=h(d),M=c.z*w,X=C.longitude+e*t.granYSin+o*t.granXCos,Y=S*g(X),m=S*h(X),p=c.x*Y,f=c.y*m,v=u(p*Y+f*m+M*w);if(i.x=p/v,i.y=f/v,i.z=M/v,r){var G=t.stNw<PERSON>orner;n.defined(G)?(d=G.latitude-t.stGranYCos*e+o*t.stGranXSin,X=G.longitude+e*t.stGranYSin+o*t.stGranXCos,s.x=(X-t.stWest)*t.lonScalar,s.y=(d-t.stSouth)*t.latScalar):(s.x=(X-l.west)*t.lonScalar,s.y=(d-l.south)*t.latScalar)}}},C=new i.Matrix2,l=new a.Cartesian3,d=new a.Cartographic,S=new a.Cartesian3,w=new e.GeographicProjection;function M(t,n,r,e,o,s,g){var h=Math.cos(n),u=e*h,c=r*h,d=Math.sin(n),M=e*d,X=r*d;l=w.project(t,l),l=a.Cartesian3.subtract(l,S,l);var Y=i.Matrix2.fromRotation(n,C);l=i.Matrix2.multiplyByVector(Y,l,l),l=a.Cartesian3.add(l,S,l),s-=1,g-=1;var m=(t=w.unproject(l,t)).latitude,p=m+s*X,f=m-u*g,v=m-u*g+s*X,G=Math.max(m,p,f,v),R=Math.min(m,p,f,v),x=t.longitude,b=x+s*c,y=x+g*M,O=x+g*M+s*c;return{north:G,south:R,east:Math.max(x,b,y,O),west:Math.min(x,b,y,O),granYCos:u,granYSin:M,granXCos:c,granXSin:X,nwCorner:t}}c.computeOptions=function(t,a,n,r,e,i,g){var h,u,c,C,l,X=t.east,Y=t.west,m=t.north,p=t.south,f=!1,v=!1;m===o.CesiumMath.PI_OVER_TWO&&(f=!0),p===-o.CesiumMath.PI_OVER_TWO&&(v=!0);var G=m-p;c=(l=Y>X?o.CesiumMath.TWO_PI-Y+X:X-Y)/((h=Math.ceil(l/a)+1)-1),C=G/((u=Math.ceil(G/a)+1)-1);var R=s.Rectangle.northwest(t,i),x=s.Rectangle.center(t,d);0===n&&0===r||(x.longitude<R.longitude&&(x.longitude+=o.CesiumMath.TWO_PI),S=w.project(x,S));var b=C,y=c,O=s.Rectangle.clone(t,e),P={granYCos:b,granYSin:0,granXCos:y,granXSin:0,nwCorner:R,boundingRectangle:O,width:h,height:u,northCap:f,southCap:v};if(0!==n){var W=M(R,n,c,C,0,h,u);m=W.north,p=W.south,X=W.east,Y=W.west,P.granYCos=W.granYCos,P.granYSin=W.granYSin,P.granXCos=W.granXCos,P.granXSin=W.granXSin,O.north=m,O.south=p,O.east=X,O.west=Y}if(0!==r){n-=r;var _=s.Rectangle.northwest(O,g),j=M(_,n,c,C,0,h,u);P.stGranYCos=j.granYCos,P.stGranXCos=j.granXCos,P.stGranYSin=j.granYSin,P.stGranXSin=j.granXSin,P.stNwCorner=_,P.stWest=j.west,P.stSouth=j.south}return P},t.RectangleGeometryLibrary=c}));
