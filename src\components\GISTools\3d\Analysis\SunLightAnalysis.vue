<template>
  <div v-if="isShow" v-dragable class="analyse-wrap">
    <!--    <Header :header-info="headerInfo" @closePanel="closePanel" />-->
    <div class="tab-container">
      <div class="date-picker">
        <span class="demonstration">选择日期</span>
        <el-date-picker
          v-model="dateTime"
          type="date"
          placeholder="选择日期时间"
          :clearable="false"
          :default-time="['08:00:00']"
        />
      </div>
      <div class="date-picker">
        <span class="demonstration">开始时间</span>
        <el-select v-model="startTimeValue" placeholder="请选择" style="width: 220px">
          <el-option
            v-for="item in timeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
      <div class="date-picker">
        <span class="demonstration">结束时间</span>
        <el-select v-model="endTimeValue" placeholder="请选择" style="width: 220px">
          <el-option
            v-for="item in timeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
      <el-button type="primary" @click="startAnalyse">
        <svg-icon icon-class="start_analyse" />
        <span class="sight-btn">开始分析</span>
      </el-button>
      <el-button type="primary" @click="clearResult">
        <svg-icon icon-class="clear" />
        <span class="sight-btn">清除</span>
      </el-button>
    </div>
  </div>
</template>

<script setup name="SunLightAnalysis" text="日照分析">
import useMapViewStore from "@/store/modules/map/mapView.js"
import { setLayerSelectStatus } from "@/utils/Cesium/CesiumTool.js"

defineProps({
  isShow: {
    type: Boolean,
    default: true
  },
  headerInfo: {
    type: Object
  }
})

const toolTip = ref(undefined)
const shadowQuery = ref(undefined)
const timeInterval = ref(undefined)
const dateTime = ref(new Date())
const startTimeValue = ref("08:00")
const endTimeValue = ref("14:00")
const timeOptions = ref([
  {
    value: "00:00",
    label: "00:00"
  },
  {
    value: "02:00",
    label: "02:00"
  },
  {
    value: "04:00",
    label: "04:00"
  },
  {
    value: "06:00",
    label: "06:00"
  },
  {
    value: "08:00",
    label: "08:00"
  },
  {
    value: "10:00",
    label: "10:00"
  },
  {
    value: "12:00",
    label: "12:00"
  },
  {
    value: "14:00",
    label: "14:00"
  },
  {
    value: "16:00",
    label: "16:00"
  },
  {
    value: "18:00",
    label: "18:00"
  },
  {
    value: "20:00",
    label: "20:00"
  },
  {
    value: "22:00",
    label: "22:00"
  },
  {
    value: "23:59",
    label: "23:59"
  }
])
const viewer3d = computed(()=>useMapViewStore().viewer3d)

const emits = defineEmits(["closePanel"])

const closePanel = ()=>{
  emits("closePanel")
}


/**
 * 开始日照分析
 */
const startAnalyse = ()=>{
  clearResult()
  const scene = viewer3d.value.scene
  if (!scene.pickPositionSupported){
    ElMessage.warning('不支持深度纹理,阴影分析功能无法使用！');
    return;
  }

  // 创建阴影查询对象
  shadowQuery.value = new Cesium.ShadowQueryPoints(scene);
  const layers = scene.layers.layerQueue;
  setLayerSelectStatus(viewer3d.value,false)
  for (let i = 0; i < layers.length; i++) {
    // 设置所有模型都产生阴影
    layers[i].shadowType = 2;
  }

  shadowQuery.value.build();

  const formatTime = getTimeFormat(dateTime.value)
  const startTime = new Date(formatTime + " " + startTimeValue.value);
  const endTime = new Date(formatTime + " " + endTimeValue.value);

  let startHour = startTime.getHours()
  const endHour = endTime.getHours()

  if (startHour >= endHour) {
    ElMessage.warning("开始时间大于等于结束时间")
    return;
  }
  let nTimer = 0.0;
  timeInterval.value = setInterval(() => {
    if (startHour < endHour) {
      startTime.setHours(startHour);
      startTime.setMinutes(nTimer);
      viewer3d.value.clock.currentTime = Cesium.JulianDate.fromDate(startTime);
      nTimer += 10.0;
      if (nTimer > 60.0){
        startHour += 1.0;
        nTimer = 0.0;
      }
    } else {
      clearInterval(timeInterval.value);
    }
  }, 20);
}

/**
 * 简单时间格式
 */
const getTimeFormat = (date)=>{
  const dateTime = new Date(date);
  return dateTime.getFullYear() + "-" + (date.getMonth() + 1) + "-" + (date.getDate());
}
/**
 * 警用图层选择
 */
const disableSelect = ()=>{
  // 获取场景图层列表
  const layers = viewer3d.value.scene.layers.layerQueue
  layers.forEach(layer => {
    layer.selectEnabled = false;
  })
}


/**
 * 清除结果
 */
const clearResult = ()=>{
  // 移除图元
  viewer3d.value.entities.removeAll()
  // 移除阴影
  shadowQuery.value && shadowQuery.value.clear();
  // 移除定时器
  timeInterval.value && clearInterval(timeInterval.value);
  viewer3d.value.scene.sun.show = true
}


onBeforeUnmount(()=>{
  clearResult();
  setLayerSelectStatus(viewer3d.value,true)
})

</script>

<style scoped lang="scss">
.analyse-wrap {
  color: #fff;
  background-image: url(@/assets/images/map/tool.png);
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-size: cover;
  box-shadow: 0 0 8px 0 #057595;
}
.tool-tabs{
  display: flex;
  justify-items: center;
  justify-content: space-between;
  background-color: #4f7287;
  span{
    display: inline-block;
    width: 50%;
    padding: 10px 5px;
    text-align: center;
    color: #fff;
    &:hover{
      cursor: pointer;
      background-color: #00baff;
      filter: brightness(110%);
      color: #fff;
      transition: background-color .25s;
    }
  }
}
.tab-active{
  color: #fff;
  background-color: #00baff;
}
.tool-tab-content{
  padding-top: 20px;
}
:deep(.el-form-item__label){
  width: 60px!important;
  color: #b5b5b5;
}
.tab-container{
  display: flex;
  padding: 10px;
  flex-direction: column;
  column-count: 2;
}
.tool-btn{
  margin-top: 20px;
  padding: 10px;
  width: 48%;
  background-color: #00cffa;
  border-color: #fff;
  text-align: center;
  border-radius: 4px;
  color: #ffffff;
  &:hover{
    cursor: pointer;
    color:#fff;
    filter: brightness(110%) opacity(100%);
    transition: all .5s ease-in;
    background: linear-gradient(to bottom right, #00baff, #00cffa);
  }
  svg{
    margin-right: 10px;
  }
}
.sight-btn{
  margin-left: 5px;
}
.el-button--primary {
  margin: 10px;
}
.date-picker{
  padding: 10px;
  .demonstration{
    margin-right: 10px;
  }
}
</style>
