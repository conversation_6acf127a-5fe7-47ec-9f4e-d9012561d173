<template>
  <div class="resource-warp">
    <div
      v-show="isShow"
      class="map-tree-warp"
    >
      <div class="search-div">
        <el-input
          v-model="filterText" class="search-input" placeholder="请输入图层名"
          clearable
        >
          <template v-slot:prefix>
            <i  class="el-input__icon el-icon-search" />
          </template>
        </el-input>
        <span
          class="search-btn"
          type="primary"
        >
          <i class="el-icon-search" />
          搜索
        </span>
      </div>
      <el-tree
        ref="layerTree"
        :render-after-expand="false"
        class="map-tree"
        :data="layerResources"
        node-key="id"
        :props="layerDefaultProps"
        :default-expanded-keys="idArr"
        :default-checked-keys="checkLayerResources"
        :filter-node-method="filterNode"
        show-checkbox
        icon-class="none"
        element-loading-text="正在加载"
        @check-change="handleCheckChange"
      >
        <template v-slot="{ node, data }">
          <span
            class="custom-tree-node"
            @click="treeSelect(node,data)"
          >
            <template v-if="data.serviceValue==='catalog'">
              <span class="layer-parent">
                <svg-icon
                  v-if="data.icon"
                  :icon-class="data.icon" class-name="layer-parent-icon"
                />
                <span>{{ node.label }}</span>
                <span
                  v-if="data.children.length"
                  class="layer-count"
                >{{ " (" + data.children.length + ")" }}</span>
              </span>
            </template>
            <template v-else>
              <span class="layer-children">
                <span v-if="node.label.length>14">
                  <el-tooltip
                    class="item" effect="dark" :content="node.label"
                    placement="top"
                  >
                    <span>{{ node.label.toString().substring(0, 14) }} ···</span>
                  </el-tooltip>
                </span>
                <span v-else>{{ node.label }}</span>
              </span>
            </template>
          </span>
        </template>
      </el-tree>
    </div>
    <div
      v-show="regionResources.length"
      class="map-tree-warp"
    >
      <div class="search-div">
        <el-input
          v-model="filterText2" class="search-input" placeholder="请输入行政区名"
          clearable
        >
          <template v-slot:prefix>
            <i  class="el-input__icon el-icon-search" />
          </template>
        </el-input>
        <span
          class="search-btn"
          type="primary"
        >
          <i class="el-icon-search" />
          搜索
        </span>
      </div>
      <el-scrollbar>
        <el-tree
          ref="tree2"
          v-loading="isShowLoading"
          class="map-tree"
          :data="regionResources"
          node-key="id"
          :props="regionDefaultProps"
          :expand-on-click-node="false"
          :default-expanded-keys="idArr"
          lazy
          :load="load"
          :filter-node-method="filterNode2"
          @node-click="handleRegionCheckChange"
        >
          <template v-slot="{ node }">
            <span class="custom-tree-node layer-children">
              <template v-if="!node.isLeaf">
                <span>{{ node.label }}</span>
                <span
                  v-show="node.childNodes.length"
                  class="layer-count"
                >{{ " (" + node.childNodes.length + ")" }}</span>
              </template>
              <template v-else>
                <el-tooltip
                  v-if="node.label.length>10" class="item" effect="dark"
                  :content="node.label" placement="top"
                >
                  <span>{{ node.label.toString().substring(0, 10) }} ···</span>
                </el-tooltip>
                <span v-else>{{ node.label }}</span>
              </template>
            </span>
          </template>
        </el-tree>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup name="Resource">
import useUserStore from "@/store/modules/user.js"
import useMapViewStore from "@/store/modules/map/mapView.js"
import { getTreeDataByRole, regionTreeAsync } from "@/api/gis/layerTree.js"
import { getConfigKey } from "@/api/system/config.js"
import { addLayer } from "@/utils/OpenLayers/olLayer.js"
import { removeLayerByName } from "@/utils/OpenLayers/olTool.js"
import { getMapQueryList } from "@/api/gis/mapQuery.js"
import { queryCountryRegion, queryCountyRegion, queryTownRegion } from "@/api/gis/postgisInterface.js"
import VectorSource from "ol/source/Vector"
import GeoJSON from "ol/format/GeoJSON"
import VectorLayer from "ol/layer/Vector"
import Style from "ol/style/Style"
import { Fill, Stroke } from "ol/style"

const { proxy } = getCurrentInstance()

const isAllLayerResource = ref(true) // 是否点击数据分析按钮
const isRegionResource = ref(false) // 是否点击分屏对比
const isShow = ref(false)
const isShowLoading = ref(true)
const layerResources = ref([])
const regionResources = ref([])
const checkLayerResources = ref([])
const filterText = ref('')
const filterText2 = ref('')
const idArr = ref([])
const loading = ref(true)
const regionList = ref([])
// 树形组件默认属性
const layerDefaultProps = {
  disabled: data => {
    // return Number(data.isBasemap) || data.serviceValue !== "map"
  }
}

const regionDefaultProps = {
  isLeaf: (data, node) => {
    return data.level === '3'
  },
  children: 'children',
  label: 'name'
}
const defaultProps2 = {
  children: 'children',
  label: 'name'
}
const queryList = ref([])

const user = computed(() => useUserStore().user)
const map = computed(() => useMapViewStore().map)

// 监控data中的数据变化
watch(filterText, (newValue, oldValue) => {
  proxy.$refs.layerTree.filter(newValue)
})
watch(filterText2, (newValue, oldValue) => {
  proxy.$refs.tree2.filter(newValue)
})
watch(isAllLayerResource, (newValue, oldValue) => {
  if (newValue) {
    getLayers()
  }
})
watch(isRegionResource, (newValue, oldValue) => {
  if (newValue) {
    layerResources.value = []
    getXZQList()
    getRegionList()
  }
})
watch(layerResources, (newValue, oldValue) => {
  if (newValue) {
    isShow.value = true
  }
})

/**
 * 节点数据过滤
 * @param value
 * @param data
 * @returns {boolean}
 */
const filterNode = (value, data) => {
  if (!value) return true
  return data.name.indexOf(value) > -1
}
const filterNode2 = (value, data) => {
  if (!value) return true
  return data.name.indexOf(value) > -1
}

/**
 * 数据分析按钮
 */
const dataAnalyse = () => {
  isShowLoading.value = true
  if (layerResources.value.length) {
    // removeAllLayers()
  }
  isAllLayerResource.value = true
  isRegionResource.value = false
}
/**
 * 行政区按钮
 */
const regionResource = () => {
  isShowLoading.value = true
  isRegionResource.value = true
  isAllLayerResource.value = false
}

/**
 * 树形数据选择操作
 */
const treeSelect = (node, data) => {
  // console.log('node:', node)
  // console.log('data:', data)
}

/**
 * 获取图层数据
 */
const getLayers = async() => {
  const sysId = await getConfigKey('sys.id')
  const map2dId = await getConfigKey('sys.map2d.id')
  getTreeDataByRole({ userId: user.value.userId, systemId: sysId.data, mapId: map2dId.data }).then(res => {
    const layerResource = res.data[0].children[0].children
    setNodeStatus(layerResource)
    layerResources.value = layerResource
    isShowLoading.value = false
    // 默认展开前两个图层
    if (layerResources.value?.length > 1) {
      idArr.value.push(layerResources.value[0].id)
      idArr.value.push(layerResources.value[1].id)
    }
  })
}
/**
 * 获取行政区列表
 */
const getRegionList = () => {
  regionTreeAsync().then(res => {
    regionResources.value = res.data
    idArr.value.valuer.push(res.data[0].id)
  })
}

/**
 * 设置默认显示的图层
 */
const setNodeStatus = (data) => {
  data.forEach((item) => {
    if (item?.visible === '1') {
      checkLayerResources.value.push(item.id)
      // 默认加载底图和显示图层
      if (window._map && item.layerName) {
        const wmtsOptions = {
          url: item.url,
          layerName: item.layerName,
          isBasemap: item.isBasemap,
          layerId: item.id,
          gridsetName: item.gridsetName
          // extent: JSON.parse(item.extent),
          // center: JSON.parse(item.center)
        }
        addLayer(item.serviceType, wmtsOptions)
      }
    }
    item.children?.length && setNodeStatus(item.children)
  })
}

/**
 * 节点check状态发生变化时的回调事件
 */
const handleCheckChange = (node, isChecked) => {
  if (isChecked) {
    if (node?.children) {
      idArr.value.push(node.id)
    }
    if (node.layerName) {
      const wmtsOptions = {
        url: node.url,
        layerName: node.layerName,
        isBasemap: node.isBasemap,
        layerId: node.id,
        gridsetName: node.gridsetName,
        extent: node.extent,
        center: node.center
      }
      addLayer(node.serviceType, wmtsOptions)
      queryList.value.push(node.layerName)
    }
  } else {
    idArr.value.pop()
    removeLayerByName(node.layerName, map.value)
    if (node.layerName) {
      queryList.value.splice(queryList.value.indexOf(node.layerName), 1)
    }
  }
  window._layerList = queryList.value
}
/**
 * 获取行政区查询数据
 */
const getXZQList = () => {
  getConfigKey('sys.id').then(res => {
    getMapQueryList(res.msg.toString()).then(res => {
      regionList.value = res.data[0].children
    })
  })
}

/**
 * 懒加载行政区数据
 * @param node
 * @param resolve
 */
const load = (node, resolve) => {
  // regionTreeAsync({ 'pId': node.key }).then(response => {
  //   // 过滤几个村委会
  //   const regions = response.data.filter(region => {
  //     return !['仁义村委会','礼智村委会','田心村委会'].includes(region.name)
  //   })
  //   resolve(regions)
  //   isShowLoading.value = false
  // })
}

const handleRegionCheckChange = async(node, isChecked) => {
  const regionList = regionList.value
  const queryParams = node.level === '1' ? {
    tableName: regionList[0].dataSet,
    xzqdm: String(node.areaCode),
    xzqmc: node.name
  } :
    node.level === '2' ? { tableName: regionList[1].dataSet, xzqdm: String(node.areaCode), xzqmc: node.name } :
      { tableName: regionList[2].dataSet, zldwdm: String(node.areaCode), zldwmc: node.name }

  // console.log("行政区参数：",queryParams)
  const result = node.level === '1' ? await queryCountyRegion(queryParams) :
    node.level === '2' ? await queryTownRegion(queryParams) : await queryCountryRegion(queryParams)
  // console.log(result)
  const geoJson = JSON.parse(result.data[0].intersetgeo)

  const source = new VectorSource({
    format: new GeoJSON(),
    features: (new GeoJSON()).readFeatures(geoJson),
    wrapX: false
  })

  const extent = source.getExtent()

  const vectorLayer = new VectorLayer({
    source: source,
    style: new Style({
      fill: new Fill({
        color: 'rgba(255, 255, 255, 0.2)'
      }),
      stroke: new Stroke({
        color: "#fffcc3",
        width: 1.5
      })
    })
  })
  map.value.addLayer(vectorLayer)
  map.value.getView().fit(extent, map.value.getSize())
}

onBeforeMount(() => {
  getLayers()
})
</script>

<style scoped lang="scss">
@import '@/styles/map2d.scss';
@import "@/styles/variables.module.scss";

.resource-warp {
  //top: $topBarHeight;
  width: $functionContentWidth;
  height: 100%;
  color: $mapMenuText;
}

.map-tree-warp {
  display: flex;
  flex-direction: column;
  height: $contentHeight;
  padding: 10px;

  .search-div {
    display: flex;
    justify-content: space-between;

    .search-btn {
      display: inline-block;
      width: 100px;
      height: 40px;
      line-height: 38px;
      text-align: center;
      color: #fff;
      background: #0f7dff;
      border: 1px solid #409eff;
      border-top-right-radius: 3px;
      border-bottom-right-radius: 3px;

      &:hover {
        cursor: pointer;
        filter: brightness(95%);
      }
    }
  }

  .map-tree {
    margin-top: 20px;
    height: calc(100vh - 180px);
    width: 100%;
    // color: #fff;
    background: none;
    overflow-y: auto;
  }

  .map-tree-filter {
    width: 95%;
  }

  .layer-parent {
    font-size: 16px;
    font-family: 'Arial Negreta', 'Arial Normal', 'Arial';

    .layer-parent-icon {
      height: 16px;
      width: 16px;
      color: #409eff;
    }

    .layer-count {
      color: #fff849;
    }
  }

  .layer-children {
    font-size: 14px;
  }
}

.el-divider--vertical {
  height: 2em;
  margin: 0 !important;
}

.map-tree {
  .el-tree-node {
    position: relative;
    padding-left: 16px; // 缩进量
  }

  .el-tree-node__children {
    padding-left: 16px; // 缩进量
  }

  .el-tree-node__content {
    padding-left: 0px !important;
  }

  // 竖线
  .el-tree-node::before {
    content: '';
    height: 100%;
    width: 1px;
    position: absolute;
    left: -3px;
    top: -26px;
    border-width: 1px;
    border-left: 1px dashed #52627c;
  }

  // 当前层最后一个节点的竖线高度固定
  .el-tree-node:last-child::before {
    height: 38px; // 可以自己调节到合适数值
  }

  // 横线
  .el-tree-node::after {
    content: '';
    width: 24px;
    height: 20px;
    position: absolute;
    left: -3px;
    top: 12px;
    border-width: 1px;
    border-top: 1px dashed #52627c;
  }

  // 去掉最顶层的虚线，放最下面样式才不会被上面的覆盖了
  & > .el-tree-node::after {
    border-top: none;
  }

  & > .el-tree-node::before {
    border-left: none;
  }

  // 展开关闭的icon
  .el-tree-node__expand-icon {
    font-size: 16px;
    // 叶子节点（无子节点）
    &.is-leaf {
      color: transparent;
      // display: none; // 也可以去掉
    }
  }
}

:deep(.el-input__wrapper) {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

:deep(.el-checkbox__input.is-checked) .el-checkbox__inner {
  background-color: #0f7dff;
  border-color: #0f7dff;
}

:deep(.el-tree-node:focus) > .el-tree-node__content {
  background-color: #0f7dff;
  color: #ffffff;

  .svg-icon {
    color: #ffffff;
  }

  .layer-count {
    color: #fff;
  }
}

:deep(.el-tree-node__content):hover {
  background-color: #0167cc9c;
}

:deep(.el-input__inner) {
  background-color: #ffffff45;
  //border: 1px solid #409eff;
  //color: #fff;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

:deep(.el-input__prefix) {
  color: #ffffff;
}

:deep(.el-loading-mask) {
  background-color: #f9f9f9 !important;
  filter: brightness(95%) opacity(85%);
}
</style>
