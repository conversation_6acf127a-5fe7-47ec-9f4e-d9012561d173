<template>
  <div>
    <div v-for="(item, index) in fileTypeList" :key="index">
      <div class="file-type">
        <div class="file-type-icon">
          <el-icon color="#C1C6CD" @click="fileOpen(item)">
            <CaretBottom v-if="item.open" />
            <CaretRight v-else />
          </el-icon>
          <svg-icon icon-class="fileList" @click="fileOpen(item)" />
          <el-input @change="(value) => inputChange(value, item)" v-if="item.input" v-model="item.dictLabel"
            style="width: 240px" placeholder="请输入分组名" />
          <div @click="fileOpen(item)" v-else class="file-type-name">
            {{ item.dictLabel }}
          </div>
        </div>
        <div v-if="item.input !== undefined && !item.input" class="file-type-icon">
          <el-icon color="#67C23A" @click="item.input = true">
            <Edit />
          </el-icon>
          <el-icon color="#F56C6C" @click="delFileType(item)">
            <Delete />
          </el-icon>
        </div>
      </div>
      <div class="file-upload" v-show="item.open">
        <el-upload class="upload-demo" v-model:file-list="item.fileList" :headers="headers" :action="uploadFileUrl"
          multiple :on-remove="beforeRemove" :on-preview="onPreview"
          :on-success="(response, uploadFile, uploadFiles) => beforeUpload(response, uploadFile, uploadFiles, item)"
          :on-progress="onProgress" :disabled="!upload" :show-file-list="false" v-if="upload">
          <div class="file-upload-btn" v-if="upload">点击上传</div>
        </el-upload>

        <div class="custom-file-list">
          <div v-for="(file, secondIndex) in item.fileList" :key="file.name" class="custom-file-item">
            <el-progress v-if="file.status === 'uploading'" :percentage="file.percentage" :indeterminate="true"
              status="active" style="flex: 1; margin-right: 10px;"></el-progress>
            <span v-else @click="onPreview(file)">{{ file.name }}</span>

            <template v-if="file.status === 'success' || item.fileList.length > 0">
              <el-icon v-if="isView" class="ml5" color="#409efc" @click="onPreview(file)">
                <View />
              </el-icon>
              <el-icon v-if="isEdit || file.uid" class="ml5" color="#409efc" @click="editFile(file)">
                <Edit />
              </el-icon>
              <el-icon v-if="isDowload" class="ml5" color="#409efc" @click="downloadFile(file)">
                <Download />
              </el-icon>
              <el-icon v-if="file.uid || isDelete" class="ml5" color="#F56C6C"
                @click="removeFiled(index, secondIndex, file)">
                <DeleteFilled />
              </el-icon>
            </template>
          </div>
        </div>
      </div>
    </div>
    <el-empty v-if="fileTypeList.length === 0" description="暂无附件" />
    <div class="file-list-btn mt10" v-if="upload && fileTypeList.length > 0">
      <el-button type="primary" @click="addFileList">新增分组</el-button>
    </div>
  </div>
</template>

<script setup>
import { getToken } from "@/utils/auth";
import { computed, ref } from "vue";
import { useRouter } from "vue-router";
import { base64EncodeUnicode } from "@/utils/base64.js";
import { downloadFileFromUrl } from "@/utils/common.js";


const router = useRouter();
const uploadFileUrl = ref(
  import.meta.env.VITE_APP_BASE_API + "/document/affix/upload"
);
const headers = ref({
  Authorization: "Bearer " + getToken(),
  clientid: import.meta.env.VITE_APP_CLIENT_ID,
});
const emit = defineEmits();
const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
  isView: {
    type: Boolean,
    default: true
  },
  isEdit: {
    type: Boolean,
    default: true
  },
  isDelete: {
    type: Boolean,
    default: false
  },
  isDowload: {
    type: Boolean,
    default: true
  },
  upload: {
    type: Boolean,
    default: true,
  },
});
const fileTypeList = ref([])
onMounted(() => {
  let data = props.data.map(item => ({ ...item }))

  data.map((item) => {
    if (!item.fileList)
    {
      item.fileList = [];
    }
    item.open = true;
    return item;
  });
  fileTypeList.value = data

})


function fileOpen (item) {
  item.open = !item.open;
}

function addFileList () {
  fileTypeList.value.push({
    dictLabel: "",
    id: new Date().getTime(),
    open: true,
    input: true,
    fileList: [],
  });
}

function delFileType (item) {
  fileTypeList.value = fileTypeList.value.filter((it) => it.id !== item.id);
}

function inputChange (value, item) {
  item.input = false;
}

function beforeUpload (res, uploadFile, uploadFiles, item) {
  let fileList = {};
  fileList.url = res.data.url;
  fileList.format = res.data.format;
  fileList.name = res.data.name;
  fileList.fullUrl = res.data.fullUrl;
  fileList.typeName = item.dictLabel;
  emit("onSuccess", fileList);
}

function beforeRemove (file) {
  emit("onRemove", file);
}

function onProgress (event, file, fileList) {
  file.percentage = Math.round((event.loaded * 100) / event.total);
}

function onPreview (file) {
  const url = file.response
    ? file.response.data.fullUrl
    : import.meta.env.VITE_APP_FILE_SERVICE_URL + file.url;
  window.open(
    `${import.meta.env.VITE_APP_FILE_PERVIEW_URL}?url=${base64EncodeUnicode(
      url
    )}`
  );
}

function editFile (file) {
  const url = file.response
    ? file.response.data.fullUrl
    : import.meta.env.VITE_APP_FILE_SERVICE_URL + file.url;
  // const url = import.meta.env.VITE_APP_FILE_SERVICE_URL + file.url;
  window.open(import.meta.env.VITE_APP_CONTEXT_PATH + `office?url=${url}&id=${url}&isEdit=true`);
}

function downloadFile (file) {
  const url = file.response
    ? file.response.data.fullUrl
    : import.meta.env.VITE_APP_FILE_SERVICE_URL + file.url;
  downloadFileFromUrl(url, file.name);
}

const removeFiled = (index, secondIndex, file) => {
  fileTypeList.value[index].fileList.splice(secondIndex, 1)
  emit("onRemove", file);
}
</script>

<style scoped lang="scss">
.file-type {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.file-type-icon {
  display: flex;
  align-items: center;
  cursor: pointer;

  .svg-icon {
    margin-left: 5px;
    margin-right: 2px;
    height: 1.2rem;
    width: 1.2rem;
  }
}

.file-upload {
  margin-left: 30px;
  margin-top: 5px;

  .file-upload-btn {
    font-size: 14px;
    color: #409eff;
    text-decoration: underline;
  }
}

.custom-file-list {
  margin-top: 10px;

  .custom-file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    //   background: #f5f5f5;
    margin-bottom: 5px;
    padding: 5px 0;
    border-radius: 4px;

    span {
      flex: 1;
      font-size: 14px;
      color: #606266;
      cursor: default;
    }
  }
}

.file-list-btn {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>