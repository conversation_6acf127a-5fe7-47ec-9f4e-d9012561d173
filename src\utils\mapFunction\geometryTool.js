
/**
 * 计算平面坐标面积
 * @param originCoords：GeoJSON 坐标
 * @param pType：几何对象类型，单面：Polygon 多面：MultiPolygon
 * @returns { area }：面积，单位：平方米
 * @constructor
 */
import * as olProj from 'ol/proj';
import olSRS from "@/utils/OpenLayers/olSRS";

export function PlanarArea(originCoords,pType) {
  let measureArea = 0
  if (!Array.isArray(originCoords)){
    console.error("请传入GeoJSON坐标数组！")
    return measureArea
  }
  // console.log("经纬度坐标：",originCoords)
  // 先暂时计算单面的
  const targetCoords = []
  if (pType === 'Polygon'){
    originCoords.forEach(coords => {
      const innerCoords = []
      coords.forEach(coord => {
        const numCoord = [Number(coord[0]),Number(coord[1])]
        const prjCoords = olProj.transform(numCoord,olSRS["4490"],olSRS["4522"])
        innerCoords.push(prjCoords)
        // innerCoords.push(coord)
      })
      targetCoords.push(innerCoords)
    })
    targetCoords.forEach(ringPoints => {
      measureArea += getArea(ringPoints)
    })
  } else if (pType === 'MultiPolygon'){
    originCoords.forEach(polygonCoords => {
      const innerPolygonCoords = []
      polygonCoords.forEach(coords => {
        const innerCoords = []
        coords.forEach(coord => {
          const numCoord = [Number(coord[0]),Number(coord[1])]
          const prjCoords = olProj.transform(numCoord,olSRS["4490"],olSRS["4522"])
          innerCoords.push(prjCoords)
          // innerCoords.push(coord)
        })
        innerPolygonCoords.push(innerCoords)
      })
      targetCoords.push(innerPolygonCoords)
    })
    targetCoords.forEach(ringPoints => {
      // 判断是否有孔洞，如果有孔洞，则需减去孔洞的面积
      if (ringPoints.length > 1){
        // 外环面积
        const outerArea = getArea(ringPoints[0])
        const holeAreas = ringPoints.slice(1).map(hole => {
          return getArea(hole)
        })
        measureArea = outerArea - holeAreas.reduce((sum,area) => {
          return sum + area
        },0)
      } else {
        measureArea += getArea(ringPoints[0])
      }
    })
  }
  return measureArea
}

/**
 * 计算多个面的面积
 */

export function getMultiPolygonArea(coordsCollection){
  let totalArea = 0
  coordsCollection.forEach(originCoords => {
    totalArea += PlanarArea(originCoords,'Polygon')
  })
  return totalArea
}

/**
 * @function SuperMap.Geometry.LinearRing.prototype.getArea
 * @description 获得当前几何对象区域大小，如果是沿顺时针方向的环则是正值，否则为负值。
 * @returns {number} 环的面积。
 * @Points: [[]] 坐标结构
 *
 */
function getArea(Points) {
  let area = 0.0;
  if (!Points || Points.length < 1){
    return area
  }
  const components = [];
  for (let kk = 0;kk < Points.length;kk++){
    components.push(Points[kk])
  }

  const firstPnt = components[0];
  const lastPnt = components[components.length - 1];
  if (firstPnt.x != lastPnt.x && firstPnt.y != lastPnt.y){
    components.push(firstPnt)
  }
  if (components && (components.length > 2)) {
    let sum = 0.0;
    for (let i = 0, len = components.length; i < len - 1; i++) {
      const b = components[i];
      const c = components[i + 1];
      sum += (b[0] + c[0]) * (c[1] - b[1]);
    }
    area = -sum / 2.0;
  }
  return Math.abs(area) ;
}

// 根据几何坐标计算长度
export function getDistance(coordinates){
  if (!Array.isArray(coordinates)){
    console.error("请传入数组坐标！")
  }
  let distance = 0
  for (let i = 1;i < coordinates.length;i++){
    const currentPoint = olProj.transform(coordinates[i],olSRS["4490"],olSRS["4522"])
    const previousPoint = olProj.transform(coordinates[i - 1],olSRS["4490"],olSRS["4522"])
    // const currentPoint = coordinates[i]
    // const previousPoint = coordinates[i - 1]
    console.log(currentPoint);
    console.log(previousPoint);
    const value = Math.pow(currentPoint[1] - previousPoint[1],2) + Math.pow(currentPoint[0] - previousPoint[0],2)
    distance += Math.sqrt(value)
  }
  // console.log("测量距离：",distance);
  return Math.abs(distance)
}
