import request from "@/utils/request";

/**
 * 开始码流转发
 * @returns {*}
 */
export const streamConvertersPost = (data) => {
    return request({
        url: '/uav/flightHub2/cloud_connect/stream-converters',
        method: 'post',
        data: data
    });
};

/**
 * 获取码流转发频道转码器
 * @returns {*}
 */
export const streamConvertersGet = (data) => {
    return request({
        url: '/uav/flightHub2/cloud_connect/stream-converters',
        method: 'get',
        params: data
    });
};

/**
 * 关闭码流转发
 * @returns {*}
 */
export const streamConvertersDelete = (converterId) => {
    return request({
        url: '/uav/flightHub2/cloud_connect/stream-converters/'+converterId,
        method: 'delete',
    });
};
