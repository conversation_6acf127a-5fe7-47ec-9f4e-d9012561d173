import request from "@/utils/request";

/**
 * 查询公司名下土地信息列表
 * @param query
 * @returns {*}
 */

export const listTopicLand = (query) => {
  return request({
    url: "/patrol/topicLand/list",
    method: "get",
    params: query,
  });
};

/**
 * 查询公司名下土地信息详细
 * @param id
 */
export const getTopicLand = (id) => {
  return request({
    url: "/patrol/topicLand/" + id,
    method: "get",
  });
};

/**
 * 新增公司名下土地信息
 * @param data
 */
export const addTopicLand = (data) => {
  return request({
    url: "/patrol/topicLand",
    method: "post",
    data: data,
  });
};

/**
 * 修改公司名下土地信息
 * @param data
 */
export const updateTopicLand = (data) => {
  return request({
    url: "/patrol/topicLand",
    method: "put",
    data: data,
  });
};

/**
 * 删除公司名下土地信息
 * @param id
 */
export const delTopicLand = (id) => {
  return request({
    url: "/patrol/topicLand/" + id,
    method: "delete",
  });
};
